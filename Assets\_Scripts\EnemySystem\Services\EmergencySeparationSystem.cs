using UnityEngine;
using System.Collections.Generic;
using BTR.EnemySystem;
using Pathfinding;

namespace BTR.EnemySystem
{
    /// <summary>
    /// Emergency separation system that immediately separates enemies when they get too close.
    /// Runs every frame to ensure enemies never cluster together.
    /// </summary>
    public class EmergencySeparationSystem : MonoBehaviour
    {
        [Header("Emergency Separation Settings")]
        [SerializeField] private float emergencyDistance = 1.5f;
        [SerializeField] private float emergencyForce = 15f;
        [SerializeField] private float maxEmergencyForce = 25f;
        [SerializeField] private bool enableEmergencyPushback = true;

        [Header("Real-time Monitoring")]
        [SerializeField] private bool enableContinuousMonitoring = true;
        [SerializeField] private int maxChecksPerFrame = 50;
        [SerializeField] private float violationThreshold = 2f;

        [Header("Debug")]
        [SerializeField] private bool enableDebugLogs = true;
        [SerializeField] private bool showDebugVisualization = true;

        // Tracking data
        private Dictionary<Transform, Vector3> lastFramePositions = new Dictionary<Transform, Vector3>();
        private Dictionary<Transform, float> violationCounts = new Dictionary<Transform, float>();
        private List<Transform> trackedEnemies = new List<Transform>();
        private List<(Transform, Transform)> violatingPairs = new List<(Transform, Transform)>();

        public static EmergencySeparationSystem Instance { get; private set; }

        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                if (enableDebugLogs)
                {
                    Debug.Log("[EmergencySeparationSystem] Emergency separation system initialized");
                }
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Update()
        {
            if (!enableContinuousMonitoring) return;

            UpdateTrackedEnemies();
            CheckForViolations();
            ApplyEmergencySeparation();
        }

        private void UpdateTrackedEnemies()
        {
            trackedEnemies.Clear();

            // Get all enemies from EnemyManager
            if (EnemyManager.Instance != null)
            {
                // Add legacy enemies
                foreach (var enemy in EnemyManager.Instance.Enemies)
                {
                    if (enemy != null && enemy.gameObject.activeInHierarchy)
                    {
                        trackedEnemies.Add(enemy.transform);
                    }
                }

                // Add new combat entities
                foreach (var entity in EnemyManager.Instance.CombatEntities)
                {
                    if (entity != null && entity.IsAlive && entity.GameObject.activeInHierarchy)
                    {
                        trackedEnemies.Add(entity.Transform);
                    }
                }
            }
        }

        private void CheckForViolations()
        {
            violatingPairs.Clear();
            int checksThisFrame = 0;

            for (int i = 0; i < trackedEnemies.Count && checksThisFrame < maxChecksPerFrame; i++)
            {
                var enemy1 = trackedEnemies[i];
                if (enemy1 == null) continue;

                for (int j = i + 1; j < trackedEnemies.Count && checksThisFrame < maxChecksPerFrame; j++)
                {
                    var enemy2 = trackedEnemies[j];
                    if (enemy2 == null) continue;

                    float distance = Vector3.Distance(enemy1.position, enemy2.position);

                    if (distance < emergencyDistance)
                    {
                        violatingPairs.Add((enemy1, enemy2));

                        // Track violation counts
                        if (!violationCounts.ContainsKey(enemy1))
                            violationCounts[enemy1] = 0f;
                        if (!violationCounts.ContainsKey(enemy2))
                            violationCounts[enemy2] = 0f;

                        violationCounts[enemy1] += Time.deltaTime;
                        violationCounts[enemy2] += Time.deltaTime;

                        if (enableDebugLogs && violationCounts[enemy1] > violationThreshold)
                        {
                            Debug.LogWarning($"[EmergencySeparationSystem] CRITICAL: {enemy1.name} and {enemy2.name} too close! Distance: {distance:F2}");
                        }
                    }

                    checksThisFrame++;
                }
            }

            if (enableDebugLogs && violatingPairs.Count > 0)
            {
                Debug.Log($"[EmergencySeparationSystem] Found {violatingPairs.Count} violating pairs this frame");
            }
        }

        private void ApplyEmergencySeparation()
        {
            if (!enableEmergencyPushback || violatingPairs.Count == 0) return;

            foreach (var (enemy1, enemy2) in violatingPairs)
            {
                if (enemy1 == null || enemy2 == null) continue;

                Vector3 direction = (enemy1.position - enemy2.position).normalized;
                float distance = Vector3.Distance(enemy1.position, enemy2.position);

                if (distance < 0.01f)
                {
                    // If enemies are exactly on top of each other, use random direction
                    direction = Random.insideUnitSphere.normalized;
                    direction.y = 0f;
                }

                // Calculate emergency force based on how close they are
                float forceMultiplier = Mathf.InverseLerp(emergencyDistance, 0f, distance);
                float currentForce = Mathf.Lerp(emergencyForce, maxEmergencyForce, forceMultiplier);

                // Apply immediate position adjustment
                Vector3 separationOffset = direction * (emergencyDistance - distance) * 0.5f;

                // Move both enemies apart
                ApplyEmergencyMovement(enemy1, separationOffset);
                ApplyEmergencyMovement(enemy2, -separationOffset);

                if (enableDebugLogs)
                {
                    Debug.Log($"[EmergencySeparationSystem] Applied emergency separation: {enemy1.name} <-> {enemy2.name}, force: {currentForce:F1}");
                }
            }
        }

        private void ApplyEmergencyMovement(Transform enemy, Vector3 offset)
        {
            // Apply position adjustment
            Vector3 newPosition = enemy.position + offset;
            enemy.position = newPosition;

            // Update any movement systems
            var followerEntity = enemy.GetComponent<FollowerEntity>();
            if (followerEntity != null)
            {
                // Force recalculation of path
                followerEntity.destination = newPosition;
            }

            var movementStrategy = enemy.GetComponent<BasicChaseMovementStrategy>();
            if (movementStrategy != null)
            {
                // Force movement strategy to update
                movementStrategy.ForceUpdate();
            }

            // Force separation service update
            if (EnemySeparationService.Instance != null)
            {
                EnemySeparationService.Instance.ForceUpdateSeparation(enemy);
            }

            // Force spacing enforcer validation
            if (MandatorySpacingEnforcer.Instance != null)
            {
                MandatorySpacingEnforcer.Instance.ForceValidation(enemy);
            }
        }

        /// <summary>
        /// Check if a specific enemy is violating emergency distance
        /// </summary>
        public bool IsViolatingEmergencyDistance(Transform enemy)
        {
            if (enemy == null) return false;

            foreach (var other in trackedEnemies)
            {
                if (other == null || other == enemy) continue;

                float distance = Vector3.Distance(enemy.position, other.position);
                if (distance < emergencyDistance)
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Get emergency separation statistics
        /// </summary>
        public (int totalEnemies, int violatingPairs, float avgViolationTime) GetEmergencyStats()
        {
            float totalViolationTime = 0f;
            foreach (var time in violationCounts.Values)
            {
                totalViolationTime += time;
            }

            float avgViolationTime = violationCounts.Count > 0 ? totalViolationTime / violationCounts.Count : 0f;
            return (trackedEnemies.Count, violatingPairs.Count, avgViolationTime);
        }

        /// <summary>
        /// Force immediate separation check and correction
        /// </summary>
        public void ForceEmergencySeparation()
        {
            UpdateTrackedEnemies();
            CheckForViolations();
            ApplyEmergencySeparation();

            if (enableDebugLogs)
            {
                Debug.Log($"[EmergencySeparationSystem] Forced emergency separation check - found {violatingPairs.Count} violations");
            }
        }

        private void OnDrawGizmos()
        {
            if (!showDebugVisualization || !Application.isPlaying) return;

            // Draw emergency distance circles for violating enemies
            Gizmos.color = Color.red;
            foreach (var enemy in trackedEnemies)
            {
                if (enemy == null) continue;

                if (violationCounts.ContainsKey(enemy) && violationCounts[enemy] > 0f)
                {
                    Gizmos.DrawWireSphere(enemy.position, emergencyDistance);
                }
            }

            // Draw violation connections
            Gizmos.color = Color.magenta;
            foreach (var (enemy1, enemy2) in violatingPairs)
            {
                if (enemy1 != null && enemy2 != null)
                {
                    Gizmos.DrawLine(enemy1.position + Vector3.up, enemy2.position + Vector3.up);
                }
            }
        }

        private void OnGUI()
        {
            if (!showDebugVisualization) return;

            var stats = GetEmergencyStats();

            GUILayout.BeginArea(new Rect(10, 220, 300, 150));
            GUILayout.Label("=== Emergency Separation ===");
            GUILayout.Label($"Tracked Enemies: {stats.totalEnemies}");
            GUILayout.Label($"Violating Pairs: {stats.violatingPairs}");
            GUILayout.Label($"Avg Violation Time: {stats.avgViolationTime:F2}s");

            if (GUILayout.Button("Force Emergency Check"))
            {
                ForceEmergencySeparation();
            }

            GUILayout.EndArea();
        }

        private void OnDestroy()
        {
            if (Instance == this)
            {
                Instance = null;
            }
        }
    }
}