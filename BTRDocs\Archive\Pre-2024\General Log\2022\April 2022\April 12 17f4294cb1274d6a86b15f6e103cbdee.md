# April 12

Wavespawning issue - have a good lead!

> Hi again,
> 

> Sorry it sounds like I was not very clear. The spawned items should indeed have a SpawnableIdentity component attached but the scene game object that you are cloning as part of your pooling solution should not. It sounds like everything is setup correctly though.
> 

> When you are destroying your items are you using ‘Object.Destroy’? If so, that could be the problem because that will only work correctly if you are not using a custom pooling solution and could be causing the problem. Not sure why it did not occur to me sooner to check this, as it is certainly the way most users would setup their game to start with. Instead it is recommended to use one of the ‘UltimateSpawing.Despawn(…)’ methods and pass in the game object or associated component. There will be no need to inform the spawner of the item destruction in this case as that will all be done via the single call. It will also redirect the final ‘Destroy’ call to your pooling code, and trigger all despawn events if any components on the spawned item implements ‘IDespawnEventReceiver’.
> 

> Hope that makes sense and hopefully it could be the final answer to the problem. Let me know if you are still having issues.
> 

Need to look at how im killing enemies and despawing in pool

[http://docs.poolmanager.path-o-logical.com/home/<USER>//docs.poolmanager.path-o-logical.com/home/<USER>

Seem to have a working solution! Emailed dev about it in comparison to their own recomendations, curious to hear back. Spawn and Pool assets are informed upon death of enemy, so that may be all that’s needed!

Enemy can pull pool name from parent object, since pool is always the parent object. Does this work moving forward? 

IMP - when an enemy is reused it doesn’t seem to properly come back. Some refinement of the enemy class and instantiation / make active will possibly need to be made

IMP - don’t forget the double shot from enemies - this is likely the particle system shooting off in the opposite direction, will have to verify and fix

Need to read up on Pool Manager - there is a LOT to it, may be handy