**Summary of Audio & Music Related Entries in 2024**

**General Audio Direction:**
*   Focus on meaningful music and sound design that is driven by gameplay and interactivity.
*   Draw inspiration from rhythm games like Rez to inform sound design with deeper meaning.

**Technical Issues & Fixes (FMOD & Koreographer):**
*   Addressed issues with Koreographer integration with the enemy twin snake boss.
*   Fixed music looping problems in Section 3.
*   Resolved Koreographer track issues breaking infinite areas.
*   Debugged and fixed problems related to shooting and rewind music phases, potentially linked to FMOD and Koreographer.
*   Identified and fixed a Koreographer issue caused by accidentally deleting a Perc track.

**Sound Design & Implementation:**
*   Explored polyrhythmic shooting tied to music.
*   Considered tempo/music changes with speed dash.
*   Researched techniques to make audio "POP" with color, referencing a GDC talk on audio as the "player-whisperer".
*   Experimented with Koreos for alternating patterns.
*   Improved bounce sounds with more note choices and harmonic surprise.
*   Upgraded FMOD to version 2.03 for new features like multiband and frequency sidechain.
*   Improved enemy lock-on sounds.
*   Planned creature sound design based on "Blue/Red - Soft/Angry" concept.
*   Designed pre-attack sounds using a "bookend" method.
*   Considered using Granulate granular synth.
*   Explored Transient/Body/Tail structure for sound clips in FMOD.
*   Experimented with spatializer for bigger sound impact.
*   Looked into music and lighting integration.
*   Considered bus structure for sound sections (Sub/Low/Mids/Highs) and sidechaining.

**Design Ideas & Concepts:**
*   Inspired by X-Men Sentinel fight for visual and musical synchronization (flashes of color and light).
*   Explored "Hyperpassive" as a rubric for music and interactivity.
*   Focused on design fundamentals, thinking of the player as the star of a music video.
*   Addressed level transition sounds and issues with locking on during level transitions.
*   Considered FX for time slow/rewind glitches and their utility in gameplay, including potential Doppler effect on projectiles during slow time.
*   Revisited rhythmic enemy shooting patterns controlled by a projectile manager.
*   Explored making success in gameplay easy to understand and showing room for improvement.
*   Noted the interconnectedness of time, music, and health in the game, where music stops when time stops.