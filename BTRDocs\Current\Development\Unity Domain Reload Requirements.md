# Unity Domain Reload Requirements

## Critical Issue
Unity's domain reload system resets all static fields when stopping/starting games in the editor, but GameObjects marked with `DontDestroyOnLoad` persist. This creates broken singleton states and system failures.

## Mandatory Requirements for All Systems

### 1. Singleton Pattern Implementation
**REQUIRED**: All singleton patterns MUST implement domain reload handling:

```csharp
[RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.SubsystemRegistration)]
private static void ResetStaticData()
{
    _instance = null;
    _applicationIsQuitting = false;
    // Reset any other static fields
}
```

### 2. Static Data Management
**REQUIRED**: All static fields that hold references or state MUST be reset during domain reload:
- Static collections (Lists, Dictionaries, etc.)
- Static references to GameObjects/Components
- Static configuration data
- Static event subscriptions

### 3. Awake Method Robustness
**REQUIRED**: Awake methods MUST handle reinitialization scenarios:
- Check if existing instance is actually valid
- Handle cases where static reference is lost but GameObject exists
- Properly reinitialize when needed

### 4. Event System Cleanup
**REQUIRED**: All event subscriptions MUST be properly cleaned up:
- Unsubscribe in OnDestroy
- Clear static event handlers during domain reload
- Re-subscribe during reinitialization

## Systems That Need Audit

### High Priority (Likely Affected)
- [ ] ProjectileManager ✅ FIXED
- [ ] EnemyManager
- [ ] PlayerManager
- [ ] AudioManager systems
- [ ] TimeManager/EpochTimekeeper
- [ ] Any custom singleton managers

### Medium Priority
- [ ] Event systems (EnemyEventSystem, etc.)
- [ ] Pool managers
- [ ] Configuration managers
- [ ] Debug systems

## Testing Protocol

### Manual Testing
1. Start game in editor
2. Verify system works correctly
3. Stop game
4. Start game again
5. Verify system still works correctly
6. Check console for any null reference errors

### Automated Testing
- Create unit tests that simulate domain reload scenarios
- Test singleton access patterns
- Verify event subscription cleanup

## Implementation Checklist

For each singleton/manager system:
- [ ] Add `RuntimeInitializeOnLoadMethod` for static field reset
- [ ] Enhance Awake method for reinitialization handling
- [ ] Add proper OnDestroy cleanup
- [ ] Add debug logging for domain reload scenarios
- [ ] Test stop/start game cycle
- [ ] Document any special considerations

## Common Patterns to Avoid

### ❌ Bad Pattern
```csharp
public static MyManager Instance { get; private set; }

private void Awake()
{
    if (Instance == null)
    {
        Instance = this;
        DontDestroyOnLoad(gameObject);
    }
    else
    {
        Destroy(gameObject);
    }
}
```

### ✅ Good Pattern
```csharp
private static MyManager _instance;

[RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.SubsystemRegistration)]
private static void ResetStaticData()
{
    _instance = null;
}

public static MyManager Instance
{
    get
    {
        if (_instance == null)
            _instance = FindObjectOfType<MyManager>();
        return _instance;
    }
}

private void Awake()
{
    if (_instance == null)
    {
        _instance = this;
        DontDestroyOnLoad(gameObject);
        Initialize();
    }
    else if (_instance != this)
    {
        if (_instance.gameObject == null)
        {
            _instance = this;
            Initialize();
        }
        else
        {
            Destroy(gameObject);
        }
    }
}
```

## Priority Action Items

1. **Immediate**: Audit all existing singleton managers
2. **Short-term**: Create automated tests for domain reload scenarios
3. **Long-term**: Establish code review checklist including domain reload considerations

## Notes
- This issue was discovered when ProjectileManager.Instance returned null after stopping/starting the game
- Enemies stopped shooting because ProjectileManager singleton was broken
- The fix involved proper static field reset and enhanced Awake handling
- This is a systemic issue that likely affects multiple systems
