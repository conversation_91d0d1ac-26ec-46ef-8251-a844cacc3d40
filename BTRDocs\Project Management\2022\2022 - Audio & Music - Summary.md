# 2022 - Audio & Music - Summary

This document summarizes the 2022 audio and music development notes, focusing on key decisions, experiments, and issues.

## Key Summary Points:

*   **Haptic Feedback Exploration (April):** Explored using Razer haptic headphones for music creation and experimented with translating hi-hat patterns to controller vibrations using Koreographer and MIDI. Latency issues were noted for fast musical cues, but potential for effects was recognized.
*   **FMOD Integration Focus (March):** Deep dive into FMOD 2.0 features like Labeled Parameters, Global Parameters, Speed Parameter, Flexible Parameters, Multi-Instruments, and Command Instruments. Implementation of sound effects for core actions like locking, shooting, and tagging.
*   **Sound Design System (March):** Identified the need for a clear sound design language to differentiate between projectiles, enemies, and ambient sounds, including distinct end tags for sound combinations.
*   **Dynamic Music Progression (Various Dates):**  Planned for music tracks to evolve based on gameplay events such as enemy waves and barrier transitions. Investigated transition markers and JSON for managing track changes.
*   **Technical Challenges (Various Dates):**  Addressed technical hurdles in achieving clean audio looping, triggering new sounds effectively, and seamless integration of Koreographer and FMOD within Unity.
*   **Inspirational Sources (March, May):** Drew inspiration from resources like the Mixolumia Music doc, <PERSON>'s HyperPassive, space vortex soundscapes, Space Invaders' audio, and various FMOD and Unity tutorials.
*   **Open Questions and Future Direction (Various Dates):** Raised key questions about interactive performance in music composition, managing event IDs in Unity, and defining the overarching audio direction for the game.

This summary captures the main points and progression of audio and music development throughout 2022, highlighting both creative explorations and technical problem-solving efforts.