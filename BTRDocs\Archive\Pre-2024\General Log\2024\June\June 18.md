# June 18

Doing some dev build optimization to change things up from working on coding problem of level changes

Waking all these game objects at start cause a big frame spike 

![Untitled](June%2018%20129035f997e64f1db9d605749b914beb/Untitled.png)

Debug Logs from Death of PSB causing spikes

![Untitled](June%2018%20129035f997e64f1db9d605749b914beb/Untitled%201.png)

Seems like using Deep Profiler was causing some issues, not good to use on device connected live i think, probably better for a separate device 

Changing projectile state based to use update for lifetime isntead of a seperate coroutine. this is supposed to be more performant. debug!

Also causing calls to projectiel manager to be paced across several frames, that way a higher frame rate is maintaned and less of a spike. Seems to be working well!

Replacing dotween with primetween, may be better. taking a look!

I dont entirely see the animation happening, but the code looks right. need to flag this, may be some adjustment needed to get it working properly. 

Back to DOtween for the time being - for ProjecitleStateBased at least

Moved Projecitle lifetine to Projectile Manager now - for performance!

Projectile Radar prefabs are pooled as well, added to projectiles wehn shot by enemy

definitely closer to 60! not dropping to 15 frequently like before as well

Still need to fix transition between scenes, dont forget