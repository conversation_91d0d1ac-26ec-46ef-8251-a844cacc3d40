**January 10th, 2024:**

*   Use gravity rush enemies as inspiration for my enemies - look up the various types

    ![Untitled](January%2010th,%202024%206ab92944aeed4d9c85ad8aad0b842133/Untitled%201.png)
*   Want to apply this to text and ui in Unity

    [https://twitter.com/ashlee3dee/status/1745524506872176970](https://twitter.com/ashlee3dee/status/1745524506872176970)

**February 1st, 2024:**

*   Useful for Ophanim

    [Ring World Using Random Flow](https://www.youtube.com/watch?v=YHkhEyh5G68)
*   [Unity VFX Graph：Use SDF to make model particle effects](https://www.youtube.com/watch?v=FBP9k6W48vM&t=905s)

    Model for how I implemented SDF particle effects - May need as reference

**February 5th, 2024:**

*   to make things brighter / more emissive / stand out, HDR intensity needs to be adjusted along with color. Makes a big difference!

**February 6th, 2024:**

*   Added HBAO and changed Bakery settings for rendering

    Not a real noticeable improvement, something to revisit later in optimizing pipeline
*   Adding HDR color to Joost shader, looking into character being more visible. A good start!
*   use this effect?

    [Recreating the Water Caustics Effect from Blade Runner 2049](https://www.youtube.com/watch?v=Oh21hYx_Jbk)

**February 8th, 2024:**

*   Adjusting see through material for enemies - making it less visible

**February 13th, 2024:**

*   [Unity VFX Graph：Ribbons and Balls (Event control)](https://www.youtube.com/watch?v=h9ApA9tHiqk&list=WL&index=12)
*   Hyperpassive as a rubric for the music and interactivity in Beat Remake

    [https://www.youtube.com/watch?v=S4pHJN8YclE](https://www.youtube.com/watch?v=S4pHJN8YclE)

**February 23rd, 2024:**

*   Adjusted shader for galaxy texture, combine with other shaders, etc
*   Got this message from Amplify Shader Editor
    *   A new URP version 14.0.10 was detected and new templates are being imported.
        Please hit the Update button on your ASE canvas to recompile your shader under the newest version.

**February 25th, 2024:**

*   Added Snake head to infinite track, so it’s like you’re being chased by ouroboros
    *   Needs refinement, liekly doesnt need to be on its own spline

**February 27th, 2024:**

*   Found a snake that seems good, try applying new animator controls on it, open jaw, see what we can do for chasing player

**February 29th, 2024:**

*   What kind of snake mouth with some aniamtion to pull it off though

**March 4, 2024:**

*   The eyeballs could be littering the snakes - what other aspects could be present

**March 11, 2024:**

*   Added Sensor Toolkit for enabling / disabling these static enemy shooters

    Need to add shooters to other ourobors sections
*   Used Hexagon / Ostagon like things, like a crystal!

**April 22, 2024:**

*   Find the fun!!

**August 14, 2024:**

*   Thoughts on sound design
    *   [https://www.youtube.com/watch?v=oxeD8kuCT\_g](https://www.youtube.com/watch?v=oxeD8kuCT_g)
    *   Bookends and Highway method
        *   Use FilterFreak and Speakerphone for processing your SFX
        *   Plan creatures as Blue / Red - Soft / Angry

**August 16/17, 2024:**

*   This seems to have disappeared. Interesting!
*   Thoughts on sound design
    *   [https://www.youtube.com/watch?v=oxeD8kuCT\_g](https://www.youtube.com/watch?v=oxeD8kuCT_g)
    *   Bookends and Highway method
        *   Use FilterFreak and Speakerphone for processing your SFX
        *   Plan creatures as Blue / Red - Soft / Angry

**August 30, 2024:**

*   Some recommendations on Game Design
    *   GDC talks
    *   Designer Notes Podcast
    *   Google Scholar
        *   games and culture
        *   gamestudies.org
    *   Abstract - The Art of Design

**December 11, 2024**

*   Idea- Shrink and grow cursor to indicate target locking
*   Change Reticle over to a system based on Shapes asset pack
*   Need proper effect for ricochet - blast no longer works?

**October 21, 2024:**

*   Updating Cinemachine 3 camera to be a bit more refined

**September 7/8/9, 2024**

*   Need to fix snake chase scene, but also go over all my notes
    [Game Name Ideas](September%207%208%209%20fb8457ead97e413f9459f1cc8109f64e/Game%20Name%20Ideas%20ddff5910d0534bfa893528b399025049.md)
*   FMOD plugin development
    *   Implement bit crusher better to exclude elements
        *   Item Pickups
            *   Dodge to grab?
*  Add spot lights to areas i think
*  give more variation
