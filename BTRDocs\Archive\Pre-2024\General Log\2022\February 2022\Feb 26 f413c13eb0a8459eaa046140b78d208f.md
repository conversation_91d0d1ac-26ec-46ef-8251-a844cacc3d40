# Feb 26

Adapting to Master Audio for testing - is this better than just unity’s audio system?

Also realizing Rewind System needs to be programmed better - no delay between number of rewinds, some errors that need to be fixed here - also too man if’s!!!

Is rewind important to my game? Might not be able to use FMOD if so !

Was having issue with Master Audio implementation, appears to be 2D/3D  issue

Shooting/Locking/Tag all work when forced to 2D

Really need to fix lock on - doesn’t seem to highlight and lock target. visualize this with debug tools?

Use new song! Make a level using playlists etc?

Look through this