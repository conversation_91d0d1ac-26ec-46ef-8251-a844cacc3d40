# Sept 1 - 3

Plasytesting checklist

- each wave should have minimum number of enemies killed to progress to next
    - Need effect that shows wave end - explosion, burst at center of level?
- fixing camera problems in infinite snake sections
- fixing prefab static shooter placements and projectile lfietime issues / shooting directions, most evident in infinite snake section

Integrating proper death → restart to loop

Major Game Manager restructure

- need to fix scene transitions
- Msotly working!

Split Game Manager, <PERSON><PERSON><PERSON>, Projectile Manager into many classes. 

Also Projectile State Based! Much easier to deal with these things now

May do more tomorrow with enemies etc. 

Building out QTE

Ideas about a tutorial 

IMP: Fix projectile accuracy - nothitting form palyer shot state and not so accurate when shot from enemies

- theres an ensure hit methow now, im not sure if thats going to be game breaking and jsut patch work on top of a system thats not fucntioning. keep an eye on this

Killing enemies gives a score value of 2*the start health - this is a value to play with 

Setup projectiles so enemy shot state is affected by time scale but other states are not affected byt the time scale - done!

Weird Fmod issues with automation maybe? cant get rewind sounds to play - automation is just not triggered i think? Odd issues that need more investigation. 

Lock seems to work? Make a new paramter - play with this?