using UnityEngine;
using BTR.Projectiles;

namespace BTR.Testing
{
    /// <summary>
    /// Test script for the new movement strategy system.
    /// Demonstrates how to use and switch between different movement strategies.
    /// </summary>
    public class MovementStrategyTest : MonoBehaviour
    {
        [Header("Test Configuration")]
        [SerializeField] private bool runTestOnStart = true;
        [SerializeField] private ProjectileEntity? testProjectile;
        [SerializeField] private Transform? testTarget;

        [Header("Strategy Testing")]
        [SerializeField] private MovementStrategyType initialStrategy = MovementStrategyType.Straight;
        [SerializeField] private bool enableStrategyOnStart = true;
        [SerializeField] private float testDuration = 5f;

        [Header("Test Parameters")]
        [SerializeField] private float testSpeed = 25f;
        [SerializeField] private float testLifetime = 10f;
        [SerializeField] private bool testHoming = false;

        private float testStartTime;
        private bool testRunning = false;

        private void Start()
        {
            if (runTestOnStart)
            {
                StartMovementStrategyTest();
            }
        }

        private void Update()
        {
            if (testRunning && Time.time - testStartTime > testDuration)
            {
                CompleteTest();
            }
        }

        [ContextMenu("Start Movement Strategy Test")]
        public void StartMovementStrategyTest()
        {
            Debug.Log("=== Movement Strategy Test Started ===");

            if (!ValidateTestSetup())
                return;

            // Setup projectile
            SetupTestProjectile();

            // Enable movement strategy
            if (enableStrategyOnStart)
            {
                EnableMovementStrategy();
            }

            testStartTime = Time.time;
            testRunning = true;

            Debug.Log($"Test will run for {testDuration} seconds");
        }

        [ContextMenu("Test Straight Movement")]
        public void TestStraightMovement()
        {
            if (testProjectile?.coreComponent != null)
            {
                testProjectile.coreComponent.SetMovementStrategy(true, MovementStrategyType.Straight);
                Debug.Log("✅ Switched to Straight Movement Strategy");
                LogStrategyStatus();
            }
        }

        [ContextMenu("Test Homing Movement")]
        public void TestHomingMovement()
        {
            if (testProjectile?.coreComponent != null)
            {
                testProjectile.coreComponent.SetMovementStrategy(true, MovementStrategyType.Homing);

                // Set target for homing
                if (testTarget != null)
                {
                    testProjectile.coreComponent.SetTarget(testTarget);
                }

                Debug.Log("✅ Switched to Homing Movement Strategy");
                LogStrategyStatus();
            }
        }

        [ContextMenu("Disable Movement Strategy")]
        public void DisableMovementStrategy()
        {
            if (testProjectile?.coreComponent != null)
            {
                testProjectile.coreComponent.SetMovementStrategy(false);
                Debug.Log("✅ Movement Strategy Disabled - Using Legacy Movement");
                LogStrategyStatus();
            }
        }

        [ContextMenu("Log Strategy Status")]
        public void LogStrategyStatus()
        {
            if (testProjectile?.coreComponent != null)
            {
                Debug.Log("--- Movement Strategy Status ---");
                Debug.Log(testProjectile.coreComponent.GetMovementStrategyStatus());
                Debug.Log("--- End Status ---");
            }
        }

        private bool ValidateTestSetup()
        {
            if (testProjectile == null)
            {
                Debug.LogError("[MovementStrategyTest] No test projectile assigned!");
                return false;
            }

            if (testProjectile.coreComponent == null)
            {
                Debug.LogError("[MovementStrategyTest] Test projectile missing ProjectileCore component!");
                return false;
            }

            if (testTarget == null)
            {
                Debug.LogWarning("[MovementStrategyTest] No test target assigned - homing tests may not work properly");
            }

            return true;
        }

        private void SetupTestProjectile()
        {
            // Setup projectile with test parameters
            testProjectile.SetupProjectile(
                damage: 10f,
                speed: testSpeed,
                lifetime: testLifetime,
                homing: testHoming,
                scale: 1f,
                target: testTarget
            );

            Debug.Log($"✅ Test projectile setup complete - Speed: {testSpeed}, Lifetime: {testLifetime}");
        }

        private void EnableMovementStrategy()
        {
            testProjectile.coreComponent.SetMovementStrategy(true, initialStrategy);

            // Set target if using homing strategy
            if (initialStrategy == MovementStrategyType.Homing && testTarget != null)
            {
                testProjectile.coreComponent.SetTarget(testTarget);
            }

            Debug.Log($"✅ Movement strategy enabled: {initialStrategy}");
            LogStrategyStatus();
        }

        private void CompleteTest()
        {
            testRunning = false;
            Debug.Log("=== Movement Strategy Test Completed ===");
            LogStrategyStatus();

            // Log final projectile status
            if (testProjectile != null)
            {
                Debug.Log("--- Final Projectile Status ---");
                Debug.Log(testProjectile.GetComponentStatus());
                Debug.Log("--- End Final Status ---");
            }
        }

        private void OnDrawGizmos()
        {
            // Draw test setup visualization
            if (testProjectile != null && testTarget != null)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawLine(testProjectile.transform.position, testTarget.position);

                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(testTarget.position, 1f);

                Gizmos.color = Color.blue;
                Gizmos.DrawWireSphere(testProjectile.transform.position, 0.5f);
            }
        }
    }
}
