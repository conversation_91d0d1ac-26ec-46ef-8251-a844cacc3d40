# Unity Assets

Procedural UI - <PERSON><PERSON> Flair

[https://assetstore.unity.com/packages/tools/gui/procedural-ui-image-52200](https://assetstore.unity.com/packages/tools/gui/procedural-ui-image-52200)

Amplify Impostors

**Master Audio AAA Sound** - use this for randomization??

Works with <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as well

Kinematic Character Controller

Ignis - All fire! Very Cool!

Highlight Plus - highlight selectable items thing

Realistic Effects Pack 4

Lots of stuff!

[https://assetstore.unity.com/publishers/5224](https://assetstore.unity.com/publishers/5224)

Final IK and Puppetmaster

Mentioned for VR in particular as good for mapping

ODIN - finally give this a try?

EasySave

Vegetation Engine + Ignis??? Try something?

Nature Renderer

Enivro = day/night cycles

RealIvy 2 growing ivy, interesting!

ObiRope

Rainbow Heirarchy - color your objects in scene list

Bakery!

Paint in 3D

good vfx! - [https://assetstore.unity.com/publishers/8569](https://assetstore.unity.com/publishers/8569)

UI Builder - Collection of premade sprites / fonts/ colors

- great for prototyping!

Easy Mobile Pro - good for cross platform mobile!

Pathologic or another pooling system recommended

Breadcrumb AI - good basic enemy AI system

A* pathfinding Project Pro - Advanced AI

- supports dynamicly shifting terrain - useful!

PlayMaker alongside coding mentioned as useful? But need to dig into it's own system a lot

BehaviourDesigner recommend as well - good AI pack - 

NodeCanvas - highlighted as less feature filled - simple - very good

Bolt - can automate your script into nodes - interesting!

- can be slow? need to look into it
- also seems cool - look into it!

Curved World!

[Assets Sorted](Unity%20Assets%208c7cfbbc2e434620acf53d6c11bfff1d/Assets%20Sorted%2084de4d11648f4970b67eb11fc1dbc691.md)

[Mesh Effects Notes](Unity%20Assets%208c7cfbbc2e434620acf53d6c11bfff1d/Mesh%20Effects%20Notes%2099e381df18d54e349d1f82c1f2b00d6e.md)