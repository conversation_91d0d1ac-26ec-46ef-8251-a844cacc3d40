March 28th 2020

Procedural Boss ex - https://www.youtube.com/watch?v=LVSmp0zW8pY&t=124s

Enemy ideas - Krang-like

IDEA - At the end of odd grouping of locked on targets, play a tag to even it out!

April 4th

Reviewing Post-Processing https://www.youtube.com/watch?v=9tjYz6Ab0oc

April 5th

Doing some of Particles tutorial - https://www.youtube.com/watch?v=hyBbcFCvDR8&

April 8th

Added Cube Snake - https://learn.unity.com/tutorial/using-animation-rigging-damped-transform?projectId=5f9350ffedbc2a0020193331#5faddb95edbc2a0fd05a3512

April 22

Look at Rez Ref tracks and think level design - FITNESSS ?

Mayy 11th

Look up Hindsight game trailer for effect

May 16

Unity Events - https://www.youtube.com/watch?v=OuZrhykVytg&t=12s

May 24

Mechanically inspired by helix jumper

https://assetstore.unity.com/packages/templates/packs/helix-smash-with-live-auto-generator-147944

May 27

Refer to VJ videos

https://answers.unity.com/questions/1250587/how-can-i-rotate-an-object-to-a-specific-angle-ove.html

Went back to old basic rotate- issue flagged for consultation!

June 18

https://www.youtube.com/watch?v=GioRYdZbGGk

Can make a Material with flat "base map" of level, apply it to a plane, and build from there

Lots of good greyboxing advice in for 20 minutes

August 3

https://www.youtube.com/watch?v=hVBpwwjqM00

Refer to action blocks Figma file

Try this asset for exploration? https://assetstore.unity.com/packages/templates/packs/space-journey-42259 - downloaded

August 9

Found this - https://www.resurgamstudios.com/cgf-manual

Seems like a cool way to have aim assist for bullets

August 16

https://www.youtube.com/watch?v=CTBor4rhnQs&t=1s

Thinking about Level Design - Radically Nonlinear Level Design

Discuss Doom 2016's levels. Combo of linear areas with combat arenas

Goals were constant movement and improvisation of navigation

Look up this Doom 2016 talk - push forward combat?

MDA framework for game analysis mentioned

What about Linear level Design?

Watched this on visual novels as well

https://www.youtube.com/watch?v=vrxz3s0L8F8

August 21

Boomerang X Unity Dev Spotlight - https://www.youtube.com/watch?v=L7lf2VnTC74

Sept 7th

http://codebetter.com/patricksmacchia/2008/11/19/an-easy-and-efficient-way-to-improve-net-code-performances/

Nov. 29

https://thegamedev.guru/unity-cpu-performance/object-pooling/#how-to-use-the-new-object-pooling-api-in-unity-2021

or do basic pooling tutorial

https://learn.unity.com/tutorial/introduction-to-object-pooling#5ff8d015edbc2a002063971c

Dec 7

Cinemachine tutorials

Dec 9

Relevant? https://forum.unity.com/threads/test-if-prefab-is-an-instance.562900/

December 14

Did some research on behaviour designer

December 16

https://opsive.com/support/documentation/behavior-designer/referencing-scene-objects/

December 19

Conditional Aborts - https://www.youtube.com/watch?v=GFsK5x6ZW7k

December 20

Followed advice in this video

Conditional Aborts - https://www.youtube.com/watch?v=GFsK5x6ZW7k

December 31

May help - [https://www.opsive.com/forum/index.php?threads/patrol-waypoints-disappear-after-changing-scene.4727/](https://www.opsive.com/forum/index.php?threads/