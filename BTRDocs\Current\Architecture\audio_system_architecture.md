---
systems: [audio]
components: [fmod, events, management]
tags: [game-system, audio]
priority: p1
last_touched: 2025-04-09
links:
  - ![[Systems/Enemies/Architecture#^audio-integration]]
  - ![[Systems/Projectiles/Implementation#^audio-hooks]]
---
# BTR Audio System Architecture
[[Sound Design Principles]] → [[Game Audio Implementation]]

```mermaid
flowchart TB
    %% Core Audio Components
    AudioManager[AudioManager]
    FmodOneshots[FmodOneshots]
    FMODCustomLogger[FMODCustomLogger]
    FMODFilterControl[FMODFilterParameterControl]
    
    %% Game Systems
    GameManager[GameManager]
    EnemyAudioManager[EnemyAudioManager]
    ProjectileAudio[ProjectileAudioManager]
    MusicManager[MusicManager]
    
    %% Event System
    GameEvents[GameEvents]
    
    %% FMOD Integration
    FMODStudio[FMOD Studio]
    
    %% Core Audio Relationships
    AudioManager --> FmodOneshots
    AudioManager --> FMODCustomLogger
    AudioManager --> FMODFilterControl
    
    %% System Integration
    GameManager --> AudioManager
    AudioManager --> EnemyAudioManager
    AudioManager --> ProjectileAudio
    AudioManager --> MusicManager
    
    %% Event System Integration
    GameEvents --> AudioManager
    GameEvents --> MusicManager
    
    %% FMOD Connection
    AudioManager --> FMODStudio
    FmodOneshots --> FMODStudio
    FMODFilterControl --> FMODStudio
    
    %% Subsystem Audio Management
    EnemyAudioManager --> FMODStudio
    ProjectileAudio --> FMODStudio
    MusicManager --> FMODStudio

    %% Styling
    classDef core fill:#f9f,stroke:#333,stroke-width:2px
    classDef system fill:#bbf,stroke:#333,stroke-width:2px
    classDef support fill:#bfb,stroke:#333,stroke-width:2px
    classDef external fill:#fbb,stroke:#333,stroke-width:2px
    
    class AudioManager,FmodOneshots,FMODCustomLogger,FMODFilterControl core
    class GameManager,EnemyAudioManager,ProjectileAudio,MusicManager system
    class GameEvents support
    class FMODStudio external
```

## Color Legend
- 🟪 Core (Purple): Central audio components
- 🟦 Systems (Blue): Game system integrations
- 🟩 Support (Green): Supporting features
- 🟥 External (Red): FMOD integration

## System Description
This diagram details the Audio System's architecture, showing:

1. **Core Components**
   - AudioManager as central coordinator
   - FMOD integration components
   - Custom audio controls
   - Logging system

2. **Game Integration**
   - Enemy audio management
   - Projectile sound effects
   - Music system
   - Game state audio

3. **Event System**
   - Game state audio triggers
   - Music state management
   - Event-based sound effects

4. **FMOD Integration**
   - Studio event management
   - Parameter control
   - Custom logging
   - Oneshot system

5. **Performance Features**
   - Async audio loading
   - Instance pooling
   - Cancellation support
   - Error handling

[[Architecture/architecture_overview|Back to Overview]]