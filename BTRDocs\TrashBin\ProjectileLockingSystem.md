# Projectile Locking System Analysis

## Overview
The projectile locking system allows players to lock onto enemy projectiles and redirect them. This document breaks down the entire process, requirements, and potential failure points.

## Table of Contents
- [[#Core Components]]
- [[#Locking Process]]
- [[#Shooting Process]]
- [[#Failure Points]]
- [[#Debug Checklist]]

## Core Components

### Required Components
- `PlayerLocking`: Main controller for locking mechanics
- `CrosshairCore`: Handles input and musical timing
- `StaminaController`: Controls rewind ability
- `ProjectileStateBased`: Base projectile behavior
- `PlayerLockedState`: Temporary state for locked projectiles (returns to pool immediately)
- `EnemyShotState`: Initial state of lockable projectiles

### Critical References
```mermaid
graph TD
    A[PlayerLocking] --> B[CrosshairCore]
    A --> C[StaminaController]
    A --> D[SquareReticle]
    A --> E[AimAssistController]
    B --> F[PlayerInputActions]
    B --> G[Koreographer]
    B --> H[AudioManager]
    I[ProjectilePool] --> J[ProjectileManager]
    J --> K[ProjectileJobSystem]
```

## State Transitions

### 1. Initial State (EnemyShotState)
```csharp
Properties:
- homing = target != null
- targetLocking = true
- isPlayerShot = false
- accuracy = ProjectileManager.Instance.projectileAccuracy
```

### 2. Locked State (PlayerLockedState)
```csharp
Properties:
- isPlayerShot = true
- homing = false
Action: Immediately returns to pool
Purpose: Used only for lock counting
```

### 3. Shooting State (PlayerShotState)
```csharp
Properties:
- isPlayerShot = true
- homing = hasAssignedTarget
- accuracy = 1.0f
- rotateSpeed = 20f
```

## Locking Process

### 1. Input Detection
- Input System: `DefaultControls` (New Input System)
- Method: `CrosshairCore.CheckLockProjectiles()`
- Requirements:
  - `playerInputActions.Player.LockProjectiles.ReadValue<float>() > 0`
  - `Time.timeScale != 0f`

### 2. Musical Timing
- Triggered by: `OnMusicalLock(KoreographyEvent)`
- Requirements:
  - Koreographer instance must be active
  - Event `eventIDShooting` must be registered

### 3. Detection Box
- Method: `PlayerLocking.OnLock()`
- Detection Types:
  1. SphereCast (Primary):
     ```csharp
     radius = Max(adjustedBoxSize.x, adjustedBoxSize.y) / 2f
     origin = reticlePos
     direction = reticleForward
     ```
  2. OverlapBox (Secondary/Backup):
     ```csharp
     center = reticlePos + (reticleForward * (adjustedBoxSize.z / 2f + offsetFromReticle))
     size = adjustedBoxSize * detectionBoxWidthMultiplier
     ```

### 4. Lock Validation
Method: `TryLockOntoBullet()`
Requirements:
1. `staminaController != null && staminaController.canRewind`
2. `crosshairCore.CheckLockProjectiles() == true`
3. `projectile.GetCurrentStateType() == typeof(EnemyShotState)`
4. `lockedProjectileCount < maxTargets`

## Shooting Process

### 1. Launch Trigger
Method: `CrosshairCore.OnMusicalShoot()`
Requirements:
- `(!CheckLockProjectiles() || playerLocking.triggeredLockFire)`
- `playerLocking.GetLockedProjectileCount() > 0`
- `Time.timeScale != 0f`

### 2. Projectile Creation
Method: `PrepareProjectilesToLaunch()`
Process:
1. Gets count of locked projectiles
2. Resets lock count
3. Creates new projectiles from pool
4. Sets up projectile properties:
   ```csharp
   damage = 10f
   speed = 50f
   lifetime = 10f
   ```
5. Registers with ProjectileManager

### 3. Target Assignment
Two scenarios:
1. With Enemy Targets:
   ```csharp
   - Uses enemyTargetList for direction
   - Applies damage based on lockedProjectileDamages
   - Creates PlayerShotState with target
   ```
2. Without Targets:
   ```csharp
   - Shoots projectiles forward
   - Uses default damage values
   - Creates PlayerShotState without target
   ```

## Failure Points

### Input System Failures
- [ ] `playerInputActions` not initialized
- [ ] Input action map not enabled
- [ ] Input binding missing/incorrect
- [ ] Time scale affecting input reading

### Component Reference Failures
- [ ] Missing `CrosshairCore` reference
- [ ] Missing `StaminaController` reference
- [ ] Null `RaySpawn` GameObject
- [ ] Unassigned `projectileLayer`
- [ ] Missing `ProjectilePool` instance
- [ ] Missing `ProjectileManager` instance

### Detection Failures
- [ ] Box/sphere cast size too small
- [ ] Incorrect layer mask
- [ ] Projectile moving too fast
- [ ] Detection box offset incorrect

### State Failures
- [ ] Projectile in wrong state
- [ ] `staminaController.canRewind` false
- [ ] Maximum targets reached
- [ ] Projectile already locked
- [ ] State transition failure
- [ ] Pool return failure

### Musical Timing Failures
- [ ] Koreographer events not registered
- [ ] Event timing misaligned
- [ ] Audio system not initialized
- [ ] Missing FMOD components

## Debug Checklist

### Visual Debugging
1. Enable debug visualization:
   ```csharp
   PlayerLocking.showDebugVisuals = true;
   ```
2. Check detection box size and position in Scene view
3. Verify projectile layer assignments
4. Monitor lock indicator activation
5. Watch for visual state indicators

### Console Logging
Key debug messages to watch:
1. "Starting lock attempt"
2. "Failed: Need rewind ability"
3. "Failed: CheckLockProjectiles returned false"
4. "Found {x} potential projectiles in box"
5. "Found projectile - State: {state}"
6. "Successfully locked projectile! Count: {count}"

### Runtime Checks
1. Verify StaminaController:
   ```csharp
   Debug.Log($"Rewind Available: {staminaController?.canRewind}");
   ```
2. Check Input System:
   ```csharp
   Debug.Log($"Lock Input Value: {playerInputActions.Player.LockProjectiles.ReadValue<float>()}");
   ```
3. Monitor Lock Count:
   ```csharp
   Debug.Log($"Current Locks: {lockedProjectileCount}/{maxTargets}");
   ```
4. Check Pool Status:
   ```csharp
   Debug.Log($"Pool Available: {ProjectilePool.Instance != null}");
   ```

### Common Solutions
1. **No Detection**:
   - Increase `detectionBoxSize`
   - Adjust `detectionBoxWidthMultiplier`
   - Check projectile layer assignments
   - Verify projectile states

2. **Can't Lock**:
   - Verify `staminaController.canRewind`
   - Check projectile state
   - Monitor input system
   - Check pool availability

3. **Won't Shoot**:
   - Check musical timing
   - Verify projectile pool
   - Check enemy target list
   - Verify state transitions

4. **Performance Issues**:
   - Reduce detection frequency
   - Optimize box cast size
   - Check for memory leaks in projectile pool
   - Monitor state transition frequency 