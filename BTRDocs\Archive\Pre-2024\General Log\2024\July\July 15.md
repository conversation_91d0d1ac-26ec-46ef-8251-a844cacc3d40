# July 15

Reverted to an old version due to this issues, and picking through the improvements to carry forward. 

Don’t think i’ll touch the splitting the enemy class much at the moment, feel the issue is here but couldn’t figure it out. 

Things to bring over

- linger Ophanim developments
- Deform attributes
- Polyfew? Or did this cause problems….
- Optimizations - use profiler to diagnose
- Newish camera system - not bound to layer up/down/left/right as much
    - Important for Ophanim level
- Menu System
- Enemy Basic Shoot script + SHooting patterns

![Untitled](July%2015%20fc61f760a9724ed8b24c749f2e419c2e/Untitled.png)

Also - simplify render pipeline for big performance gain - normal / transparent rendering 

Only have one? Find a simpler way to do this?