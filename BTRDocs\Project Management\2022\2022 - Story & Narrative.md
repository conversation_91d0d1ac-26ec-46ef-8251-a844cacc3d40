
## April 2022

#### April 11

*   Mechanics and core narrative pillars - tying these things together
    *   Is movement core or is it a method of getting from one puzzle to the next?

#### April 18

*   What do I currently have?
    *   Lock on system - can shoot enemies
    *   Basic enemy AI for shooting me and running around the playing field
    *   My lock on system - Can work overhead / first person / third person
    *   Narrative Idea - You are an AI auto protection system guarding a being transmitted through earth
    *   Think AI protection in Alien, Prometheus, etc.

What are attributes that could be influenced by power ups
*   Lock On Stamina
    *   Total Lock Ons
    *   Time Rewind Stamina

## May 16

*   “<PERSON><PERSON><PERSON> was the antagonist to <PERSON><PERSON>'s protagonist but the villain was the world/Tyrell corporation."
    *   reminds me of ‘simulacra & simulation’: culture has become a copy without an original

*   "Okay, see this stick? If I swing it, it moves swoosh (Quantum Idea)
### Serial Experiments Lain Idea

## June 7

"Need to know code for enemy when all objects are added and when you create it
Are you trying to find the beat
*   I am sitting in a room

## June 13

ANGELS
*   Ophanim
    *   group of angels that are famous for their knowledge and wisdom
    *   Thrones / Wheels
        *   who are never asleep and are always guarding the throne of the god
            *   Astrolabe?
        *   Maybe the Ophanim are lost but their rings maintained
        *   Wiki - According to this conception, the heavenly <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> as well as the Ophanim continue to **aid humans in spiritual evolution**
            ; as do the heavenly Archangels and Angels.

*   You’re a digital archaeologist, venturing to old parts of the web. Or maybe it’s religious studies?
    *   **According to QM, before you look inside the box, the cat is both in an alive state, and in a dead state, and the bomb both did and did not go off**. It is only when you look inside an see either an alive or dead cat. Prior to looking in, it is only when you look inside an see either an alive or dead cat that the cat actually becomes either alive or dead. Prior to looking in, it was both at the same time.
    *   Could the old data / old ways of doing things VS the new be a nod to quantum mechanics?
    *   On a personal level, it touches on how what we believe to be true is truth.

*     According to QM, before you look inside the box, the cat is both in an alive state, and in a dead state, and the bomb both did and did not go off”
Limiting Creativity
""What am I even making anymore????
* [I"Ve learned to create a song at

## June 23

"Favelas abandoned places - private business and government gave up on them
On story, use of technology, and more
think eight grade - what are the kids doing?

""A moral backbone to society (for better or worse ) ???

""What is the action that's being done with the code - can i simplify this down some""

*     All data is semiotic. Even choosing what not to count is a political act."

https://iblog.iup.edu/thisprofessorplays/2018/01/09/what-is-videogame-literature/comment-page-1/What to do and what things do you need to know

## June 28

Thumper meets Pacman idea
""Are you working on a remix or the original ""

*   "Should create a small thing
-Can I add boomerang x teleport mechanic? Think about this? Surfboard ? Teleport away from it but need to return back / quick button for returning back?
## June 29

There has to be a way to get this started, I can’t keep putting this off."

"You need to write this game and see where you end up

## September 28

*   [https://www.figma.com/file/INP1bi0nvSmmcCCu4RVie2/Game-Design-Mood-Board?node-id=0%3A1](https://www.figma.com/file/INP1bi0nvSmmcCCu4RVie2/Game-Design-Mood-Board?node-id=0%3A1)"
*   I like things implied by environment more than outright said / dialogue

**How will the track transition and what is happening

The more I see - what changes to this code
*Is the project done*"*"
*   Is a short or more ""
*   "Is it done or can you try to make it again"
### Do your best to make the best game of this kind

*   What’s the story
  -I’m trying to make this all as good as possible for it

#November 23
what’s it all take in order to get good,

## November 24
How will all this be
Which games do you think would do better in a console
""How should I market this video game" ""

###November 15

*You’re going to go to this site with code you remember
You would be great and happy if i was a video game maker too
-What would i do to be great to
-How does this help you and where did I go
Is this game going anywhere or not

## December 1
https://.youtube.com
"""Do I want to get better or what else might help""
-I do my best
-What could it all take to get everything up to par"""#"

1.  ""Why do you start the game over""
   1. I’m trying my best
   #""How do I keep doing

        Where should I take inspiration or go next""
   # ""Is it making
What happens with this information
It feels more complete now
I feel I have more control to what I can do right at that time
Why it might be to make a lot of stuff
There are certain moments that will give people that feel""

"""What is it like to think ""this could be a masterpiece"""
"What types of video games do you think will survive the changing nature of the video game world""
Does it create a feeling""
Does it do its best
What are you trying to do"
"Can I add that and what do you want to create

This is very fun and now has a solid footing
All these things are great
Are certain things going or not
what would you say"
""What do i need to be doing now?"""
https://dune.fandom.com/wiki/Bene_Gesserit
https://en.m.wikipedia.org/wiki/The_Hero_with_a_Thousand_Faces

Is there something you could give her instead and does it make a difference - what difference should you use and where.
*   -What can be learned?""
*   Do you need to be doing it - why is everything working?"

"""""What is a video game in the modern world"""""
"""How do I get better code and information - what is needed """"""
""Where did you want it

* I try to look at the themes from other games - look above

[Storyboard Intro](June 29 c2cb95adb0fb4a818f5372fb34a60952 Storyboard Intro ebc813b2c63340b69614cab102afa4aa.md)
"""Which questions do you need""
Did you get that going or didn't "
"""I’d get the world building to be better
Do what would make more sense and then make it
Does it require the information to get it to where it is
what did you say, what does it need to do

Have a code for all the
*   [Ophanim](https://the-demonic-paradise.fandom.com/wiki/Ophanim

### September 22

*   [12:16 PM]
        *   -What can be learned?"
        * -I like things implied by environment more than outright said / dialogue

## September 23

You just make one game""

What would it be like to be good and is it more about
""What are the themes of the game- what is cool""

### December 1
https://m.youtube.com#""

What is a cool game""
#https://i.redd.it""
#Did you use it""

Is it okay now or - did i get everything good
"""It was just - i didn't like what I did with the
"""Have you made other things work and or do these things have codes to do stuff

If you want to improve then how would you do it - with or without what there"
# I’m thinking of the same things - code etc
Where do I need to go
"""What are the levels - or something?""""
#What are the

*   It does exist - but has to be with this"
*   Was there and does it work-

Did you do that
How do you get back on beat and get more points on those beats""""
Where does it hurt?

*   (What are some of the best ""bullet"" names for a game)"

Does it work to create a level"What can people use?What are the ideas that the player is going to have"
Can I add that or can we do that one way and what do you want to create

I'd ask if I can add it on and what were you trying to make of this?

## June 13

https://dune.fandom.com/wiki/Bene_Gesserit
https://en.m.wikipedia.org/wiki/The_Hero_with_a_Thousand_Faces
""

What type of video games do I make
-Should try to not use a game

Can use the same event names for multiple koreo and it automatically carries over registration - this is ideal!
""What types of video games am I playing and what do i think needs""
-Need more content in music !
        *   Make a visually dynamix level
#### -Does it do its best"

## June 16

What am I making? What is the context?
I can not seem to make
Then the pieces of code should stay or not.
Are these just some of the steps I should have taken more.
How well were you made and what does that make

## Is there something good you should do?
Can you do it or
Are these just some of the steps I should have taken more.
Where is it broken?
What has to do for everything

## June 22

Should I keep the project or not?

I am working well with code""
""If the goal is to do it then you should"
Does all the work have

If there is no goal then there is not a great outcome
-Does this make sense then everything good or what
-""Which ones do you like the most and what are the keys to success.

What’s a basic concept and idea that you could work with"
Why are you working to improve performance
I have the opportunity for this one to be a great game
-How do you show what type of game there?
What do you need to make a video game work? - What is the video?
-What are steps you should make
Then what’s a game
There aren't any good video games"
It may not be possible"