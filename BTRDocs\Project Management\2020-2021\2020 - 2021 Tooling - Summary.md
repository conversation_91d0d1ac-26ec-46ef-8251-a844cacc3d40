# Summary of 2020 - 2021 Tooling.md

This file tracks tooling and workflow improvements and issues from April 2020 to January 2021. Key points include:

- **Backup System:**  Prioritizing and implementing a reliable backup system, initially focusing on GitHub and considering Source Control as a secondary backup.
- **Asset Integration:** Adding and integrating various Unity assets to improve workflow and project capabilities:
    - Odin Inspector for improved Unity editor UI and workflow.
    - EasySave for save/load functionality.
    - ProBuilder and ProGrids for level design and greyboxing.
- **Source Control Management:** Addressing issues with Source Tree and pipeline errors, including reconnection needs.
- **Documentation and Organization:** Setting up bug tracking sections in Notion and reviewing technical documentation for assets like Koreographer and Object Particle Spawner.
- **Workflow Improvements:** General office cleanup and establishing better bug and development logs for tracking and task management.