# 2022 - Audio & Music

This document aggregates all notes and considerations regarding the audio and music aspects of the game from the 2022 development logs. It encompasses sound design, music implementation, Koreographer integration, FMOD integration, sound effect choices, and the interplay between audio and gameplay.

---

## April 2022

#### April 6

*   **Need to go through all Electronic Music Notes for ideas and harmonic concept thoughts**

#### April 16

*   Got the new Razer headphones, experimenting with writing music with these haptic headphones in mind
*   Can I translate hi hat patterns to controller? Is that even interesting?
*   Play with it on test scene!
*   Koreographer and midi to trigger motor
    *   [https://guavaman.com/projects/rewired/docs/HowTos.html#Vibration-Rumble](https://guavaman.com/projects/rewired/docs/HowTos.html#Vibration-Rumble)
    *   Something to try!

#### April 17

*   Mixolumia Music doc - may be interesting!
    *   [Mixolumia - Making Music](https://mixolumia.com/makingmusic/)
*   Didn’t get to my Hi Hat / Controller vibration idea today - tomorrow maybe!

---

#### April 18

*   Finally getting around to hi hat controller vibrate
*   Is this cool? Interesting? Does it work? Lol
*   Implemented in Shooting / Crosshair class as OnMusicalFakeHiHat
*   Fast speeds are not that effective - latency is part of the problem
*   In part due to the speed at which the motor can be kicked in. Can this be accounted for?
*   Is it even that interesting?
*   for FX, things happening on screen, lock on, I think the latency may be ok.
*   But representing a quick musical sound - Not working
*   Options within Unity’s new input system but not sure it’s worth pursuing
    *   [https://www.youtube.com/watch?v=WSw82nKXibc](https://www.youtube.com/watch?v=WSw82nKXibc)

Ways in which a track will progress / change
*Death of an enemy Waves
 Pass a barrier / object

## January 31
Triggering new sounds

## March 1
 Double click logic tracks to bring in faces etc for better transitions - can add new sounds this way too - like stingers

Not looping cleanly - can fake this with a Transition Marker that pulls you back? Is this a bad practice?
Looking over FMOD 2.0 + Additions - Things to Investigate
   Labeled Parameters
   Global Parameters to effect many event instances
   Built-in Speed Parameter
   Flexible Paramters - One parameter changes another parameter
   Nested Multi-Instruments
Command Instruments - lots to this i think!!
*   Labeled Parameters
 *  https://www.fmod.com/resources/documentation-studio?version=2.02&page=welcome-to-fmod-studio-new-in-200.html#event-workflow

## March 2
Adding Lock / Shoot / Tag sound effects for FMOD
 all enemies need to reregister attack capabilitiles with currently playing track
Example of issue - maybe im overthinking it? Just output tracks as one whole audio file, do analysis over all of that. May just be easier - DONE
Try wait for it with one solid track, giving enemies full koreo for the entirety of the time
[Koreo Support Response](March%202%206d0124c221e8460398062c22150ceb01/Untitled%201.png)

## March 3

[https://qa.fmod.com/t/assistance-using-settimelineposition-in-unity-studioeventemitter/16846](https://qa.fmod.com/t/assistance-using-settimelineposition-in-unity-studioeventemitter/16846)
"Parameter: User Labeled instead of using values

## March 4
Different enemies could shoot same projectile types, and vice versa
Need a design language for projectiles vs enemies vs more decorative sounds
Should use different end tags for different combos of sounds

What did they use sound for in Space Invaders * [https://gist.github.com/colin-vandervort/2d20b92d17e8a4aab27d8d03398d2d8d](https://gist.github.com/colin-vandervort/2d20b92d17e8a4aab27d8d03398d2d8d)

## March 7
Struggling to get the sounds i think would work for Level Test 5""
Referencing Lee Gamble - HyperPassive
Lookin at SPace vortex sounds for ref

## March 8
Build Note: transition area - removing transition area to fix the drop out - is this a common problem?

A little later then 40 min into tutorial video
Trigger arrow and setting probability of event occuring - interesting!
Build Note: enemies exist too long after death - fix this

## May 2

"how does one interact in a performance and also in composition?
Need to fix for 3D scene
https://qa.fmod.com/t/assistance-using-settimelineposition-in-unity-studioeventemitter/16846"
I use code in script FMOD Studio Parameter Trigger as basis for swithcing sections

## 12

### May 3

"How to dictate which events show up when choosing Event ID???
Homing is the key for the project

#### Try this on the hi-hat bullets
*   Also fixed Projectile class issues with Launching - there are still some error cases but much better now!"

Also need some kind of YOU DIED screen - and music section!

# May 4
JSON for the transitions or something similar
https://www.youtube.com/watch?v=Ri8PEbD4w8A
What am I doing with OnTargetDestroy Event on Projectile Target (TargetCube) ???
Don’t recall what I was going for here
Need to STOP the audio loop at some point as well. On death for bullets???
    * -Can’t seem to add these to events on bullet because its destroyed
Create a more interesting sound effect from sound library
You want to use the sound effect then trigger sounds
Should create unique sounds
Do to all these separated audio clips mean a lot of work for Koreographer implementation?
"Double click logic tracks to bring in faces etc for better transitions - can add new sounds this way too - like stingers
-What is the need of this when you have a map? Level idea - DISABLED map
-Need a design language for projectiles vs enemies vs more decorative sounds""Need to add more

### 1991 = what they did in 1991
        *   2022 = you know what’s up,
### May 7
*   ""Adding shooting particle effect from enemies
* Added Unlauchable Projectile to the mix
* What are the main ideas
### May 8
A little later then 40 min into tutorial video
Try Kart project with 2.1 version of FMOD - should be able to zip through quick
Add more sound fx to game (FMOD tutorial)

## 10 - https://www.youtube.com/watch?v=c-z4y2u0J5g.

What type of game is this for
Did some research and now there is more knowledge in place
Also what types of video games could i be getting into?
Try new level ideas with a good idea""

*       You’re working for the same company as you now.

#### Is that just the way it will be

### What are the rules?

*   Are you the main character or not""

### 12
How do you want to handle it
I like the visual style of what i have - where can this style take me

### May 13

*   (0:00 / 7:57)  [1]
*   You save their data
### https://www.youtube.com/watch?v=7EZ2F-TzHYw
*   Did almost immediately - seems it’s not unusual! Maybe not why it’s getting stuck

### 16
This level requires a lot of the
[20 Mins of GREATNESS - BEST #unitytips](https://www.youtube.com/watch?v=dQnAc2mEDI0)
[https://www.youtube.com/watch?v=dQnAc2mEDI0
""Can’t do all at once because - need to test the stuff,
What would i do if i just wanted to get better at unity?
How does this help if the information is just on the page?
How do i learn to do more

What is the action you are doing?""

[4 new Unity Features YOU'RE MISSING!](https://www.youtube.com/watch?v=mCkjwJGtRI0)
You are about to show the main goal of the game
You will try to beat it
You know of what you are capable because you were so sure you beat the thing""
What was it
That seems to be the only information here

### May 23

*   (You have to play this game and beat the level to understand it

### June 18

*   [Ophanim](https://the-demonic-paradise.fandom.com/wiki/Ophanim

"## June 28"

*   [Why Rhythm Games Haven't Changed for 20 Years](https://www.youtube.com/watch?v=wb45O2NciL8&list=PL67pghfWOIM3r3fd_ydsyr0HvuGH7gcXd)
        *   Dance Rush Stardom applied to a controller?
            *   breaking
                *   Withholding info from your player
                *   Leave some things out on purpose!
* May need to change direction and throw ideas aside

###June 29

*   Using backup form June 27th
*   [Storyboard Intro](June%2029%20c2cb95adb0fb4a818f5372fb34a60952/Storyboard%20Intro%20ebc813b2c63340b69614cab102afa4aa.md)

##  September 23
    -Changed Multi-instrument from Consecutive to Concurrent
    *   does this help the sounds playing?
* Also need more notes / options / modes / scales?
More musical elements
What types of video games do I want to make
#september 28
*   [https://www.figma.com/file/INP1bi0nvSmmcCCu4RVie2/Game-Design-Mood-Board?node-id=0%3A1](https://www.figma.com/file/INP1bi0nvSmmcCCu4RVie2/Game-Design-Mood-Board?node-id=0%3A1)
What’s the story

## November 24
Add Graphics settings to menu! Add ability to save these as well
Bake lighting!
What kind of music?
What is the action you do
What does success of this action unlock?

## December 1
# Is the code working or not?""
""Where does this happen - code wise""
#""How do you get back on beat and get more points on those beats ""
#""What happens when I shoot - and does it work ?""#

"""