Visual Style & Art Direction
April 3

Look at <PERSON><PERSON> or other shaders effecting environment

April 5th

Playing with <PERSON><PERSON> on bullets - need script to alter parameters. THinking of use cases

Basic color changing environment method added to Crosshairs class

Looking for particles for bullet destruction

Looking for better lock on graphic

April 6th

Tried new shaders!

April 7th

Learning Living Particles - affectors

Living Particles - Tiling and Offset create interesting effects - manipulate these with Koreogrpaher?

April 9

Figure out issues with Particle Clip Channel Texture- What is it doing? Effects look drastically

April 10

Experimenting with particle shapes for bullets - can emit patterns and lots of things

Testing mixamo floating animation and Local Volume on player character

April 11

Matched Player HOLO Shader to Global SceneBPM

April 13

Greybox 1-2 - Melodic ideas and new skybox/Global Volume on

May 19

Changed MMFeedbacks to Feel - may be some missed connections I will find in time

MeshManipulation pLugin - use this on objects??? Performant? NEED TO BUY $5

May 23

Added Mesh Tracer to project

Some interesting effects for enemies / objects - flag for later use

May 25

Added Easy Performant Outline - Try this with lock on for visual clarity

May 27

Mirror rotating idea, dont forget!

https://assetstore.unity.com/packages/vfx/shaders/urp-lwrp-mirror-shaders-135215

Try Easy Outliner and get it working

June 28

Upgraded to HDRP over past couple days and also played with Tetris implementation

July 1

Implementing Ethereal Urp - Unsure on proper Forward Rendering to choose for camera

Need Fog to cover up LOD pop in issues?

July 19

Early level is not performant - Greybox 12

Need a better building → Polyfew → TessGen pipeline

Need to use more performant shaders that can be batched

Look at mobile transparency shaders?

THING TO TRY - Polyfew Combine Meshes for level layouts - only when finalized?

July 20

Ghost Shader - Studio CS Ghost UPR Advanced Always Visible

Make buildings one Mesh, some sevenral?

July 24

Used Lighting Shot Material on platform

SineVFX may be better for this?

Reduced Far Clip Plane works for populated dense building area - likely not for less populated areas

August 7

Lots of experimenting with fog - not much luck! Ethereal URP is difficult

Think I will stick with it though. Like it more then not having it

Sept 13th

Mixed post processing objects with non-post!

Nov 21

Prefab ships into project

Need Ultiamte SPawner to spawn these as children on A* nav mesh

Jan 18

IDEA: Use Mirror / Portal like effect for a BOX level / section?

Jan 22

Research adjust material color at runtime. DOTWeen can probably lerp this?

Tried Ethereal URP again for FOG and such, no great luck it seems! Maybe try again later

Jan 27th

Add bullet particle effect with kick track?

Jan 28

Early thoughts - Outline on main character?