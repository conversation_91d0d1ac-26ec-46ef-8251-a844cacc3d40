# May 15

Need to look at Vibrant Matter tracks as more minimal approach to soundtrack level

Ouroboro<PERSON> as a pass on this

- remove many top level elements, keep  kick / snare pattern?

Creating a plan using chat gpt and past couple logs / aggregations 

### **High Impact / Easy**

1. **Upgrade Visual Studio 2019 to 2022**: Resolves performance warnings.
2. **Add Quit to Desktop**: Already done, but high impact for user experience.
3. **Adjust Prediction Values on Projectiles**: Improvements noted, continue progress.
4. **Improve Enemy Death Effect Visibility**: Minor tweak with a significant impact on gameplay feedback.
5. **Increase Player Speed**: Quick tweak to make gameplay feel faster and more intense.

### **High Impact / Difficult**

1. **Refine Reticle Control and Enemy/Projectile Lock On**: Key to core gameplay; needs significant rework.
2. **New UI for Projectile and Enemy Locks**: Essential for player feedback, requires redesign.
3. **Snake-like Enemy Implementation**: Complex, but adds unique gameplay mechanics.
4. **Play with Parry Feature**: Needs rethinking to improve responsiveness.
5. **Integrate Mechanics for Combined Actions**: Ensures mechanics interplay well together.
6. **Rework Bullet Clocks for Time Changes**: Critical for time mechanics to function properly.
7. **Time Glitch Enhancements**: Highlight enemy locations, adjust player/enemy speeds.
8. **Slow Time Enhancements**: Allow backward movement, adjust time effects.

### **Low Impact / Easy**

1. **Make Enemy Death Effect More Visible**: Simple visual adjustment.
2. **Toonkit Manager Adjustments**: Minor visual tweaks.
3. **Keijiro Duotone for Scene Transitions**: Test and potentially implement visual effect.
4. **Game Objects Inactive on Scene Load**: Find a proper solution, minor impact but necessary for consistency.
5. *A or Ground Fitter Fix*: Fix Y rotation issues, minor but required for functionality.
6. **Particle Effects Visibility**: Ensure particle effects are more exaggerated.
7. **Skip Between Scenes**: Fix method for scene transitions.

### **Low Impact / Difficult**

1. **Motion Extraction Effect**: Needs significant effort to figure out and implement.
2. **Ricochet Dodge Feature Testing**: Requires testing and refinement, but lower overall impact.
3. **Enemy Waves Adjustment**: Fine-tune enemy types and projectiles, requires playtesting.
4. **Scene Transition and Music Choice Edits**: Ongoing adjustments, minor but important for polish.

---

### **Implementation Plan**

**Phase 1: Immediate Improvements (High Impact / Easy)**

1. Upgrade Visual Studio 2019 to 2022.
2. Improve enemy death effect visibility.
3. Adjust prediction values on projectiles.
4. Increase player speed.
5. Add quit to desktop (verify implementation).

**Phase 2: Core Gameplay Enhancements (High Impact / Difficult)**

1. Refine reticle control and enemy/projectile lock on.
2. Design and implement a new UI for projectile and enemy locks.
3. Play with parry feature to improve responsiveness.
4. Integrate mechanics for combined actions to enhance gameplay depth.
5. Rework bullet clocks to ensure proper time mechanics.
6. Implement snake-like enemy idea with tail animator.
7. Enhance time glitch and slow time mechanics for better player interaction.

**Phase 3: Visual and Functional Tweaks (Low Impact / Easy)**

1. Make enemy death effect more visible.
2. Adjust Toonkit Manager for shadows and light.
3. Test and implement Keijiro Duotone for scene transitions.
4. Ensure game objects are active on scene load.
5. Fix A* or ground fitter Y rotation issues.
6. Exaggerate particle effects for better visibility.
7. Fix method for skipping between scenes.

**Phase 4: Detailed Testing and Refinements (Low Impact / Difficult)**

1. Figure out and implement motion extraction effect.
2. Continue testing and refining the ricochet dodge feature.
3. Fine-tune enemy waves and projectiles for better gameplay balance.
4. Continue scene transition and music choice edits for a polished experience.

WORK DONE

OUROBOROS

Didnt totally touch the above list, but did dig in on sounds and approach

pulled back to minimalist sound a bit more, can see a better direction here

then synced that with vfx explosion event i can place around the scene

makes more sense i think - sound and visual seem more in sync

A better direction forward

THings that occurs to me that cross over with above list

make character a bit faster

balance enemy speed and their bullet speeds

continue on path of adjusting time based effects so they are better to interact with / more desirable

IMP need to figure out how to reverse the music sounds as well as the drums