# April 1

Two weeks to job - whats important - whats next?

Looking at issues with player charcater bopping and rotating

rotating aimlessly is a A* issue?

- when A* disabled on player it doesnt fix it

jittery / bopping is a ground detection issue

Seemed to have smoothed out the jittery isssue with lerping on ground detection

Also enabling the rigidbody as kinematic has seemed to fix my intermittent rotation issues

wel… not quite! still debugging

seems to help only if i have player movement script disabled

A* stuff seems to not effect this issue