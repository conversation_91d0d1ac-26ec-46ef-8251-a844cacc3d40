# Degenerate Geometry A* Error

Exception: Caught in a potentially infinite loop. The navmesh probably contains degenerate geometry.
Pathfinding.PathTracer.IsInnerVertexTriangleMesh (Pathfinding.Collections.CircularBuffer`1[T] nodes, Pathfinding.Funnel+PathPart part, System.Int32 portalIndex, System.Boolean rightSide, System.Collections.Generic.List`1[T] alternativeNodes, Pathfinding.NNConstraint nnConstraint, System.Int32& startIndex, System.Int32& endIndex, Pathfinding.ITraversalProvider traversalProvider, Pathfinding.Path path) (at ./Library/PackageCache/com.arongranberg.astar@5.2.3/Utilities/PathTracer.cs:1278)
Pathfinding.PathTracer.IsInnerVertex (Pathfinding.Collections.CircularBuffer`1[T] nodes, Pathfinding.Funnel+PathPart part, System.Int32 portalIndex, System.Boolean rightSide, System.Collections.Generic.List`1[T] alternativeNodes, Pathfinding.NNConstraint nnConstraint, System.Int32& startIndex, System.Int32& endIndex, Pathfinding.ITraversalProvider traversalProvider, Pathfinding.Path path) (at ./Library/PackageCache/com.arongranberg.astar@5.2.3/Utilities/PathTracer.cs:1166)
Pathfinding.PathTracer.FirstInnerVertex (Unity.Collections.NativeArray`1[T] indices, System.Int32 numCorners, System.Collections.Generic.List`1[T] alternativePath, System.Int32& alternativeStartIndex, System.Int32& alternativeEndIndex, Pathfinding.ITraversalProvider traversalProvider, Pathfinding.Path path) (at ./Library/PackageCache/com.arongranberg.astar@5.2.3/Utilities/PathTracer.cs:1296)
Pathfinding.PathTracer.GetNextCornerIndices (Unity.Collections.NativeArray`1[System.Int32]& buffer, System.Int32 maxCorners, Unity.Collections.Allocator allocator, System.Boolean& lastCorner, Pathfinding.ITraversalProvider traversalProvider, Pathfinding.Path path) (at ./Library/PackageCache/com.arongranberg.astar@5.2.3/Utilities/PathTracer.cs:1406) Pathfinding.ECS.JobRepairPath.Execute (Unity.Transforms.LocalTransform& transform, Pathfinding.ECS.MovementState& state, Pathfinding.ECS.AgentCylinderShape& shape, Pathfinding.ECS.AgentMovementPlane& movementPlane, Pathfinding.ECS.DestinationPoint& destination, Unity.Entities.EnabledRefRW`1[T] readyToTraverseOffMeshLink, Pathfinding.ECS.ManagedState managedState, Pathfinding.ECS.MovementSettings& settings, Unity.Collections.NativeList`1[T] nextCornersScratch, Unity.Collections.NativeArray`1[System.Int32]& indicesScratch, Unity.Collections.Allocator allocator, System.Boolean onlyApplyPendingPaths) (at ./Library/PackageCache/com.arongranberg.astar@5.2.3/Core/ECS/Jobs/JobRepairPath.cs:182)
Pathfinding.ECS.JobRepairPath.Execute (Unity.Entities.ArchetypeChunk& chunk, System.Int32 unfilteredChunkIndex, System.Boolean useEnabledMask, Unity.Burst.Intrinsics.v128& chunkEnabledMask) (at ./Library/PackageCache/com.arongranberg.astar@5.2.3/Core/ECS/Jobs/JobRepairPath.cs:123)
Pathfinding.ECS.JobRepairPath.Unity.Entities.IJobChunk.Execute (Unity.Entities.ArchetypeChunk& chunk, System.Int32 unfilteredChunkIndex, System.Boolean useEnabledMask, Unity.Burst.Intrinsics.v128& chunkEnabledMask) (at <9d3e9f9e6b8946f9abda6538b875606e>:0)
Unity.Entities.JobChunkExtensions+JobChunkProducer`1[T].ExecuteInternal (Unity.Entities.JobChunkExtensions+JobChunkWrapper`1[T]& jobWrapper, System.IntPtr bufferRangePatchData, Unity.Jobs.LowLevel.Unsafe.JobRanges& ranges, System.Int32 jobIndex) (at ./Library/PackageCache/com.unity.entities@1.3.2/Unity.Entities/IJobChunk.cs:420)
Unity.Entities.JobChunkExtensions+JobChunkProducer`1[T].Execute (Unity.Entities.JobChunkExtensions+JobChunkWrapper`1[T]& jobWrapper, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, Unity.Jobs.LowLevel.Unsafe.JobRanges& ranges, System.Int32 jobIndex) (at ./Library/PackageCache/com.unity.entities@1.3.2/Unity.Entities/IJobChunk.cs:363)