# March 1 2023

How can I handle varying levels of gameplay speed - liek thumper and it's increasing difficulty?

choice as being almost 50% of what a player projects onto an aparatus rather than what the aparatus is doing

Technical Issues Today

- Map not constrained properly - bullets flying off
    - Mostly Fixed - scaling issue with screen aspect ratio now, i think. check black square / map size as well
- bullets circling enemies, not hitting them. Rotation and other projectile numbers need to be fine tuned?
    - higher rotation speed fixes this
    -use low rotation speed as gameplay mechanic for some projectiles?
- Not transitioning between <PERSON><PERSON><PERSON> rings properly - implement script for transition again
    - pulled scripts from old backup and fixed this