using UnityEngine;
using System.Collections.Generic;
using BTR.EnemySystem;
using ZLinq;
using Pathfinding;

namespace BTR.EnemySystem
{
    /// <summary>
    /// Mandatory spacing enforcer that guarantees enemies maintain minimum distance from each other.
    /// Works as a hard constraint system alongside the soft separation forces.
    /// </summary>
    public class MandatorySpacingEnforcer : MonoBehaviour
    {
        [Header("Mandatory Spacing Settings")]
        [SerializeField] private float mandatoryMinimumDistance = 2.5f; // Increased from 2f
        [SerializeField] private bool enforceHardSpacing = true;
        [SerializeField] private float spacingValidationInterval = 0.05f; // Reduced from 0.1f for faster validation
        [SerializeField] private int maxValidationAttempts = 12; // Increased from 8
        [SerializeField] private float validationSearchRadius = 8f; // Increased from 5f

        [Header("Performance Settings")]
        [SerializeField] private int maxValidationsPerFrame = 25; // Increased from 15
        [SerializeField] private bool enableDistanceLOD = true;
        [SerializeField] private float lodNearDistance = 20f;
        [SerializeField] private float lodFarDistance = 40f;

        [Header("Debug")]
        [SerializeField] private bool enableDebugLogs = false;
        [SerializeField] private bool showDebugVisualization = false;

        // Validation data
        private class SpacingValidationData
        {
            public Transform transform;
            public float lastValidationTime;
            public Vector3 lastValidatedPosition;
            public int violationCount;
            public bool needsValidation;
        }

        // Service state
        private Dictionary<Transform, SpacingValidationData> trackedEnemies = new Dictionary<Transform, SpacingValidationData>();
        private Queue<Transform> validationQueue = new Queue<Transform>();
        private List<Transform> tempNearbyEnemies = new List<Transform>();
        private Transform playerTransform;
        private float lastGlobalValidation;

        public static MandatorySpacingEnforcer Instance { get; private set; }

        // Domain reload handling
        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.SubsystemRegistration)]
        private static void ResetStaticData()
        {
            Instance = null;
        }

        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                InitializeService();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void InitializeService()
        {
            // Find player transform
            var player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                playerTransform = player.transform;
            }

            if (enableDebugLogs)
            {
                Debug.Log($"[MandatorySpacingEnforcer] Initialized with minimum distance: {mandatoryMinimumDistance}");
            }
        }

        private void Update()
        {
            if (!enforceHardSpacing) return;

            if (Time.time >= lastGlobalValidation + spacingValidationInterval)
            {
                ProcessSpacingValidations();
                lastGlobalValidation = Time.time;
            }
        }

        /// <summary>
        /// Register an enemy for mandatory spacing enforcement
        /// </summary>
        public void RegisterEnemy(Transform enemyTransform)
        {
            if (enemyTransform == null || trackedEnemies.ContainsKey(enemyTransform))
                return;

            var data = new SpacingValidationData
            {
                transform = enemyTransform,
                lastValidationTime = 0f,
                lastValidatedPosition = enemyTransform.position,
                violationCount = 0,
                needsValidation = true
            };

            trackedEnemies[enemyTransform] = data;
            validationQueue.Enqueue(enemyTransform);

            if (enableDebugLogs)
            {
                Debug.Log($"[MandatorySpacingEnforcer] Registered enemy: {enemyTransform.name}");
            }
        }

        /// <summary>
        /// Unregister an enemy from mandatory spacing enforcement
        /// </summary>
        public void UnregisterEnemy(Transform enemyTransform)
        {
            if (enemyTransform != null && trackedEnemies.ContainsKey(enemyTransform))
            {
                trackedEnemies.Remove(enemyTransform);

                if (enableDebugLogs)
                {
                    Debug.Log($"[MandatorySpacingEnforcer] Unregistered enemy: {enemyTransform.name}");
                }
            }
        }

        /// <summary>
        /// Enforce minimum distance for a target position. Returns a validated position that maintains minimum spacing.
        /// </summary>
        public Vector3 EnforceMinimumSpacing(Vector3 desiredPosition, Transform enemy, float customMinDistance = -1f)
        {
            if (!enforceHardSpacing || enemy == null)
                return desiredPosition;

            float minDistance = customMinDistance > 0f ? customMinDistance : mandatoryMinimumDistance;

            // Check if desired position violates minimum distance
            if (!IsPositionValid(desiredPosition, enemy, minDistance))
            {
                // Find alternative position that maintains minimum distance
                Vector3 validPosition = FindValidPositionWithMinimumDistance(desiredPosition, enemy, minDistance);

                if (enableDebugLogs)
                {
                    Debug.Log($"[MandatorySpacingEnforcer] Adjusted position for {enemy.name}: {desiredPosition} → {validPosition}");
                }

                return validPosition;
            }

            return desiredPosition;
        }

        /// <summary>
        /// Check if a position maintains minimum distance from all other enemies
        /// </summary>
        public bool IsPositionValid(Vector3 position, Transform excludeEnemy, float minDistance)
        {
            if (EnemyManager.Instance == null)
                return true;

            tempNearbyEnemies.Clear();
            EnemyManager.Instance.GetEnemiesInRadius(position, minDistance, tempNearbyEnemies);

            foreach (var nearbyEnemy in tempNearbyEnemies)
            {
                if (nearbyEnemy == null || nearbyEnemy == excludeEnemy)
                    continue;

                float distance = Vector3.Distance(position, nearbyEnemy.position);
                if (distance < minDistance)
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Find a valid position near the desired position that maintains minimum distance
        /// </summary>
        public Vector3 FindValidPositionWithMinimumDistance(Vector3 desiredPosition, Transform enemy, float minDistance)
        {
            Vector3 currentPos = enemy.position;

            // Try positions in expanding circles around the desired position
            for (int attempt = 0; attempt < maxValidationAttempts; attempt++)
            {
                float searchRadius = (attempt + 1) * (minDistance * 0.5f);

                // Try multiple angles at this radius
                int angleSteps = 8 + (attempt * 2); // More angles for larger radii
                for (int i = 0; i < angleSteps; i++)
                {
                    float angle = (360f / angleSteps) * i * Mathf.Deg2Rad;
                    Vector3 offset = new Vector3(
                        Mathf.Cos(angle) * searchRadius,
                        0f,
                        Mathf.Sin(angle) * searchRadius
                    );

                    Vector3 testPosition = desiredPosition + offset;

                    if (IsPositionValid(testPosition, enemy, minDistance))
                    {
                        return testPosition;
                    }
                }
            }

            // If no valid position found, move away from nearest enemy
            Vector3 fallbackPosition = GetFallbackPosition(desiredPosition, enemy, minDistance);
            return fallbackPosition;
        }

        /// <summary>
        /// Get a fallback position by moving away from the nearest enemy
        /// </summary>
        private Vector3 GetFallbackPosition(Vector3 desiredPosition, Transform enemy, float minDistance)
        {
            if (EnemyManager.Instance == null)
                return desiredPosition;

            tempNearbyEnemies.Clear();
            EnemyManager.Instance.GetEnemiesInRadius(desiredPosition, validationSearchRadius, tempNearbyEnemies);

            if (tempNearbyEnemies.Count == 0)
                return desiredPosition;

            // Find the nearest enemy
            Transform nearestEnemy = null;
            float nearestDistance = float.MaxValue;

            foreach (var nearbyEnemy in tempNearbyEnemies)
            {
                if (nearbyEnemy == null || nearbyEnemy == enemy)
                    continue;

                float distance = Vector3.Distance(desiredPosition, nearbyEnemy.position);
                if (distance < nearestDistance)
                {
                    nearestDistance = distance;
                    nearestEnemy = nearbyEnemy;
                }
            }

            if (nearestEnemy != null)
            {
                // Move away from nearest enemy
                Vector3 awayDirection = (desiredPosition - nearestEnemy.position).normalized;
                Vector3 fallbackPosition = nearestEnemy.position + awayDirection * (minDistance + 0.5f);
                return fallbackPosition;
            }

            return desiredPosition;
        }

        /// <summary>
        /// Force validation for a specific enemy
        /// </summary>
        public void ForceValidation(Transform enemyTransform)
        {
            if (trackedEnemies.TryGetValue(enemyTransform, out var data))
            {
                data.needsValidation = true;
            }
        }

        /// <summary>
        /// Check if an enemy is currently violating minimum distance
        /// </summary>
        public bool IsViolatingMinimumDistance(Transform enemy, float customMinDistance = -1f)
        {
            float minDistance = customMinDistance > 0f ? customMinDistance : mandatoryMinimumDistance;
            return !IsPositionValid(enemy.position, enemy, minDistance);
        }

        private void ProcessSpacingValidations()
        {
            int processedCount = 0;
            int maxProcessThisFrame = Mathf.Min(maxValidationsPerFrame, validationQueue.Count);

            while (processedCount < maxProcessThisFrame && validationQueue.Count > 0)
            {
                Transform enemy = validationQueue.Dequeue();

                if (enemy == null || !trackedEnemies.ContainsKey(enemy))
                {
                    processedCount++;
                    continue;
                }

                ProcessEnemySpacingValidation(enemy);

                // Re-queue for next validation cycle
                validationQueue.Enqueue(enemy);
                processedCount++;
            }

            if (enableDebugLogs && processedCount > 0)
            {
                Debug.Log($"[MandatorySpacingEnforcer] Processed {processedCount} spacing validations");
            }
        }

        private void ProcessEnemySpacingValidation(Transform enemy)
        {
            var data = trackedEnemies[enemy];

            // Calculate LOD level for performance optimization
            float lodLevel = 0f;
            if (enableDistanceLOD && playerTransform != null)
            {
                float distanceToPlayer = Vector3.Distance(enemy.position, playerTransform.position);
                lodLevel = Mathf.InverseLerp(lodNearDistance, lodFarDistance, distanceToPlayer);
            }

            // Skip validation if LOD level is too high and not enough time passed
            float validationInterval = Mathf.Lerp(spacingValidationInterval, spacingValidationInterval * 3f, lodLevel);
            if (Time.time - data.lastValidationTime < validationInterval && !data.needsValidation)
                return;

            // Check if enemy is violating minimum distance
            bool isViolating = IsViolatingMinimumDistance(enemy);

            if (isViolating)
            {
                data.violationCount++;

                // Apply immediate position correction for severe violations
                if (data.violationCount > 3)
                {
                    ApplyImmediatePositionCorrection(enemy);
                }

                // If enemy has a movement strategy, trigger repositioning
                var movementStrategy = enemy.GetComponent<BasicChaseMovementStrategy>();
                if (movementStrategy != null)
                {
                    // Force the movement strategy to recalculate position
                    movementStrategy.ForceUpdate();
                }

                if (enableDebugLogs)
                {
                    Debug.LogWarning($"[MandatorySpacingEnforcer] {enemy.name} violating minimum distance (violations: {data.violationCount})");
                }
            }
            else
            {
                // Reset violation count if no longer violating
                data.violationCount = Mathf.Max(0, data.violationCount - 1);
            }

            data.lastValidationTime = Time.time;
            data.lastValidatedPosition = enemy.position;
            data.needsValidation = false;
        }

        /// <summary>
        /// Get spacing enforcement statistics
        /// </summary>
        public (int trackedCount, int violatingCount, float avgViolations) GetSpacingStats()
        {
            int violatingCount = 0;
            float totalViolations = 0f;

            foreach (var data in trackedEnemies.Values)
            {
                if (data.violationCount > 0)
                {
                    violatingCount++;
                }
                totalViolations += data.violationCount;
            }

            float avgViolations = trackedEnemies.Count > 0 ? totalViolations / trackedEnemies.Count : 0f;
            return (trackedEnemies.Count, violatingCount, avgViolations);
        }

        private void OnDrawGizmos()
        {
            if (!showDebugVisualization || !Application.isPlaying)
                return;

            // Draw minimum distance circles for violating enemies
            Gizmos.color = Color.red;
            foreach (var kvp in trackedEnemies)
            {
                if (kvp.Key == null) continue;

                var enemy = kvp.Key;
                var data = kvp.Value;

                if (data.violationCount > 0)
                {
                    Gizmos.DrawWireSphere(enemy.position, mandatoryMinimumDistance);
                }
            }

            // Draw validation search radius for selected enemy
#if UNITY_EDITOR
            if (UnityEditor.Selection.activeTransform != null &&
                trackedEnemies.ContainsKey(UnityEditor.Selection.activeTransform))
            {
                Gizmos.color = new Color(1f, 1f, 0f, 0.3f);
                Gizmos.DrawWireSphere(UnityEditor.Selection.activeTransform.position, validationSearchRadius);
            }
#endif
        }

        /// <summary>
        /// Apply immediate position correction for enemies with severe spacing violations
        /// </summary>
        private void ApplyImmediatePositionCorrection(Transform enemy)
        {
            if (enemy == null) return;

            // Find a valid position immediately
            Vector3 correctedPosition = FindValidPositionWithMinimumDistance(
                enemy.position, enemy, mandatoryMinimumDistance * 1.2f);

            // Apply position correction
            enemy.position = correctedPosition;

            // Update any pathfinding components
            var followerEntity = enemy.GetComponent<FollowerEntity>();
            if (followerEntity != null)
            {
                followerEntity.destination = correctedPosition;
            }

            if (enableDebugLogs)
            {
                Debug.Log($"[MandatorySpacingEnforcer] Applied immediate position correction to {enemy.name}");
            }
        }

        private void OnDestroy()
        {
            if (Instance == this)
            {
                Instance = null;
            }
        }
    }
}