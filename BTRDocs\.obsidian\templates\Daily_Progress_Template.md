<%* 
const date = tp.date.now("YYYY-MM-DD");
const yearFolder = "Project Management/" + tp.date.now("YYYY");
await tp.file.create_new(
  tp.file.find_tfile("Daily_Progress_Template"),
  date, 
  true, // open after creation
  tp.file.folder(true) + "/" + yearFolder
);
_%>
---
systems: []
components: []
tags: [#project/daily-log, #priority/p0]
date: <% tp.date.now("YYYY-MM-DD") %>
links:
  - [[<% tp.date.now("YYYY-MM-DD") %>|Today]]
  - [[<% tp.date.yesterday("YYYY-MM-DD") %>|Yesterday]]
  - [[<% tp.date.tomorrow("YYYY-MM-DD") %>|Tomorrow]]
---

# <% tp.date.now("MMMM D, YYYY") %> Progress Journal

```ad-summary
title: Daily Focus Areas
- [ ] Core System Development
- [ ] Bug Fixes
- [ ] Research
- [ ] Code Review
```

## Key Progress Summary `#priority/p0`
```dataview
TABLE WITHOUT ID link(file.link) AS "Related Files", progress AS "Status" 
FROM #project/task AND [[<% tp.date.now("YYYY-MM-DD") %>]]
```

### Major Accomplishments
- <% await tp.system.suggester(["Added new enemy type", "Fixed audio bug", "Optimized rendering"], ["{{cursor}}"]) %>

### Systems Worked On
- [[<% await tp.system.suggester(["Enemy System", "Audio System", "UI System"], ["{{cursor}}"]) %>]] 

## Detailed Log
<%*
const timeBlocks = [
  { start: "9:00 AM", end: "12:00 PM", label: "Morning Session" },
  { start: "1:00 PM", end: "5:00 PM", label: "Afternoon Session" },
  { start: "6:00 PM", end: "8:00 PM", label: "Evening Review" }
];

for (let block of timeBlocks) {
-%>
```ad-time
title: <% block.start %> - <% block.end %>
```
<% } -%>