# May 23

Drop bullets lower when locked to clean up visual field a bit?

Added this to bullet script! does help clean up sight lines

UI Target Tracker not working - look into this - Lock on feature could be useful

Trying this to get bullets to stop spinning in circles

![Untitled](May%2023%2030145d0228ee4f4f857f0925c57eafd6/Untitled.png)

DIDNT WORK, neither did homing = false

Need to get bullets to just move straight forward once they enter the player area

Rethink from start! Use Player Target!

Created Page for prioritizing work

[](../../../../Issue%20Tracking%20a7b8efc25f594d25b8558fc230255e64.md) 

Experimenting with camera tilt around player - trying different options to ahcieve this

<aside>
💡 Camera Dutch in level as effect would be cool!

</aside>

Removed Camera Follow script from CM vcam1 becasue Cinemachine seems to work great instead

Ignore Time Scale on CinemachineBrain is now enabled

[How to use Cameras in Unity: Cinemachine Virtual Cameras Explained](https://www.youtube.com/watch?v=asruvbmUyw8)

Working on Camera movement

FOLLOW will affect position of camera

LOOK AT will affect rotation of camera

Brought back Camera Follow cause of Limit Feature

May not use it. Need to investigate camera movement more - could be a big game feel thing

Tackling spinning bullets

Projectile Target is being set to Player again

When projectile hits PlayerRadius it should reset and keep going forward now

Bad if / else tags seems to be the problem with my collision not working

Seems to work now! Probably some bug testing necessary

Bunch of bullet sin scene floating like this 

![Untitled](May%2023%2030145d0228ee4f4f857f0925c57eafd6/Untitled%201.png)

Seem to have fixed it with bullet death code refinement

need to figure out a good way release any help locks by the player