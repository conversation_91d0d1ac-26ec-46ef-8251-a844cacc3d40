---
title: Enemy Types Overview
date: 2024-01-15
tags: [enemy, types, index]
aliases: [EnemyTypes, EnemyIndex]
---

# Enemy Types Overview

## Introduction
This document serves as an index for all enemy types in BTR. Each enemy type has its own specific configuration, behavior patterns, and implementation details.

## Enemy Categories

### Basic Enemies
- [[BasicEnemy|Basic Enemy]] - Standard enemy with projectile combat
  - Foundation for other enemy types
  - Uses basic pathfinding and projectile attacks
  - Balanced health and damage output

### Special Enemies
- [[ExplodingEnemy|Exploding Enemy]] - Kamikaze-style enemy
  - Rushes player and self-destructs
  - High burst damage
  - Two-phase movement pattern

## Common Components
All enemy types share these core components:
1. [[EnemyCore]] - Base functionality and health management
2. [[PathfindingMovementBehavior]] - Movement and navigation
3. [[DamageVisualizationBehavior]] - Visual feedback
4. [[TargetIndicatorBehavior]] - Player targeting

## Configuration Structure
Enemy configurations use ScriptableObjects with these sections:
- Core Settings (health, speed, vulnerability)
- Combat Settings (distances, damage, cooldowns)
- Special Settings (projectiles, explosions, shields)
- Effect Settings (visual and audio effects)
- Phase Settings (movement phases)

## Related Documents
- [[EnemySystemGuide|Enemy System Documentation]]
- [[SystemsDesignAnalysis#Core Systems|Systems Design Analysis]]
- [[ProjectileSystem|Projectile System Documentation]] 