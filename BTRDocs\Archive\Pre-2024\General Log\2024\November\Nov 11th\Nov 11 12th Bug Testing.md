# Nov. 11/12th Bug Testing

**Restart - does it work?**

- music not properly restarting, editing and testing

Now working within first scene, follow up scenes seem to work but need further testing

**Code to address this may have highlighted / introduced wave transition error**

- Start4 not found by FMOD - trying to fix this
- Need better way to handle start sound of waves / levels - investigate this

**locking must be reset between waves and scenes**

- made code adjustments to account for this

**Fix broken effects** 

- Scene start effects
- Ricochet effect
- Glitch times efectsd

**Mild Improvements - probably need to replace many of them**

Need to make incoming projectiles that will hit player, more obvious or something, need a better chance at shielding these

Scene 1

- Need to increase ability for camera to look up and down
    - try to do this without clipping things
- Clamping in ShooterMovement script influecnes this - need to adjsut and potentially adjust Cienmachien camera confiner to be adjusted depening on reticle position as well.
- What enabled clamping on start? this is a a bit vague, somethng does i on scene star

Increase clamped limits but also adjust confiner? Need to investigate

Somewhat working - test it

- need to adjust when facing backwards - player is out of screen
- needs refinement on sphere position vertically.

look at waht camera is doing in scene 

- is it rotating properly? could it rotate more? is this the issue?

TEST TEST AND OBSERVE

(can revert shooter movement if needed)

Nov. 14th

Dont think adjust Camera Restriction is doing anything good

Problems were in Shooter Movement, rotation restrictions are tehre

MAJORLY increased now

Also added dead zone that will haveto be refined

Scene 2 

- Snake not gfetting hurt byu projectiles - why?
    - Are they shooting properly?

Scene 3 

Enemy Shoot Manager check music look state is taking 85 !!! ms !!!

- must fix this

Look of static projectile shots may also be shooting too much

- look into shooting rate

Need to edit my VFX transparency effect present on meshes to use the actual in scene mesh and not a reference to the model - maybe this will address problem with positioning