# May 10

Need to do an overview of ideas and progress past month or so 

Get an idea of mechanics design / technical limitations

Ideation on level designs

Ouroboros

- kill required amount while travelling in loop
    - 3 phases

Seraphim 

- kill required amount before you’re devoured
    - 3 phases

Yggdrasil

Seperate game into component parts to more easily move things between scenes?

Metatron Level

Made a Mesh Processor script to automatically generate prefabs at intersections of a mesh

Will turn these into waypoints for Cinemachine 

IDEA

Player just moves around circles while enemies move around complex straight lines?

Using Cinemachine Path for straight lines - No need to use CInemachine Smooth Path in these scenarios!

Have a PathDetector script for character movement that looks at direction of reticle and tries to choose the best path. Somewhat working, but needs fine tuning and debugging.

Need to colour code junction points and available paths for player, too many currently, would be wonky am imprecise.

Enemies movies around on the whole navmesh, but not sticking to it. Can I lock there verticality and only do 2D movement in this scenario? Look into this.