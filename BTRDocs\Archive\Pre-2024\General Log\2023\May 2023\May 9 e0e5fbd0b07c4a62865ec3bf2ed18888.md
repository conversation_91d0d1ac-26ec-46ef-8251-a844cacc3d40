# May 9

Spawner was not working properply yesterday, trying different approaches

Added Spawner Target script to <PERSON> and <PERSON>er to use nearest methods in Ultimate Spawner

Spawnable Items was broken in recent update! RTFM - Documentation is important!!!

Completely reinstalling Ultimate SPawner  + Waves add on

Seem to have it working better now! Stick to Navmesh is way too processor intensive though. 

Trying a singelton manager approach for some efficieny, waiting on <PERSON><PERSON> for more tips. 

A* Not moving issues - after scanning graph - save to cache! Important!!

Doesn’t exist at runtime otherwise

Need to look at Birth/Death Particles again 

- All screwed up!

Not using Recast graph in current iteration of Ouroboros level - no longer need to have a prefab disable in the scene for graph attachment? Testing this out 

It worked! No need to include in scene

Trying a Gimbal-like approach to movement, due to the jerky nature of the Navmesh movement. 

Using Gimbal script on enemies. 

Added upper bound to Gimbal script, so that the child object quickly moves to new location of parent if they get too far. Not sure it’s working but may be good to workshop.

Issues with level design - moving around mobius strips its very difficult to have enemies to attack all the time

Maybe attack the Snake Head on the strip?

Have a lot of Dodeca’s everywhere, but make the main goal attacking the snake head? 

Trying to do interest AI stuff but it may just be easier to setup strict waypoints for this level, possibly others? 

Need to experiment and think this through. 

Added Snake head to Mobius Strip - placeholder / testing

Idea, snake head slowly moving back and forth on it’s tail, maybe use way points, may a bit of dotween smoothness