# Project Knowledge Vault Guide

This document summarizes the information found in the repository.

## Vault Organization Philosophy

### Structural Hierarchy
```mermaid
flowchart TD
    A[Core Systems] --> A1[Player]
    A --> A2[Enemies]
    A --> A3[Projectiles]
    B[Support Systems] --> B1[UI]
    B --> B2[Audio]
    B --> B3[VFX]
    C[Development] --> C1[Active]
    C --> C2[Archive]
    D[Resources] --> D1[Tech Recipes]
    D --> D2[Asset Library]
    D --> D3[Research Hub]

    style A fill:#FFE4B5,stroke:#333
    style B fill:#D8BFD8,stroke:#333
    style C fill:#98FB98,stroke:#333
    style D fill:#ADD8E6,stroke:#333
```

### Documentation Standards
1. **Architecture**:
   - `core_architecture.md`: Living document updated quarterly
   - Subsystem specs follow `[Area]_system_architecture.md` pattern
2. **Implementation**:
   - Current state: `Systems/[SystemName].md`
   - Experimental: `Design/Systems/[PrototypeName].md`
3. **Historical**:
   - Yearly retrospectives in `Project Management/202X`
   - Migration guides use `[System]_migration.md` format

### Cross-Linking Examples
```obsidian
![[core_architecture.md#Event System]]
See annual progress in [[2022 Design & Mechanics.md#Q3]]
Related optimization: [[Tech Recommendations/DOTS Notes.md]]
```

## Knowledge Network

```mermaid
flowchart LR
    AD[Architecture Docs] -->|guides| ID(Implementation)
    ID -->|feeds into| OP[Optimization]
    PM[Project Mgmt] -->|context for| AD
    OP -->|informs| PM
    TR[Tech Recs] -->|applied in| ID
    RS[Research] -->|supports| TR

    click AD "core_architecture.md"
    click ID "Design/Systems/ProjectileSystem.md"
    click OP "Tech Recommendations/Spatial Hashing.md"
    click PM "Project Management/2024 Design & Mechanics.md"
    click TR "Tech Recommendations/DOTS Notes.md"
    click RS "Research/Progress Journal/2025"
```

### Key Relationships
1. **System Lifecycle**:
   ```plaintext
   Proposal (Research/) → Prototype (Design/) → Implementation (Systems/) → Optimization (Tech/)
   ```
2. **Temporal Context**:
   ```plaintext
   2021 Mechanics → 2023 Refactor → 2025 Roadmap
   ```

### System Interfaces
- `IEventCompatible`: Implemented by GameManager, PlayerSystem, EnemySystem
- `IDamageable`: Unified damage handling across Player, Enemies, and Parts

### Event Architecture
- Dedicated event channels for Game, Enemy, Scene, and Time events
- Event-driven communication between systems
- State changes propagated through EventSystem

### Optimization
- Projectile System uses Unity DOTS (Data-Oriented Tech Stack)
- Object pooling for frequent creations/destructions
- Enemy System supports complexity scaling (basic → multi-state behaviors)

## 2024 Design & Mechanics

### Core Experimental Systems
- **Rhythm Combat**: Polyrythmic shooting tied to musical beats
- **Time Manipulation**: Reversible time effects for puzzle solving
- **Boss Mechanics**:
  - Ouroboros (encircling snake boss)
  - Twin Snakes (temporal mirror enemies)
  - Metatron's Cube (geometric puzzle boss)
- **Advanced AI**: Behavior Designer patterns including:
  - "Scoot and Shoot" flanking
  - Wave-based attack coordination
  - Phased movement systems

## Implementation Framework

### System Development Flow
```mermaid
journey
    title Enemy System Evolution
    section 2021
      Prototype: 5: Research/EnemyAI_Prototype.md
    section 2022
      Core Implementation: 8: Design/Systems/EnemySystem.md
    section 2023
      DOTS Migration: 7: EnemySystemRefactor.md
    section 2024
      Optimization: 9: Tech/EnemyAI_Optimizations.md
```

### Directory Conventions
| Path                          | Content Type                | Example Files                          |
|-------------------------------|-----------------------------|-----------------------------------------|
| `Systems/Core/Projectiles/`   | Implementation details      | Implementation.md                      |
| `DevelopmentStates/Active/`   | Current design docs         | 2024_Design_Mechanics.md               |
| `Project Management/202[X]/`  | Yearly development context  | 2024 Design & Mechanics.md             |
| `Tech Recommendations/`       | Proven optimization patterns| DOTS Patterns.md, SpatialHashing.md    |
| `Research/Progress Journal/`  | Experimental implementations| 2025/ProjectileRadarUpdate.md          |

### Proposed Structure Changes
1. **System-Centric Organization**:
   ```plaintext
   Systems/
   ├── Player/
   │   ├── Architecture.md
   │   ├── Implementation/
   │   └── Optimizations/
   └── Projectiles/
       ├── Tech Recipes/
       └── Research/
   ```

2. **Asset Pipeline Integration**:
   - Central `Asset Library` with subfolders:
     - `SourceFiles/`, `Imported/`, `RuntimeUsage.md`

3. **Development Workflow**:
   ```mermaid
   graph LR
       Idea --> Research
       Research --> Prototype
       Prototype -->|Approved| Active
       Active -->|Obsolete| Archive
   ```

```mermaid
graph TB
    N[New Idea] --> R[Research Journal]
    R --> P[Design Prototype]
    P --> C{Approved?}
    C -->|Yes| I[System Implementation]
    C -->|No| A[Archive]
    I --> O[Optimization]
    O --> M[Project Mgmt Report]
```

#### Implemented Tech Recommendations
- **Projectile System**:
  ✓ Spatial hashing collision detection
  ✓ DOTS physics with 10k+ concurrent projectiles
  ✗ Async load balancing (planned Q3 2025)

- **Enemy AI**:
  ✓ Behavior Tree parallel execution
  ✓ Jobified movement calculations
  ✓ LOD-based update rates

### Technical Implementations
- **Unity Architecture**:
  - ECS-DOTS hybrid model
  - Event bus for system communication
  - Asynchronous scene loading system
- **Performance Critical Paths**:
  - Burst-compiled projectile physics
  - Jobified enemy behavior trees
  - GPU-driven visual effects

### Iterative Design Process
- Monthly mechanic prototypes
- Playtest-driven balancing
- Performance profiling integration
- Third-party tool integration:
  - Ultimate Spawner
  - A* Pathfinding
  - PrimeTween

## Unit Testing

The project uses unit testing with the MVP/MVC architectures. It uses separate test assemblies for editor and runtime tests, the Unity Test Framework, and NSubstitute for mocking.

## Progress Journal

The project's progress journal includes notes on implementing radar tracking for enemy projectiles using Sickscore Games' HUD Navigation System.