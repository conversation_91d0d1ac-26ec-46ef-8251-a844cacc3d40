using UnityEngine;
using System.Collections.Generic;
using Unity.Mathematics;
using BTR.EnemySystem;
using ZLinq;

namespace BTR.EnemySystem
{
    /// <summary>
    /// High-performance enemy separation service that prevents enemies from clustering together.
    /// Uses spatial grid for efficient neighbor detection and integrates with A* pathfinding.
    /// </summary>
    public class EnemySeparationService : MonoBehaviour
    {
        [Header("Separation Settings")]
        [SerializeField] private float separationRadius = 4f;
        [SerializeField] private float separationForce = 20f; // Increased from 5f
        [SerializeField] private float maxSeparationDistance = 12f;
        [SerializeField] private AnimationCurve separationFalloff = AnimationCurve.Linear(0f, 1f, 1f, 0f);

        [Header("Performance Settings")]
        [SerializeField] private int maxSeparationChecksPerFrame = 30; // Increased from 20
        [SerializeField] private float separationUpdateInterval = 0.05f; // Reduced from 0.1f for faster updates
        [SerializeField] private bool enableDistanceLOD = true;
        [SerializeField] private float lodNearDistance = 15f;
        [SerializeField] private float lodFarDistance = 30f;

        [Header("Integration Settings")]
        [SerializeField] private bool integrateWithPathfinding = true;
        [SerializeField] private float pathfindingInfluence = 0.5f; // Reduced from 0.7f
        [SerializeField] private float separationInfluence = 0.5f; // Increased from 0.3f

        [Header("Debug")]
        [SerializeField] private bool enableDebugLogs = false;
        [SerializeField] private bool showDebugVisualization = false;

        // Cached data for performance
        private class EnemySeparationData
        {
            public Transform transform;
            public Vector3 lastPosition;
            public Vector3 separationForce;
            public float lastUpdateTime;
            public int neighborCount;
            public bool needsUpdate;
            public float lodLevel; // 0 = high detail, 1 = low detail
        }

        // Service state
        private Dictionary<Transform, EnemySeparationData> trackedEnemies = new Dictionary<Transform, EnemySeparationData>();
        private Queue<Transform> updateQueue = new Queue<Transform>();
        private List<Transform> tempNeighbors = new List<Transform>();
        private Transform playerTransform;
        private float lastGlobalUpdate;

        public static EnemySeparationService Instance { get; private set; }

        // Domain reload handling
        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.SubsystemRegistration)]
        private static void ResetStaticData()
        {
            Instance = null;
        }

        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                InitializeService();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void InitializeService()
        {
            // Find player transform
            var player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                playerTransform = player.transform;
            }

            if (enableDebugLogs)
            {
                Debug.Log($"[EnemySeparationService] Initialized with separation radius: {separationRadius}");
            }
        }

        private void Update()
        {
            if (Time.time >= lastGlobalUpdate + separationUpdateInterval)
            {
                ProcessSeparationUpdates();
                lastGlobalUpdate = Time.time;
            }
        }

        /// <summary>
        /// Register an enemy for separation tracking
        /// </summary>
        public void RegisterEnemy(Transform enemyTransform)
        {
            if (enemyTransform == null || trackedEnemies.ContainsKey(enemyTransform))
                return;

            var data = new EnemySeparationData
            {
                transform = enemyTransform,
                lastPosition = enemyTransform.position,
                separationForce = Vector3.zero,
                lastUpdateTime = 0f,
                neighborCount = 0,
                needsUpdate = true,
                lodLevel = 0f
            };

            trackedEnemies[enemyTransform] = data;
            updateQueue.Enqueue(enemyTransform);

            if (enableDebugLogs)
            {
                Debug.Log($"[EnemySeparationService] Registered enemy: {enemyTransform.name}");
            }
        }

        /// <summary>
        /// Unregister an enemy from separation tracking
        /// </summary>
        public void UnregisterEnemy(Transform enemyTransform)
        {
            if (enemyTransform != null && trackedEnemies.ContainsKey(enemyTransform))
            {
                trackedEnemies.Remove(enemyTransform);

                if (enableDebugLogs)
                {
                    Debug.Log($"[EnemySeparationService] Unregistered enemy: {enemyTransform.name}");
                }
            }
        }

        /// <summary>
        /// Get separation-adjusted target position for an enemy
        /// </summary>
        public Vector3 GetSeparationAdjustedPosition(Transform enemyTransform, Vector3 originalTarget)
        {
            if (!trackedEnemies.TryGetValue(enemyTransform, out var data))
                return originalTarget;

            // Apply separation force to the original target
            Vector3 separationOffset = data.separationForce * separationInfluence;
            Vector3 adjustedTarget = originalTarget + separationOffset;

            // Ensure we don't move too far from the original target
            Vector3 toAdjusted = adjustedTarget - originalTarget;
            if (toAdjusted.magnitude > maxSeparationDistance)
            {
                adjustedTarget = originalTarget + toAdjusted.normalized * maxSeparationDistance;
            }

            return adjustedTarget;
        }

        /// <summary>
        /// Get separation force for an enemy (for direct integration with movement)
        /// </summary>
        public Vector3 GetSeparationForce(Transform enemyTransform)
        {
            if (trackedEnemies.TryGetValue(enemyTransform, out var data))
            {
                return data.separationForce;
            }
            return Vector3.zero;
        }

        /// <summary>
        /// Check if an enemy has nearby neighbors (for decision making)
        /// </summary>
        public bool HasNearbyEnemies(Transform enemyTransform, float radius = -1f)
        {
            if (radius < 0f) radius = separationRadius;

            if (trackedEnemies.TryGetValue(enemyTransform, out var data))
            {
                return data.neighborCount > 0;
            }

            // Fallback: direct spatial query
            return GetNearbyEnemyCount(enemyTransform.position, radius) > 0;
        }

        /// <summary>
        /// Get count of nearby enemies within radius
        /// </summary>
        public int GetNearbyEnemyCount(Vector3 position, float radius)
        {
            if (EnemyManager.Instance == null)
                return 0;

            tempNeighbors.Clear();
            EnemyManager.Instance.GetEnemiesInRadius(position, radius, tempNeighbors);
            return tempNeighbors.Count;
        }

        private void ProcessSeparationUpdates()
        {
            int processedCount = 0;
            int maxProcessThisFrame = Mathf.Min(maxSeparationChecksPerFrame, updateQueue.Count);

            while (processedCount < maxProcessThisFrame && updateQueue.Count > 0)
            {
                Transform enemy = updateQueue.Dequeue();

                if (enemy == null || !trackedEnemies.ContainsKey(enemy))
                {
                    processedCount++;
                    continue;
                }

                ProcessEnemySeparation(enemy);

                // Re-queue for next update cycle
                updateQueue.Enqueue(enemy);
                processedCount++;
            }

            if (enableDebugLogs && processedCount > 0)
            {
                Debug.Log($"[EnemySeparationService] Processed {processedCount} separation updates");
            }
        }

        private void ProcessEnemySeparation(Transform enemy)
        {
            var data = trackedEnemies[enemy];

            // Calculate LOD level based on distance to player
            if (enableDistanceLOD && playerTransform != null)
            {
                float distanceToPlayer = Vector3.Distance(enemy.position, playerTransform.position);
                data.lodLevel = Mathf.InverseLerp(lodNearDistance, lodFarDistance, distanceToPlayer);
            }

            // Skip update if LOD level is too high and not enough time passed
            float updateInterval = Mathf.Lerp(separationUpdateInterval, separationUpdateInterval * 3f, data.lodLevel);
            if (Time.time - data.lastUpdateTime < updateInterval && !data.needsUpdate)
                return;

            // Calculate separation force
            Vector3 separationForce = CalculateSeparationForce(enemy, data);
            data.separationForce = separationForce;
            data.lastPosition = enemy.position;
            data.lastUpdateTime = Time.time;
            data.needsUpdate = false;
        }

        private Vector3 CalculateSeparationForce(Transform enemy, EnemySeparationData data)
        {
            if (EnemyManager.Instance == null)
                return Vector3.zero;

            // Get nearby enemies using spatial grid
            tempNeighbors.Clear();
            EnemyManager.Instance.GetEnemiesInRadius(enemy.position, separationRadius, tempNeighbors);

            Vector3 separationForce = Vector3.zero;
            int neighborCount = 0;

            foreach (var neighbor in tempNeighbors)
            {
                if (neighbor == null || neighbor == enemy)
                    continue;

                Vector3 toNeighbor = neighbor.position - enemy.position;
                float distance = toNeighbor.magnitude;

                if (distance > 0.01f && distance < separationRadius)
                {
                    // Calculate separation force with stronger repulsion for closer enemies
                    float normalizedDistance = distance / separationRadius;
                    float forceStrength = this.separationForce * separationFalloff.Evaluate(normalizedDistance);

                    // Apply stronger force for very close enemies
                    if (distance < separationRadius * 0.5f)
                    {
                        forceStrength *= 2f; // Double force for very close enemies
                    }

                    // Use inverse distance for stronger close-range repulsion
                    forceStrength /= Mathf.Max(distance, 0.1f);

                    // Apply force away from neighbor
                    Vector3 forceDirection = -toNeighbor.normalized;
                    separationForce += forceDirection * forceStrength;
                    neighborCount++;
                }
            }

            data.neighborCount = neighborCount;

            // Normalize and apply maximum force limit
            if (separationForce.magnitude > this.separationForce)
            {
                separationForce = separationForce.normalized * this.separationForce;
            }

            return separationForce;
        }

        /// <summary>
        /// Force update separation for a specific enemy (useful when enemy changes behavior)
        /// </summary>
        public void ForceUpdateSeparation(Transform enemyTransform)
        {
            if (trackedEnemies.TryGetValue(enemyTransform, out var data))
            {
                data.needsUpdate = true;
            }
        }

        /// <summary>
        /// Get separation statistics for debugging/monitoring
        /// </summary>
        public (int trackedCount, int queueSize, float avgNeighbors) GetSeparationStats()
        {
            float totalNeighbors = 0f;
            foreach (var data in trackedEnemies.Values)
            {
                totalNeighbors += data.neighborCount;
            }

            float avgNeighbors = trackedEnemies.Count > 0 ? totalNeighbors / trackedEnemies.Count : 0f;
            return (trackedEnemies.Count, updateQueue.Count, avgNeighbors);
        }

        private void OnDrawGizmos()
        {
            if (!showDebugVisualization || !Application.isPlaying)
                return;

            // Draw separation forces
            Gizmos.color = Color.cyan;
            foreach (var kvp in trackedEnemies)
            {
                if (kvp.Key == null) continue;

                var enemy = kvp.Key;
                var data = kvp.Value;

                if (data.separationForce.magnitude > 0.1f)
                {
                    Gizmos.DrawRay(enemy.position, data.separationForce);
                    Gizmos.DrawWireSphere(enemy.position, separationRadius * 0.5f);
                }
            }

#if UNITY_EDITOR
            // Draw separation radius for selected enemy
            if (UnityEditor.Selection.activeTransform != null &&
                trackedEnemies.ContainsKey(UnityEditor.Selection.activeTransform))
            {
                Gizmos.color = new Color(1f, 0f, 1f, 0.3f);
                Gizmos.DrawSphere(UnityEditor.Selection.activeTransform.position, separationRadius);
            }
#endif
        }

        private void OnDestroy()
        {
            if (Instance == this)
            {
                Instance = null;
            }
        }
    }
}