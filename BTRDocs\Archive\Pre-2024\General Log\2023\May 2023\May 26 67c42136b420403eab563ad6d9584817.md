# May 26

Enemies dying when i dont even shoot some of the bullets

fix this! is it from falling off or is it bullet collisions

---

---

---

---

---

Metatron level

- move through making different shapes
- path changes colour as you move laong it, make certain shapes
- with each wave, change the dolly shape a player moves through

<PERSON><PERSON> as inbetween thing on section of level, possibly before boss as well

Performance in tube allows critical hit on boss? 

Killing all bullets when wave ended - done in Level Debugging

Some errors turning up throughout level

Error at 303

```csharp
// Error catching assertions.. not included in build
            if (_numberOfAliveParticles < _aliveParticles.Length)
                UnityEngine.Assertions.Assert.IsTrue(_aliveParticles[_numberOfAliveParticles].remainingLifetime < 0.001f);
            if (_numberOfAliveObjects < _aliveObjects.Length)
                UnityEngine.Assertions.Assert.IsTrue(_aliveObjects[_numberOfAliveObjects] == null || !_aliveObjects[_numberOfAliveObjects].IsInitialized);
        }
```

![Untitled](May%2026%2067c42136b420403eab563ad6d9584817/Untitled.png)

![Untitled](May%2026%2067c42136b420403eab563ad6d9584817/Untitled%201.png)

Error occuring at 158

```csharp
// Error catching assertions.. not included in build
            if (_numberOfAliveObjects < _aliveObjects.Length)
                UnityEngine.Assertions.Assert.IsTrue(_aliveObjects[_numberOfAliveObjects] == null || !_aliveObjects[_numberOfAliveObjects].IsInitialized);
```

Still having enemy time rewind issues

![Untitled](May%2026%2067c42136b420403eab563ad6d9584817/Untitled%202.png)

Order of customAIPathAlignedToSurface does not seem to improve this

Need alternative method for handling time rewind for enemies.