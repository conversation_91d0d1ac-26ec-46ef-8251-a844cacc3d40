# 2022 - Design & Mechanics - Summary

This document summarizes the 2022 design and mechanics development notes, focusing on core mechanics, level design thoughts, and influences.

## Key Summary Points:

*   **Camera and Movement (April):** Experimentation with camera behavior, initially linking it to cursor movement and then to the shooting reticle. Adjustments to player movement clamping and consideration of camera tilting inspired by After Burner.
*   **Wave Spawner and Enemy AI (April):** Identified the need for a robust wave spawner and refined basic enemy AI for shooting and movement. Explored different enemy behaviors, including fast-dying enemies and slower, more deliberate enemy types.
*   **Core Mechanics Definition (April, June):** Defined core mechanics including a lock-on system, bullet collection and shooting, and enemy types with varying behaviors. Explored stamina for lock-on and potential for unlockable bullet types.
*   **Level Design Concepts (May, June):** Brainstormed level design ideas, including transition sections, wave patterns, and integration of scenery and buildings for enemy avoidance. Considered level progression and themes, drawing inspiration from <PERSON>z and Panzer Dragoon.
*   **Inspiration and Influences (April, May, June):**  Drew inspiration from games like Panzer Dragoon, After Burner, Boomerang X, Rez, Olli Olli World, and Nex Machina. Analyzed mechanics from games like Pokemon Snap and Graze Counter.
*   **Puzzle and Core Loop (June):** Explored puzzle elements and the core game loop, considering a blend of Rez, Panzer Dragoon, and Dodgeball.  Focused on movement, dodging, and rhythmic action, potentially influenced by Thumper.
*   **Narrative Integration (April, May):** Developed a narrative concept of the player as an AI auto-protection system. Considered themes of digital corruption, excavating digital memories, and the evolution of civilization and life.
*   **Technical Considerations (March, May):** Addressed spatialization of sound, JSON for transitions, and explored state machines for player actions and projectile behaviors.

This summary outlines the iterative design process in 2022, marked by experimentation across camera controls, enemy behaviors, and level concepts, all while grounding the game in a blend of action and rhythm mechanics inspired by classic and contemporary games.