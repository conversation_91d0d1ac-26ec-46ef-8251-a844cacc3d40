# Summary of 2020 - 2021 Audio.md

This file documents the audio and music development for the project during 2020-2021. Key areas of focus include:

- **Koreographer Integration:** Extensive use of Ko<PERSON><PERSON> for rhythm-based gameplay, specifically for shoot and lock-on mechanics.
    - Implementing Koreographer for timing game events with music tempo.
    - Exploring Koreographer Pro for MIDI implementation and advanced features.
    - Investigating Koreographer/DOTween integrations for synchronized animations.
- **Sound Design:** Refining and cleaning up sounds for lock-on and shooting actions.
    - Experimenting with different music timings.
    - Adding kick vibration scripts synchronized with the music.
    - Designing audio mixing groups and ducking noise tracks for lock/shoot actions.
    - Considering bullet particle effects with kick tracks and audio cues.
- **Rhythm and Timing Experiments:**
    - Experimenting with offset rhythms inspired by Rival Consoles.
    - Timing particle releases to the beat.
    - Integrating Chronos and Koreographer for rewind mechanics synchronized with music.
- **Technical Considerations:**
    - Exploring FMOD integration instead of Unity's built-in audio tools.
    - Planning a naming scheme for Koreographer objects similar to audio submixes.
    - Addressing Koreographer-related issues, such as timing problems and bullet behavior.
- **Inspiration:** Drawing inspiration from the film "Synchronic" for music and drug-related game ideas.
- **Collaboration:** Planning to contact <PERSON> (Level Curve?) and <PERSON> regarding FMOD and audio implementation.