using UnityEngine;
using System.Collections.Generic;
using Pathfinding;
using BTR;
using BTR.EnemySystem;

namespace BTR
{
    [RequireComponent(typeof(FollowerEntity))]
    public class PathfindingMovementBehavior : MonoBehaviour, IMovementBehavior, I<PERSON><PERSON><PERSON>ble, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPathHandler
    {
        [Header("Path Request Settings")]
        [SerializeField] private float normalPathRequestInterval = 0.5f;
        [SerializeField] private float stoppedPathRequestInterval = 0.1f;
        [SerializeField] private float maxStoppedTime = 0.5f;

        [Header("Line of Sight Settings")]
        [SerializeField] private float lineOfSightCacheTime = 0.1f;

        [Header("Debug")]
        [SerializeField] private bool enableDebugLogs = false;

        private EnemyCore enemyCore;

        private FollowerEntity followerEntity;
        private bool isShuttingDown;
        private bool hasLineOfSight;
        private float lastLineOfSightCheck;
        private float lastPathRequest;
        private Vector3 targetPosition;
        private float stoppedTime;
        private Vector3 lastPosition;
        private bool wasMoving;

        private void Awake()
        {
            followerEntity = GetComponent<FollowerEntity>();
            lastPosition = transform.position;
            wasMoving = true;

            // Get EnemyCore reference
            enemyCore = GetComponent<EnemyCore>();
            if (enemyCore == null)
            {
                Debug.LogError($"[{GetType().Name}] Missing EnemyCore component on {gameObject.name}");
                enabled = false;
                return;
            }

            if (followerEntity == null)
            {
                Debug.LogError($"[{GetType().Name}] Missing FollowerEntity component on {gameObject.name}");
                enabled = false;
                return;
            }
        }

        public bool HasReachedDestination => followerEntity.reachedDestination;

        public bool HasLineOfSightToTarget(Vector3 targetPosition)
        {
            float currentTime = Time.time;
            if (currentTime - lastLineOfSightCheck >= lineOfSightCacheTime)
            {
                lastLineOfSightCheck = currentTime;
                hasLineOfSight = enemyCore.CheckLineOfSight(targetPosition);
            }
            return hasLineOfSight;
        }

        public void Initialize(Transform transform, EnemyConfiguration config)
        {
            // Use config values if available, otherwise use FollowerEntity's default values
            followerEntity.maxSpeed = config != null ? config.moveSpeed : followerEntity.maxSpeed;
            followerEntity.stopDistance = config != null ? config.minPlayerDistance : followerEntity.stopDistance;
            followerEntity.radius = 0.5f;  // Default radius

            // Set pathfinding settings
            followerEntity.pathfindingSettings.graphMask = Pathfinding.GraphMask.everything;  // Default to all graphs
            followerEntity.pathfindingSettings.traversableTags = -1;  // Default to all tags
            followerEntity.pathfindingSettings.tagPenalties = new uint[32];  // Default no penalties

            // Enable movement
            followerEntity.isStopped = false;
            followerEntity.canMove = true;
            stoppedTime = 0f;
            lastPosition = transform.position;
            wasMoving = true;

            // Register with EnemyPathManager
            if (EnemyPathManager.Instance != null)
            {
                EnemyPathManager.Instance.RegisterEnemy(enemyCore);
            }
            else if (enableDebugLogs)
            {
                Debug.LogWarning($"[{GetType().Name}] EnemyPathManager instance not found during initialization");
            }

            if (enemyCore != null && enemyCore.PlayerTransform != null)
            {
                SetTarget(enemyCore.PlayerTransform);
            }
        }

        public void UpdateMovement(float deltaTime)
        {
            if (isShuttingDown) return;

            // Check if we're actually moving
            bool isMoving = (transform.position - lastPosition).sqrMagnitude > 0.0001f;
            lastPosition = transform.position;

            // Update stopped time
            if (!isMoving && wasMoving)
            {
                stoppedTime = 0f; // Reset stopped time when we first stop
            }
            else if (!isMoving)
            {
                stoppedTime += deltaTime;
            }
            wasMoving = isMoving;

            float currentTime = Time.time;
            float requestInterval = (stoppedTime >= maxStoppedTime) ? stoppedPathRequestInterval : normalPathRequestInterval;

            // Request path more frequently if we've been stopped for too long
            if (currentTime - lastPathRequest >= requestInterval)
            {
                lastPathRequest = currentTime;
                RequestNewPath();

                // Force a new destination if we've been stopped too long
                if (stoppedTime >= maxStoppedTime && enemyCore != null && enemyCore.PlayerTransform != null)
                {
                    SetTarget(enemyCore.PlayerTransform);
                }
            }
        }

        private void RequestNewPath()
        {
            if (EnemyPathManager.Instance != null && enemyCore != null)
            {
                EnemyPathManager.Instance.RequestPath(enemyCore, this);
            }
        }

        public void SetDestination(Vector3 position)
        {
            if (followerEntity == null || isShuttingDown) return;

            // Apply separation adjustment to the target position
            Vector3 adjustedPosition = ApplySeparationToTarget(position);
            targetPosition = adjustedPosition;
            RequestNewPath();
            stoppedTime = 0f; // Reset stopped time when setting new destination
        }

        public void SetTarget(Transform target)
        {
            if (followerEntity == null || isShuttingDown || target == null) return;

            // Apply separation adjustment to the target position
            Vector3 adjustedPosition = ApplySeparationToTarget(target.position);
            targetPosition = adjustedPosition;
            RequestNewPath();
            stoppedTime = 0f; // Reset stopped time when setting new target
        }

        public Vector3 GetTargetPosition()
        {
            return targetPosition;
        }

        public void OnPathComplete(List<Vector3> path)
        {
            if (followerEntity == null || isShuttingDown || path == null || path.Count == 0) return;

            // Update the follower entity's destination with the next point in the path
            followerEntity.destination = path[path.Count - 1];

            // If we've been stopped for too long, try to unstick by adjusting the path
            if (stoppedTime >= maxStoppedTime)
            {
                followerEntity.isStopped = false;
                followerEntity.canMove = true;
            }
        }

        public void Reset()
        {
            isShuttingDown = false;
            stoppedTime = 0f;
            if (followerEntity != null)
            {
                followerEntity.isStopped = false;
            }
        }

        public void OnDeath()
        {
            isShuttingDown = true;
            if (followerEntity != null)
            {
                followerEntity.isStopped = true;
            }
        }

        private void OnEnable()
        {
            // Register with EnemyPathManager when enabled
            if (EnemyPathManager.Instance != null && enemyCore != null)
            {
                EnemyPathManager.Instance.RegisterEnemy(enemyCore);
            }
        }

        private void OnDisable()
        {
            // Unregister from EnemyPathManager when disabled
            if (EnemyPathManager.Instance != null && enemyCore != null)
            {
                EnemyPathManager.Instance.UnregisterEnemy(enemyCore);
            }

            // Ensure cleanup happens in OnDisable before components are destroyed
            if (!isShuttingDown)
            {
                SafeCleanup();
            }
        }

        private void OnDestroy()
        {
            // Mark as shutting down but don't try to access components
            isShuttingDown = true;
        }

        public void Cleanup()
        {
            SafeCleanup();
        }

        private void SafeCleanup()
        {
            try
            {
                // Only try to access followerEntity if the component and game object are still valid
                if (this != null && gameObject != null && gameObject.activeInHierarchy && followerEntity != null)
                {
                    followerEntity.isStopped = true;
                    followerEntity.destination = transform.position;
                }

                // Reset state
                hasLineOfSight = false;
                lastLineOfSightCheck = 0f;
                stoppedTime = 0f;
                isShuttingDown = true;
            }
            catch (System.Exception e)
            {
                // Only log if we're not already shutting down
                if (!isShuttingDown)
                {
                    Debug.LogWarning($"[{GetType().Name}] Error during cleanup: {e.Message}");
                }
            }
        }

        public void OnDrawGizmos()
        {
            if (!Application.isPlaying || followerEntity == null) return;

            // Draw line of sight visualization if we have a target
            if (followerEntity.destination != Vector3.positiveInfinity)
            {
                Gizmos.color = hasLineOfSight ? Color.green : Color.red;
                Gizmos.DrawLine(transform.position, followerEntity.destination);
            }

            // Draw stopped time indicator
            if (stoppedTime > 0)
            {
                float ratio = Mathf.Clamp01(stoppedTime / maxStoppedTime);
                Gizmos.color = Color.Lerp(Color.yellow, Color.red, ratio);
                Gizmos.DrawWireSphere(transform.position + Vector3.up, 0.5f + ratio * 0.5f);
            }
        }

        public void DrawPathGizmos(Color pathColor)
        {
            if (followerEntity == null) return;

            // Draw destination
            if (followerEntity.destination != Vector3.positiveInfinity)
            {
                Gizmos.color = Color.yellow;
                Gizmos.DrawWireSphere(followerEntity.destination, 0.5f);
            }
        }

        /// <summary>
        /// Apply separation forces to target position before pathfinding
        /// </summary>
        private Vector3 ApplySeparationToTarget(Vector3 originalTarget)
        {
            if (enemyCore == null) return originalTarget;

            Vector3 adjustedTarget = originalTarget;

            // Apply soft separation forces if available
            // Separation now handled by SimpleEnemySpacing component
            // if (EnemyManager.Instance?.SeparationService != null)
            {
                // adjustedTarget = EnemyManager.Instance.SeparationService.GetSeparationAdjustedPosition(
                //     enemyCore.transform, adjustedTarget);
            }

            // Apply hard spacing constraints if available
            // if (EnemyManager.Instance?.MandatorySpacingEnforcer != null)
            {
                adjustedTarget = EnemyManager.Instance.MandatorySpacingEnforcer.EnforceMinimumSpacing(
                    adjustedTarget, enemyCore.transform);
            }

            // Debug visualization
            if (enableDebugLogs && Vector3.Distance(originalTarget, adjustedTarget) > 0.1f)
            {
                Debug.Log($"[{GetType().Name}] Separation adjusted target for {gameObject.name}: " +
                         $"{originalTarget} -> {adjustedTarget} (distance: {Vector3.Distance(originalTarget, adjustedTarget):F2})");
            }

            return adjustedTarget;
        }
    }
}
