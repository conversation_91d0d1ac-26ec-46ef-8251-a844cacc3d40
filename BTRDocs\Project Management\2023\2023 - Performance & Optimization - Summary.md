# Summary of 2023 - Performance & Optimization.md

This file contains notes and observations related to performance and optimization efforts for the game project throughout 2023. Key points include:

*   **Shader Optimization:** Shader variants compilation significantly impacts build times. Preloading shader variants is identified as a solution. Experimentation with different shader settings (alpha clipping, outline shaders, HDR) for performance and visual quality.
*   **Color Space & Rendering Issues:**  Addressing washed-out colors in builds by switching to Linear color space and experimenting with anti-aliasing. Height fog may contribute to color issues.
*   **Build Performance:**  Switching from Mono to IL2CPP backend dramatically improved frame rates in builds, despite longer build times. Asset Hunter is used to reduce project size and improve build times.
*   **Object Pooling:**  Debugging issues with object pooling, particularly for enemies. Assertion failures point to problems with enemy reuse and pooling logic in the ObjectParticleSpawner script. Bullet pooling seems less problematic.
*   **Unity Crashes & Errors:**  Troubleshooting Unity crashes, often related to GPU issues, shader problems, or specific assets (Buto, A*, GPU Instancer). Downgrading to DirectX11 and adjusting settings (D12 to D11) helped stabilize the editor.
*   **Navmesh & AI Performance:**  Navmesh calculations and AI performance are recurring concerns, especially with complex scenes or features like Funnel Modifier. Optimization efforts include updating A* Pathfinding, adjusting navmesh settings, and monitoring enemy behavior on navmeshes.
*   **Memory Leaks:**  Investigating potential memory leaks with Forward+ Renderer and transparency issues. Switching to DirectX 11 resolved some memory leak problems.
*   **Profiling & Debugging:**  Using Unity Profiler to identify performance bottlenecks (Crosshair script, Physics.Boxcast). Implementing video gameplay recording for bug tracking and on-device debugging tools (SRDebugger, custom debug console).
*   **Asset Management:**  Strategies for removing unnecessary assets to improve build times and project size. Exporting assets caused issues, suggesting direct scene cleanup and asset identification tools are preferred.
*   **Specific Issues & Fixes:**  Notes on specific performance problems and their resolutions, such as fixing map constraints, optimizing UI, addressing projectile pooling, and resolving errors after Unity upgrades.

The document reflects a focus on identifying and resolving performance bottlenecks, optimizing rendering and build processes, and ensuring стабильность across different hardware (Steam Deck, PC). Object pooling, shader optimization, and memory management are key areas of concern and ongoing investigation.