# Nov. 20th

```jsx
- Fix alternate loop firing (CheckMusicState)
    - Just know the lock state - no need to talk to FMOD - its not central!
        - but how do we know the repeat time? integrate fmod callback system.
        - markers that delineate loop point + knowing if we are in lock state
```

Addressed this with a triplet koreography track

need to think if this is how i still want to go, but it works 

- alternate system was fmod callbacks, but that breaks koreographer

also split shooting projectiles over several frames, majorly reduces performance spike

Also reworked audio system in Projectile Audio Manager for performance benefits

Tried implementing spatial hashing quickly, no success. may not be necessary - consider other bottleneck first