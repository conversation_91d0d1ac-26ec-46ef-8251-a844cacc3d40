{"main": {"id": "d02b5b891efdc825", "type": "split", "children": [{"id": "986442906c128cb2", "type": "tabs", "children": [{"id": "61375fbd139aba43", "type": "leaf", "state": {"type": "markdown", "state": {"file": "copilot-conversations/Give_me_information_on_my_Ouroboros_level,_tryign_to_remember@20250723_142626.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "Give_me_information_on_my_Ouroboros_level,_tryign_to_remember@20250723_142626"}}]}], "direction": "vertical"}, "left": {"id": "d07fc4b4ed739cc2", "type": "split", "children": [{"id": "788c85c58fa3a62c", "type": "tabs", "children": [{"id": "6a99bddacb0ba96b", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "f7826612408a13da", "type": "leaf", "state": {"type": "search", "state": {"query": "Treatment", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "2c7cde2c32767cc3", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 321.5}, "right": {"id": "ca506bf28ba1a7be", "type": "split", "children": [{"id": "427014b49a5288f2", "type": "tabs", "children": [{"id": "354027e49469d360", "type": "leaf", "state": {"type": "backlink", "state": {"file": "KnownIssues.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for KnownIssues"}}, {"id": "0f89a0fb133814fa", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "KnownIssues.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from KnownIssues"}}, {"id": "baa96c8c323bc6bf", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "b27ce0d98c0b14ae", "type": "leaf", "state": {"type": "outline", "state": {"file": "copilot-conversations/Give_me_information_on_my_Ouroboros_level,_tryign_to_remember@20250723_142626.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of Give_me_information_on_my_Ouroboros_level,_tryign_to_remember@20250723_142626"}}, {"id": "493a661d67072494", "type": "leaf", "state": {"type": "copilot-chat-view", "state": {}, "icon": "message-square", "title": "Copilot"}}], "currentTab": 4}], "direction": "horizontal", "width": 442.5}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false, "syncthing-integration:Open Syncthing conflict manager modal": false, "copilot:Open Copilot Chat": false}}, "active": "61375fbd139aba43", "lastOpenFiles": ["copilot-conversations/Give_me_information_on_my_Ouroboros_level,_tryign_to_remember@20250723_142626.md", "copilot-conversations", "Current/Scenario Planning/Levels and Themes.md", "Current/Scenario Planning/Levels and Bosses Elaborated.md", "Current/Scenario Planning/Asset Ideation_References.md", "Current/Scenario Planning/Asset Ideation.md", "REORGANIZATION_SUMMARY.md", "FINAL_DOCUMENTATION_STATUS.md", "Pooling_Migration_Guide.md", "Code Pattern Analysis.md", "Archive/2024_Design_Mechanics.md", "Current/Asset Generation.md", "Current/Game Systems Tools.md", "Current/Errors.md", "Current/Important Links.md", "Current/README.md", "Vault-Standards.md", "Archive/Migration Guides/README.md", "TrashBin/CLAUDE.local.md", "Archive/Migration Guides/MANUAL_COMPONENT_MIGRATION_GUIDE.md", "Archive/Migration Guides/Fix_ManagerIntegrationAdapter_Error.md", "Archive/Migration Guides", "Code Patter Analysis.md", "Errors.md", "Game Systems Tools.md", "Important Links.md", "Scenario Planning/Asset Ideation_References.md", "Scenario Planning/Asset Ideation.md", "Archive/Game Development/Project Beat Traveller GDD/Beat Traveller Treatment 2/Untitled 2.png", "Archive/Game Development/Project Beat Traveller GDD/Beat Traveller Treatment 2/Untitled 1.png", "Archive/Game Development/Project Beat Traveller GDD/Beat Traveller Treatment 2/Untitled.png", "Archive/Game Development/Project Beat Traveller GDD/Beat Traveller Treatment 1/Untitled 8.png", "Archive/Game Development/Project Beat Traveller GDD/Beat Traveller Treatment 1/Untitled 4.png", "Archive/Game Development/Project Beat Traveller GDD/Beat Traveller Treatment 1/Untitled 5.png", "Archive/Game Development/Project Beat Traveller GDD/Beat Traveller Treatment 1/Untitled 6.png", "Archive/Game Development/Project Beat Traveller GDD/Beat Traveller Treatment 1/Untitled 7.png", "Archive/Game Development/Project Beat Traveller GDD/Beat Traveller Treatment 1/Untitled 2.png", "Archive/Game Development/Project Beat Traveller GDD/Beat Traveller Treatment 1/Untitled 3.png", "Scenario Planning", "Design/Systems/Projectiles", "Design/Systems/Player", "Design/Systems/Enemies", "Design/Systems/Mechanics/Movement", "Design/Systems/Mechanics/Combat", "Design/Systems/Mechanics/Economy", "Design/Systems/UI-UX/Menus", "Untitled.canvas"]}