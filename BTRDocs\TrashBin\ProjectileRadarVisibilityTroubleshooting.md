# Projectile Radar Visibility Troubleshooting Guide

## Overview
This document outlines potential issues and solutions for projectiles not appearing on the radar system in BTR. The radar visibility system relies on multiple components working in sync, and issues can arise from state mismatches between these systems.

## Key Components
1. **ProjectileStateBased**: Manages individual projectile behavior and state
2. **ProjectileJobSystem**: <PERSON>les parallel processing of projectile movement and state
3. **ProjectileTrackingManager**: Manages radar registration and tracking
4. **ProjectilePool**: Handles projectile instantiation and recycling

## Critical State Flags
The radar visibility system relies on three critical flags in the job system:
- `ActiveFlags`: Indicates if the projectile is active in the job system
- `HomingFlags`: Indicates if the projectile has homing enabled
- `HasTargetFlags`: Indicates if the projectile has a valid target

## Potential Issues and Solutions

### 1. Job System State Synchronization
#### Issue
State mismatches between the ProjectileStateBased component and the ProjectileJobSystem can cause projectiles to disappear from radar.

#### Symptoms
- Projectile is visible but not on radar
- Projectile appears briefly on radar then disappears
- Inconsistent radar tracking behavior

#### Verification
```csharp
// Add this logging to ProjectileStateBased
bool isActive = jobSystem.IsProjectileActive(projectileIndex);
bool hasHomingFlag = jobSystem.HomingFlags[projectileIndex] == 1;
bool hasTargetFlag = jobSystem.HasTargetFlags[projectileIndex] == 1;

Debug.Log($"[ProjectileStateBased] State Check - ID: {GetInstanceID()}" +
         $"\n  Active: {isActive}" +
         $"\n  Has Homing Flag: {hasHomingFlag}" +
         $"\n  Has Target Flag: {hasTargetFlag}");
```

#### Solution
Ensure proper synchronization at these key points:
1. Initial Setup:
```csharp
jobSystem.UpdateProjectileMovementData(
    projectileIndex,
    transform.position,
    transform.rotation,
    velocity,
    targetPosition,
    enableHoming && target != null,  // Validate both conditions
    rotateSpeed,
    bulletSpeed,
    timeScale,
    lifetime
);
```

2. State Changes:
```csharp
// When changing homing or target
jobSystem.CompleteProjectileUpdate();  // Ensure no pending jobs
// Update state
jobSystem.UpdateProjectileMovementData(...);
jobSystem.CompleteProjectileUpdate();  // Ensure update is processed
```

### 2. Radar Registration Flow
#### Issue
The ProjectileTrackingManager might not correctly evaluate when a projectile should be on radar.

#### Symptoms
- Projectiles with valid targets not appearing on radar
- Inconsistent radar registration between similar projectiles

#### Verification
```csharp
private bool ShouldBeOnRadar(ProjectileTrackingState state)
{
    if (!state.IsActive || !state.HasTarget) 
    {
        Debug.Log($"Projectile {state.Projectile.GetInstanceID()} failed basic radar check:" +
                 $"\n  IsActive: {state.IsActive}" +
                 $"\n  HasTarget: {state.HasTarget}");
        return false;
    }

    if (state.JobSystemIndex >= 0 && jobSystem != null)
    {
        bool isValid = jobSystem.IsProjectileActive(state.JobSystemIndex);
        Debug.Log($"Projectile {state.Projectile.GetInstanceID()} job system check:" +
                 $"\n  IsValid: {isValid}");
        return isValid;
    }

    return true;
}
```

#### Solution
Update radar registration checks to include all necessary conditions:
```csharp
private bool ShouldBeOnRadar(ProjectileTrackingState state)
{
    if (!state.IsActive || !state.HasTarget) return false;

    // Validate job system state if we have a valid index
    if (state.JobSystemIndex >= 0 && jobSystem != null)
    {
        return jobSystem.IsProjectileActive(state.JobSystemIndex) &&
               jobSystem.HomingFlags[state.JobSystemIndex] == 1 &&
               jobSystem.HasTargetFlags[state.JobSystemIndex] == 1;
    }

    return true;
}
```

### 3. Projectile Setup Sequence
#### Issue
Incorrect initialization order or missing state updates during projectile setup.

#### Symptoms
- Projectiles never appear on radar from start
- Radar visibility lost after projectile recycling

#### Verification
Add comprehensive logging during setup:
```csharp
Debug.Log($"[ProjectilePool] Setting up projectile {projectile.GetInstanceID()}:" +
         $"\n  EnableHoming: {request.EnableHoming}" +
         $"\n  Has Target: {request.Target != null}" +
         $"\n  Job System Index: {projectile.projectileIndex}");
```

#### Solution
Ensure correct setup sequence:
1. Initialize basic properties
2. Set target (if any)
3. Enable homing (if required)
4. Update job system
5. Register with tracking manager

```csharp
// In ProjectilePool
projectile.gameObject.SetActive(false);  // Prevent premature updates
projectile.transform.position = request.Position;
projectile.transform.rotation = request.Rotation;

// Set target before enabling homing
if (request.Target != null)
{
    projectile.SetHomingTarget(request.Target);
}

// Initialize with job system
projectile.SetupProjectile(
    request.Damage, 
    request.Speed,
    request.Lifetime,
    request.EnableHoming && request.Target != null,  // Only enable if we have target
    request.Scale,
    request.Target
);

// Verify state before activation
projectile.VerifyRadarState();
projectile.gameObject.SetActive(true);
```

### 4. State Transition Issues
#### Issue
State changes during projectile lifetime not properly propagating to all systems.

#### Symptoms
- Radar visibility lost after certain events (parry, reflection, etc.)
- Inconsistent behavior after target changes

#### Solution
Implement state verification after key transitions:
```csharp
public void OnPlayerRicochetDodge()
{
    // Existing ricochet logic...

    // Verify radar state after significant state change
    VerifyRadarState();
}

public void SetHomingTarget(Transform target)
{
    string targetName = target != null ? target.name : "None";
    currentTarget = target;
    bool isTracking = (target != null && homing);
    
    // Update job system immediately
    var jobSystem = ProjectileManager.Instance?.GetProjectileJobSystem();
    if (jobSystem != null && projectileIndex >= 0)
    {
        jobSystem.CompleteProjectileUpdate();
        jobSystem.UpdateProjectileMovementData(
            projectileIndex,
            transform.position,
            transform.rotation,
            rb.linearVelocity,
            target != null ? target.position : transform.position + transform.forward * 100f,
            isTracking,
            _rotateSpeed,
            bulletSpeed,
            TLine != null ? TLine.timeScale : 1f,
            lifetime
        );
    }

    // Verify state after update
    VerifyRadarState();
}
```

## Debugging Tools

### State Verification Method
Add this method to ProjectileStateBased to help diagnose issues:
```csharp
public void VerifyRadarState()
{
    var jobSystem = ProjectileManager.Instance?.GetProjectileJobSystem();
    if (jobSystem != null && projectileIndex >= 0)
    {
        bool isActive = jobSystem.IsProjectileActive(projectileIndex);
        bool hasHomingFlag = jobSystem.HomingFlags[projectileIndex] == 1;
        bool hasTargetFlag = jobSystem.HasTargetFlags[projectileIndex] == 1;
        
        string radarInfo = radarSymbol != null ? 
            $"Has Radar Icon at {radarSymbol.transform.position}" : 
            "No Radar Icon";
        
        Debug.Log($"[ProjectileStateBased] Radar State Check - ID: {GetInstanceID()}" +
                 $"\nProjectile State:" +
                 $"\n  Active: {gameObject.activeInHierarchy}" +
                 $"\n  Homing: {homing}" +
                 $"\n  Has Target: {currentTarget != null}" +
                 $"\n  Target Position: {(currentTarget != null ? currentTarget.position.ToString() : "None")}" +
                 $"\n  Radar Status: {radarInfo}" +
                 $"\nJob System State:" +
                 $"\n  Index: {projectileIndex}" +
                 $"\n  Is Active: {isActive}" +
                 $"\n  Has Homing Flag: {hasHomingFlag}" +
                 $"\n  Has Target Flag: {hasTargetFlag}");

        // Force update if states are mismatched
        if (gameObject.activeInHierarchy && homing && currentTarget != null &&
            (!isActive || !hasHomingFlag || !hasTargetFlag))
        {
            Debug.LogWarning($"[ProjectileStateBased] Detected state mismatch - forcing job system update");
            jobSystem.UpdateProjectileMovementData(
                projectileIndex,
                transform.position,
                transform.rotation,
                rb.linearVelocity,
                currentTarget.position,
                true,
                _rotateSpeed,
                bulletSpeed,
                TLine != null ? TLine.timeScale : 1f,
                lifetime
            );
        }
    }
}
```

## Best Practices

1. **Always Complete Jobs Before State Changes**
```csharp
jobSystem.CompleteProjectileUpdate();
// Make state changes
jobSystem.CompleteProjectileUpdate();
```

2. **Validate State Transitions**
- Call `VerifyRadarState()` after significant state changes
- Log state mismatches for debugging
- Force synchronization when mismatches are detected

3. **Proper Cleanup**
- Ensure proper deactivation sequence
- Clear all flags when recycling projectiles
- Unregister from all systems in the correct order

4. **Initialization Order**
- Set target before enabling homing
- Update job system before activating GameObject
- Verify state before final activation

## Common Debugging Steps

1. Enable detailed logging for specific projectiles:
```csharp
// Add to ProjectileStateBased
private bool debugMode = false;
public void EnableDebugMode() => debugMode = true;
```

2. Track state transitions:
```csharp
if (debugMode)
{
    Debug.Log($"[ProjectileStateBased] State transition for {GetInstanceID()}:" +
             $"\n  Previous State: {previousState}" +
             $"\n  New State: {newState}");
}
```

3. Monitor job system synchronization:
```csharp
if (debugMode)
{
    Debug.Log($"[ProjectileStateBased] Job system sync for {GetInstanceID()}:" +
             $"\n  Completed previous job: {jobSystem.CurrentJobHandle.IsCompleted}" +
             $"\n  New job scheduled: {hasScheduledJob}");
}
```

4. Verify radar registration:
```csharp
if (debugMode)
{
    var state = ProjectileTrackingManager.Instance.GetProjectileState(GetInstanceID());
    Debug.Log($"[ProjectileStateBased] Radar registration for {GetInstanceID()}:" +
             $"\n  Is Registered: {state?.IsRegistered ?? false}" +
             $"\n  Should Be On Radar: {ProjectileTrackingManager.Instance.ShouldBeOnRadar(state)}");
}
``` 