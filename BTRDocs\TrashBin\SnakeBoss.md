---
title: Snake Boss Enemy
date: 2024-01-16
tags: [enemy, boss, snake, music-sync]
aliases: [<PERSON><PERSON><PERSON>, MidBossSnake]
---

# Snake Boss Enemy

## Overview
The Snake Boss is a mid-boss enemy type that implements advanced combat mechanics including music-synchronized attacks, time manipulation, and limb-based damage system. It serves as an example of complex enemy behavior integration.

## Component Integration
```mermaid
graph TD
    A[EnemyCore] --> B[TimelineController]
    A --> C[MusicSyncedCombat]
    A --> D[ProjectileCombat]
    A --> E[DamageVisualization]
    C --> D
    B --> F[FMOD Audio]
    D --> F
```

## Configuration
```yaml
# Core Settings
startHealth: 100
enemyType: "SnakeMidBoss"

# Combat Settings
projectileDamage: 10
shootSpeed: 20
projectileLifetime: 5
projectileScale: 1

# Music Sync Settings
koreographyEventID: "BossAttack"
projectileTimelineName: "ProjectileTimeline"

# Timeline Settings
defaultTimeScale: 1.0
slowdownTimeScale: 0.5
pitchModulation: 0.25  # 1/4 for 12 semitones down

# Phase Settings
phases:
  - healthThreshold: 75
    attackSpeedMultiplier: 1.2
    damageMultiplier: 1.0
    animationTrigger: "Phase2Start"
  - healthThreshold: 50
    attackSpeedMultiplier: 1.5
    damageMultiplier: 1.2
    animationTrigger: "Phase3Start"
  - healthThreshold: 25
    attackSpeedMultiplier: 2.0
    damageMultiplier: 1.5
    animationTrigger: "Phase4Start"

# Limb Damage Settings
limbDamageMultipliers:
  - limbName: "Head"
    multiplier: 2.0
  - limbName: "Body"
    multiplier: 1.0
  - limbName: "Tail"
    multiplier: 0.5
```

## Required Components
1. [[EnemyCore]]
2. [[TimelineControllerBehavior]]
3. [[MusicSyncedCombatBehavior]]
4. [[ProjectileCombatBehavior]]
5. [[DamageVisualizationBehavior]]
6. [[TargetIndicatorBehavior]]

## Behavior Implementation

### Time Manipulation
The Snake Boss uses the TimelineControllerBehavior to:
- Manipulate time scale during specific attacks
- Adjust audio pitch based on time scale
- Reset time scale on death or phase transition

### Music-Synced Combat
The MusicSyncedCombatBehavior provides:
- Attack timing synchronized with music beats
- Integration with Koreographer events
- Phase-based attack pattern variations

### Phase-Based Mechanics
Each phase introduces:
- Increased attack speed
- Modified damage output
- Unique animation triggers
- Phase-specific sound effects

### Limb Damage System
Implements part-based damage with:
- Configurable damage multipliers per limb
- Visual feedback on hit parts
- Part-specific health tracking
- Progressive part destruction

## Configuration Validation
The SnakeBossConfiguration includes built-in validation:
```csharp
// Health validation
startHealth = Mathf.Max(1f, startHealth);

// Combat settings validation
projectileDamage = Mathf.Max(0f, projectileDamage);
shootSpeed = Mathf.Max(0f, shootSpeed);

// Phase validation
phase.healthThreshold = Mathf.Clamp(phase.healthThreshold, 0f, lastThreshold);
phase.attackSpeedMultiplier = Mathf.Max(0.1f, phase.attackSpeedMultiplier);
```

## Testing Checklist
- [ ] Time manipulation works correctly
  - [ ] Slowdown affects both visuals and audio
  - [ ] Pitch modulation matches time scale
  - [ ] Proper reset on death/disable
- [ ] Music sync functions properly
  - [ ] Attacks align with beats
  - [ ] Proper event registration/unregistration
  - [ ] Phase transitions maintain sync
- [ ] Phase system works
  - [ ] Correct threshold triggers
  - [ ] Proper multiplier application
  - [ ] Animation triggers fire
  - [ ] Audio cues play
- [ ] Limb damage system functions
  - [ ] Multipliers apply correctly
  - [ ] Visual feedback works
  - [ ] Part destruction triggers
  - [ ] Hit counting accurate

## Best Practices
1. **Time Manipulation**
   - Always reset time scale in cleanup
   - Handle pitch modulation carefully
   - Consider performance impact

2. **Music Sync**
   - Register/unregister events properly
   - Handle missing Koreographer gracefully
   - Maintain consistent timing

3. **Phase Management**
   - Validate threshold order
   - Clean up phase-specific effects
   - Handle interrupted phases

4. **Performance**
   - Pool visual effects
   - Cache material properties
   - Optimize time scale checks

## Related Documents
- [[EnemySystemGuide|Enemy System Documentation]]
- [[ProjectileSystem|Projectile System Documentation]]
- [[SystemsDesignAnalysis#Boss Systems|Systems Design Analysis]]
- [[AudioSystem#FMOD Integration|Audio System Documentation]] 