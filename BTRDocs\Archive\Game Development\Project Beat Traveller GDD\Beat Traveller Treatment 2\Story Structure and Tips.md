# Story Structure and Tips

---

**BASICS OF STORYTELLING**
By <PERSON><PERSON> (@MataHaggis)
*Professor of Creative & Entertainment Games*
(Inspired by <PERSON>, <PERSON>, & <PERSON>)

---

**MOTIVATIONS:**

- **EXTERNAL**
    - What does the protagonist want to change in the world around them or in their status/relationships?
        - These often start with verbs such as 'Find...', 'Arrest...', 'Save...', etc.
- **INTERNAL**
    - What does the protagonist need to change inside them to make them a more complete person?
        - These often start with the words 'Overcoming...' or 'Learning...', followed by an emotion or feeling relating to an event.

**Per scene:**

- **OBJECTIVE**: What does the protagonist want to do or expect to learn in this scene?
- **CONFLICT**: What prevents this from happening?
- **OUTCOME**: What happens that sends the protagonist on a new course, or makes their life more difficult?
    - The more times they fail to get exactly what they want, the more challenging their life becomes, and the greater their final achievement will be.

---

**BEGINNING**

- **OPTIONAL: 'Grabber'** - start with tension or high-action.
- Tolerable life before the problem. Make the lead likeable.
- The protagonist tries to live with the problem, hopes it isn't too bad, or thinks that someone else is going to fix it. They often show their internal block which may be resolved through the story. It becomes obvious though that they should not avoid action...
- The protagonist tries to solve the problem. This is their first attempt: it is usually reasonably safe, sensible, and appears to have a good chance of success.
    - For example: running away from a dangerous situation.
- The problem was not fixed, and the situation has become worse. The protagonist realizes that their first plan was flawed, others lose hope in them, and they doubt their ability to fix the problem... Research time!
- Often there will be a division that appears between the protagonist and their companions, triggering ideas.
- It becomes clear that a dangerous idea might work, possibly with self-sacrifice. The group reconciles around their mutual goal, and together they will try a desperate plan to overcome the problem...
- Training/equipping/powering up montage!
- The story is resolved quickly and in a satisfying manner.
- **OPTIONAL: Evil lurks!** The enemy may return, resurrect, or have escaped.

---

**THE END**

---

**Diagrams**:

- 1%: Action/thriller/horror genres. Foreshadows future events and possibilities.
- 0%: 'The Inciting Incident': something goes wrong, that means that the life of the protagonist will need to change.
- < 25%: State the external objective: the protagonist literally says what they are going to do to fix the problem (as they see it).
- < 50%: The low mid-point: the first try at fixing the problem didn't work, often causing (or caused by) a rift in the protagonist's group.
- < 75%: Hope: something is found or happens (internal/external change) that gives the protagonist hope for overcoming the problem... If they risk everything!
- < 95%: 'The Black Moment': all those risks look like they won't pay off. All is lost! Except that one small thing they learned/did along the journey might just help enough... Maybe!
- 100%: The story's climax.
- < 101%: Usually only horror/thriller genres. Foreshadows future events and possibilities.

---

**SCENE DEBUGGING QUESTIONS**

---

---

**1. Is the character/player’s objective clear?**

- In the context of internal/external motivations, why does this scene exist? (Examples: drama, traversal, combat, building atmosphere.)
- If the scene is only filler, it's suggested to remove it to improve quality.

---

**2. Does the outcome meaningfully add to the mechanics, narrative, or both?**

- The outcome should be different than the expected objective, thus provoking change.
- If the outcome aligns perfectly with the character/player's objective, then the narrative or mechanics should be strengthened or recontextualized. This adjustment ensures that the conflict continues until a new, meaningful adaptation is introduced.

---

**3. Is there escalating mechanical or narrative conflict, or is it just repetition?**

- Factors to consider include what stands in the way of the predictable completion of the objective. Examples include character disagreements, branching paths in the storyline, the introduction of new enemies, or environmental storytelling.
- It's important to monitor the pacing at which new elements (mechanics, scenarios, information, characters) are introduced. Consider blending or recombining existing elements to present challenges in fresh ways.

---

**4. Over the course of the level or the game, was there a change from start to finish, for the character, player, or both?**

- Evaluate the alterations in how gameplay might proceed after this point. Questions to ponder:
    - Is the player/character smarter, different, damaged, or better equipped than before?
    - Ensure the progression feels meaningful, whether the motivations are internal, external, or a mix of both.

---

**Tips:**

- A character can still face setbacks even if the player is succeeding. Recognizing that sometimes the "right" choices can still lead to conflict can make the narrative more engaging.
- Internal changes (e.g., character development) often have a deeper impact than external changes (e.g., acquiring a new weapon).
- Strategically spacing out high-tension moments with breaks can enhance the player's engagement.

---

I hope this helps! Let me know if you need any further assistance.