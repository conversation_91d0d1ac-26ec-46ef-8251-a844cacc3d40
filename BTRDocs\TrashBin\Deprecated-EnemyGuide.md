---
title: Enemy System Guide
systems: [enemy]
components: [ai, spawning, pathfinding]
priority: p1
date: 2024-01-15
last_touched: 2025-04-09
tags:
  - #system/enemy
  - #status/active
  - #priority/p1
  - enemies
  - systems
  - gameplay
  - unity
aliases: [EnemyGuide, EnemySystem]
links:
  - [[2024-design-mechanics]]
  - [[tech-recs-pathfinding]]
  - [[SystemsDesignAnalysis#Implementation Strategy]]
---

# Enemy System Documentation

## Overview
This document outlines the enemy system architecture, comparing it with the old system and providing guidelines for creating new enemies. The system integrates with [[ProjectileSystem|Projectile System]] for combat and [[ManagementSystem#Event System|Event System]] for state management.

## System Integration Map
```mermaid
graph TD
    A[Enemy System] --> B[Projectile System]
    A --> C[Event System]
    A --> D[Time Management]
    E[Player System] --> A
    F[Pool System] --> A
```

## Related Documents
- [[SystemsDesignAnalysis#Implementation Strategy|Systems Design Analysis]]
- [[ProjectileSystem|Projectile System Documentation]]
- [[PlayerSystem|Player System Documentation]]
- [[ManagementSystem|Management Systems Documentation]]
- [[Changelog]]

## Project Structure
The enemy system is organized into the following directories:
- `Core/` - Contains core system components like [[EnemyCore]]
- `Behaviors/` - Contains behavior components like [[PathfindingMovementBehavior]], [[ProjectileCombatBehavior]], and [[ExplosionCombatBehavior]]
- `Configurations/` - Contains ScriptableObject configurations
- `Interfaces/` - Contains system interfaces
- `Utilities/` - Contains utility classes for common functionality

## Core Components

### EnemyCleanupCoordinator
✅ **Features**:
- Standardized three-phase cleanup process
  1. Immediate Cleanup: Stops behaviors and clears states
  2. Deferred Cleanup: Handles animations and effects
  3. Final Cleanup: Resource cleanup and object disposal
- Priority-based cleanup ordering
- Pooling system integration
- Error handling and logging
- Automatic cleanup handler collection
- Safe cleanup sequence coordination
- Exception handling for each cleanup phase
- Debug logging support

### EnemyCore
✅ **Properly Replicated Features**:
- Health management and damage handling
- Part-based damage system
- Event system (OnDamage, OnDeath, OnReset)
- Timeline/Clock integration
- Box collider requirement
- Player targeting and line of sight
- Effect spawning system
- Pooling support
- Debug logging
- Enemy model instantiation
- Clock key configuration
- Projectile registration
- Part damage visualization
- Effect pool validation
- Robust player finding system
- Part hit tracking and destruction
- Lock-on indicator system

### PathfindingMovementBehavior
✅ **Properly Replicated Features**:
- A* pathfinding integration
- Movement settings configuration
- Target following
- Position evaluation
- Line of sight checks
- Enemy spacing
- Path optimization
- Path request throttling
- Dead enemy cleanup
- Position attempt caching
- Enemy distance caching
- Configurable position weights
- Combat state awareness integration

### ProjectileCombatBehavior
✅ **Properly Replicated Features**:
- Attack cooldown system
- Projectile spawning via [[ProjectileManager]]
- Combat state management and awareness
- Audio integration with FMOD
- Line of sight checks
- Projectile configuration with separate damage and speed settings
- Protected damage calculation system
- Configurable base damage values
- State-specific damage multipliers
- Proper damage initialization
- Homing projectile support with improved accuracy
- Initial engagement delay system
- Combat state visualization
- Proper cleanup and reset handling
- Debug logging support
- Efficient state transitions
- Target tracking and validation
- Distance-based accuracy adjustments
- Guaranteed hit mechanics at close range

### ExplosionCombatBehavior
✅ **Features**:
- Proximity-based explosion triggering
- Configurable explosion radius and damage
- Visual pulse feedback when near player
- Material emission effects
- Particle system integration
- FMOD audio integration with IsNull checks
- Effect spawning via [[EffectPoolValidator]]
- Debug visualization
- Combat state management
- Proper cleanup and reset handling
- Self-destruction after explosion
- Damage scaling with distance

## Enemy Types and Complexity Levels

### Simple Enemies
Some enemy types don't require the full EnemyCore functionality. These are implemented as standalone components with focused functionality:

1. **Static Shooter**
   - Simple, stationary shooter
   - No health/damage system needed
   - No movement behavior
   - Managed by EnemyManager for group shooting
   - Uses ScriptableObject configuration
   - See [[StaticShooter]] for implementation details

### Standard Enemies
These enemies use the full EnemyCore implementation with standard behaviors:

1. **Basic Enemy**
   - Full health and damage system
   - Movement and combat behaviors
   - See [[BasicEnemy]] for implementation details

### Complex Enemies
These enemies extend EnemyCore with additional systems:

1. **Phased Enemy (Boss Type)**
   - Multiple combat phases
   - Complex behavior patterns
   - See [[PhasedEnemy]] for implementation details

2. **Exploding Enemy**
   - Kamikaze-style behavior
   - Special damage mechanics
   - See [[ExplosionCombatBehavior]] for implementation details

## Creating a New Enemy

### 1. Determine Complexity Level
Before creating a new enemy, determine which level of complexity it requires:

1. **Simple Enemy** (Like StaticShooter)
   - Focused, single-purpose enemies
   - No need for health/damage
   - No movement requirements
   - Use standalone components

2. **Standard Enemy**
   - Requires health/damage system
   - Needs movement and combat
   - Use full EnemyCore setup

3. **Complex Enemy**
   - Multiple behaviors/states
   - Special mechanics
   - Extend EnemyCore with additional systems

### 2. Required Components
Based on complexity level, choose appropriate components:

**Simple Enemy:**
- Main behavior component
- Configuration ScriptableObject

**Standard/Complex Enemy:**
- [[EnemyCore]] component
- `Timeline` component
- `BoxCollider` component
- Movement behavior
- Combat behavior
- Target indicator
- Damage visualization

### 3. Configuration
1. Create a new `EnemyConfiguration` ScriptableObject:
   - Set enemy type and pool name
   - Configure health and vulnerability
   - Set movement settings
   - Configure combat settings (projectile or explosion)
   - Set effect names and prefabs
   - Configure projectile settings (if using ProjectileCombatBehavior):
     - Speed, lifetime, scale, damage
     - Enable/disable homing
     - Set projectile accuracy (0-1)
   - Configure explosion settings (if using ExplosionCombatBehavior)
   - Set audio configuration

2. Set up required references:
   - Assign the configuration to EnemyCore
   - Set up the enemy model
   - Configure damageable parts if needed
   - Set up effect pools

### 4. Testing Checklist
- [ ] Enemy spawns correctly with effects
- [ ] Movement and pathfinding work
- [ ] Combat behavior functions properly
- [ ] Damage system works (both taking and dealing)
- [ ] Death and cleanup handle correctly
- [ ] Target indicator shows/hides properly
- [ ] Audio plays correctly
- [ ] Pooling works if enabled
- [ ] Parts system works correctly
- [ ] Effects spawn properly
- [ ] Line of sight works correctly
- [ ] Damage visualization works
- [ ] Homing projectiles work correctly
- [ ] Lock-on indicator works
- [ ] Part destruction works correctly

## Best Practices

### Component Organization
- Keep all enemy scripts in the EnemySystem folder structure
- Use appropriate namespaces (BTR.EnemySystem)
- Follow component naming conventions
- Use interfaces for behavior contracts

### Configuration
- Use ScriptableObjects for enemy configurations
- Don't hardcode values in components
- Keep effects in appropriate pools
- Configure debug settings per component

### Behavior Extension
- Implement relevant interfaces (IMovementBehavior, ICombatBehavior)
- Override base behavior methods as needed
- Use events for communication between components
- Follow the component pattern

### Performance
- Use object pooling for frequently spawned objects
- Implement proper cleanup timing
- Cache component references
- Use appropriate update intervals
- Implement distance and line of sight caching
- Cache validation results
- Validate component states before cleanup
- Handle cleanup gracefully during editor stop

### Memory Management
- Use object pooling for projectiles and effects
- Clean up resources in OnDisable for component lifecycle
- Implement proper event unsubscription
- Handle component cleanup before destruction
- Separate GameObject cleanup to OnDestroy
- Use safe cleanup patterns with proper validation
- Cache component references
- Implement proper cleanup order in component hierarchy

### State Management
- Use IStateHandler for explicit state transitions
- Implement state-specific behaviors
- Provide visual feedback for state changes
- Handle state cleanup properly

### Effect System
- Use IEffectSpawner for effect management
- Validate effect pools before use
- Clean up effects when not needed
- Use optimized effect spawning

## Special Enemy Types

### Static Shooter Enemy
The static shooter is a simple but effective enemy type designed for fixed-position combat. It integrates with the group shooting system for coordinated attacks.

1. **Core Components**:
   - [[StaticShooterCore]] for base functionality
   - [[StaticShootingBehavior]] for combat
   - [[DamageVisualizationBehavior]] for feedback
   - [[TargetIndicatorBehavior]] for targeting

2. **Key Features**:
   - Group-coordinated shooting
   - Configurable projectile properties
   - Safety cooldown system
   - No movement requirements

See [[StaticShooter]] for detailed implementation guide.

### Phased Enemy (Boss Type)
The phased enemy is a complex enemy type designed for boss encounters, featuring multiple combat phases and limb-based damage mechanics.

1. **Core Components**:
   - [[EnemyPhaseCore]] for phase management
   - [[LimbDamageBehavior]] for part-based damage
   - [[ProjectileCombatBehavior]] for attacks
   - [[VisualEffectBehavior]] for feedback

2. **Key Features**:
   - Multiple combat phases with health thresholds
   - Phase-specific vulnerable points
   - Configurable attack patterns per phase
   - Visual indicators for vulnerable parts
   - Transition effects between phases
   - Persistent limb damage

See [[PhasedEnemy]] for detailed implementation guide.

### Exploding Enemy
The exploding enemy is a kamikaze-style enemy that rushes toward the player and explodes when in close proximity.

1. **Configuration**:
   - Set `enemyType` to "Exploder"
   - Configure `explosionRadius`, `explosionDamage`, and `explosionTriggerDistance`

See [[ExplosionCombatBehavior]] for implementation details.

## Integration Points

### ChildActivator Integration
The ChildActivator component provides group management for StaticShooter enemies:
- Handles batch activation/deactivation
- Ensures proper initialization timing
- Manages shooting state
- Provides debug feedback

Usage:
1. Add ChildActivator to parent object
2. Place StaticShooter objects as children
3. Use SetChildrenActive to control group state
4. Automatic handling of shooting enablement

Best Practices:
1. Group related static shooters under one activator
2. Use for pattern-based shooting sections
3. Leverage for level streaming optimization
4. Consider activation timing with music events

### Effect System 