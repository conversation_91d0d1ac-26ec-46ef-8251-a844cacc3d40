# Scene Unload Timeout Fix - July 27, 2025

## Problem Summary

Scene transitions were hanging indefinitely during normal gameplay, but worked fine when using the EnemyKiller debug tool to clear enemies before transitioning. The issue manifested as:

- Scene unload operations timing out completely
- `[SceneManagerBTR] Scene unload timed out completely, forcing continue with new scene load`
- Game continuing to work but with potential memory leaks from unloaded scenes

## Root Cause Analysis

The problem was **not** the enemies themselves, but the **cascade of cleanup events** triggered during scene unload:

1. **Normal Enemy Cleanup**: During scene transition, enemies were being cleaned up through normal Unity lifecycle (`OnDisable`, `SetActive(false)`)

2. **SensorToolkit Cascade**: This triggered `OnTriggerExit` events in SensorToolkit components, which then called `removeCollider` operations

3. **ChildActivator Cascade**: `ChildActivator.SetChildrenActive(false)` was being called, which triggered more deactivation events

4. **Timing Issue**: This cascade of cleanup operations was happening **during** the Unity scene unload process, preventing `UnloadSceneAsync` from completing

5. **EnemyKiller Difference**: EnemyKiller worked because it destroyed enemies **before** any scene transition began, avoiding the cascade entirely

## Evidence from Logs

```
[EnemyManager] Unregistered static shooter: Snake Eyeball (0). Total count: 99
...
BTR.StaticShooter:OnDisable () 
UnityEngine.GameObject:SetActive (bool)
ChildActivator:SetChildrenActive (bool)
UnityEngine.Events.UnityEvent`2<UnityEngine.GameObject, SensorToolkit.Sensor>:Invoke
SensorToolkit.TriggerSensor:removeCollider
SensorToolkit.TriggerSensor:OnTriggerExit
```

The stack trace clearly shows the cascade: `SetActive(false)` → `ChildActivator` → `SensorToolkit.TriggerSensor` → `OnTriggerExit` → `removeCollider`

## Solution Implemented

Modified `CleanupEnemiesForSceneTransition()` in `SceneManagerBTR.cs` to:

1. **Disable SensorToolkit First**: Find and disable all `SensorToolkit.Sensor` components before any enemy cleanup
2. **Disable ChildActivator Components**: Prevent cascade deactivation by disabling `ChildActivator` components
3. **Aggressive Component Disabling**: Disable all `MonoBehaviour` components on enemies before destruction
4. **Use DestroyImmediate**: Bypass normal Unity lifecycle by using `DestroyImmediate()` instead of `Die()` or `SetActive(false)`

## Code Changes

### Added Using Statement
```csharp
using SensorToolkit;
```

### Updated CleanupEnemiesForSceneTransition Method
```csharp
// First, disable all SensorToolkit components to prevent cascade cleanup during scene unload
var allSensors = FindObjectsByType<SensorToolkit.Sensor>(FindObjectsSortMode.None);
foreach (var sensor in allSensors)
{
    if (sensor != null)
    {
        sensor.enabled = false;
    }
}

// Disable all ChildActivator components to prevent cascade deactivation
var childActivators = FindObjectsByType<ChildActivator>(FindObjectsSortMode.None);
foreach (var activator in childActivators)
{
    if (activator != null)
    {
        activator.enabled = false;
    }
}

// For each enemy: disable all components first, then DestroyImmediate
foreach (var enemy in enemies)
{
    if (enemy != null)
    {
        // Disable all components first to prevent cleanup cascades
        var components = enemy.GetComponentsInChildren<MonoBehaviour>();
        foreach (var comp in components)
        {
            if (comp != null) comp.enabled = false;
        }
        // Immediately destroy without going through normal death process
        DestroyImmediate(enemy.gameObject);
    }
}
```

## Key Insights

1. **Timing Matters**: The difference between working and broken wasn't what was cleaned up, but **when** it was cleaned up
2. **Unity Event Cascades**: Complex component interactions can block Unity's internal scene management
3. **Aggressive Cleanup**: Sometimes you need to bypass normal Unity lifecycle to prevent interference
4. **Debug Tools Reveal Truth**: EnemyKiller working was the key clue that enemies weren't the problem - the cleanup process was

## Testing

This fix should resolve the scene unload timeouts by preventing the cascade of events that was blocking Unity's scene unload operation. The scene transitions should now complete successfully without timing out.

## Files Modified

- `Assets/_Scripts/Management/SceneManagerBTR.cs`
  - Added `using SensorToolkit;`
  - Updated `CleanupEnemiesForSceneTransition()` method