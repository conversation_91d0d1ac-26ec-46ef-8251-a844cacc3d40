Audio & Music
March 28th 2020

Koreographer shoot / lock on mechanisms

April 3

Having Lock and Shoot working, need to refine sounds and clean up

April 4th

Trying out different timings for music

Added Kick vibration Script to Koreo Object

April 9

Think about Koreographer / DOTween intergrations?

April 10th

Shoot time already based on Koreographer Tempo

Created GlobalControls.SceneBPM for referencing Tempo

April 12

Introduced Audio Mixing Group

Ducking Noise track when Locking/Shooting

April 14

Looking into offset rhythm experiment with Rival Consoles track as example

Koreogrpaher Pro Ideal for this?

Use F mod instead of Unity Tools? Need to see Koreographer integration

April 17th

Koreogrpaher Pro - Midi implementation

Ableton2Midi - MidiEditor for tempo - Import

DoTween Movement script - Koreo Square Movement

Object moves in square pattern in time with track

April 18th

Want to time particle releases to beat more

Need to identify how I want mixer to work - proper submixes etc

Need naming scheme on Koreographers - G7 - etc - similar to submixes

April 29

Looking at Chronos/Koreographer integration now. Can hit rewind button on beat, but cannot return to normal time on beat of koreographer timeline

June 16

Need more thorough implementation to keep in time

July 9

Synchronic film - music - drug is the needle on the record idea

Jan 13

Contact Robbie - Level Curve? - about FMOD and other things- talk to <PERSON> about this!

Jan 27th

Add bullet particle effect with kick track?

Audio Cue for every time particle emits

Jan 28

Add bullet particle effect with kick track?

Analyze the entire file, we will be extracting any notes related to the following categories, and saving them into their own files, with the dates of information intact.

Jan 29

Bullets caught in circling loops when missing enemy target

Add bullet particle effect with kick track?

Seems to be Koreographer related issue. Is it a timing thing?