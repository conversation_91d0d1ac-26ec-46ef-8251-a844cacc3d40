# March 22

Moving json song structure for fmod into a scriptable obect

should be better for editing - will need to address this per scene

Checking how audio transitions from one scene to another 

It sort of works? But seems to be playing two sections are the same time 

Maybe related to how enemy wave spawner is defining things on new scene load? 

Need to adjus this / make its own musical section / ensure it transitions properly

Privating event calls in Adjust Song Paramters because if the object is persistent across scenes, this is easier way to handle it. 

Lots of work done and now scene changes work! FMOD persists across them

Is there a more elegant way to handle the static koreos in one scene where they’re laid out, versus another where they are static and disabled? I think so, base around how it’s been handled in Ourboros Infinite Track, will likely think of a way 

Not totally working i think… need to address this. Issues in build too

Need to remove enemy basics class, just keep enemy basics setup?

Debugging the lack of movement of A* enemies when loading into a scene

Movement in these fields when when enemy movement working

![Untitled](March%2022%20b8f1455c63a842ea9374d147e0ee14a8/Untitled.png)

And this is how the behaviour designer looks during movement

![Untitled](March%2022%20b8f1455c63a842ea9374d147e0ee14a8/Untitled%201.png)

But the one that isnt moving looks like this

![Untitled](March%2022%20b8f1455c63a842ea9374d147e0ee14a8/Untitled%202.png)

THinks its complete? Likely the issue

Interesting thing is that moving into the scene, the enemies dont move

but if i use R to reload that scene, the enemies move just fine. 

There’s a difference in these operations 

The behaviour operates like it should, not showing complete. 

[BD Tactical Shoot and Scoot - succeeds and stops running?](https://www.opsive.com/forum/index.php?threads/bd-tactical-shoot-and-scoot-succeeds-and-stops-running.9739/)

This was the solution! I solved this once before!!!

Removing Default Behavior game object, as its not really doing anything. It was just finding the player but the enemies can do that themselves, its not a big deal

Important note on behavior designer performance spikes

Pooling the trees may be a way to avoid the spikes

[External Behavior Trees - Opsive](https://opsive.com/support/documentation/behavior-designer/external-behavior-trees/)

Fixed score issues when changing between scenes, maintains score properly now. 

Death of midboss snake now has trasition ot next scene (talks to game manager) in its death

Not sure if i should be using beautify 3, is it helpful? need to assess. may be a burden

SIDENOTE

I need to consolidate these input chekcs

```jsx
private void HandleRewindTime()
{
    if (Time.timeScale != 0f && CheckRewind())
    {
        StartCoroutine(RewindToBeat());
    }
    if (Time.timeScale != 0f && SlowTime())
    {
        StartCoroutine(SlowToBeat());
    }
    if (CheckRewind() && rewindTriggedStillPressed)
    {
        rewindTriggedStillPressed = false;
    }
}
```