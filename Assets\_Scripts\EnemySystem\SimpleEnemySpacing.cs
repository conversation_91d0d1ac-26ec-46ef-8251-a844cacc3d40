using UnityEngine;
using Pathfinding;
using System.Collections.Generic;

namespace BTR.EnemySystem
{
    /// <summary>
    /// Simple, direct enemy spacing solution that modifies A* pathfinding destinations
    /// to prevent clustering. Attaches directly to enemy prefabs.
    /// </summary>
    [RequireComponent(typeof(FollowerEntity))]
    public class SimpleEnemySpacing : MonoBehaviour
    {
        [Header("Spacing Settings")]
        [SerializeField] private float minimumSpacing = 3f;
        [SerializeField] private float spacingCheckRadius = 6f;
        [SerializeField] private float spacingForce = 2f;
        [SerializeField] private LayerMask enemyLayerMask = -1;

        [Header("Debug")]
        [SerializeField] private bool showDebugGizmos = true;
        [SerializeField] private bool enableDebugLogs = false;

        private FollowerEntity followerEntity;
        private Vector3 originalDestination;
        private Vector3 lastCheckedPosition;
        private float lastSpacingCheck;
        private const float SPACING_CHECK_INTERVAL = 0.1f;

        private void Start()
        {
            followerEntity = GetComponent<FollowerEntity>();
            if (followerEntity == null)
            {
                Debug.LogError($"SimpleEnemySpacing requires FollowerEntity component on {gameObject.name}");
                enabled = false;
                return;
            }

            lastSpacingCheck = Time.time;
            lastCheckedPosition = transform.position;
        }

        private void Update()
        {
            if (followerEntity == null) return;

            // Only check spacing periodically for performance
            if (Time.time - lastSpacingCheck >= SPACING_CHECK_INTERVAL)
            {
                ApplySpacingToDestination();
                lastSpacingCheck = Time.time;
            }
        }

        private void ApplySpacingToDestination()
        {
            // Get current destination from FollowerEntity
            Vector3 currentDestination = followerEntity.destination;

            // Find nearby enemies
            Collider[] nearbyEnemies = Physics.OverlapSphere(transform.position, spacingCheckRadius, enemyLayerMask);

            Vector3 spacingOffset = Vector3.zero;
            int nearbyCount = 0;

            foreach (var enemyCollider in nearbyEnemies)
            {
                if (enemyCollider.transform == transform) continue; // Skip self

                Vector3 toOther = transform.position - enemyCollider.transform.position;
                float distance = toOther.magnitude;

                if (distance < minimumSpacing && distance > 0.1f)
                {
                    // Calculate repulsion force
                    Vector3 repulsion = toOther.normalized * (minimumSpacing - distance) * spacingForce;
                    spacingOffset += repulsion;
                    nearbyCount++;

                    if (enableDebugLogs)
                    {
                        Debug.Log($"[SimpleEnemySpacing] {gameObject.name} too close to {enemyCollider.name}, distance: {distance:F2}");
                    }
                }
            }

            // Apply spacing offset to destination
            if (spacingOffset.magnitude > 0.1f)
            {
                Vector3 adjustedDestination = currentDestination + spacingOffset;

                // Clamp the offset to prevent extreme movements
                Vector3 offsetFromOriginal = adjustedDestination - currentDestination;
                if (offsetFromOriginal.magnitude > spacingForce * 2f)
                {
                    offsetFromOriginal = offsetFromOriginal.normalized * spacingForce * 2f;
                    adjustedDestination = currentDestination + offsetFromOriginal;
                }

                // Apply the adjusted destination
                followerEntity.destination = adjustedDestination;

                if (enableDebugLogs)
                {
                    Debug.Log($"[SimpleEnemySpacing] Adjusted destination for {gameObject.name}: {currentDestination} -> {adjustedDestination}");
                }
            }
        }

        /// <summary>
        /// Force immediate spacing check (called by external systems)
        /// </summary>
        public void ForceSpacingCheck()
        {
            ApplySpacingToDestination();
        }

        private void OnDrawGizmos()
        {
            if (!showDebugGizmos) return;

            // Draw spacing radius
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(transform.position, minimumSpacing);

            // Draw check radius
            Gizmos.color = Color.cyan;
            Gizmos.DrawWireSphere(transform.position, spacingCheckRadius);

            // Draw nearby enemies
            if (Application.isPlaying)
            {
                Collider[] nearbyEnemies = Physics.OverlapSphere(transform.position, spacingCheckRadius, enemyLayerMask);
                Gizmos.color = Color.red;

                foreach (var enemy in nearbyEnemies)
                {
                    if (enemy.transform == transform) continue;

                    float distance = Vector3.Distance(transform.position, enemy.transform.position);
                    if (distance < minimumSpacing)
                    {
                        Gizmos.DrawLine(transform.position, enemy.transform.position);
                    }
                }
            }
        }
    }
}