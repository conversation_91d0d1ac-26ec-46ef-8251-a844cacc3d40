# Scene Management System - Complete Reference

_Created: 2025-01-27_
_Status: Current - Core System Reference_
_System Location: `Assets/_Scripts/Management/SceneManagerBTR.cs`_

## Overview

The **BTR Scene Management System** is a sophisticated, music-synchronized scene progression framework that combines **additive scene loading**, **wave-based progression**, **musical section synchronization**, and **spline-based visual progression**. This system is the core mechanism that drives level progression and scene transitions throughout the game.

## System Architecture

```mermaid
flowchart TB
    %% Core Scene Management
    SMBTR[SceneManagerBTR<br/>Central Scene Controller]
    SG[SceneGroup<br/>Level Configuration]
    SLD[SceneListData<br/>Scene Definitions]
    SS[SongSection<br/>Musical Progression]

    %% Event System
    ES[Event System<br/>Communication Hub]
    SE[SceneEvents<br/>Scene Lifecycle]
    WEC[WaveEventChannel<br/>Wave Coordination]
    GE[GameEvents<br/>Game State]

    %% Wave System Integration
    WS[Wave System<br/>Progression Logic]
    WSC[WaveSpawnController<br/>Enemy Spawning]
    WM[WaveManager<br/>Wave Coordination]

    %% Audio Integration
    AI[Audio Integration<br/>Music Synchronization]
    AM[AudioManager<br/>FMOD Integration]
    SC[Stylo.Cadance<br/>Beat Synchronization]

    %% Visual Progression
    VP[Visual Progression<br/>Track Movement]
    SM[SplineManager<br/>Track Control]
    GVM[GlobalVolumeManager<br/>Visual Effects]

    %% Loading System
    LS[Loading System<br/>Scene Transitions]
    LSI[LoadingScreen<br/>UI Feedback]
    MO[Memory Optimization<br/>Resource Management]
    ER[Error Recovery<br/>Retry Logic]

    %% Performance Monitoring
    PM[Performance Monitoring<br/>System Health]
    PT[Performance Tracking<br/>Metrics Collection]
    PO[Performance Optimization<br/>Dynamic Scaling]

    %% Core Relationships
    SMBTR --> SG
    SG --> SLD
    SG --> SS

    %% Event Integration
    SMBTR --> ES
    ES --> SE
    ES --> WEC
    ES --> GE

    %% Wave System Integration
    SMBTR --> WS
    WS --> WSC
    WS --> WM

    %% Audio Integration
    SMBTR --> AI
    AI --> AM
    AI --> SC

    %% Visual Integration
    SMBTR --> VP
    VP --> SM
    VP --> GVM

    %% Loading Integration
    SMBTR --> LS
    LS --> LSI
    LS --> MO
    LS --> ER

    %% Performance Integration
    SMBTR --> PM
    PM --> PT
    PM --> PO

    %% Styling
    classDef core fill:#f9f,stroke:#333,stroke-width:2px
    classDef events fill:#bbf,stroke:#333,stroke-width:2px
    classDef wave fill:#bfb,stroke:#333,stroke-width:2px
    classDef audio fill:#fbb,stroke:#333,stroke-width:2px
    classDef visual fill:#ffb,stroke:#333,stroke-width:2px
    classDef loading fill:#fbf,stroke:#333,stroke-width:2px
    classDef performance fill:#bff,stroke:#333,stroke-width:2px

    class SMBTR,SG,SLD,SS core
    class ES,SE,WEC,GE events
    class WS,WSC,WM wave
    class AI,AM,SC audio
    class VP,SM,GVM visual
    class LS,LSI,MO,ER loading
    class PM,PT,PO performance
```

## Core Components

### **SceneManagerBTR** (Central Scene Controller)

- **Role**: Master coordinator for all scene transitions and progression
- **Pattern**: Singleton with async scene management and performance monitoring
- **Location**: `Assets/_Scripts/Management/SceneManagerBTR.cs`

**Key Features**:

- Additive scene loading with base scene persistence
- Music-synchronized scene progression
- Wave-based section transitions
- Spline-based visual progression
- Performance monitoring and optimization
- Error recovery with retry logic
- Memory management and cleanup

### **SceneGroup Configuration System**

```csharp
[System.Serializable]
public class SceneGroup
{
    [Tooltip("Human-readable name for this scene group")]
    public string name;

    [Tooltip("Scenes in this group, in order")]
    public SceneListData[] scenes;

    [Tooltip("Musical sections for this group")]
    public SongSection[] songSections;

    [Tooltip("Total expected waves across all scenes")]
    public int totalExpectedWaves;

    [Tooltip("Performance parameters for this group")]
    public ScenePerformanceConfig performanceConfig;
}

[System.Serializable]
public class SceneListData
{
    [Tooltip("Name of the scene to load")]
    public string sceneName;

    [Tooltip("Musical sections within this scene")]
    public SongSection[] songSections;

    [Tooltip("Expected total waves for this scene")]
    public int expectedWaves;

    [Tooltip("Minimum time to spend in this scene")]
    public float minSceneTime = 0f;
}

[System.Serializable]
public class SongSection
{
    [Tooltip("Human-readable name for this section")]
    public string name;

    [Tooltip("Musical section timing (0.0 = start, 1.0 = end)")]
    public float section;

    [Tooltip("Expected number of waves in this section")]
    public int waves;

    [Tooltip("Minimum time to spend in this section")]
    public float minSectionTime = 0f;

    [Tooltip("Visual effects intensity for this section")]
    public float intensityMultiplier = 1f;
}
```

## Scene Transition Triggers

### **1. Wave-Based Progression**

The primary scene transition mechanism is driven by wave completion:

```csharp
private void HandleWaveCustomEvent(string eventType, int waveNumber)
{
    switch (eventType.ToLower())
    {
        case "switch scene":
            // Direct scene transition trigger
            _ = ChangeSceneWithTransitionToNextAsync().AttachExternalCancellation(_destroyToken.Token);
            break;

        case "wavestart":
            // Handle section transitions within scenes
            if (isFirstUpdate)
            {
                isFirstUpdate = false;
                expectedWaves = CurrentSection.waves;
                UpdateMusicSection();
                waveEventChannel.TriggerSectionStarted(CurrentSection.name, expectedWaves);

                // Move to next section if this is a 0-wave transition section
                if (expectedWaves == 0 && currentSectionIndex < CurrentScene.songSections.Length - 1)
                {
                    MoveToNextSection();
                }
            }
            break;

        case "waveend":
            // Progress through sections and scenes
            if (expectedWaves > 0)
            {
                completedWaves++;
                if (completedWaves >= expectedWaves)
                {
                    waveEventChannel.TriggerSectionCompleted();
                    _ = MoveToNextSectionOrSceneAsync().AttachExternalCancellation(_destroyToken.Token);
                }
            }
            break;
    }
}
```

### **2. Spline-Based Progression**

Visual track progression triggers scene changes:

```csharp
private void HandleFinalSplineReached()
{
    // Prevent multiple simultaneous transitions
    if (isTransitioning || Time.time - lastSceneChangeTime < SCENE_CHANGE_COOLDOWN)
    {
        return;
    }

    try
    {
        _ = ChangeSceneWithTransitionToNextAsync().AttachExternalCancellation(_destroyToken.Token);
    }
    catch (Exception e)
    {
        Debug.LogError($"Error handling final spline: {e.Message}");
    }
}
```

### **3. EnemyKiller Debug Trigger**

The EnemyKiller system can trigger scene changes by completing all waves instantly:

```csharp
// EnemyKiller.cs triggers mass enemy death
public void KillAllEnemies()
{
    var enemies = FindObjectsByType<EnemyCore>(FindObjectsSortMode.None);
    foreach (var enemy in enemies)
    {
        if (enemy != null)
        {
            enemy.Die(); // Triggers wave completion events
        }
    }
}

// This leads to wave completion events which trigger scene progression
```

## Scene Loading Process

### **Additive Scene Loading Architecture**

```csharp
public async UniTask LoadAdditiveSceneAsync(string sceneName, IProgress<float> progressReporter = null, CancellationToken cancellationToken = default)
{
    try
    {
        using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);

        // Trigger scene load started event
        sceneEvents?.TriggerSceneLoadStarted(sceneName);

        // Unload current additive scene if exists
        if (currentAdditiveScene.IsValid() && currentAdditiveScene.isLoaded)
        {
            var unloadOp = SceneManager.UnloadSceneAsync(currentAdditiveScene);
            await unloadOp.ToUniTask(cancellationToken: linkedCts.Token);
        }

        // Memory optimization before loading
        await OptimizeMemoryBeforeLoad(linkedCts.Token);

        // Load new scene additively
        var loadOp = SceneManager.LoadSceneAsync(sceneName, LoadSceneMode.Additive);

        // Progress reporting
        while (!loadOp.isDone)
        {
            progressReporter?.Report(loadOp.progress);
            await UniTask.Yield(PlayerLoopTiming.Update, linkedCts.Token);
        }

        // Update current scene reference
        currentAdditiveScene = SceneManager.GetSceneByName(sceneName);

        // Trigger scene load completed event
        sceneEvents?.TriggerSceneLoadCompleted(sceneName);

        // Performance validation
        ValidateSceneLoadPerformance();
    }
    catch (Exception e)
    {
        await HandleSceneLoadError(e, sceneName);
    }
}
```

### **Scene Transition with Loading Screen**

```csharp
public async UniTask ChangeSceneWithTransitionToNextAsync(CancellationToken cancellationToken = default)
{
    if (isTransitioning) return;

    try
    {
        isTransitioning = true;
        lastSceneChangeTime = Time.time;

        using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);

        // Show loading screen if enabled
        if (useLoadingScreen && LoadingScreen.Instance != null)
        {
            await LoadingScreen.Instance.StartFadeIn();
        }

        // Determine next scene
        string nextSceneName = GetNextSceneName();

        // Load next scene with progress reporting
        var progress = new Progress<float>(p =>
        {
            LoadingScreen.Instance?.UpdateProgress(p);
        });

        await LoadAdditiveSceneAsync(nextSceneName, progress, linkedCts.Token);

        // Update scene indices
        currentSceneIndex = GetNextSceneIndex();
        currentSectionIndex = 0;
        completedWaves = 0;
        isFirstUpdate = true;

        // Hide loading screen
        if (useLoadingScreen && LoadingScreen.Instance != null)
        {
            await LoadingScreen.Instance.StartFadeOut();
        }
    }
    finally
    {
        isTransitioning = false;
    }
}
```

## Music Synchronization

### **Musical Section Management**

```csharp
private void UpdateMusicSection()
{
    try
    {
        if (AudioManager.Instance == null) return;

        var currentScene = currentGroup.scenes[currentSceneIndex];
        var currentSection = currentScene.songSections[currentSectionIndex];

        // Update audio manager with new section
        AudioManager.Instance.ChangeSongSection(currentGroup, currentSceneIndex, currentSection.section);
        AudioManager.Instance.ApplyMusicChanges(currentGroup, currentSceneIndex, currentSection.section);

        // Update debug display
        currentSongSectionDisplay = $"Scene {currentSceneIndex} - {currentSection.name} (Value: {currentSection.section})";
    }
    catch (Exception e)
    {
        Debug.LogError($"Error updating music section: {e.Message}");
    }
}
```

### **Section Progression Logic**

```csharp
private void MoveToNextSection()
{
    try
    {
        // Validate current state
        if (currentGroup == null || currentSceneIndex >= currentGroup.scenes.Length)
        {
            Debug.LogError($"Invalid scene index {currentSceneIndex}");
            return;
        }

        var currentScene = currentGroup.scenes[currentSceneIndex];
        if (currentSectionIndex >= currentScene.songSections.Length - 1)
        {
            Debug.Log($"Already at last section, cannot move further");
            return;
        }

        // Progress to next section
        var oldSection = currentScene.songSections[currentSectionIndex];
        currentSectionIndex++;
        completedWaves = 0;

        var newSection = currentScene.songSections[currentSectionIndex];
        expectedWaves = newSection.waves;

        // Update music only if section value changed
        if (Math.Abs(oldSection.section - newSection.section) > 0.01f)
        {
            UpdateMusicSection();
        }

        // Trigger section events
        waveEventChannel.TriggerSectionStarted(newSection.name, expectedWaves);

        // Update spline progression
        if (splineManager != null)
        {
            splineManager.IncrementSpline();

            // Force verification for transition sections
            if (newSection.waves == 0)
            {
                splineManager.ForceVerifyAndReset();
            }
        }
    }
    catch (Exception e)
    {
        Debug.LogError($"Error moving to next section: {e.Message}");
    }
}
```

## Performance Monitoring and Optimization

### **Performance Metrics Tracking**

```csharp
[System.Serializable]
public struct ScenePerformanceMetrics
{
    /// <summary>Time taken to load the scene in seconds</summary>
    public float LoadTime;
    /// <summary>Total time including transitions</summary>
    public float TransitionTime;
    /// <summary>Memory usage before loading in MB</summary>
    public int MemoryUsageBefore;
    /// <summary>Memory usage after loading in MB</summary>
    public int MemoryUsageAfter;
    /// <summary>Impact on frame time in seconds</summary>
    public float FrameTimeImpact;
}

private readonly Dictionary<string, ScenePerformanceMetrics> _performanceMetrics = new();
```

### **Memory Optimization**

```csharp
private async UniTask OptimizeMemoryBeforeLoad(CancellationToken cancellationToken = default)
{
    try
    {
        var memoryBefore = GC.GetTotalMemory(false) / 1024 / 1024;

        // Force garbage collection if configured
        if (memoryConfig.AggressiveGarbageCollection)
        {
            GC.Collect();
            GC.WaitForPendingFinalizers();
        }

        // Unload unused assets
        await Resources.UnloadUnusedAssets().ToUniTask(cancellationToken: cancellationToken);

        // Optimize audio memory
        AudioManager.Instance?.OptimizeMemoryUsage();

        var memoryAfter = GC.GetTotalMemory(false) / 1024 / 1024;
        LogOperation($"Memory optimization: Freed {memoryBefore - memoryAfter}MB", logCategory);
    }
    catch (Exception e)
    {
        Debug.LogError($"Error during memory optimization: {e.Message}");
    }
}
```

### **Error Recovery System**

```csharp
private async UniTask<bool> TryLoadSceneWithRetry(string sceneName, IProgress<SceneLoadProgress> progress = null, CancellationToken cancellationToken = default)
{
    float currentDelay = retryConfig.RetryDelay;

    for (int attempt = 0; attempt < retryConfig.MaxRetries; attempt++)
    {
        try
        {
            if (attempt > 0)
            {
                progress?.Report(SceneLoadProgress.Create(
                    sceneName,
                    0f,
                    0f,
                    $"Retrying load ({attempt + 1}/{retryConfig.MaxRetries})"
                ));

                await UniTask.Delay(TimeSpan.FromSeconds(currentDelay), cancellationToken: cancellationToken);

                if (retryConfig.EnableProgressiveDelay)
                {
                    currentDelay *= retryConfig.ProgressiveDelayMultiplier;
                }
            }

            await LoadSceneAsync(sceneName, cancellationToken);
            return true;
        }
        catch (OperationCanceledException)
        {
            throw; // Propagate cancellation
        }
        catch (Exception e) when (attempt < retryConfig.MaxRetries - 1)
        {
            Debug.LogWarning($"Load attempt {attempt + 1}/{retryConfig.MaxRetries} failed for {sceneName}: {e.Message}");
        }
    }

    return false;
}
```

## Visual Progression Integration

### **Spline Manager Integration**

```csharp
private void InitializeSplineManagerAsync()
{
    splineManager = FindFirstObjectByType<SplineManager>();
    if (splineManager != null)
    {
        splineManager.OnFinalSplineReached += HandleFinalSplineReached;
        splineManager.InitializeSplineSystem();
    }
}

// Spline progression tied to section changes
private void UpdateSplineProgression()
{
    if (splineManager != null)
    {
        // Increment spline when moving to new section
        splineManager.IncrementSpline();

        // Force verification for transition sections
        if (CurrentSection.waves == 0)
        {
            splineManager.ForceVerifyAndReset();
        }
    }
}
```

### **Global Volume Effects**

```csharp
private void OnSceneLoaded(Scene scene, LoadSceneMode mode)
{
    // Apply visual effects transitions
    GlobalVolumeManager.Instance.TransitionEffectIn(0.5f);
    GlobalVolumeManager.Instance.TransitionEffectOut(0.5f);

    // Initialize scene-specific visual settings
    ApplySceneVisualSettings(scene);
}
```

## Event System Integration

### **Scene Events**

```csharp
// Scene lifecycle events
sceneEvents.OnSceneLoadRequested += LoadScene;
sceneEvents.OnSceneLoadStarted += HandleSceneLoadStarted;
sceneEvents.OnSceneLoadCompleted += HandleSceneLoadCompleted;

// Wave system events
waveEventChannel.OnWaveStarted += HandleWaveStarted;
waveEventChannel.OnWaveCompleted += HandleWaveCompleted;
waveEventChannel.OnWaveCustomEvent += HandleWaveCustomEvent;
```

### **Cross-System Communication**

```csharp
public class SceneTransitionCoordinator : MonoBehaviour
{
    private void OnSceneTransitionStarted(string sceneName)
    {
        // Notify all systems of impending transition
        PlayerLocking.Instance?.ReleasePlayerLocks();
        ProjectileManager.Instance?.ClearAllProjectiles();
        EnemyManager.Instance?.PrepareForSceneTransition();
        AudioManager.Instance?.PrepareForSceneTransition();
    }

    private void OnSceneTransitionCompleted(string sceneName)
    {
        // Initialize systems for new scene
        GameManager.Instance?.InitializeListenersAndComponents();
        SplineManager.Instance?.InitializeSplineSystem();
        WaveManager.Instance?.ResetWaveState();
    }
}
```

## Configuration and Usage

### **Scene Group Setup**

```csharp
[SerializeField] private SceneGroup ouroborosGroup = new SceneGroup
{
    name = "Ouroboros Campaign",
    scenes = new SceneListData[]
    {
        new SceneListData
        {
            sceneName = "Ouroboros_Section_1",
            songSections = new SongSection[]
            {
                new SongSection { name = "Intro", section = 0.0f, waves = 4, minSectionTime = 10f },
                new SongSection { name = "Transition", section = 0.2f, waves = 0, minSectionTime = 2f },
                new SongSection { name = "Build", section = 0.25f, waves = 6, minSectionTime = 15f }
            },
            expectedWaves = 10
        },
        new SceneListData
        {
            sceneName = "Ouroboros_Section_2",
            songSections = new SongSection[]
            {
                new SongSection { name = "Climax", section = 0.5f, waves = 8, minSectionTime = 20f },
                new SongSection { name = "Resolution", section = 0.75f, waves = 6, minSectionTime = 12f }
            },
            expectedWaves = 14
        }
    },
    totalExpectedWaves = 24
};
```

### **Manual Scene Transitions**

```csharp
// Force transition to specific scene (for debugging)
public void ForceTransitionToScene(string sceneName)
{
    if (string.IsNullOrEmpty(sceneName)) return;

    // Find scene index in current group
    int targetSceneIndex = -1;
    if (currentGroup != null)
    {
        for (int i = 0; i < currentGroup.scenes.Length; i++)
        {
            if (currentGroup.scenes[i].sceneName == sceneName)
            {
                targetSceneIndex = i;
                break;
            }
        }
    }

    if (targetSceneIndex >= 0)
    {
        currentSceneIndex = targetSceneIndex;
        currentSectionIndex = 0;
        completedWaves = 0;
        expectedWaves = currentGroup.scenes[targetSceneIndex].songSections[0].waves;
        isFirstUpdate = true;

        _ = LoadAdditiveSceneAsync(sceneName).AttachExternalCancellation(_destroyToken.Token);
    }
}
```

## Debugging and Monitoring

### **Debug Information Display**

```csharp
// Inspector debug fields
[SerializeField] private string currentSongSectionDisplay; // Shows current section info
[SerializeField] private bool enableDebugLogs = false;
[SerializeField] private bool showSceneLoadLogs = false;
[SerializeField] private bool showSectionChangeLogs = false;
[SerializeField] private bool showWaveSystemLogs = false;

// Public properties for debugging
public int CurrentSceneIndex => currentSceneIndex;
public int CurrentSectionIndex => currentSectionIndex;
public int CompletedWaves => completedWaves;
public int ExpectedWaves => expectedWaves;
public bool IsTransitioning => isTransitioning;
```

### **Operation Logging**

```csharp
private void LogOperation(string operation, string category)
{
    if (_operationLog.Count >= MAX_LOG_ENTRIES)
    {
        _operationLog.Dequeue();
    }

    _operationLog.Enqueue($"[{Time.time:F2}] [{category}] {operation}");

    if (enableDebugLogs)
    {
        Debug.Log($"[{GetType().Name}] [{category}] {operation}");
    }
}

private void LogCurrentState()
{
    if (enableDebugLogs)
    {
        Debug.Log($"<color=cyan>[SCENE] Current State - Scene: {currentSceneIndex}, Section: {currentSectionIndex}, Wave: {currentWaveCount}</color>");
    }
}
```

## How EnemyKiller Triggers Scene Changes

### **The Complete Chain of Events**

1. **EnemyKiller.TriggerKillAll()** is called (via input or debug button)

2. **Mass Enemy Death**: All enemies are killed instantly via `enemy.Die()`

3. **Wave Completion Events**: Enemy deaths trigger wave completion through the event system

4. **Section Progression**: Completed waves cause section progression via `HandleWaveCustomEvent("waveend")`

5. **Scene Transition**: When all sections in a scene are complete, `MoveToNextSectionOrSceneAsync()` is called

6. **Additive Scene Loading**: New scene is loaded while maintaining base scene

7. **Music Synchronization**: Audio transitions to match new scene's musical section

8. **Visual Progression**: Spline system updates to reflect new scene position

### **Why It "Always Works"**

- **Immediate Effect**: Kills all enemies instantly, completing all active waves
- **Event-Driven**: Uses the same event system as normal gameplay
- **State Consistency**: Maintains proper scene indices and wave counts
- **Error Recovery**: Built-in retry logic handles any loading failures
- **Performance Optimization**: Memory cleanup ensures smooth transitions

## Best Practices

### **Scene Design**

- **Musical Alignment**: Design scenes around musical structure and timing
- **Progressive Difficulty**: Gradually increase challenge through sections
- **Performance Budgets**: Monitor memory and frame time impact
- **Transition Smoothness**: Use 0-wave sections for smooth musical transitions

### **Performance Optimization**

- **Memory Management**: Clean up resources between scene transitions
- **Async Loading**: Use non-blocking scene loading with progress feedback
- **Error Handling**: Implement robust retry logic for failed operations
- **Monitoring**: Track performance metrics and optimize based on data

### **Integration**

- **Event-Driven Communication**: Use event channels for system coordination
- **Configuration-Driven**: Use ScriptableObjects for easy content iteration
- **Modular Design**: Keep systems loosely coupled for maintainability
- **Debug Support**: Provide comprehensive debugging and monitoring tools

## Related Systems

- **[[Wave System - Level Progression]]** - Wave-based progression logic
- **[[FMOD Advanced Audio Integration]]** - Music synchronization system
- **[[Stylo.Cadance - Music Synchronization Framework]]** - Beat-accurate timing
- **[[Performance Monitoring System]]** - System health tracking
- **[[Loading Screen System]]** - User feedback during transitions

## Notes

The **BTR Scene Management System** represents a **sophisticated approach to level progression** that seamlessly integrates music, visuals, and gameplay into a cohesive experience. Its strength lies in the **tight coupling between musical structure and scene progression**, creating a unique rhythm-action experience where the music truly drives the gameplay flow.

The system's **additive scene loading architecture** combined with **comprehensive performance monitoring** ensures smooth transitions while maintaining optimal performance across all target platforms. The **robust error recovery system** and **extensive debugging capabilities** make it reliable for production use.

This documentation serves as the **complete reference** for understanding how scene changes work in the BTR project, from the high-level architecture down to the specific implementation details that make the EnemyKiller debug tool so effective at triggering scene transitions.
