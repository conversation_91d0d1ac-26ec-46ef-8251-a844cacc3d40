# Wave Transition System Documentation

## Overview
This document outlines the complete flow of wave transitions in BTR, from wave events to scene transitions. It covers all relevant components, their interactions, and the sequence of method calls.

## Key Components

### 1. Scene<PERSON>ana<PERSON>BTR
Primary coordinator for scene and wave management.
- Tracks current scene, section, and wave progress
- Handles scene loading/unloading with fade transitions
- Manages the Scene Group configuration
- Coordinates transitions between sections and scenes
- Handles wave events through HandleWaveCustomEvent method
- Manages music section changes via AudioManager
- Controls scene transition VFX via GlobalVolumeManager

### 2. Wave Event System
Handles wave-related events and coordinates between systems.
- Subscribes to wave events from WaveSpawnController
- Manages wave spawn controller events
- Coordinates with <PERSON>ManagerBTR for transitions
- Handles wave start/end notifications
- Manages wave custom events
- Handles enemy spawning and registration
- Triggers VFX events on wave completion

### 3. WaveEventChannel
Event bus for wave-related communications.
- Provides events for wave start/end
- Handles section start/end events
- Manages custom wave events
- Coordinates between WaveEventSubscriptions and SceneManagerBTR
- Provides enemy spawn event notifications

## Wave Transition Flow

### 1. Wave Start Sequence
```mermaid
sequenceDiagram
    WaveSpawnController->>WaveEventChannel: OnWaveStarted
    WaveEventChannel->>SceneManagerBTR: HandleWaveCustomEvent("wavestart")
    SceneManagerBTR->>AudioManager: UpdateMusicSection [if first wave]
    SceneManagerBTR->>WaveEventChannel: TriggerSectionStarted
    Note over SceneManagerBTR: Updates expected waves count
    SceneManagerBTR-->>SplineManager: IncrementSpline [if available]
```

### 2. Wave End Sequence
```mermaid
sequenceDiagram
    WaveSpawnController->>WaveEventChannel: OnWaveCompleted
    WaveEventChannel->>SceneManagerBTR: HandleWaveCustomEvent("waveend")
    SceneManagerBTR->>SceneManagerBTR: Check wave completion
    alt All Waves Complete
        SceneManagerBTR->>WaveEventChannel: TriggerSectionCompleted
        SceneManagerBTR->>SceneManagerBTR: MoveToNextSectionOrScene
    end
```

### 3. Section Management
SceneManagerBTR's section management flow:
1. Tracks expected waves and completed waves for current section
2. On first wave start in a section:
   - Sets expected waves from section configuration
   - Updates music section via AudioManager only if section value changes
   - Triggers section started event
   - Updates currentSongSectionDisplay for debugging
3. On wave completion:
   - Increments completed waves counter
   - If all waves completed, moves to next section
4. For zero-wave sections (transitions):
   - Automatically advances on next wave start if not the last section
   - Double-checks section bounds
   - Updates music only if section value changes
   - Updates spline position

### 4. Scene Transition Triggers
Scene transitions can be initiated by:
1. "Switch Scene" wave custom event
2. Reaching final spline position
3. Completing all sections in current scene

## Music Section Management

### 1. Section Value Changes
```csharp
private void MoveToNextSection()
{
    var oldSection = currentScene.songSections[currentSectionIndex];
    currentSectionIndex++;
    var newSection = currentScene.songSections[currentSectionIndex];
    
    // Only update music if section value actually changed
    if (Math.Abs(oldSection.section - newSection.section) > 0.01f)
    {
        UpdateMusicSection();
    }
}
```

### 2. Music Update Flow
```mermaid
sequenceDiagram
    SceneManagerBTR->>SceneManagerBTR: Check section value change
    alt Section Value Changed
        SceneManagerBTR->>AudioManager: ChangeSongSection
        SceneManagerBTR->>AudioManager: ApplyMusicChanges
        SceneManagerBTR->>SceneManagerBTR: Update debug display
    end
```

## Wave Event Handling

### 1. Event Types
- "wavestart": Initial wave setup and section initialization
- "waveend": Wave completion and section progression
- "switch scene": Scene transition trigger
- Custom events: Additional wave-specific behaviors

### 2. Event Flow
```csharp
private void HandleWaveCustomEvent(string eventType, int waveNumber)
{
    switch (eventType.ToLower())
    {
        case "switch scene":
            ChangeSceneWithTransitionToNextAsync();
            break;
        case "wavestart":
            HandleWaveStart();
            break;
        case "waveend":
            HandleWaveEnd();
            break;
    }
}
```

## Critical States

### 1. Transition Flags
- `isTransitioning`: Prevents multiple simultaneous transitions
- `isFirstUpdate`: Tracks initial wave in new section
- `_isLoadingScene`: Monitors scene loading state
- `isHandlingSceneLoad`: Prevents duplicate scene load handling

### 2. Progress Tracking
- `expectedWaves`: Total waves in current section
- `completedWaves`: Waves finished in current section
- `currentWaveCount`: Total waves processed
- `currentSectionIndex`: Current section in scene
- `currentSceneIndex`: Current scene in group

### 3. Cooldowns and Timings
- `SCENE_CHANGE_COOLDOWN`: 1 second cooldown between scene changes (reduced from 5s for faster transitions)
- VFX transition duration: 1.5 seconds for both in/out effects

## Debugging Tips

### Key Debug Logs
Monitor these log patterns:
1. `[SCENE] Handling wave event: {eventType} in section {name}`
2. `[SCENE] Moving from section {oldName} to {newName}`
3. `[SCENE] Section value changed, updating music`
4. `[SCENE] Wave completed in section {name} ({completed}/{expected})`
5. `[SCENE] Received Switch Scene event, initiating scene transition`

### Common Issues
1. Multiple transition attempts
   - Check `isTransitioning` flag
   - Monitor transition state logs
   - Note: 1-second cooldown prevents rapid transitions while maintaining responsiveness
2. Missed wave events
   - Check event case sensitivity
   - Verify event propagation chain
3. Music not updating
   - Check section value differences
   - Verify AudioManager initialization
4. Scene switching issues
   - Monitor "Switch Scene" event handling
   - Check transition state flags
   - Verify cooldown has elapsed (1-second window)

## Scene Management System

### 1. Scene Architecture
- **Base Scene**: Persistent scene containing core managers and systems
- **Additive Scenes**: Game content scenes loaded additively
- **Scene Groups**: Configuration of scenes and their sections/waves
```csharp
public class SceneGroup
{
    public SceneData[] scenes;  // Array of scenes in the group
}

public class SceneData
{
    public string sceneName;
    public SongSection[] songSections;  // Sections within the scene
}
```

### 2. Scene Loading Types
1. **Initial Load**
   ```csharp
   public async Task InitializeScenes()
   {
       await LoadBaseSceneAsync();  // Load persistent base scene
       bool hasExistingScenes = await TryUseOpenOuroborosSceneAsync();
       
       if (!hasExistingScenes)
       {
           await LoadFirstAdditiveSceneAsync();  // Load first game scene
       }
   }
   ```

2. **Additive Scene Loading**
   ```csharp
   public async Task LoadAdditiveSceneAsync(string sceneName)
   {
       // 1. Start transition
       sceneEvents.TriggerSceneLoadStarted(sceneName);
       
       // 2. Unload current scene if exists
       if (currentAdditiveScene.IsValid() && currentAdditiveScene.isLoaded)
       {
           await SceneManager.UnloadSceneAsync(currentAdditiveScene);
       }
       
       // 3. Load new scene
       AsyncOperation asyncLoad = SceneManager.LoadSceneAsync(
           sceneName,
           LoadSceneMode.Additive
       );
       
       // 4. Monitor progress
       while (!asyncLoad.isDone)
       {
           gameEvents.TriggerSceneLoadProgress(asyncLoad.progress);
           await Task.Yield();
       }
       
       // 5. Setup new scene
       currentAdditiveScene = SceneManager.GetSceneByName(sceneName);
       SceneManager.SetActiveScene(currentAdditiveScene);
       
       // 6. Complete transition
       sceneEvents.TriggerSceneLoadCompleted(sceneName);
   }
   ```

### 3. Scene Transition Process
1. **Pre-Transition Checks**
   - Cooldown verification (5 second minimum between transitions)
   - Transition state check
   - Scene group validation

2. **Transition Sequence**
   ```mermaid
   sequenceDiagram
       SceneManagerBTR->>GameManager: ClearAllPlayerLocks()
       SceneManagerBTR->>LoadingScreen: StartFadeIn()
       SceneManagerBTR->>SceneManager: UnloadSceneAsync(currentScene)
       SceneManagerBTR->>SceneManager: LoadSceneAsync(nextScene)
       SceneManagerBTR->>AudioManager: ApplyMusicChanges()
       SceneManagerBTR->>SplineManager: IncrementSpline()
       SceneManagerBTR->>GlobalVolumeManager: TransitionEffectIn(1.5s)
       SceneManagerBTR->>GlobalVolumeManager: TransitionEffectOut(1.5s)
       SceneManagerBTR->>LoadingScreen: StartFadeOut()
   ```

3. **State Management**
   - Scene indices and references
   - Section tracking
   - Wave counters
   - Music section state
   - Loading flags

4. **Error Handling**
   ```csharp
   try
   {
       // Scene transition logic
   }
   catch (Exception e)
   {
       Debug.LogError($"[SCENE] Error during scene transition: {e.Message}");
       isTransitioning = false;
       throw;
   }
   ```

### 4. Scene Transition Triggers
1. **Wave-Based Triggers**
   - "Switch Scene" custom event
   - Completing all sections in current scene
   - Reaching final spline position

2. **Manual Triggers**
   - Debug scene changes
   - Game restart
   - Menu transitions

### 5. Scene Loading States
```csharp
public class SceneLoadingState
{
    public bool isTransitioning;        // Active transition
    public bool _isLoadingScene;        // Scene load in progress
    public bool isHandlingSceneLoad;    // Load event handling
    public float lastSceneChangeTime;   // Cooldown tracking
    public Scene currentAdditiveScene;  // Current game scene
    public Scene baseScene;             // Persistent scene
}
```

### 6. Integration Points
1. **Scene Events**
   - SceneLoadStarted
   - SceneLoadProgress
   - SceneLoadCompleted
   - SceneTransition

2. **Manager Coordination**
   - AudioManager: Music section updates
   - GlobalVolumeManager: Visual transitions
   - GameManager: Player state management
   - SplineManager: Path progression

3. **Loading Screen**
   - Fade transitions
   - Progress updates
   - State indication

## Event Handling

### 1. Wave Custom Events
```mermaid
sequenceDiagram
    WaveEventNode->>WaveSpawnController: OnWaveCustomEvent
    WaveSpawnController->>WaveEventSubscriptions: HandleWaveCustomEvent()
    WaveEventSubscriptions->>WaveEventChannel: TriggerWaveCustomEvent()
    WaveEventChannel->>SceneManagerBTR: HandleWaveCustomEvent()
    SceneManagerBTR->>SceneManagerBTR: updateStatus()
```

### 2. Scene Transition Events
```mermaid
sequenceDiagram
    SceneManagerBTR->>SceneEvents: TriggerSceneLoadStarted()
    SceneManagerBTR->>GameEvents: TriggerSceneTransition()
    SceneManagerBTR->>GameEvents: TriggerSceneLoadProgress()
    SceneManagerBTR->>SceneEvents: TriggerSceneLoadCompleted()
    SceneManagerBTR->>GlobalVolumeManager: TransitionEffectIn()
    SceneManagerBTR->>GlobalVolumeManager: TransitionEffectOut()
```

## Integration Points

### 1. Manager Communications
- WaveEventSubscriptions → SceneManagerBTR
- SceneManagerBTR → LoadingScreen
- WaveSpawnController → WaveEventSubscriptions
- SceneManagerBTR → GlobalVolumeManager
- SceneManagerBTR → AudioManager
- WaveEventSubscriptions → BackgroundFX

### 2. Event Systems
- WaveEventChannel
- SceneEvents
- GameEvents
- VFX Events

### 3. State Tracking
- Wave progress
- Section progress
- Scene transitions
- Loading states
- Music sections
- Background effects
- VFX transitions 