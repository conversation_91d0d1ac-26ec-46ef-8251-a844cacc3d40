using UnityEngine;
using Pathfinding;
using BTR.EnemySystem;
using System.Collections.Generic;

namespace BTR.EnemySystem
{
    /// <summary>
    /// Basic chase movement strategy that makes AI decisions for pursuing the player.
    /// Uses FollowerEntity for actual A* pathfinding implementation.
    /// </summary>
    [RequireComponent(typeof(FollowerEntity))]
    public class BasicChaseMovementStrategy : MovementStrategy
    {
        // Removed ZLogger - using Unity Debug.Log instead

        [Header("Chase Behavior Settings")]
        [ReadOnly][SerializeField] private float minimumDistance = 3f; // Overridden by configuration (minPlayerDistance)
        [ReadOnly][SerializeField] private float maximumChaseDistance = 15f; // Overridden by configuration (maxPlayerDistance)
        [SerializeField] private float repositionThreshold = 2f;

        [Header("Behavior Timing")]
        [SerializeField] private float decisionUpdateInterval = 0.5f;
        [SerializeField] private float stuckCheckInterval = 1f;
        [SerializeField] private float stuckThreshold = 0.5f;

        [Header("Advanced Behavior")]
        [SerializeField] private bool enableCircling = false;
        [SerializeField] private float _circlingRadius = 6f;
        [SerializeField] private float circlingSpeed = 2f;

        [Header("Centralized Pathfinding")]
        [SerializeField] private bool useCentralizedPathfinding = false; // Disabled by default during migration
        [SerializeField] private float pathRequestCooldown = 0.2f;

        [Header("Enemy Separation")]
        [SerializeField] private bool enableSeparation = true;
        [SerializeField] private float separationWeight = 0.3f;
        [SerializeField] private float separationCheckRadius = 4f;
        [SerializeField] private bool adaptivePositioning = true;

        [Header("Continuous Movement")]
        [SerializeField] private bool forceContinuousMovement = true;
        [SerializeField] private float idleTimeThreshold = 2f; // Max time to stay idle
        [SerializeField] private float minimumMovementDistance = 1f; // Min distance to move when repositioning
        [SerializeField] private float restlessnessFactor = 0.5f; // How often to add small movements

        [Header("Mandatory Spacing")]
        [SerializeField] private bool enableMandatorySpacing = true;
        [SerializeField] private float mandatoryMinimumDistance = 2f; // Hard minimum distance constraint
        [SerializeField] private bool validateAllTargets = true; // Validate all target positions

        [Header("Performance Optimization")]
        [SerializeField] private bool useOptimizedDistanceChecks = true;
        [SerializeField] private float distanceCheckInterval = 0.2f;

        [Header("Configuration")]
        [SerializeField] private EnemyConfiguration configuration;

        // Component references
        private CombatStrategy combatStrategy;

        // A* Pathfinding integration
        private FollowerEntity followerEntity;

        // Decision making state
        private float lastDecisionTime;
        private float lastStuckCheckTime;

        private MovementDecision currentDecision = MovementDecision.Chase;
        private Vector3 targetOffset = Vector3.zero;
        private float circlingAngle = 0f;

        // Centralized pathfinding state
        private float lastPathRequest;
        private bool hasPendingPathRequest;
        private List<Vector3> currentPath;

        // Performance optimization state
        private float lastDistanceCheck;

        private bool distanceCacheValid;

        // Continuous movement state
        private float lastMovementTime;
        private float lastRestlessMovement;
        private Vector3 lastKnownPosition;

        // Movement decisions
        public enum MovementDecision
        {
            Chase,      // Move toward target
            Maintain,   // Stay at current distance
            Retreat,    // Move away from target
            Circle,     // Circle around target
            Reposition  // Find better position
        }

        protected override void PerformMovementInitialization()
        {
            // Apply configuration settings if available
            ApplyConfigurationSettings();

            // Get FollowerEntity component
            followerEntity = GetComponent<FollowerEntity>();

            // Get combat strategy for attack range reference
            combatStrategy = GetComponent<CombatStrategy>();
            if (followerEntity == null)
            {
                Debug.LogError($"[{nameof(BasicChaseMovementStrategy)}] No FollowerEntity found on {gameObject.name}");
                return;
            }

            // Initialize decision making
            lastDecisionTime = 0f;
            lastStuckCheckTime = 0f;
            // lastPosition is handled by parent class
            currentDecision = MovementDecision.Chase;
            lastMovementTime = Time.time;
            lastRestlessMovement = Time.time;
            lastKnownPosition = entityTransform.position;

            // Register with separation service
            if (enableSeparation && EnemySeparationService.Instance != null)
            {
                EnemySeparationService.Instance.RegisterEnemy(entityTransform);
            }

            // Register with mandatory spacing enforcer
            if (enableMandatorySpacing && MandatorySpacingEnforcer.Instance != null)
            {
                MandatorySpacingEnforcer.Instance.RegisterEnemy(entityTransform);
            }

#if UNITY_EDITOR || DEVELOPMENT_BUILD
            if (enableDebugLogs)
            {
                Debug.Log($"[{nameof(BasicChaseMovementStrategy)}] Initialized - Min distance: {minimumDistance}, Max distance: {maximumChaseDistance}, Separation: {enableSeparation}");
            }
#endif
        }

        protected override void PerformMovementActivation()
        {
            // Reset decision state
            lastDecisionTime = 0f;
            currentDecision = MovementDecision.Chase;

#if UNITY_EDITOR || DEVELOPMENT_BUILD
            if (enableDebugLogs)
            {
                Debug.Log($"[{nameof(BasicChaseMovementStrategy)}] Movement strategy activated");
            }
#endif
        }

        protected override void PerformMovementDeactivation()
        {
            // Stop movement
            if (followerEntity != null)
            {
                followerEntity.isStopped = true;
            }

#if UNITY_EDITOR || DEVELOPMENT_BUILD
            if (enableDebugLogs)
            {
                Debug.Log($"[{nameof(BasicChaseMovementStrategy)}] Movement strategy deactivated");
            }
#endif
        }

        protected override void PerformMovementCleanup()
        {
            // Unregister from separation service
            if (enableSeparation && EnemySeparationService.Instance != null)
            {
                EnemySeparationService.Instance.UnregisterEnemy(entityTransform);
            }

            // Unregister from mandatory spacing enforcer
            if (enableMandatorySpacing && MandatorySpacingEnforcer.Instance != null)
            {
                MandatorySpacingEnforcer.Instance.UnregisterEnemy(entityTransform);
            }

            // Clear references to prevent serialization issues
            followerEntity = null;
            currentTarget = null;

            // Reset state
            currentDecision = MovementDecision.Chase;
            lastDecisionTime = 0f;
            lastStuckCheckTime = 0f;

#if UNITY_EDITOR || DEVELOPMENT_BUILD
            if (enableDebugLogs)
            {
                Debug.Log($"[{nameof(BasicChaseMovementStrategy)}] Movement strategy cleaned up");
            }
#endif
        }

        protected override void PerformMovementUpdate()
        {
            if (followerEntity == null || currentTarget == null)
                return;

            float currentTime = Time.time;

            // Check for continuous movement requirements
            if (forceContinuousMovement)
            {
                CheckContinuousMovement(currentTime);
            }

            // Update movement decisions periodically
            if (currentTime - lastDecisionTime >= decisionUpdateInterval)
            {
                lastDecisionTime = currentTime;
                UpdateMovementDecision();
            }

            // Check if we're stuck
            if (currentTime - lastStuckCheckTime >= stuckCheckInterval)
            {
                lastStuckCheckTime = currentTime;
                CheckIfStuck();
            }

            // Execute current decision
            ExecuteMovementDecision();
        }

        private float circlingRadius
        {
            get
            {
                // Get optimal distance from combat strategy attack range
                if (combatStrategy != null)
                {
                    return combatStrategy.AttackRange * 0.8f; // Position at 80% of attack range
                }
                return _circlingRadius; // Fallback to serialized value
            }
            set
            {
                _circlingRadius = value;
            }
        }

        private void UpdateMovementDecision()
        {
            if (currentTarget == null)
                return;

            float distanceToTarget = GetOptimizedDistanceToTarget();
            MovementDecision newDecision = currentDecision;

            // Decide based on distance to target
            if (distanceToTarget < minimumDistance)
            {
                newDecision = MovementDecision.Retreat;
            }
            else if (distanceToTarget > maximumChaseDistance)
            {
                newDecision = MovementDecision.Chase;
            }
            else if (Mathf.Abs(distanceToTarget - GetOptimalAttackDistance()) <= repositionThreshold)
            {
                // Prefer circling over maintaining to keep movement
                if (enableCircling)
                {
                    newDecision = MovementDecision.Circle;
                }
                else if (forceContinuousMovement)
                {
                    // Use repositioning instead of maintain to keep enemies moving
                    newDecision = MovementDecision.Reposition;
                }
                else
                {
                    newDecision = MovementDecision.Maintain;
                }
            }
            else if (distanceToTarget > GetOptimalAttackDistance())
            {
                newDecision = MovementDecision.Chase;
            }
            else
            {
                newDecision = MovementDecision.Retreat;
            }

            // Update decision if changed
            if (newDecision != currentDecision)
            {
                currentDecision = newDecision;

#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs)
                {
                    Debug.Log($"[{nameof(BasicChaseMovementStrategy)}] Decision changed to: {currentDecision} (Distance: {distanceToTarget:F1})");
                }
#endif
            }
        }

        private void ExecuteMovementDecision()
        {
            if (currentTarget == null)
                return;

            Vector3 targetPosition = CalculateTargetPosition();

            // Use centralized pathfinding if available and enabled
            if (useCentralizedPathfinding && ShouldRequestCentralizedPath())
            {
                RequestCentralizedPath(targetPosition);
            }
            else if (followerEntity != null)
            {
                // Fallback to individual FollowerEntity pathfinding
                UseIndividualPathfinding(targetPosition);
            }

            // Update base class destination tracking
            SetDestination(targetPosition);
        }

        private bool ShouldRequestCentralizedPath()
        {
            if (EnemyManager.Instance == null || !EnemyManager.Instance.IsCentralizedPathfindingAvailable())
                return false;

            if (hasPendingPathRequest)
                return false;

            return Time.time - lastPathRequest >= pathRequestCooldown;
        }

        private void RequestCentralizedPath(Vector3 targetPosition)
        {
            var entity = GetComponent<IEntity>();
            if (entity != null && EnemyManager.Instance != null)
            {
                hasPendingPathRequest = true;
                lastPathRequest = Time.time;

                EnemyManager.Instance.RequestCentralizedPath(entity, targetPosition, OnCentralizedPathReceived);

#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs)
                {
                    Debug.Log($"[{nameof(BasicChaseMovementStrategy)}] Requested centralized path to {targetPosition}");
                }
#endif
            }
        }

        private void OnCentralizedPathReceived(List<Vector3> path)
        {
            hasPendingPathRequest = false;
            currentPath = path;

            if (path != null && path.Count > 0 && followerEntity != null)
            {
                // Use the path with FollowerEntity
                followerEntity.destination = path[path.Count - 1];
                followerEntity.isStopped = false;

#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs)
                {
                    Debug.Log($"[{nameof(BasicChaseMovementStrategy)}] Received centralized path with {path.Count} points");
                }
#endif
            }
        }

        private void UseIndividualPathfinding(Vector3 targetPosition)
        {
            if (followerEntity == null)
                return;

            // Set destination using FollowerEntity
            followerEntity.destination = targetPosition;
            followerEntity.isStopped = false;

#if UNITY_EDITOR || DEVELOPMENT_BUILD
            if (enableDebugLogs)
            {
                Debug.Log($"[{nameof(BasicChaseMovementStrategy)}] Using individual pathfinding to {targetPosition}");
            }
#endif
        }

        private Vector3 CalculateTargetPosition()
        {
            Vector3 targetPos = currentTarget.position;
            Vector3 directionToTarget = (targetPos - entityTransform.position).normalized;
            Vector3 basePosition;

            switch (currentDecision)
            {
                case MovementDecision.Chase:
                    // Move toward target, stopping at optimal distance
                    basePosition = targetPos - directionToTarget * GetOptimalAttackDistance();
                    break;

                case MovementDecision.Retreat:
                    // Move away from target to minimum safe distance
                    basePosition = targetPos - directionToTarget * minimumDistance;
                    break;

                case MovementDecision.Maintain:
                    // Stay roughly where we are, but add slight movement if continuous movement is enabled
                    if (forceContinuousMovement && ShouldAddRestlessMovement())
                    {
                        Vector3 restlessOffset = GetRestlessMovementOffset();
                        basePosition = entityTransform.position + restlessOffset;
                    }
                    else
                    {
                        basePosition = entityTransform.position;
                    }
                    break;

                case MovementDecision.Circle:
                    // Circle around target at optimal distance
                    circlingAngle += circlingSpeed * Time.deltaTime;
                    Vector3 circleOffset = new Vector3(
                        Mathf.Cos(circlingAngle) * circlingRadius,
                        0f,
                        Mathf.Sin(circlingAngle) * circlingRadius
                    );
                    basePosition = targetPos + circleOffset;
                    break;

                case MovementDecision.Reposition:
                    // Find a better position using separation-aware repositioning
                    basePosition = CalculateRepositionTarget(targetPos, directionToTarget);
                    break;

                default:
                    basePosition = targetPos - directionToTarget * GetOptimalAttackDistance();
                    break;
            }

            // Apply separation if enabled
            if (enableSeparation && EnemySeparationService.Instance != null)
            {
                basePosition = EnemySeparationService.Instance.GetSeparationAdjustedPosition(entityTransform, basePosition);
            }

            // Apply mandatory spacing validation if enabled
            if (enableMandatorySpacing && validateAllTargets && MandatorySpacingEnforcer.Instance != null)
            {
                basePosition = MandatorySpacingEnforcer.Instance.EnforceMinimumSpacing(basePosition, entityTransform, mandatoryMinimumDistance);
            }

            return basePosition;
        }

        /// <summary>
        /// Calculate a repositioning target that considers nearby enemies and separation
        /// </summary>
        private Vector3 CalculateRepositionTarget(Vector3 targetPos, Vector3 directionToTarget)
        {
            Vector3 baseRepositionPos = targetPos - directionToTarget * circlingRadius;

            if (enableSeparation && EnemySeparationService.Instance != null)
            {
                // Get separation force to find less crowded areas
                Vector3 separationForce = EnemySeparationService.Instance.GetSeparationForce(entityTransform);

                if (separationForce.magnitude > 0.1f)
                {
                    // Move in the direction of separation force
                    Vector3 separationDirection = separationForce.normalized;
                    float repositionDistance = Mathf.Max(repositionThreshold * 2f, minimumMovementDistance);
                    baseRepositionPos += separationDirection * repositionDistance;
                }
                else
                {
                    // Fallback to random offset if no separation force
                    float repositionDistance = Mathf.Max(repositionThreshold, minimumMovementDistance);
                    Vector3 randomOffset = Random.insideUnitSphere * repositionDistance;
                    randomOffset.y = 0f; // Keep on ground plane
                    baseRepositionPos += randomOffset;
                }
            }
            else
            {
                // Standard random repositioning with minimum distance
                float repositionDistance = Mathf.Max(repositionThreshold, minimumMovementDistance);
                Vector3 randomOffset = Random.insideUnitSphere * repositionDistance;
                randomOffset.y = 0f; // Keep on ground plane
                baseRepositionPos += randomOffset;
            }

            return baseRepositionPos;
        }

        /// <summary>
        /// Check if enemy has been idle too long and force movement if needed
        /// </summary>
        private void CheckContinuousMovement(float currentTime)
        {
            // Check if we've moved recently
            float distanceFromLastKnown = Vector3.Distance(entityTransform.position, lastKnownPosition);

            if (distanceFromLastKnown > 0.1f)
            {
                // We've moved, update tracking
                lastMovementTime = currentTime;
                lastKnownPosition = entityTransform.position;
            }
            else if (currentTime - lastMovementTime > idleTimeThreshold)
            {
                // We've been idle too long, force repositioning
                ForceMovementToAvoidIdleness();
                lastMovementTime = currentTime;
            }
        }

        /// <summary>
        /// Force enemy to move when they've been idle too long
        /// </summary>
        private void ForceMovementToAvoidIdleness()
        {
            // Force a repositioning decision to get the enemy moving
            currentDecision = MovementDecision.Reposition;

            // Force immediate decision update
            lastDecisionTime = 0f;

            // Force separation update if enabled
            if (enableSeparation && EnemySeparationService.Instance != null)
            {
                EnemySeparationService.Instance.ForceUpdateSeparation(entityTransform);
            }

#if UNITY_EDITOR || DEVELOPMENT_BUILD
            if (enableDebugLogs)
            {
                Debug.Log($"[{nameof(BasicChaseMovementStrategy)}] Forcing movement to avoid idleness on {gameObject.name}");
            }
#endif
        }

        /// <summary>
        /// Check if we should add restless movement to maintain behavior
        /// </summary>
        private bool ShouldAddRestlessMovement()
        {
            return Time.time - lastRestlessMovement > (1f / restlessnessFactor);
        }

        /// <summary>
        /// Get a small random offset for restless movement
        /// </summary>
        private Vector3 GetRestlessMovementOffset()
        {
            lastRestlessMovement = Time.time;

            // Create small random movement around current position
            Vector3 randomOffset = Random.insideUnitSphere * minimumMovementDistance;
            randomOffset.y = 0f; // Keep on ground plane

            // Bias movement away from crowded areas if separation is enabled
            if (enableSeparation && EnemySeparationService.Instance != null)
            {
                Vector3 separationForce = EnemySeparationService.Instance.GetSeparationForce(entityTransform);
                if (separationForce.magnitude > 0.1f)
                {
                    // Blend random movement with separation force
                    randomOffset = Vector3.Lerp(randomOffset, separationForce.normalized * minimumMovementDistance, 0.7f);
                }
            }

            return randomOffset;
        }

        private float GetOptimizedDistanceToTarget()
        {
            if (currentTarget == null)
                return 0f;

            // Use cached distance if optimization enabled and cache is valid
            if (useOptimizedDistanceChecks && distanceCacheValid &&
                Time.time - lastDistanceCheck < distanceCheckInterval)
            {
                return GetCachedDistanceToTarget(); // Use parent's cached distance
            }

            // Calculate distance and update cache validity
            float distance = Vector3.Distance(entityTransform.position, currentTarget.position);
            lastDistanceCheck = Time.time;
            distanceCacheValid = true;

            return distance;
        }

        private void InvalidateDistanceCache()
        {
            distanceCacheValid = false;
        }

        /// <summary>
        /// Gets the optimal attack distance for this enemy.
        /// This is the preferred distance to maintain from the target for effective combat.
        /// </summary>
        /// <returns>The optimal attack distance in world units</returns>
        private float GetOptimalAttackDistance()
        {
            return circlingRadius;
        }

        private void CheckIfStuck()
        {
            float distanceMoved = Vector3.Distance(entityTransform.position, lastPosition);

            if (distanceMoved < stuckThreshold && isMoving)
            {
                // Check if we're stuck due to crowding or spacing violations
                bool hasCrowding = enableSeparation && EnemySeparationService.Instance != null &&
                                  EnemySeparationService.Instance.HasNearbyEnemies(entityTransform, separationCheckRadius);

                bool hasSpacingViolation = enableMandatorySpacing && MandatorySpacingEnforcer.Instance != null &&
                                          MandatorySpacingEnforcer.Instance.IsViolatingMinimumDistance(entityTransform, mandatoryMinimumDistance);

                if ((hasCrowding || hasSpacingViolation) && adaptivePositioning)
                {
                    // Use separation-aware repositioning when crowded or violating spacing
                    currentDecision = MovementDecision.Reposition;

                    if (EnemySeparationService.Instance != null)
                    {
                        EnemySeparationService.Instance.ForceUpdateSeparation(entityTransform);
                    }

                    if (MandatorySpacingEnforcer.Instance != null)
                    {
                        MandatorySpacingEnforcer.Instance.ForceValidation(entityTransform);
                    }
                }
                else
                {
                    // Standard repositioning
                    currentDecision = MovementDecision.Reposition;
                }

                InvalidateDistanceCache(); // Force distance recalculation

#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs)
                {
                    Debug.Log($"[{nameof(BasicChaseMovementStrategy)}] Detected stuck state, repositioning (crowding: {hasCrowding}, spacing violation: {hasSpacingViolation})");
                }
#endif
            }

            // lastPosition is handled by parent class
        }

        // Override base class methods to integrate with FollowerEntity
        protected override void HandleDestinationSet(Vector3 destination)
        {
            if (followerEntity != null)
            {
                followerEntity.destination = destination;
            }
        }

        protected override void HandleTargetSet(Transform target)
        {
            // Reset decision making when target changes
            currentDecision = MovementDecision.Chase;
            lastDecisionTime = 0f;
            InvalidateDistanceCache(); // Force distance recalculation for new target
        }

        protected override void HandleMovementStopped()
        {
            if (followerEntity != null)
            {
                followerEntity.isStopped = true;
            }
        }

        protected override void HandleMovementStateChanged(bool isMoving)
        {
            // Movement state is handled by FollowerEntity
        }

        protected override void HandleDestinationReached()
        {
            // Destination reached, evaluate next decision
            UpdateMovementDecision();
        }

        // Public API for configuration
        public void SetcirclingRadius(float distance)
        {
            circlingRadius = Mathf.Max(0f, distance);
        }

        public void SetDistanceRange(float minDistance, float maxDistance)
        {
            minimumDistance = Mathf.Max(0f, minDistance);
            maximumChaseDistance = Mathf.Max(minimumDistance, maxDistance);
        }

        public void SetCirclingBehavior(bool enabled, float radius = 6f, float speed = 2f)
        {
            enableCircling = enabled;
            circlingRadius = Mathf.Max(0f, radius);
            circlingSpeed = Mathf.Max(0f, speed);
        }

        /// <summary>
        /// Apply settings from EnemyConfiguration if available
        /// </summary>
        private void ApplyConfigurationSettings()
        {
            if (configuration != null)
            {
                // Apply movement settings from configuration
                minimumDistance = configuration.minPlayerDistance;
                maximumChaseDistance = configuration.maxPlayerDistance;

#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs)
                {
                    Debug.Log($"[{nameof(BasicChaseMovementStrategy)}] Applied configuration settings - Min distance: {minimumDistance}, Max distance: {maximumChaseDistance}");
                }
#endif
            }
            else
            {
                // Try to find configuration from parent entity
                var entity = GetComponent<CombatEntity>();
                if (entity != null && entity.Configuration != null)
                {
                    configuration = entity.Configuration;
                    ApplyConfigurationSettings(); // Recursive call with found configuration
                    return;
                }

#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs)
                {
                    Debug.Log($"[{nameof(BasicChaseMovementStrategy)}] No configuration found, using serialized values - Min distance: {minimumDistance}");
                }
#endif
            }
        }

        /// <summary>
        /// Set the configuration for this strategy
        /// </summary>
        public void SetConfiguration(EnemyConfiguration config)
        {
            configuration = config;
            ApplyConfigurationSettings();
        }

        // Debug information
        public MovementDecision CurrentDecision => currentDecision;
        public float DistanceToTarget => GetOptimizedDistanceToTarget(); // Use optimized distance calculation
        public EnemyConfiguration CurrentConfiguration => configuration;

#if UNITY_EDITOR
        [ContextMenu("Force Chase")]
        private void ForceChase()
        {
            currentDecision = MovementDecision.Chase;
        }

        [ContextMenu("Force Retreat")]
        private void ForceRetreat()
        {
            currentDecision = MovementDecision.Retreat;
        }

        [ContextMenu("Toggle Circling")]
        private void ToggleCircling()
        {
            enableCircling = !enableCircling;
        }
#endif

        // Enhanced gizmo drawing
        protected override void OnDrawGizmos()
        {
            base.OnDrawGizmos();

            if (!Application.isPlaying || !IsActive || currentTarget == null)
                return;

            // Draw distance ranges
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(currentTarget.position, minimumDistance);

            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(currentTarget.position, circlingRadius);

            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(currentTarget.position, maximumChaseDistance);

            // Draw current decision
            Gizmos.color = GetDecisionColor();
            Gizmos.DrawLine(transform.position, transform.position + Vector3.up * 2f);
        }

        private Color GetDecisionColor()
        {
            switch (currentDecision)
            {
                case MovementDecision.Chase: return Color.green;
                case MovementDecision.Retreat: return Color.red;
                case MovementDecision.Maintain: return Color.blue;
                case MovementDecision.Circle: return Color.magenta;
                case MovementDecision.Reposition: return Color.orange;
                default: return Color.white;
            }
        }

        /// <summary>
        /// Force immediate update of movement decision and pathfinding (called by emergency systems)
        /// </summary>
        public void ForceUpdate()
        {
            // Force immediate decision update
            lastDecisionTime = 0f;

            // Force distance cache invalidation
            InvalidateDistanceCache();

            // Force separation updates if enabled
            if (enableSeparation && EnemySeparationService.Instance != null)
            {
                EnemySeparationService.Instance.ForceUpdateSeparation(entityTransform);
            }

            if (enableMandatorySpacing && MandatorySpacingEnforcer.Instance != null)
            {
                MandatorySpacingEnforcer.Instance.ForceValidation(entityTransform);
            }

            // Immediately update movement decision and execute
            UpdateMovementDecision();
            ExecuteMovementDecision();

#if UNITY_EDITOR || DEVELOPMENT_BUILD
            if (enableDebugLogs)
            {
                Debug.Log($"[{nameof(BasicChaseMovementStrategy)}] Force update triggered on {gameObject.name}");
            }
#endif
        }
    }
}

