# Game Design Tools; Cognitive, Psychological, and Practical Approaches Summary

Book summary of Diego

Here is a summary of advice for game designers from the book:

1. **Democratization of Tools**: With the growth of the gaming industry, there has been an increase in access to learning content and tools. Anyone with an idea and a computer can make a game, leading to an explosion of content. Small teams and even solo developers can build new experiences and bring them to market.
2. **Iterative Process**: Game design is not a one-way process. It involves a series of iterative processes and countless decisions throughout development to bring visions to life and create experiences for players.
3. **Engaging Players**: The designer's job is to engage players right from the moment they launch the game and to keep them engaged in the long term. This involves understanding cognitive load, user experience, and the overall player journey.
4. **Design as Manipulation**: Design, in essence, is a form of manipulation. It involves using design tools to direct users into the best possible experience. This can range from adding a visual effect to show an interactable item, to creating a whole system to make the players live everything the game has to offer.
5. **Affordance**: Understanding affordance, the relationship between the properties of an object and the capabilities of the agent that determine how the object could possibly be used, is crucial. Good design makes it easy for the player to understand how things work.
6. **Clear Goals**: It's important to have a clear goal in mind. Remember why you first wanted to be a game designer and use that as your guiding principle.
7. **Communication**: When describing your game, use specific design language. However, when communicating with players, press, or people who aren't designers, feel free to use more general language that they can understand.
8. **Understanding Feedback**: When receiving feedback from players, use your design tools to understand what they meant. It's like decrypting a code, and with experience, you'll be able to do it quickly in most cases.

Continuing from the previous summary, here are more insights on game design principles from the book:

1. **Experimentation and Iteration**: Game design involves a lot of trial and error. You have an idea, develop a concept, make a prototype, see where people don't perform as expected, and then fix it. No game is born perfect; they just get tested and fixed enough through an iterative process.
2. **Unique Experiences and Emotions**: Game design allows each player to live unique experiences and feel uncommon or rare emotions that other media might struggle to arouse. This is achieved by controlling the player's actions and reactions within the game.
3. **Player Manipulation**: Games often manipulate players to enhance their experience. For example, Gears of Wars gave a bonus to every player every 24 hours, allowing them to feel confident and keep playing. However, players weren't told about this power, as the target of manipulation needs to be unaware of it.
4. **Respect for Players' Time**: As game designers, we are the custodians of the players' time. Every game makes a promise on an emotion, a tone, or an experience in return for borrowing time from our players' lives. It's our job to deliver on those promises, ensuring that the transaction of a player's time for our experience is fulfilled, fair, and treated with respect.
5. **Shared Vocabulary**: Having a shared vocabulary across a spectrum of topics that occur throughout any project from ideation and brainstorming techniques through to understanding player motivations and building mechanics is crucial. This provides a strong foundation for both newcomers and experienced developers.

Continuing from the previous summary, here are more insights on game design strategies from the book:

1. **Creative Freedom**: It's important for game designers to have creative freedom. This might mean needing some extra space to work on a project closer to yourself. You can create your indie studio and develop your own game, but be aware that for every successful company of this kind, there are ten that fail.
2. **Understanding Player Behavior**: Understanding and predicting player behavior is crucial in game design. For example, in League Of Legends, when allies are low, they have an icon standing above their head. This icon instantly shows the user playing Soraka which teammate needs her help, allowing a quicker response time by said user, meaning a better team collaboration and a higher chance of winning the game.
3. **Design Tools**: The book provides a series of transdisciplinary tools to help game designers improve their design pipeline and design output. It covers archetypes, praxeology, behavioral game design, and emotional game design. It also includes chapters on documentation, production, evaluation, analysis, and marketing tools.
4. **Creating Memories**: As designers, always remember we should allow people to live their wildest fantasies, unite with friends, create memories, and bring them back to when they were kids. We are almost their parents when they play.

That's all the information I could extract from the document. I hope this summary provides a comprehensive overview of the advice for game designers from the book.

---

Here is a summary of advice for game designers from the book:

1. **Clear Roadmap**: Keep your project roadmap as simple and short as possible. Rather than focusing on perfect values, talk about what you will accomplish in what time. Be transparent about where the resources will be going to make the project more understandable.
2. **Pitching**: When pitching your game to a publisher, highlight the strengths of your team and your past successes. Be clear about what you need from the publisher and be honest about the game and its timeline. Remember, pitching is about making your idea feel perfect, even if it's for just a second.
3. **Flexibility in Design**: Game design is about finding the best possible compromise among a hundred different things—time, money, vision, players’ satisfaction, etc. A good designer is someone who gets you to the closest possible place to the destination on time, on budget, with everyone still alive—and hopefully happy enough to get ready for the next adventure.
4. **Key Elements**: Define the key elements or pillars of your game. Every single element you add during the development needs to support one of these pillars. If it doesn’t, that element is just a nice to have.
5. **Creativity**: Creativity can be trained and improved. Expand your creativity by experiencing new things, seeing new places, reading new books, living as a different culture, doing things you are too scared to do, and asking yourself the 9 Whys for everything you don’t understand.
6. **Inclusivity in Creativity**: A good designer is someone who sees the creative value in everything—and everyone—around them. A good designer listens to all the ideas around them and understands which can work and which just won’t. Listen to anyone, but don’t say yes to everyone.
7. **Choosing the Best Idea**: Once you gather tons of possibilities, the design team needs to choose the best one for the game, no matter if it comes from the “creative team” or not. Like parents with our babies, we need to do what’s best for them, putting our ego aside.

Continuing from the previous summary, here are more pieces of advice for game designers from the book:

1. **Practice Pitching**: If you need to practice before your first pitch, a simple but effective beginner’s technique is pitching to yourself in the mirror. This allows you to see your level of confidence, how you move, and even if you turn red after talking too much without taking a breath.
2. **High Vision Document**: A high vision document works great because it gives enough guidelines to know what you can and can’t do but not enough to make you feel like you are just following a checklist. It should include the title of the game, a concept brief, key elements or pillars, and primes or objectives.
3. **Combining Objects Creatively**: Combining objects without criteria is more like gambling than creating. To know which objects you should put together you need analysis and data. With the right research, you know in advance which of the thousands of possible combinations will not work and which will be valid.
4. **Improving Creativity**: Creativity can be trained and improved. Go see new places, read new books, live as a different culture, do things you are too scared to do, ask yourself the 9 Whys for everything you don’t understand. Each one of these activities will expand your creativity because it will change—even if just a bit—the way you see the world.
5. **Generating Ideas**: Another way to improve your creativity is by forcing yourself to come up with ideas. Take a problem, give yourself one hour, and come up with 50 possible solutions for the said problem. Over time, you will have more ideas and, most importantly, more valid ones.
6. **Everyone Can Be Creative**: A good designer is someone who sees the creative value in everything—and everyone—around them. A good designer listens to all the ideas around them and understands which can work and which just won’t. Listen to anyone, but don’t say yes to everyone. Once you gather tons of possibilities, the design team needs to choose the best one for the game, no matter if it comes from the “creative team” or not.