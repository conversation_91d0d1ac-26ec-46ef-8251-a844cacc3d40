# May 7 / 8 / 9
 2 | 
 3 | Didn’t get to work on this for more than a few minutes, but fixed a Reach UI error 
 4 | 
 5 | Need to assess UI - How should it exist within each scene? Prefab? Singleton?
 6 | 
 7 | Load a scene additively as pause menu? Look into this - interesting 
 8 | 
 9 | [Unity - Scripting API: SceneManager](https://docs.unity3d.com/ScriptReference/SceneManagement.SceneManager.html)
10 | 
11 | also
12 | 
13 | “My usual workflow is that I'll have a scene that loads in the beginning with a single GameManager object, with an attached GameManager script that is responsible for handling things like scene transitions and scene logic, as well as any other things that are useful to have in a singleton. Then I set that object to not destroy on load. Any UI elements that I want to exist in all of my other scenes are simply parented to that Gamemanager object (in a canvas of course), so that they are always accessible. If you want, you can then add some logic for not having your UI available in certain instances (say you don't want your player to pause the game on the title screen)”
14 | 
15 | Try parents Timekeeper, UI, and such to GameManager, and as it moves along scenes, see if things still work? 
16 | 
17 | May 8th
18 | 
19 | Trying the above idea for UI and other things to transition between scenes
20 | 
21 | Not working, may be a better time for it
22 | 
23 | Still encountering Text Mesh Pro Issues, investigating. Seems like it’s not properply installed, strangely’
24 | 
25 | Fixed! It seems
26 | 
27 | Trying UI stuff again now and debugging
28 | 
 29 | Using Persistent Object script to bring forward to other scenes
30 | 
31 | Not working it seems. ALso parenting things to the game manager breaks the game in unexpected ways. Need to analyze or just be careful about this
32 | 
33 | Had issues with UI being a prefab, unsure why… need to check this out again too. 
34 | 
35 | Doing it and it mostly seems to work? This may be the best method for unified UI among multiple scenes - will continue testing but moving forward with this! :)
36 | 
37 | Also trying to rewrite my reload scene script - want thing working better - fails currently, may not be easily solvable. May not be worth it!
38 | 
39 | Rough implemention integrated
40 | 
41 | Added next scene to game manager, rough implementation, seems to be skipping ahead too much 
42 | 
43 | Need to get out of the bug trench and into gameplay trench a bit more!
44 | 
45 | May 9th
46 | 
47 | Also fixed Player Movement issue with double rotations occuring, debounce in the methods has fixed this. Adjust timing of this if necessary