# Object Pooling Migration Guide

**Date:** July 22, 2025  
**Author:** GitHub Copilot  
**Status:** Reference Documentation

## Overview

This guide provides instructions for migrating various pooling implementations to the standardized `Stylo.Resevoir` pooling system. The goal is to simplify our codebase by using a single, unified approach to object pooling throughout the project.

## Why Migrate to Stylo.Resevoir?

- **Performance:** Standardized approach to pooling optimizes memory usage and reduces garbage collection
- **Maintainability:** Single system to update, debug, and optimize
- **Consistency:** Unified API for all pooling operations
- **Scalability:** Centralized pool management for better resource control

## Migration Pathways

### 1. Generic ObjectPool\<T\> → Stylo.Resevoir

**Current Usage:**

```csharp
// Creation
private ObjectPool<ParticleSystem> particleSystemPool;
particleSystemPool = new ObjectPool<ParticleSystem>(CreateParticleSystem, maxParticleSystems);

// Usage
var particle = particleSystemPool.Get();
// Use particle...
particleSystemPool.Release(particle);
```

**Migration to Stylo.Resevoir:**

```csharp
// Add to imports
using Stylo.Resevoir;

// Creation - for GameObject-based objects
private GameObject particleSystemPrefab; // Reference to your prefab

// Usage - for GameObject-based objects
var particleGO = ReservoirManager.Spawn(particleSystemPrefab, position, rotation);
var particle = particleGO.GetComponent<ParticleSystem>();
// Use particle...
ReservoirManager.Despawn(particleGO);
```

**For Non-GameObject Types:**

For non-GameObject types that still need generic pooling, continue using the generic `ObjectPool<T>` since Stylo.Resevoir is designed for GameObjects.

### 2. PathologicalGames → Stylo.Resevoir

**Note:** A compatibility layer already exists in `Assets\Stylo\Resevoir\Compatibility\PathologicalAdapter.cs`. This adapter redirects PathologicalGames calls to Stylo.Resevoir, but for better performance and clarity, direct usage is recommended.

**Current Usage (via Compatibility Layer):**

```csharp
using PathologicalGames;

// Usage
Transform pooledObject = PoolManager.Pools["MyPool"].Spawn(prefab, position, rotation);
// Use pooled object...
PoolManager.Pools["MyPool"].Despawn(pooledObject);
```

**Migration to Stylo.Resevoir:**

```csharp
using Stylo.Resevoir;

// Usage
GameObject pooledObject = ReservoirManager.Spawn(prefab, position, rotation);
// Use pooled object...
ReservoirManager.Despawn(pooledObject);
```

### 3. ProjectilePool → Stylo.Resevoir

**Current Usage:**

```csharp
// Initialization
ProjectilePool projectilePool = FindObjectOfType<ProjectilePool>();

// Usage
var projectile = projectilePool.GetProjectile(prefab, position, rotation);
// Use projectile...
projectilePool.ReturnProjectile(projectile);
```

**Migration to Stylo.Resevoir:**

```csharp
using Stylo.Resevoir;

// Usage
var projectileObject = ReservoirManager.Spawn(projectilePrefab, position, rotation);
var projectile = projectileObject.GetComponent<ProjectileEntity>(); // or whatever component you need
// Use projectile...
ReservoirManager.Despawn(projectileObject);
```

### 4. ToolBuddy's UnityObjectPool → Stylo.Resevoir

**Current Usage:**

```csharp
using FluffyUnderware.DevTools;

// Reference to PrefabPool component
PrefabPool prefabPool;

// Usage
GameObject instance = prefabPool.Pop();
// Use instance...
prefabPool.Push(instance);
```

**Migration to Stylo.Resevoir:**

```csharp
using Stylo.Resevoir;

// Store prefab reference
public GameObject prefab;

// Usage
GameObject instance = ReservoirManager.Spawn(prefab, position, rotation);
// Use instance...
ReservoirManager.Despawn(instance);
```

## Stylo.Resevoir API Reference

### Key Methods

```csharp
// Spawning
GameObject ReservoirManager.Spawn(GameObject prefab);
GameObject ReservoirManager.Spawn(GameObject prefab, Vector3 position, Quaternion rotation);
GameObject ReservoirManager.Spawn(GameObject prefab, Transform parent);

// Despawning
void ReservoirManager.Despawn(GameObject instance);
void ReservoirManager.Despawn(GameObject instance, float delay);

// Pool Management
void ReservoirManager.PreloadPool(GameObject prefab, int count);
void ReservoirManager.ClearPool(GameObject prefab);
int ReservoirManager.GetActiveCount(GameObject prefab);
```

### Advanced Usage

#### Customizing Object Reset Behavior

To define custom logic when an object is returned to the pool:

```csharp
public class CustomPooledObject : MonoBehaviour, IPoolHandler
{
    public void OnDespawned()
    {
        // Reset object state here
        transform.localScale = Vector3.one;
        GetComponent<Renderer>().material.color = Color.white;
    }

    public void OnSpawned()
    {
        // Initialize object state here
        gameObject.SetActive(true);
    }
}
```

#### Pool Statistics and Debugging

```csharp
// Get statistics about a specific prefab pool
PoolStatistics stats = ReservoirManager.GetPoolStatistics(prefab);
Debug.Log($"Active: {stats.ActiveCount}, Pooled: {stats.PooledCount}, Total: {stats.TotalCount}");

// Enable verbose logging
ReservoirLog.Verbose = true;
```

## Migration Strategy

### Step 1: Identify Pooling Usage

Use grep or code search to identify all places using different pooling systems:

- Search for `new ObjectPool<`
- Search for `PoolManager.Pools`
- Search for `ProjectilePool`
- Search for references to `UnityObjectPool` or `PrefabPool`

### Step 2: Prioritize High-Impact Areas

Migrate systems in this order:

1. Projectiles (high volume, performance-critical)
2. Enemy spawning systems
3. Particle/VFX systems
4. General GameObject pooling
5. UI element pooling

### Step 3: Testing Strategy

For each migration:

1. Create a performance benchmark before changes
2. Implement the migration in a feature branch
3. Run the same benchmark to verify performance
4. Check for memory leaks using the Unity Profiler
5. Test edge cases (stress testing with high spawn/despawn rates)

### Step 4: Common Issues and Solutions

| Issue                         | Solution                               |
| ----------------------------- | -------------------------------------- |
| Objects not returning to pool | Verify proper `Despawn` calls          |
| Pool growing too large        | Set appropriate maximum pool sizes     |
| Objects retaining state       | Implement `IPoolHandler` interface     |
| Position/rotation issues      | Check if reset logic is needed         |
| Component references breaking | Use `GetComponent<T>()` after spawning |

## Conclusion

Migrating to Stylo.Resevoir provides significant benefits in terms of performance, maintainability, and code clarity. While the migration requires some upfront effort, the long-term advantages make it worthwhile.

For assistance with migration or questions about specific use cases, please contact the core engine team.
