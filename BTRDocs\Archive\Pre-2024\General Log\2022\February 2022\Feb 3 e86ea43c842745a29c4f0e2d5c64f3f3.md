# Feb 3

Naming conventions! Get more into this workflow type-stuff

[https://www.youtube.com/watch?v=Z6qBeuN-H1M](https://www.youtube.com/watch?v=Z6qBeuN-H1M)

Adding Unity video recorder plug

[https://learn.unity.com/tutorial/working-with-the-unity-recorder-2019-3#5e1efe8bedbc2a0029fbc0d6](https://learn.unity.com/tutorial/working-with-the-unity-recorder-2019-3#5e1efe8bedbc2a0029fbc0d6)

Continuing warp drive effect from yesterday

[https://www.youtube.com/watch?v=VsGG5JYlqIU](https://www.youtube.com/watch?v=VsGG5JYlqIU)

Moving texture map is here! May want this as well sometime

Trying with Koreographer implemention - looks cool!

Imported a projectiles package to make proj look better

Added muzzle effects to reticle? or just launch or particles?

Need impact effect as well

Koreo Note - how to organize all track names in a project? Need to look into this

Idea - Living Particles for zipping through walls with a break away effect

Bug found - bullets moving in circles after presumably missing target, not sure why. 

RE-Enable Target Cube to see if it’s related to this issue? Write a check that it used TargetCube and that it hit it? Not seeing any Target Cube’s created though... but maybe all were destroyed?