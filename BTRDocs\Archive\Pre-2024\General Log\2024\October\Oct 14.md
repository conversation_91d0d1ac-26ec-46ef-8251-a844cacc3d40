# Oct 14

Implemented Jobs system for enemy raycasts for line of sight

Implemented Jobs system for projectile collisions

- make sure proper layer masks are set on enemies

Using Particle system Manager to better track and optimize any large amounts of particle systems 

Tried fioguring out waht is necessary for optimzing mesh for A*, unsuccessful. Most of my mesh seem to be ok but the WaitforJob - Idle stuff from before, still present on some meshes. Awaiting <PERSON><PERSON>’s repsonse and need to investigate further or just try only meshses without large overhead

Tracking - which structures per scene? 

Scene 1

Mobius Tube 2 - works

Mobius tube 2 Variation - works

Scene 3

Mobius Tube 6 - works

<PERSON><PERSON> 5 - External - works

Scene 5

Mobius tube 7 - works

Mobius Tube 5 - Internal - works

Could not fix

Mobius Tube 2 Flipped - optimization needed

Mobius tube 2 Varaition 2  - optmization needed

In a place where i can playtest and finish up / fix / refine what is here

Delete unecessary meshes wehn ready