# Animation Learning Notes

## February 2024

### Feb 28th - Umotion Notes
- **Editing Animations with Umotion:** 
    - Uses Animation Layers - make adjustments without getting rid of original animation.
    - Can use override to adjust original.
    - Animation layers seem to be a simple quick way to make broad adjustments across base animation.
    - FK vs IK - read up on this - seems IK preferred.
    - Investigate root motion.
    - Seeing curve view of the animations is very useful - can find immediate discrepancies.
    - Copy to other side tool - good for adjustment of mirror things!
    - Setting up a shortcut for this and enabling Generate allows for quick copying of animation frames.
    - Shown well in video!
    - YouTube Video: [(2) Editing Existing Animations - UMotion In Practice](https://www.youtube.com/watch?v=TPzCp6Ezy4o)