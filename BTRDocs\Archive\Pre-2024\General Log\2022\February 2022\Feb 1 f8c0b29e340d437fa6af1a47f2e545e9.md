# Feb 1

Adding Feel to some elements - Lock and Shoot camera shake

Adding transparency for projectiles so screen is less obstructed

- Used DOTween!
- Only do this within certain range? or always?

Added diamond enemy as shape

Added particle effects for Enemy Birth and Death

Combined Enemy Basic Setup with Enemy Rhythm

- This was done to allow everything to be tied to Koreographer

Combined ProjectileAudio and Projectile for same reasons

Added Movement Particle gObj to Projectiles

EXPECT THIS TO BREAK AIM PREFAB CREATE / DESTROY

FIX THIS

Looked at ShaderGraph Dissolve Dissolve metallic for enemies but decided differently

Bug found - LockedList on Shooting is empty but Canvas number says 1

Bug found - Enemy continually rising? maybe an A* / RichAI Issue

Bug found - if bullets are heading for target and it dies first, they will get stuck!

- Need to evaluate that target is still active, and if it isn’t just continue forward and reinitiate lifetime

Bug found - bullets rotating with enemies still happening