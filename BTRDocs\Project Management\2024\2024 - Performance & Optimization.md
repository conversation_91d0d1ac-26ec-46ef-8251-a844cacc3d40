Okay, here's a more thorough breakdown of the performance and optimization notes specifically from 2024, organized chronologically:

---


**January 10th, 2024:**

*   Compilation of shaders causing builds to take an excessively long time (over an hour for a single scene).  Tried preloading shader variants under graphics settings as a potential solution.
*  Need to make some rigid decisions on Bosses / Enemies / Levels
    Strategy for creative look of bosses - several phases?
      use a base shape as point to construct boss
      use geometry nodes to make a deconstructed look for the boss
      third phase? Unique to each boss?

**January 15th - Many Links!:**

*   Links to various resources with performance tips and tools:
    *   Blog post with 65 tips on improving Unity game performance.
    *   GitHub repository for Unity Project Auditor (analyzes project settings for potential issues).

**January 17th:**

*   Need to find a way to reduce build times (Assembly Definitions were mentioned as a potential solution).
*   Should I adjust midboss death so it doesnt call scene switching so directly? Unsure

**February 13:**

*   A\* looks to have massive improvements so updated to *.95

**August 6:** A multitude of Empty Slots (for Comissions) on Twitter / X
* Made various improvements to Custom AI Path aligned to surface, unsure how I feel on these. May or may not be helping. Some bugs in behavior of this script need to be fixed (how an error is handled).
Added GPU Instancing to Ty Fast Ghost Shader
* Some more optimizations
* Seemed to have fixed this i think… monitoring
* Is this missing from Crosshair and needs to be added again?

FINALLL split up Crosshair to 3 classes.

**August 7** Also, working on FMODImproving some soundsUpgraded to 2.03 for new features that may be useful, like multiband, frequency sidechain, etc

* Some issues with hard coded fmod events pointing to the wrong things, fixing this.
* Removing firing blasts as it seems unecessary
* Also, working on FMOD

**August 16/17:** Fixed some issues with Enemy Damagable parts, and managed to maintain Burst
*  A LOT of time on resetting projectile lifetimes, necessary work. need to refine this so you cant just hold bullets forever - i think, could be a mechanic like bomberman sort of

**December 4:** Have made several major optimizations over the past few days
Need to test gameplay but seems functional so far!

* Fix broken shaderes and effects
* Also need to optimize rendering - this is the bottleneck now
   Made a texture analyser - use to delete unused textures.

**Dec 11:** Idea- Shrink and grow cursor to indicate target locking
Change Reticle over to a system based on Shapes asset pack

**Dec 18/19** fix homing for enemy shot projectiles - not homing properly Mostly working - summary of changes we implemented
- Projectile System Changes

**Dec 21:** There is Subtile code issues
*Unintentional Boxing
*String Concatenation
*LINQ on Value Type

**Dec 26:** Shader Modifications:
*Extensive changes to the Shapes package shaders
*shader modifications suggest optimization work or visual improvements.
FSROptimization
*QualitySettings.asset

**Key themes**

- Shader Optiomizaiton
- Render Optimizaion
- Performance Test - Game play

