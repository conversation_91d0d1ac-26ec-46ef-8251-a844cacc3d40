# Titanfall 2 Action Blocks

[https://www.youtube.com/watch?v=CkHGuHd9BgU](https://www.youtube.com/watch?v=CkHGuHd9BgU)

Action Blocks

![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Rez-Like%20Docs/Level%20Design/Titanfall%202%20Action%20Blocks/Untitled.png)

 
![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Rez-Like%20Docs/Level%20Design/Titanfall%202%20Action%20Blocks/Untitled%201.png)

Skill test must fit into a level - testing a players skills

Make a Roadmap of these Action Blocks

Then create a library of them

A1 - Maze action block / overhead view solves maze then run through with mech

A2 - Use gun to shoot switching to drop panels you can then run across

 
Narrowing Scope

![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Rez-Like%20Docs/Level%20Design/Titanfall%202%20Action%20Blocks/Untitled%202.png)

Had to cut two styles of combat due to number of issues in figuring that out, decided that was not the focus for this game

Set Boundaries

- By saying no to mixed combat, could focus on core problems in other two

Instant Transporting / Time travel level directly made it into game

 
Focus on a particular platforming level

First, top down sketching of ideas on how level would progress

Then blocking things out to try elements

![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Rez-Like%20Docs/Level%20Design/Titanfall%202%20Action%20Blocks/Untitled%203.png)

Using theme and context to tease out further ideas from action blocks

![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Rez-Like%20Docs/Level%20Design/Titanfall%202%20Action%20Blocks/Untitled%204.png)

In working on the factory level, what are the details of a factory? Looking at parts of an assembly line, other elements that would be barriers to get through in a platforming context

Did a duck for cover shoot enemies levle where a house was built up around the player

![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Rez-Like%20Docs/Level%20Design/Titanfall%202%20Action%20Blocks/Untitled%205.png)

Can use Action Blocks to do quick mashups of ideas

Action Block Pitfalls

- Trying to make them too perfect - just find the good gameplay and move on, don't perfect them
- They can create the impression that finally creating a level will be easier then they are. Be concious of the production cost - action block may imply a larger game then you have time to make
- Contexualization important - at some point they have to be real world equivalent of what it's trying to be
- Action Blocks are not Level Design! Seperate action blocks from Game flow and level design - these are seperate problems

Use action blocks to solve the hard gameplay problems before you commit to them!

How to do Action Blocks on a new IP?

Take a Game Jam perspective. Talking about, pitching ideas, and then game jam mentality - waht are we gonna figure out with this?