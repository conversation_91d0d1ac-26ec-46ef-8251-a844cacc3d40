# Optimization Tips

https://www.youtube.com/watch?v=ZRDHEqy2uPI&t=1s

Here's a summary of the optimization and performance tips from the video:
1. **Memory Management**: Avoid frequent instantiation and destruction of objects as it's generally slow and can lead to memory fragmentation. Instead, use object pooling to recycle objects.
2. **Garbage Collection**: Minimize the work for the Garbage Collector by reducing the number of destroyed objects. This can be achieved by using object pooling.
3. **Data Structures**: Choose the right data structure for your specific use case. For example, indexing in an array or a list is cheap, but adding or removing elements is more expensive than doing it in a dictionary.
4. **Scriptable Objects**: Use Scriptable Objects to store unchanging data. This can help reduce memory usage as only one copy of the data is stored.
5. **Properties with Getters and Setters**: Avoid using properties with getters and setters in tight loops as it can lead to performance issues. Instead, use Define Symbol or Preprocessing Directive.
6. **Resources Folder**: Avoid using the Resources folder as it can affect the startup time of the game. Instead, use the Addressable Asset System.
7. **Empty Unity Functions**: Remove any empty Unity functions such as Start(), Update(), Awake(), etc. to avoid unnecessary performance overhead.
8. **Heavy Initialization Logic**: Avoid heavy initialization logic in Awake() and Start() functions as it can delay the rendering of the first frame. Instead, activate or instantiate objects right after the first frame is rendered.
9. **Accelerometer Frequency**: Adjust the Accelerometer Frequency to the minimum possible value if your game doesn't require it. This can save CPU processing time in mobile platforms.
10. **Moving GameObjects**: If you want to transform an object that has a Rigidbody component, use the rigidBody class methods instead of transform.Translate().
11. **Adding Components at Runtime**: Avoid adding components at runtime as it can be inefficient. Instead, use Prefabs.
12. **Finding GameObjects and Components**: Avoid using GameObject.Find() and GetComponent() frequently as they can be slow in complex Scenes. Instead, cache the reference to the object or component.
Remember, these are general tips and might not apply to every situation. Always profile your game to see if these optimizations make a significant difference.

https://www.youtube.com/watch?v=EK8sX8oCQbw

Here's a summary of the optimization and performance tips from the video:
1. **Asset Importing Tips**: Optimize import settings for assets like textures and meshes. For textures, use the minimum possible value for the Max Size setting, ensure textures are Power Of Two (POT) in size, atlas your textures, remove Alpha Channels on non-transparent textures, disable Read/Write Enabled option if you don't need to access the data from the texture from code, and examine if a lower format like 16-bit is enough instead of a larger 32-bit color format. For meshes, optimize the mesh, use more aggressive mesh compression, disable Read/Write Enabled option, disable rigs if you're not using animations, disable Blendshapes if you're not using them, and disable Normals and Tangents if your material won't use them.
2. **Graphics Tips**: Use batching techniques to reduce draw calls to the GPU. Dynamic batching helps reduce draw calls on moving objects, and static batching attempts to batch GameObjects with the Static flag enabled. Disable shadow casting for objects whose shadows won't add any relevant detail to the scene. Use culling masks for lights to ensure each light only affects objects in specific Layers. Avoid mobile native resolution if it causes performance issues, and lower your game's default resolution with Screen.SetResolution() function.
3. **UI Tips**: Hide invisible UI elements to prevent unnecessary calculations in rendering. Separate your UI elements in different Canvases based on how frequently you update them. Make sure that only your Canvases that are interactable have the Graphic Raycaster component. Disable the camera rendering the 3D Scene, hide other covered UIs, disable the Canvas component, and reduce the frame rate if possible when displaying full-screen UIs.
4. **Audio Tips**: Set your audio clips option to be forced to mono to save memory space and disk space. Reduce the compression bitrate if you need additional compression. Unload any audio-related source from memory or any audio clip from memory when the game is muted.
Remember, these are general tips and might not apply to every situation. Always profile your game to see if these optimizations make a significant difference.

https://www.youtube.com/watch?v=tsGmWvf7I6c

Here's a summary of the key points from the video titled "Optimizing Mobile Games in Unity" by Alexander Zhdankin:
1. **Environment Shader**: The speaker discusses the use of a single shader for different textures and lightmaps. He mentions that the shader is optimized and takes up only 32% of the total frame time on an iPhone X. He also mentions the use of Xcode's GPU snapshot feature to analyze shader performance.
2. **Shader GUI**: The speaker talks about the use of Shader GUI for inspecting shaders. He mentions that it's useful for creating custom controllers and making the workflow easier. He also talks about the use of Shader GUI to check if textures have alpha and to compile a simpler version of the shader if they don't.
3. **Shadow Optimization**: The speaker discusses the importance of shadow optimization. He mentions that shadows typically require a lot of calculations and can be heavier than drawing environment textures. He talks about their custom solution to use a shadow mask, which is faster than the traditional approach.
4. **Character Shader**: The speaker discusses the creation of a character shader. He mentions that they started with a surface shader and then modified it to suit their needs. He also talks about the importance of optimizing shaders for different devices and adjusting device settings accordingly.
5. **Visual Effects**: The speaker discusses the importance of optimizing visual effects, particularly in relation to overdraw. He talks about the use of simple shaders that do one thing well, rather than trying to create a "mega shader" that does everything.
6. **Bloom**: The speaker talks about the importance of bloom in their game. He mentions that they use a lower resolution buffer for bloom and manage without depth testing. He also discusses a custom approach to bloom that reduces overdraw and improves performance.
7. **Custom Rain Effect**: The speaker discusses the creation of a custom rain effect using a vertex shader. He mentions that they pre-bake a large number of quads and store them in a bundle. They then use the vertex shader to give each particle different lifetimes and speeds.
8. **Android Profiling**: The speaker recommends the use of Snapdragon Profiler and GAPID for Android profiling. He mentions that these tools can provide useful information for optimization.
9. **Quality Settings**: The speaker discusses the use of different quality settings for different devices. He mentions that they try to determine the best settings automatically based on the device's capabilities.
10. **Learning from Mistakes**: The speaker acknowledges that not everything went perfectly in their optimization process. He mentions that they experienced frame drops on different devices and that some effects needed optimization. However, he also highlights their successes, including the game's good performance on most modern phones and the large amount of content they were able to include.

https://www.youtube.com/watch?v=GuODu4-cXXQ

Here's the summary of the video "Unite Copenhagen 2019 - Practical Guide to Optimization for Mobiles" by Unity:
1. **Debugging and Profiling**: Use Unity's built-in profiler to understand where your allocations are coming from and fix them. For example, excessive use of Debug.Log can cause significant memory allocations. To avoid this, you can create a custom logging class that wraps Debug.Log and use conditional compilation to strip out debug logging from the final build.
2. **Garbage Collection**: The Unity garbage collector can cause disruptions in gameplay due to its "stop the world" approach. To mitigate this, consider unloading all resources when transitioning between scenes, allocating a pool of objects for reuse during gameplay, optimizing frame time as much as possible, and enabling the incremental garbage collector. The incremental garbage collector, introduced in Unity 2019, splits the work across multiple frames, reducing the impact on gameplay.
3. **GPU Optimization**: Reduce the stress on the GPU by minimizing the number of unnecessary rendering operations, reducing the amount of data sent to the GPU, minimizing the number of state changes, and optimizing expensive shaders. Unity's Frame Debugger can provide a breakdown of all the draw calls that compose your final frame, helping you understand why certain draw calls couldn't be batched with previous ones.
4. **Level of Detail (LOD)**: For objects in the background or far from the camera, consider using lower levels of detail. This can significantly reduce the number of triangles rendered and thus the load on the GPU.
5. **Memory Footprint**: Understanding your requirements and using the correct settings can significantly reduce your memory footprint. For example, for large audio files, consider using the "streaming" load type instead of "decompress on load" to reduce memory usage.
6. **Asset Bundles**: Be aware that Unity's built-in shaders cannot be explicitly included in an asset bundle and will be implicitly included in every asset bundle that contains a material referencing the shader. To avoid this, download a copy of the built-in shaders, rename it, add it to its own asset bundle, and fix all materials to use the new renamed shader.
7. **Automation**: To avoid human errors, consider using Unity's Asset Postprocessor to automate the process of setting correct settings for your assets.
8. **General Tips**: Always profile on the target device, profile early and often, apply fixes one at a time, and follow the optimization triangle: optimize your assets, update fewer things, and draw less stuff.

[https://www.youtube.com/watch?v=YOtDVv5-0A4](https://www.youtube.com/watch?v=YOtDVv5-0A4)

Here's a summary of the key optimization and performance tips from the video titled "Alba: A Wildlife Adventure - Optimizing for Mobile" by UsTwo Games:
1. **World Generation**: The game uses procedural generation for terrain and features. However, the team found that as the project progressed, developers were hesitant to use these tools due to the disparity between the low-resolution input and the high-resolution output.
2. **Performance Challenges**: The game faced significant performance issues halfway through development. The team had to prioritize performance optimization, focusing on worst-case scenarios and catching spikes in frame rate, not just averages.
3. **Profiling Tools**: The team used a variety of profiling tools, including Unity's built-in tools (Profiler, Profile Analyzer, Frame Debugger, Memory Profiler) and platform-specific tools (Xcode Frame Debugger, Instruments Time Profiler, Instruments Game Performance). These tools helped identify areas of high expense.
4. **Foliage Rendering**: The team initially used imposter-based LODs for foliage rendering, but found them to be too expensive on mobile devices due to transparency breaking hidden surface removal on tile-based renderers. They switched to a new art direction for trees involving no transparency and pure geometry.
5. **Multifaceted LOD System**: To manage the high number of AI animals, NPCs, and interactive objects, the team created a multifaceted LOD system that controlled visibility of animators, enabled/disabled audio, and specific script behavior. This system was accelerated by Burst and Jobs.
6. **Hard-to-Profile Areas**: The team found certain areas, like the photography mechanic, hard to profile. They discovered that rendering the game twice (once for the main world and once for the camera view) was too expensive and opted for a single render on low-end devices.
7. **Miscellaneous Improvements**: The team made several other improvements, including disabling unused Cinemachine Virtual Cameras, turning off Physics.autoSyncTransforms, precompiling shaders, and optimizing asset import settings.
8. **Automated Profiling**: The team set up an automated profiling system that would regularly check performance on target devices, ensuring that performance wasn't regressing.
9. **Device-Specific Optimizations**: The team made device-specific optimizations, disabling real-time shadows and post-processing on low-end devices, while allowing these features on high-end devices.
10. **Lessons Learned**: The team emphasized the importance of having ambition informed by reality and driving optimization by profiling. They also highlighted the importance of using profiling tools to identify and address performance issues.Regenerate responseChatGPT may produce inaccurate information about people, places, or facts.