# Sept 1

looking at best way of controller character movement

trying all sorts of gravity tricks and things

now attempting just using A* to have the player follow the reticle

Works pretty well! I think there’s a refined version of this that is THE solution!

Double check on ‘Constrain Inside Graph attribute - can I use it? Pretty sure it’s not meant for this 

High setting of Funnel Modifier requires recalculating normals on navmesh. Good? WOrth it?

Try gravity for bullets or other things? 

Cool design

![Untitled](Sept%201%2039b128da45964517a5d904608723aad7/Untitled.png)

Sept 4th

Looking into leaks in code

‘You should look for the `CreateForwardPlusBuffers` method in your code (or potentially in Unity's universal rendering pipeline code if you're using that) and make sure any `NativeArray<T>` objects are being properly disposed of. If you're using Unity's universal rendering pipeline and this method is part of Unity's code, you might need to update to a newer version of Unity or the universal rendering pipeline package, as this could be a bug that's been fixed in a newer version.’

‘

You should look for the `CreateForwardPlusBuffers` method in your code (or potentially in Unity's universal rendering pipeline code if you're using that) and make sure any `GraphicsBuffer` objects are being properly released. If you're using Unity's universal rendering pipeline and this method is part of Unity's code, you might need to update to a newer version of Unity or the universal rendering pipeline package, as this could be a bug that's been fixed in a newer version.

Also, consider using Unity's Memory Profiler to get a better understanding of your game's memory usage and potentially find other memory leaks [docs.unity3d.com](https://docs.unity3d.com/Packages/com.unity.memoryprofiler@0.2/manual/workflow-memory-leaks.html).

Given that the memory leak is connected to the Unity's Universal Render Pipeline (URP), it's worth noting that there have been reported issues of memory leaks with URP in the past [issuetracker.unity3d.com](https://issuetracker.unity3d.com/issues/urp-memory-leak-when-in-play-mode). If updating Unity or URP doesn't solve the issue, you might need to report this to Unity's issue tracker.’

Forward+ Renderer appears to have memory leak issues, may need to follow up on new version of Unity LTS that addresses this problem. 

Switched to Direct X 11 and the memory leaks went away - likely a Unity issue

Switch haptics to the interhaptics razer suite? Added but maybe not working properly

May be unity issue, supposedly store is down