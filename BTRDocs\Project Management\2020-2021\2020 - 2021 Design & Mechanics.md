Design & Mechanics
March 28th 2020

Refine Lock on

Import enemies and get bullet polling working

Enemy Pooling?

April 3

Working on timing

Targets List Gathers all targets

Locked list is all targets in time

Release pulls from Locked list

Do I need true/false to prevent locking/firing from interferring with each other?

IMP - Need to run a check on Targets for anything that should be on LockedList

Having Lock and Shoot working, need to refine sounds and clean up

April 6th

Goals:

Grey box levels! - Tested, need to make one with ProBuilder - Check!

April 8th

IDEA: Fargo dePalma multiple camera angles - could use splitting the screen effectively like this?

April 12

Introduced Combo multiplier for Shooting - should this be an exponent?

Fixed Locks Number - shows number of locks counting down as enemies shot (didnt before)

Issues with Line Renderer - Not drawing lines between objects

IDEA Change particle speed when locked?

Did this! Particles slowly reverse - maybe just have them hold?

IDEA Particle Spawner releases different patterns that can be drawn?

April 14

Looking into satisfying lock on rhythm adjustments

April 22

FIX AIMING

Aiming Idea - Just use Square recticle as RaySpawn?

How to always make it face forward? or where i want?

April 24

I THINK some locked on targets are getting recycled and causing issues - need to investigate this!

Playing with Player Moment script as well

Camera Follow + Player Movement determine screen space

Need to find a nice balance here

April 26

Some bullets hanging in mid air - not being added to musical lock list. Still on targets list

Approaches to solving this problem?

Tried moving Lock=true from OnLock to OnMusicalLock, didnt work. Game feel was off and ended up with list of Targets that were not being added to MusicalLockList

Another approach, If something is on Target list for X amount of time but not MusicalLockList then DO THING?

April 28

Playing with shapes - lock on shapes? - particle emitting shapes?

Where does this bring in an interesting second action for gameplay?

And how does this fit into/effect the structure of the music?

April 29

Change bullet movement from particle system to script based movement (translate based on speed)

This allows me to rewind movement with Chronos feature

Also allowed me to add a homing feature, with a switch can make bullet look at player and move towards them

May 6th

IDEA: Move whole dolly and system in lock on pattern?

May 7th

If at full combo, slow down time? testing different combo numbers / effects

Glitching during musical firing

Make a log of current ongoing systems

IDEA - Shoot out objects in a particular shape, lock on to make shape, then particle shooter switches to new shape

Construct a volume at those points - color of objects looks different through that

shooting applies that color to the rest of the scene

This allows you to prepare for the change - but what is this change? and it's value?

Mayy 11th

Shape as notation - different combos / different rhythms? notation and puzzles?

Release bullets as triangle that you connect together

Try doing a snare or kick release pattern for this - link together generate portal? or generate thing?

Triangles!!!

May 12

Replace Rayspawn in Cross hair script with Reticle - does the aim work better????

May 24

bullets now attach to reticle before launch, and if using time skip mechanic can reaim a cluster of bullets to a new position

May 25

Tried limiting number of rewinds possible

Still buggy!

May need to rethink this whole structure. Do I need Coroutines for all of this? Could probably just use methods / events? Look into this deeply!

May 27

JUST REALIZED i should set the puzzle as waypoints, not just forward speed. Look at how to set this up with Cinemachine Dolly, and how to start/stop at waypoints

IDEA: counterpoint like occurring here

May 29

Should I change the look at of bullets / laser so it's CowCatcherForward?

Will this fix my aiming issues?

June 9

Centering not working on lock on - center aim reticle to targets - reposition aim reticle

Seems to be a problem in the computer of center with X and Y

Is this due to negative/positive numbers? COME BACK TO THIS

Need to make better level design for lock on / shooting mechanic

June 10

Adding the Panzer Dragoon camera angle changing feature

Not working properly. Needs adjustment

Need to be additive with angle on player movement

How to rotate the aiming reticle?

June 11

Need to thoroughly look into how aim reticle is bounded

Errors happening when rotated

June 15

Several days downtime due to second covid shot

Looking to Panzer Dragoon for character control

Should I just chain player character to reticle?

Need to understand reticle movement better!

Design choice: Time Glitch and rotate or just allow rotation?? Maybe just allow rotation with sound effect?

June 18

Error - if rotating while moving downward, screws up perspective, need to fix this!!!

How to handle rotation platform rotating in any direction?

Have a function recognizing Y movement that changes rotation axis - not totally working, may need to adjust for oritentation - if 1 of 4 positions, rotate accordingly to next position on X or Z/Y axis

June 22

Add Tetris gameplay - very rough and needs work.

Try to make pieces lockable and transferable between perspectives?

Integrate EnemyBasics Script with game pieces script? Maybe need an edited version of this for these

June 23

Thinking out Tetris integration problems

Movement class constantly spawning new and object is not moving down - need to fix!

June 28

Really need to assess what I'm trying to make here. Maybe scale back, keep it simpler

July 1

Need to construct moving enemies - possibly with Emerald AI? or overkill?

Line of Sight attack, move along waypoint path, ranged attack options available

Also option to have them become ally - interesting!

July 9

Adding keyboard controls - spinning and mouse work

Fire not working

July 27

Rotating needs to adjust for rotation of Dolly path

If dolly turns causes issues for TriggerL/R rotations

Removed DOTween for rotation, hard coded, rotation working better now

NEEDS WORK - Rotate HARD to one of four positions

Adding radar to bullet enemies - working!

August 1

Looking at clamping Z movement of Reticle/Shooter

Reference original Starfox project for Reticle movement

Locked player right now to figure out reticle movement

Issues with clamping Reticle position

Find way to move bullets in opposite trajectory of their travelling direction

may need to change based on viewpoint?

Clamping breaks at high speeds!!

Need a different way to clamp- see what's wrong here

August 3

Adjust bullets so when switching perspectives, they fly opposite of the aim reticle

IDEA:

Allow reticle to move bullets around?

Aim Assist for firing bullets? This could be way too complicated imo

Maybe depends on enemy?

August 4

Thinking Cow Catcher will no longer work, actually causes errors

IDEA- CowCatcher causes bullets to reduce in speed if approaching from behind? collision causes slow down

Sensor Toolkit working! Line of sight working!

August 5

Adding hittable targets - destroyed on impact

Looking at radar - adding enemies and hittable targets - need to think of design philosophy here

THings need to happen for a reason

Some combination of xall response and more traditional gameplay as seen in orta or zwei - think how to meld thumper and these - fast paced but free enemies to grab or hit

August 11

Looking into objects not locking on and staying still

rb.constraints = RigidbodyConstraints.FreezeAll;

rb.constraints = RigidbodyConstraints.None;

IDEA: Move # of Locks into aiming reticle

IDEA: after locks, release of projectiles aims wherever cursor is pointing

pass reticle/rayspawn aiming to launchback function?

Need to shoot in general direction of rayspawn, hitting target isn't necessary

Maybe add Layer to things that can be hit? Maybe end of range OR target option

IDEA: Change shape of reticle depending on mode - lock vs shoot

August 12

Trying to pass a target (RaycastTarget) to Launchback command in order to aim the bullets at a specific point (the direction of the reticle)

setup tempV target to test homing of bullets

need to go over homing again

Launchback is creating sphere at target location - use these as lock on points for homing bullets?

Successfully creating sphere at target location and projectiles shoot towards them!

Switch this to a prefab that's destroyed on impact, maybe has a collision radius as well?

August 13

Using targetCube prefabs for targets of bullets now

Using Target Solo for collision detection script on these. Mostly working

Likely need a self destruct on these aiming cubes incase no collision

IDEA: only one cube as target instead of multiple, scaled according to number of bullets selected? EXPLORE

August 21

Collider view shown in video? What is this?

August 22

IDEA: TargetCubes are sticky, need X number on hitting/attached an enemy to kill them

Enemy has a counter for how many are currently touching

Certain can accept X numebr of noise, large amount overwhelms them

IDEA: One enemies type does not affect itself - need to shoot it at another enemy type

Enemies can recognize their own type of data, this makes sense. So we need to mix in ways it does not expect

August 27

Problems: StackOverflow errors when glitch duration is too low - sub 0.2 I think

September 1st

IDEA - Playing with Shapes + Tessera manipulation!!

Nov. 10

Didn't I add a pause button? What is it / why isn't it working??

Would like to change rotation - make it more elegant!

Nov. 12

Implement a rough version of character into Dreamteck - Forever - Collider Run

Looks promising

Nov. 13

Can use .NET seed to have consistent randomization - could I use this?

Public Follow method in Collider Run can be used to start movement

Nov. 14

Played with some of the Collider Run, using bullets doesnt seem to go anywhere

Making some enemy types, need to try integrating them into a level format

Thinking about best way to construct their movement

Wave system instantiates them

They move toward the player

They then move in a particular pattern form ?

Happens X times before they fly away

Nov. 20

Got the A* working - looking to integrate with Behaviour Designer

Nov. 28th

MakeChildOfEnemyPlane script on enemies

Maybe need to add local space after it becomes child?

Dec 7

Ignore Timescale property may be important for me

Dec 15

Tried adding a Player Radius to the Player, so that enemies using Seek stop at this radius - never too close to player

Not sure how target object is being assigned to Behavior Designer Prefabs. Look into this for appropriate behaviour

Also need to look into proper structure for Seek → Wander - Repeat type BD loop

Dec 19

PROBLEM is that it heads for center of object. Does just approach next to object depending on size at all. When player radius and player in same position and both targetted, both AI will end in same spot tho Player radius MUCH bigger then player

December 27

CHRONOS: Do enemies move backward in time or no??

Wander gets stuck while moving forward - cannot calculate position properly? Stuck on walls / corners. Seems the point chosen is not contexual to a moving navmesh - work arounds to this?

Might need reference points to navigate between - patrol but move out the way of incoming objects / scenery??

December 29

Fixed Trajectory issues! Much simpler implenetation in Enemy Basics class. Seems some of the Chronos stuff is not necessary for me?

Need to make enemy basics into a bullet script

December 31

Looking at best way to implement bullet movement - current translate implementation seems to be problem with current trajectory issues

January 2

Spawner + Dynamic Waypoint testing

What are the possible scenarios for optimal shooting at enemies?

gather up bullets on aiming device, fire back in a line

lock on to target enemy, all bullets aim at them

January 3

Exploring case 2

using homing and playerTarget in Projectile to get Projectile to target the enemies

Issues with waypoint verticality might be more to do with external behaviours then anything else? Tag name in BD - white vs greyed out difference??

Dyanmic waypoint testing and spawning 1 WORKS but not 2!

January 4

LOCK ON BUG

Locks list shows 1 but Targets shows no objects

how does this issue occur? Put in a check for discrepancy?

Tactical AI pack for my enemies!

Shoot and Scoot

Flank

Ambush

Surround

IDEA: A a dodge button for attacks? general dodge option within a certain closeness, or just attach dodging to the guiding of the reticle?

January 12

Think previous bullet error stem from crashing into each other. What to do in this case?

Bullets not moving properly?

Jan 13

IMPORTANT: Need projectile to check that target still exists or it will just get stuck when it’s already destroyed. Quick fix is now in for this

Judgement Silversword - SHIELD - should I do this? Or a dodge?

Jan 18

Bullets not moving properly?

Enemies not going to way points?

Jan 21

Consider alternatives for this - could i do smaller sections and flip between scenes? Or is that even necesary?

Jan 26

Enemy IN SCENE will generate pool once made active - but does not shoot!

Enemies are getting stuck though when trying to move around though

Jan 27th

Key Problems Today

A* graph while moving (noted yesterday)

Can’t lock on to enemies anymore (no blue outline or what?)

Bullets getting stuck - maybe have them destruct when collide with each other?

Jan 28

Investigate this, adapt Wave Spawner waves and inventory other issues

Jan 29

Bullets caught in circling loops when missing enemy target