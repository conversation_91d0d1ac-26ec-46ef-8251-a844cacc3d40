# Summary of 2020 - 2021 - Research and Inspiration.md

This file is a collection of research and inspiration notes from March 2020 to December 2021. It includes links to videos, tutorials, and articles, as well as general ideas and concepts explored during this period. Key themes and topics include:

- **Procedural Generation:** Research into procedural boss generation and level design techniques.
- **Enemy Design:** Ideas for enemy types, such as "Krang-like" enemies.
- **Post-Processing and Visual Effects:** Reviewing post-processing techniques and particle systems in Unity.
- **Animation Rigging:** Experimenting with animation rigging, specifically the "Cube Snake" tutorial.
- **Level Design Inspiration:** Drawing inspiration from games like Rez and Doom 2016, focusing on level structure, flow, and combat arenas. Exploring both linear and non-linear level design approaches.
- **Game Mechanics:** Investigating mechanics from games like Helix Jumper and Boomerang X.
- **Unity Features and Assets:** Researching and experimenting with Unity Events, Cinemachine, Behavior Designer, object pooling, and various assets from the Unity Asset Store (e.g., Space Journey, Mesh Tracer, Easy Performant Outline).
- **Code Optimization:** Looking into code performance optimization techniques in C# and Unity.
- **Aim Assist:** Exploring aim assist techniques for bullets.
- **Visual Novels:** Researching visual novel design.
- **MDA Framework:** Mention of the MDA framework for game analysis.