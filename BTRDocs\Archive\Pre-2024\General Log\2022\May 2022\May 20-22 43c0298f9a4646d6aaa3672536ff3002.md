# May 20-22

Testing faster build times abiltiy

Seems to take just under 10 minutes, not much better

Added a new scene to current list and rebuilding - looking at final build time- not looking great!

Still took just under 10 minutes

Not really better!

Gonna try asset cleaner

Not sure how it works - doesnt imply that theres much i can do either. 

Deleted librayr and rebuilt it - maybe save 10 gb? still quite large. 

Found something in reference to creating large worlds in unity - Floating Origin

[https://manuel-rauber.com/2022/04/06/floating-origin-in-unity/](https://manuel-rauber.com/2022/04/06/floating-origin-in-unity/)

If we move the origin than we can avoid errors that occur due to large numbers and floating point math that unity does

Also relveant to this apporach 

![Untitled](May%2020-22%2043c0298f9a4646d6aaa3672536ff3002/Untitled.png)

Reticle bouncign around - error is within Shooter Movement script

Looking at Vertices problem in blender - not able to reduce them even though face count is no  very low

Select all vertices and Delete Loose was helpful, though it might delete too much

Mesh → Cleanup → Delete Loose

Forward speed 300

Dolly at 3000

Takes just over 1 minute

<aside>
💡 Need to make LODS before using Prefabs, or else it screws them all up!

</aside>

GPU Instancer not working since LOD work - not sure why!

<aside>
💡 AutoLOD needs to have Save Meshes enabled and they need to be reassigned hwen dragging a prefab into it. Otherwise we get the GPU Instacer errors cause there are actually no meshes assocaited with other lod groups.

</aside>

Now I really need to learn how to use the PRofiler. TIme to watch yotuube videos and deep dive!

- Update loop taking a lot of resources - rotations cause of this?
    - Disable rotation and was correct - it works!
    

ShaderGraph Skybox and VFX Graph ideas - look into whats possible and what i can do here

Can Iuse my desctructed meshes for shader graph / vfx graph stuff?

[Skinned Mesh Sampling in Unity VFX Graph](https://www.youtube.com/watch?v=bIMyCKr0bFs)

Behavior Designer

- Attack
    - Stopping distance not working
    

Probably best to create my own task based off Scoot and Shoot - look into it!

Setting up new enemies

Assing Local Space Graph appropriately

Assign Enemy Bullet Pool in Scene according to rubric

FOV - 50 Might be a better adjustment

Testing FOV 30

Can I have a lower FOV and have the camera move around a bit more?

Need a RESET of reticle and camera position on transition phase

Added the function to the FMOD phase but dont seem to work 

Need to add function to disable / re enable controls as well

This is all in Scaled Down - BD and ASTAR Testing

Reset posoiton not working - goes to bottom corner of screen 

Trying to set player movement from shooter script to see if it’s more accurate

Wroked! PLayer movement coming from Shooting works best for opposite movemen

---

If you’re shooting, you can’t lock on to more bullets can you? What happens? 

I think you’re locked out?

You have to wait while you’re in firing mode

You can retarget while in firing mode

- You can redirect bullets to a new target if you learn the patterns and pay attention to how many it takes to kill an enemy

Think - how does this relate to changing the music? Could it affect anythnig?

Is there a way to highlight this??

<aside>
💡 Could I lock on to more than one enemy, and choose how many to send to each?

</aside>

<aside>
💡 duple or triplet pattern? Could this be automatic?

</aside>

Attempting to replace enemy targetting with enemyTargetQueue

Seems a Queue was better than a stack or a list - but maybe not when i need to remove a specific element! maybe a list is what really works

Back to a list!

Multi-lock on seems to be working 

But some bullets are getting stuck now

Do they need their target reassigned ? What is making them stuck?

May need to review how lists work when you remove an item. Do all the others move index to account for this?

Bullet issue

Stuck in a locked state - 3 in current scene while playing

![Untitled](May%2020-22%2043c0298f9a4646d6aaa3672536ff3002/Untitled%201.png)

All seem to have same attributes generally - negative lifetime, etc

Locked and released - but no launching state?

If Laucnhback was called on these, laucnhnig would be true - but its not!

CUrrent active bools also suggests that LauchAtEnemy was never called

Debugging with a PAUSE GAME if locked and released both occur

DId almost immediately - seems it’s not unusual! Maybe not why it’s getting stuck

Why isn’t it getting assigned one of two scenarios - shoot at enemy or shoot in direction?

Really think it’s something in OnMusicalShoot

When I only have MaxTargetLocks set to 1, I don’t have the issue!

**Could it be how the iterated index is set?**

How do I want to handle avoiding getting hit? Maybe have bullets aim for center of screen always - and then being at the edges allows you to escape getting hit

Changed so projecitle now aim for PlayerTarget instead of the player directly

To know that this is working / effective, might need a particle effect / FLASH to indicate player was hit

SOmethin gto this dodge to side of screen thing - think it needs faster aiming and smaller bullets

Need to adjust bullet speed - quite fast up close!! Maybe just a bit slower? Currently it is 700

Possible correction for bullets constantly spinning by player - if the bullet hits PlayerTarget but not Player - it loses tagret and proceeds forward

See if thsi clears up visual intensity with many bullets!

Nothing seems to be actually hitting Player Target - No debug message, no change in any bullets. Investigate this! And consider fi this is best approach