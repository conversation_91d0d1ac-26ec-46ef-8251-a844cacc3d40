# June 7

What is a game?

A painting conveys what it’s like to experience the subject as an image; a game conveys what it’s like to experience the subject as a system of rules. The player of games doesn’t just play within the system of rules, <PERSON><PERSON><PERSON> explains, she plays with the rules. As she plays, she continually assesses the state of the system, looks for opportunities to take advantage, identifies and implements the best strategy, modifies or glitch the rules to improve the experience. - Anna Anthropy

Core game 

Rez + Panzer Dragoon + Dodgeball?

Waves of enemies + Different areas per wave + different musical loops

Movement - Forced forward + look around

Beat Blast

- Sequencer decides when you can shoot - adjust when collecting items

Movement / dodging more critical when you cant decide when to shoot?

Ideating

Movement possibilities

- Thumper rhythmic action?
- Avoid objects?

What are mechanics in making music? How does one interact in a performance and also in composition?

<aside>
💡 Added stamina for LockOn so that it can’t be held for too long!

</aside>

Automatic fire when running out placed on OnMusicalLock

- Seems better than OnLock logically

Enemy bullet shooting sound for melodies / sonic element that we are responding to? 

Doesn’t work with a lot of bullets?

<aside>
💡 RESET added to stamina controller on lock on transition - release everything, Stamina bar should be reset to default

</aside>

Reverse time to deal with overhwleming bullets - implement proper musical chnage with this

Reverse Audio - use a cue tow swap currently playing track - swap back!

<aside>
💡 Added Rewind Parameter to Drum and Drum reverse track, so when rewinding we hear reverse perc

</aside>

Need more impressive reverse sounds!

Need more content in music !

Make a visually dynamix level

There’s a rhythm to being surrounded by enemies 

Push / Pull of Reverse time and Lock on to destroy

Overwhelmed by bullets

<aside>
💡 Added Drum FX logic and Parameter for more sound variation

</aside>

<aside>
💡 Added harmonic noise audio tracks to each loop sections

</aside>

Attributes that could be influenced by power ups

- Lock On Stamina
- Total Lock Ons
- Time Rewind Stamina

Data Mosh Unity

[https://ompuco.wordpress.com/2018/03/29/creating-your-own-datamosh-effect/](https://ompuco.wordpress.com/2018/03/29/creating-your-own-datamosh-effect/)

Can’t seem to get this or Kino Datamosh (cinemachine incomparitbility) working

Thinking on VFX Graph for this