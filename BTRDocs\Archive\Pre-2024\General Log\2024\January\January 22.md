# January 22

Remove indicators from bullets - too messy and cooler when they’re not there

Fixing lock on inidicators on shooter - currently cant see them, finding a solution

- Still not great, need to look at new material or different rendering methods
    - Make slightly emissive?

Changing lock on projectile behaviour so that if you attempt to lock on within a certain radius, it just shoots at the target enemy right away

Need to also address behaviour if there are no target enemies

- ideally it just fires off into the distance

Code is in there but needs to be verified, and lockableRadius needs to be tested

Busted it! Need to try this again 

- not too hard

Added press R to reload scene, so I can start over when using hot reload

Should expand this to jumping phases? or just kill enemies to quickly move to next phase?