# May 2

Small things to address before digging into May 1st Notes

Added Ricochet Dodge this replaces dodge

Adding basic particle effect for this - something i will likely replace later

Need to pick a day to sort through particle effects!

Parry still exists in project, likely just not very functional 

These are the details of a projectiel that refuses to be ricochet dodged

![Untitled](May%202%202a26c0c7985f4c97be2005b422bddd9f/Untitled.png)

This one does not have that issue

![Untitled](May%202%202a26c0c7985f4c97be2005b422bddd9f/Untitled%201.png)

Appears to be nothing differetn!

Here is a bullet i just ricocheted

![Untitled](May%202%202a26c0c7985f4c97be2005b422bddd9f/Untitled%202.png)

Nothing obvious in any of that

trying different settings

![Untitled](May%202%202a26c0c7985f4c97be2005b422bddd9f/Untitled%203.png)

Seems better with new values!

![Untitled](May%202%202a26c0c7985f4c97be2005b422bddd9f/Untitled%204.png)

Keep playing with this