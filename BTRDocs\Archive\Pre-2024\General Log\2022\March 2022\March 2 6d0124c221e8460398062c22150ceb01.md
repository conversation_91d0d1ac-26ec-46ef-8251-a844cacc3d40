# March 2

Adding Lock / Shoot / Tag sound effects for FMOD

Found an issue - 

all enemies need to reregister attack capabilitiles with currently playing track 

player as well!

Depending on section of song, everything needs to unregister and reregister for new events

How to structure this?

Player - shooting / All living Enemies? Just ones that exists for more then one section / environment attributes 

Game Manager 

Knows what current FMOD status is / playback area of song

Sends a call out to all applicable game objects to unregister current event and register new one 

Game Object

Needs to register itself with the game manager

Needs to unregister itself as well 

Contacted Koreographer support about this. 

![Untitled](March%202%206d0124c221e8460398062c22150ceb01/Untitled.png)

Example of issue - maybe im overthinking it? Just output tracks as one whole audio file, do analysis over all of that. May just be easier 

Try wait for it with one solid track, giving enemies full koreo for the entirety of the time

Response! Can do this I think? Will seamlessly switch? Have to test this out

![Untitled](March%202%206d0124c221e8460398062c22150ceb01/Untitled%201.png)