# Sept 12

Some things to address from last session

- ~~Make enemies brighter cant see them~~
- Add trail to snake enemies.
- ~~Need a reverse of mobius tube 5 for Secrtion 3, casue its normals are flipped and i dont need that for this part~~
- ~~In build, doesnt transition to next scene. Need to rectify this
Need a system of transition that works in dev and build~~
    - Working now, but some errors on build
- ~~Quit To Desktop is not working - only on steam deck~~
    - ~~dev build issue? not sure. made adjustments need to test~~
- There are still bullets getting stuck in spinning circles
- Internal to snake, enemies approach you and explode - you want to try and take them out before they do this
    - created behaviour for this section, need to buikld out the rest and test
- Projectile errors occuring in build, thigns glitching out and no projectiles shot at times by enemies , moving or static. Not sure what's going wrong! Test
    - Testing debugging on this

Look into this - Projectile Manager

![image.png](Sept%2012%206eda1778c22745ffae09306f546ba153/image.png)

What might fit other start logic?

ChronosKoreographyHandler created to make sure koreographer gets adjusted by time scale as i dont think it currently does

Making adjustments to projectiles so they handle the state where if beign rewound before their creation time, they will disappear

- Testing, seems to be working

Enemy Adjustments

- Adding robust line of sight to enemies, so that they are not hidden as much from player
- Trying to make maitnaned distance from player/each other better

Creating ChargeAndExplode Enemy for when inside of the Snake

- ChargeAndExplode Behaviour being created for this

In build, there are camera issue in both Snake Infinite sections. Not sure why camera is so fucked

There are times wehn enemies just dont shooty prtojectiles and i dont know why that is

Need better balance of bullet hitting player and missing player 

For game design sake, need to simplify how some of this stuff works. Here’s an approach

- if locked on to target, jsut do a laser effect and automatically hit the enemy. return projectile to pool
- From here, maybe develop a lock on system where you dont need to hold the button. depending on how good aim is at time of release, you will either shoot the projectile in a straight line OR the lock on is automatic - indicated by color change of reticle or soemthing design thing / movement to signify you’re locked in

Trying this new shooting system - current iteration does — The total damage from all projectiles will be summed up and applied to each locked enemy.

Sept 13/14

Updated shootuign method is working now, im killing enemies. Needs to be refined

Fixing up accuracy of the enemy shot projectiles as well, not sure ite being appleid properly so addressing this