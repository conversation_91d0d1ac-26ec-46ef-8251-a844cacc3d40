# Feb Round-up

Added Dev features like

- Lock and Camera shake
- Transparency for locked projectile
- particle effects for enemy birth/death

Feb 2 - was still trying to fix A* Issues! Adjusting scanning of area for better enemy movement 

Slow/Jagged movement issues were occuring

Bullet movement and reparenting issues were occuring

Added war drive effect!

Referred back to Mix and Jam for adjustments to camera movement

Feb 7th - Coding tips for games! Revisit this

Still was having A* issues at this point!

A* fix - Rigidbody is being used for physics, and if set to interpolate it fixes things!

Tried fixing wave spawning issues, doesn’t seem to move through cycles properly

Added platform level objects spawned by tempo OPS

Feb 8th - Start of looking at basics of FMOD - kart tutorial

Also Koreographer - register / unregister for events

Feb 10th - 20th

Mostly Ubisoft video materials, not well documented!

Feb 22nd

Started getting back into FMOD again

Was a rough week ahead of here, not a whole lot done 

Feb 25th

Started getting FMOD working with current audio in a very basic sense

Realize Chronos no longer works for audio glitching - got advice from <PERSON><PERSON> a whole day get Master Audio AAA working because I thought I was out of luck on FMOD LOL

Silly but I did learn it, get it working, and thought about using it

Later realized I could fake audio glitching in FMOD - <PERSON><PERSON><PERSON> suggested!

Decided on 2022-02-11 wait for it as new track for FMOD implementation of audio