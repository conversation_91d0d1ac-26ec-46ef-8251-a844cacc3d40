# 2022 - Research & Inspiration - Summary

This document summarizes the research and inspiration notes from 2022, highlighting key resources, themes, and areas of investigation.

## Key Summary Points:

*   **Unity and Unreal Engine Research (April):** Initial research focused on catching up with Unreal Engine developments and exploring Unity GDC streams, specifically for Tunic's post-processing techniques.
*   **Asset Store Exploration (April, June):** Frequent visits to the Unity Asset Store to discover tools and assets, including wave spawners (Spawn Ity), retro wave skyboxes, shaders (glass shader, distortion shader), particle effects (ARA Trails), and environment packs.
*   **Technical Documentation and Forums (April, March, May):**  Consulted documentation for Rewired, PoolManager, FMOD, and Unity features. Engaged with online forums like Unity and FMOD communities for troubleshooting and learning (e.g., FMOD timeline position assistance, Unity camera clear flags).
*   **YouTube Tutorials and Talks (Various Dates):**  Extensive use of YouTube for tutorials and inspiration, covering topics like Unity performance enhancements, object pooling, LOD groups, shader techniques, VFX Graph, and general game development advice. Channels like Gabriel A<PERSON>iar Prod and various Unity channels were referenced.
*   **Game Design and Mechanics Inspiration (April, May, Sept):**  Explored game design principles and mechanics from various titles, including Panzer Dragoon, After Burner, Patapon, Vib Ribbon, Intelligent Qube, Hyper Demon, and duality gameplay examples.
*   **Shader and VFX Research (June):** Investigated shaders for datamosh effects, simulations, and general shader modification. Explored VFX Graph for skinned mesh sampling and destructed assets.
*   **Code and Optimization Research (May, June, Dec):** Researched floating origin implementation in Unity, code optimization techniques, and general coding practices for game development.
*   **Mood Boards and Visual Style (Sept):** Utilized Figma for mood board creation to define the game's visual style and direction.

This summary reflects a broad approach to research and inspiration in 2022, combining technical investigations into Unity and audio middleware with artistic and design explorations, drawing from a wide range of online resources and game examples. The recurring theme is a strong interest in visual effects, performance optimization, and efficient Unity workflows.