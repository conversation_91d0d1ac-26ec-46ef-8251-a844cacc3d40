# Manual Component Migration Guide - Phase 3

## 🎯 **Safe Manual Migration Process**

**Why Manual?** Scene automation is risky. Manual migration ensures:

- ✅ **Zero scene corruption risk**
- ✅ **Settings preservation guaranteed**
- ✅ **Immediate verification possible**
- ✅ **Easy rollback if needed**

---

## 📋 **Pre-Migration Checklist**

### **Before Starting:**

1. ✅ **Backup Verified** - Git branches created and tested
2. ✅ **Compilation Clean** - Zero errors in Phase 2 migration
3. ✅ **Cadance System Ready** - All components available
4. ⚠️ **Unity Editor Open** - Required for manual migration

### **Required Knowledge:**

- Location of target scenes: `Assets/_Scenes/Levels/`
- Component replacement mapping (see below)
- How to add/remove components in Unity Inspector

---

## 🔄 **Component Replacement Guide**

### **1. Koreographer → Cadance (Main Singleton)**

**Location:** Usually on a "Koreographer" or "MusicManager" GameObject

**Steps:**

1. **Find GameObject** with `Koreographer` component
2. **Note Settings:**
   - Audio Source reference
   - Any Koreography asset references
   - Event delay settings
3. **Remove** `Koreographer` component
4. **Add** `Cadance` component
5. **Configure:**
   - Assign Audio Source (same as before)
   - Set Event Delay (copy from old component)
   - **Important:** Will need CadanceAsset (Phase 4)

### **2. SimpleMusicPlayer → AudioSourceCadancePlayer**

**Location:** Usually on "MusicManager" GameObject

**Steps:**

1. **Find GameObject** with `SimpleMusicPlayer` component
2. **Note Settings:**
   - Audio Clip reference
   - Loop settings
   - Volume settings
   - Any Koreography asset reference
3. **Remove** `SimpleMusicPlayer` component
4. **Add** `AudioSourceCadancePlayer` component
5. **Configure:**
   - Assign Audio Clip (same as before)
   - Copy loop/volume settings
   - **Important:** Will need CadanceAsset (Phase 4)

### **3. AudioSourceVisor → AudioSourceCadancePlayer**

**Location:** Various GameObjects with audio integration

**Steps:**

1. **Find GameObject** with `AudioSourceVisor` component
2. **Note Settings:**
   - Audio Source reference
   - Visor-specific settings
3. **Remove** `AudioSourceVisor` component
4. **Add** `AudioSourceCadancePlayer` component
5. **Configure:**
   - Assign Audio Source (same as before)
   - Configure for audio integration

### **4. KoreographedEventEmitter → CadancedEventEmitter**

**Location:** Various GameObjects that emit musical events

**Steps:**

1. **Find GameObject** with `KoreographedEventEmitter` component
2. **Note Settings:**
   - Event ID
   - Emission settings
   - Any payload configurations
3. **Remove** `KoreographedEventEmitter` component
4. **Add** `CadancedEventEmitter` component (from Stylo.Cadance.FMOD namespace)
5. **Configure:**
   - Set Event ID (same as before)
   - Copy emission settings
   - Configure payload if needed

---

## 🎯 **Scene-by-Scene Migration**

### **Scene 1: Ouroboros - Base.unity**

**Expected Components:**

- [ ] **MusicManager GameObject:**
  - [ ] Koreographer → Cadance
  - [ ] SimpleMusicPlayer → AudioSourceCadancePlayer
- [ ] **Audio Integration GameObjects:**
  - [ ] AudioSourceVisor → AudioSourceCadancePlayer
- [ ] **Event Emitter GameObjects:**
  - [ ] KoreographedEventEmitter → CadancedEventEmitter

**Migration Steps:**

1. **Open Scene:** `Assets/_Scenes/Levels/Ouroboros - Base.unity`
2. **Find MusicManager:** Look for GameObject with Koreographer components
3. **Migrate Components:** Follow component replacement guide above
4. **Save Scene:** Ctrl+S after each component
5. **Test:** Enter Play Mode to verify no errors

### **Scene 2: Ouroboros - Scene 1.unity**

**Expected Components:**

- Similar structure to Base scene
- Scene-specific VFX triggers (already migrated in Phase 2)
- Musical timing components

**Migration Steps:**

1. **Open Scene:** `Assets/_Scenes/Levels/Ouroboros - Scene 1.unity`
2. **Repeat Process:** Same as Base scene
3. **Verify:** Check for scene-specific components

---

## ⚠️ **Important Notes**

### **Components Already Migrated (Phase 2):**

- ✅ **KoreoVFXTrigger** - Scripts updated, no component changes needed
- ✅ **Custom Scripts** - All 8 core scripts already use Cadance
- ✅ **Event Registry** - KoreographerEventRegistry → CadanceEventRegistry (script level)

### **Asset References (Phase 4):**

- 🔄 **Koreography Assets** - Will be converted to CadanceAssets in Phase 4
- 🔄 **Audio Clips** - References will be preserved
- 🔄 **Event IDs** - Will work with new EventID attribute system

### **Testing After Each Component:**

1. **Save Scene** immediately
2. **Check Console** for errors
3. **Enter Play Mode** briefly
4. **Exit Play Mode** if no errors

---

## 🚨 **Troubleshooting**

### **If Component Not Found:**

- Check GameObject name variations
- Look in child objects
- Component might already be migrated

### **If Settings Lost:**

- Revert scene from git backup
- Take screenshots before migration
- Copy settings to notepad first

### **If Errors Appear:**

- Check console for specific error
- Verify component namespace (Stylo.Cadance)
- Ensure all Phase 2 scripts are properly migrated

---

## ✅ **Verification Checklist**

### **Per Scene:**

- [ ] All Koreographer components removed
- [ ] All Cadance components added and configured
- [ ] Scene saves without errors
- [ ] Play Mode enters without errors
- [ ] No missing component warnings

### **Overall:**

- [ ] Both target scenes migrated
- [ ] All components functional
- [ ] Ready for Phase 4 (Asset Migration)

---

## 🎯 **Success Criteria**

**Phase 3 Complete When:**

- All Koreographer components replaced in both scenes
- All Cadance components properly configured
- Scenes load and enter Play Mode without errors
- No missing references (except Koreography assets - Phase 4)

**Estimated Time:** 2-3 hours for careful manual migration
