# Feb 22

Implemented hacky LookAt solution so player game object stops rotating 

Need a better long term solution but works for now 

Figured out orientation anchor issue with Curvy Spline

Its a feature in its custom menu that disabled. Re enable it not under gizmos but under the curvy spline menu in the scene view

Can finally fix orientation issues!

Started implementation of color picked based on enemy type shooting the projectile. will set the color parameter. not working, needs finalization.

Because of how it would globally affect a shared material, have decided against this. 

Doing a material swap instead. 

Need Kanban board or proper issue tracker for game bugs