# Video Script Revised 3

CC on video for hearing impaired

Need a logline - or aspects of it - in the dialogue

Focus on feelings?

MORE EVOCATIVE

Hi, I’m <PERSON>, 

I started WaveJam Studios, 

where i’m fusing music and games - to take people into a digital galaxy of rhythm action - welcome to <PERSON> Traveller 

Since I was a kid, I felt a connection with music

I learned how to make it, and found a community I love. Creating and sharing music all across Canada 

I took that love of sound into audio-post production, and later into designing how people can engage with audio in different ways 

- Harvest video
- include Gamma Space / TMAC byline on video

I’ve found lots of people who feel a deep connection with music the way I do

I love to share my passion for sound, and I’m always thinking about new ways to do it

As a musician, you take years of practice, make 100’s of choices, and narrow it all down to a single song for an audience

It made me think, how can I let the audience join in?

- Show controller
- Musical Cue Here - Mic Drop

With Beat Traveller, you dive in and become the music! Propel across space and time in a variety of interactive musical worlds, where the music changes with how you play.  

Explore a galaxy where your actions affect and add to the music, like the melodies heard here

All while travelling through musically reactive digital worlds

Start your journey as a Beat Traveller on the outer rim of the audio galaxy, diving deep into sub-bass hits at supersonic speeds, all on a search for the source of the galactic groove!

Players can take things in at a casual tempo, or ramp up the intensity for a lightspeed fury of music and challenges. 

With your help, I can make Beat Traveller out of this world

and together, we can share the music with everyone

Thank you

**Script direction 1**

Many music games are all about quick and precise reactions, punishing the player when things aren’t done correctly

But that’s not how a good performance feels, or how people listen to music

- It’s not the only way to engage with music

In Beat Project, players see the creativity in their approach (playstyle) reflected in the music around them

Gmae description

Beat Traveller is a barrage of rhythm action moving at lightspeed through a cyberspace universe. It’s for people who like mind-melting visuals and otherworldly beats. Start your journey as Beat Traveller on the outer rim of the audio galaxies, diving deeper into sub-bass hits and planetary vortexes to find the illustrious source of the groove!

Enjoy tons of handcrafted levels and ricochet your way through a pulsing musical journey taking you across a variety of digital galaxies and keeping you on the edge of your seat! Aim fast to collect and rebound beats as they come flying towards you – every beat adding to the fury of music in your orbit.

**Refer to Powerpoint**

Where did this idea come from?

Strong Love of music all my life – going to shows, performing, recording in musical projects etc.
Pandemic – Solo music experiences
Listening or writing music

Thinking about large gap between listening and writing music

As a listener, I wanted to go deeper into the sounds I heard, and how they fit together
As a writer, I wanted to give people the sense of play and access I had with how all these sounds fit together

Is there any accessible way for people to play in, and enjoy the same musical process that musicians do?

PROBLEM
Electronic music / DJ culture / Dance music touches on this
Remixing sounds is a large part of the style
But what if we could go further
Responsive Visuals
Accessible Game Systems

Build on that interaction and playfulness, existing in a space between linearity of listening to music and playfulness of writing and performing it

**Script direction 2**

Start with more of a question?

What if you could interact with a song?

Not just following the beat, but dictating where it goes?