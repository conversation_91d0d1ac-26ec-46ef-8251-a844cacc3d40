# January 10th, 2024

Trying to do a build of a scene, upgrading things and moving to laptop seem to bring up old issues

Compiling shaders causes build to take over an hour or more, not letting it finish. For one scene, should be much quicker I think. Tried preloading shader variants under graphics settings, will see if this moves initial build quicker. Will have to do more research if that doesn't fix the problem

This error happening on transition - player mesh had disappeared - maybe dodge related?

![Untitled](January%2010th,%202024%206ab92944aeed4d9c85ad8aad0b842133/Untitled.png)

Use gravity rush enemies as inspiration for my enemies - look up the various types 

![Untitled](January%2010th,%202024%206ab92944aeed4d9c85ad8aad0b842133/Untitled%201.png)

Want to apply this to text and ui in Unity

[https://twitter.com/ashlee3dee/status/1745524506872176970](https://twitter.com/ashlee3dee/status/1745524506872176970)

Maybe look at Ace Combat, emulates this a bit - search for this