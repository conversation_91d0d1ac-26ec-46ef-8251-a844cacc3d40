%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 54247856159ee8c4a8e8b8760ba0db07, type: 3}
  m_Name: Basic Enemy
  m_EditorClassIdentifier: 
  showCoreSettings: 1
  showLayerSettings: 1
  showCombatSettings: 1
  showProjectileSettings: 0
  showExplosionSettings: 0
  showShieldSettings: 0
  showReferences: 0
  showEffects: 0
  showPhaseSettings: 0
  maxHealth: 10
  moveSpeed: 20
  isVulnerable: 1
  clockKey: Global
  obstacleLayerMask:
    serializedVersion: 2
    m_Bits: 8
  enemyLayerMask:
    serializedVersion: 2
    m_Bits: 64
  shieldableLayerMask:
    serializedVersion: 2
    m_Bits: 0
  minPlayerDistance: 10
  maxPlayerDistance: 100
  hitsToKillPart: 3
  attackRange: 12
  attackCooldown: 1
  damageAmount: 10
  projectilePrefab: {fileID: 0}
  projectileSpawnPoint: {fileID: 0}
  hitSound:
    Guid:
      Data1: 0
      Data2: 0
      Data3: 0
      Data4: 0
    Path: 
  deathSound:
    Guid:
      Data1: 0
      Data2: 0
      Data3: 0
      Data4: 0
    Path: 
  hitEffect: {fileID: 0}
  deathEffect: {fileID: 0}
  projectileSpeed: 30
  projectileLifetime: 10
  projectileScale: 1
  projectileDamage: 10
  enableHoming: 1
  projectileAccuracy: 0.8
  explosionRadius: 3
  explosionDamage: 50
  explosionTriggerDistance: 0.5
  shieldRotationSpeed: 180
  shieldArcDegrees: 120
  shieldDamageReduction: 0.8
  shieldRegenerationDelay: 5
  shieldBreakThreshold: 100
  shieldProtectionRadius: 3
  enemyPrefab: {fileID: 919132149155446097, guid: 0d368e8b816fe4841933a3a6d86cef89, type: 3}
  spawnEffectName: EnemySpawn
  deathEffectName: EnemyDeath
  hitEffectName: EnemyHit
  explosionEffectName: ExplosionEffect
  lockDisabledEffectName: LockDisabled
  shieldEffectName: ShieldEffect
  shieldBreakEffectName: ShieldBreakEffect
  shieldHitEffectName: ShieldHitEffect
  shieldRegenerationEffectName: ShieldRegenEffect
  phase1Distance: 10
  phase1Duration: 10
  phase2Distance: 5
  phase2Duration: 5
  explosionDistance: 0.5
  koreographyEventID: 
  musicSoundEvent:
    Guid:
      Data1: 0
      Data2: 0
      Data3: 0
      Data4: 0
    Path: 
  spawnSoundEvent:
    Guid:
      Data1: 0
      Data2: 0
      Data3: 0
      Data4: 0
    Path: 
  deathSoundEvent:
    Guid:
      Data1: 0
      Data2: 0
      Data3: 0
      Data4: 0
    Path: 
  hitSoundEvent:
    Guid:
      Data1: 0
      Data2: 0
      Data3: 0
      Data4: 0
    Path: 
