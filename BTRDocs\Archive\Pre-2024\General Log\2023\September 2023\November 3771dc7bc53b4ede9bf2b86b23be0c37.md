# November

Nov. 4th

Worked on how to create UV maps in Blender

Tex Tools is great - high poly / low poly models, in same location. 

Easy way to create Normal Maps

Look into other maps it can create, may be useful

Smart UV wrap is better in new version of blender, so may work well for now

Plan out levels - <PERSON>hanim approach? 

move through rings, enemies coming at you from front / back 

Maybe some shots happening from sides but not part of the waves - static enemies

Move through several rings - enter interior cube - eyes on all sides

Shooting? Take out each eye

DESTROYED

Move to next one?

REMEBER - background must move if current player position isnt

Boss? Ophanim - from a different perspective

More complex, more to deal with 

Ouroboros

Move between multiple snakes that are static in ouroboros form

take out enemies on them

Move to moving snakes, taking out enemies on adjacent snakes backs

travelling - arrive at destination - a boss

the snakes that represent forward and backward time

Nov. 5th

Lighting theory for Games Video

Diffuse Maps - what is it?

Keep lighting separate when possible, you dont want two lights affecting the same area

Know what you want first = work out how later 

Get lots of references!! and settle on a key concept/style

![Untitled](November%203771dc7bc53b4ede9bf2b86b23be0c37/Untitled.png)

[https://jakehicksphotography.com/quick-tips](https://jakehicksphotography.com/quick-tips)

![Untitled](November%203771dc7bc53b4ede9bf2b86b23be0c37/Untitled%201.png)

![Untitled](November%203771dc7bc53b4ede9bf2b86b23be0c37/Untitled%202.png)

In general, mixing diffuse colors is not a good idea

Color banding issues, need to watch out for (also called color bleeding)

Neutral colors help avoid these issues

They also can handle both cold and warm lighting

Nov. 6th

Figured out how the datamosh stuff works / why it wasnt working in my project

Have a few variations saved in Beat Remake now

Nov. 7th

Figuring out needed associations for Player Plane as prefab object

Moved Buto to effects category, added script to parent it to player on awake

Probably need to get rid of the shooting thing where it reverses for the Camera

Expect it’s causing issues 

Consider a

BUG!!!!!!!!!!!!!!

Idea: World is run by machines and not human free will - Deluze and Foucault

[Deleuze - Control Societies & Cybernetic Posthumanism](https://www.youtube.com/watch?v=Hu4Cq_-bLlY)

Machines - Systems of information processing that interpret and remake the world according to their particular logics. Ex. Society is social, politcal, and more machines. 

Machines - a system of interruptions - a break in the flow in relation to the machine it is connected, but at at the same time is a flow itself

work on inputs → produce outputs

The process informations

Outputs of one machine are the inputs of another (usually)

Societies of Sovereignty, discipline, and control 

Discipline - regulate bodies - movement and time enclosed - restricted

Self-regulated - Self-discipline - voluntary to do this - put obvious why you would

The state only interferes with your lie when you’re breaking the rules 

Steering and Cybernetics

cybernetics means systems that use feedback loops to interpret information and elimate deviation over time

Behaviour Steering Machines

![Untitled](November%203771dc7bc53b4ede9bf2b86b23be0c37/Untitled%203.png)

NOv. 8th 

Making this Black Hole Shader

[Remaking the Black Hole Shader from Outer Wilds! | Unity Shader](https://www.youtube.com/watch?v=hNkPHPhzXVA)

Not working in 2022.3 Unit URP LTS, raised issue with dev, will see if I hear back. 

Played with kejiro’s du tone and Aqua effect

Got them into project

Input Actions - Icons of different inputs

[https://github.com/simonoliver/InputSystemActionPrompts](https://github.com/simonoliver/InputSystemActionPrompts)?

Good for controllers - settings - setup

Shader info 

[https://www.mayerowitz.io/blog/a-journey-into-shaders](https://www.mayerowitz.io/blog/a-journey-into-shaders)

Unity SOLID talk - Programming principles

[https://www.reddit.com/r/Unity3D/comments/17icly2/how_using_solid_changed_my_whole_development/](https://www.reddit.com/r/Unity3D/comments/17icly2/how_using_solid_changed_my_whole_development/)

[https://www.youtube.com/watch?v=eIf3-aDTOOA](https://www.youtube.com/watch?v=eIf3-aDTOOA)

GPU Particle System

[https://assetstore.unity.com/packages/tools/particles-effects/ultimate-gpu-particle-system-131718](https://assetstore.unity.com/packages/tools/particles-effects/ultimate-gpu-particle-system-131718)

VFX structuring in Unity

[https://www.reddit.com/r/Unity3D/comments/17bksum/what_is_the_right_approach_for_managing_vfx_and/](https://www.reddit.com/r/Unity3D/comments/17bksum/what_is_the_right_approach_for_managing_vfx_and/)

Alternative Pathfinding Option - GOAP

[https://assetstore.unity.com/packages/tools/behavior-ai/cap-n-3d-pathfinding-using-goap-258946?aid=1101l96nj&pubref=oct23tools&utm_campaign=unity_affiliate&utm_medium=affiliate&utm_source=partnerize-linkmaker](https://assetstore.unity.com/packages/tools/behavior-ai/cap-n-3d-pathfinding-using-goap-258946?aid=1101l96nj&pubref=oct23tools&utm_campaign=unity_affiliate&utm_medium=affiliate&utm_source=partnerize-linkmaker)

Dynamic Mesh Cutter - alternative to RayFire?

[https://forum.unity.com/threads/dynamic-mesh-cutter-a-flexible-run-time-mesh-cutting-solution-discussion-thread.1249558/](https://forum.unity.com/threads/dynamic-mesh-cutter-a-flexible-run-time-mesh-cutting-solution-discussion-thread.1249558/)

Unity Event Bus - implement into my project?

Reflect transparent materials in mirrors etc

[https://github.com/DMeville/RefractedTransparentRenderPass](https://github.com/DMeville/RefractedTransparentRenderPass)

Nov. 11th

Some effects not working in void area when in certain camera positions - investigating

Swirl - and like others, the issue is Render modee - Horizaontal Billboard

Billboards can be problematic with certain camera angles

Adjust to mesh render mode?

Worked!  Mostly!

Now having duplicate key erros with mesh scan A* (i suspect)

Need alternate sphere model i suspect

Seemingly fixed this sphere problem with al sphere model

Looking into why enemy ai are not movign around now

Need to ensure that moving assignment of FMOD to Awake in enemies does not cause errors

Issues with enemies not moving - seems affected by navmesh cuts and recalculate normals on the A star was the problem! At least according to a rebuild based on Ouroboros (that works)

That was the problem!!

Annoying 😟

Found issue with unity crashing

Has to do with Enemy Particle object spawner on emeies and what pools are assigned to it

Containers assigned in scene - maybe cant be assigned to itself?

Issues with projectile render layers - putting void projectile on default due to this

Crashing again! Seems projectiles aren’t moving - problem something to assign

Death Particle system missing from projectile

Nothing checked after it’s shot

Weird behaviour from bullet in void level - no differences found to account for why just yet

THings to try - original standard bullet

Not helping! Need to break down whats going on in the one working project - Ouroboros

Why does it appear as if the bullets are bound within a box?

![Untitled](November%203771dc7bc53b4ede9bf2b86b23be0c37/Untitled%204.png)

Bullets are free to move when I get rid of the navmesh - why????

THis would make sense with the Ouroboros working, because those levels are encapsulated in a box by their form

Unity Crashing - Hard to know whats happening, possibly a Particle System Issue

Removing Hot Reload to see if it is contributing

All bullets are shooting downward now - unsure why? 

THink maybe i Need to remove bottom from halfsphere as well, may be confusing things?

THe bullets are all getting trapped inside here

NOv 13

Why are there box colliders on the dollys? Is this causing an issue? 

It seems not!

Really hard time trying to diagnose this! Need to try other structures

Need to build a whole new level directly from Ouroboros level

Flipped normals for interior sphere works!!

Can refine this

Exterior Black hole

Interior Black Hole

Maybe interior of black hole is very dark, lock on to bullets lights things up?

Nov. 14th

Running through Cursor coder suggestions

[Small List of Suggestions from Cursor](November%203771dc7bc53b4ede9bf2b86b23be0c37/Small%20List%20of%20Suggestions%20from%20Cursor%2010e89b71ec3848adba5f267fa888cd0e.md)

[Big List of Cursor Suggestions](November%203771dc7bc53b4ede9bf2b86b23be0c37/Big%20List%20of%20Cursor%20Suggestions%20d497fcc7d478486091f8876625d3614e.md)

Trying to add ability for projectiles to bounce

Not working - need to investigate further

Made many small improvements to main scripts

Added Game Manager Singleton script to take care of score, will handle more as well in time

ISSUE - Trying to put shooter movement on it’s own global clock / timeline and it’s not working

Need to review this - if I do this, it should mean smoother movement - test and see!

Can Time.deltaTime work at all? Concurrently with Chronos? Need to investigate

Making a Miro Board

Created Light Flasher script, for void level. Looking at timing a light to the beat. 

Could also time a light to locking on / shooting enemies

Need to configure lighting properly

Implemented basic dodge

Need to update logic so holding dodge doesnt just continually allow you to dodge!

Need to add Vibrate on dodge - added using open source Nice Vibrations!

[1. Nice Vibrations developer documentation](https://github.com/Lofelt/NiceVibrations/wiki/1.-Nice-Vibrations-developer-documentation#haptic-components)

Nov. 19th

May need to go back to old rotation script from yesterday or day before.  Adjustments needed for Player Movement rotation . Bug testing!

Fixed, but the player game object is rotating slightly as the game progresses, after several rotations

[Boomerang X Abilities List](November%203771dc7bc53b4ede9bf2b86b23be0c37/Boomerang%20X%20Abilities%20List%20cb6d59f175a640a8ba25d0d594b6fcb6.md)

Nov. 30th

[Dreamscaper: Killer Combat on an Indie Budget](https://www.youtube.com/watch?v=3Omb5exWpd4)

Key Areas to focus on

- Establish Strong Pillars - define feeling of player experience and turn that into actionable things
    - find the right level of specificity “player should feel highly skilled, an assassin, godlike,”
    - if the pillar is too general, the target area is too wide
- Dreamscape Combat Pillars
    - Purposeful Action
    - Improvisational - player freedom, multiple approaches
    - Tough, but fair
    - Dynamic Interactions - create unique situations and solutions
    - Strong Feedback - sound, vfx, aniamtion should do this, make it feel good.
- Player Options
    - options create depth, but also complexity - “mental juggling”
    - cosnider core audience for level of complexity
        - whats appropriate? mid to hardcore for Dreamscape
        - Depth without complexity?
            - Swappable weapons would be an example
    - Consider your pillars
        - How do these options move you towards your design targets?
        - How is it implemented?
            - Dodge - invincible with generous length would be an example
    - Consider your scope
        - Can you hit your quality bar?
        - Is it too big for your team?

Camera

- be careful of camera-genre expectations
    - this will influence player expectations
- Distance, interactivity, and scope

Not every decision will move you towards all goals at once, so find alternatives ways to emphasize parts you feel are neglected

Stack systems for richer combat

Elemental systems used that can be stacked for critical hits

Hit Reactions & Enemy States

Why set up enemy states?

- increases interactivity
- provides added utility and depth to player actions

Some states

- Stun & Stagger
- Flyback - space between player and enemy, resets offensive situation
- Popup - enemy in juggle state, helps encourage offensive action in defensive players
- Knockdown - helps with shifting focus between enemies

Bringing it altogether

- Flyback combined with knockdown for example
- Popup (uppercut) increased damage as continues

Adds depth without complexity

High payoff for player here

Enemy States

Large degree of states adds interactivity

Flow - state interruptions, recovery timing, stun locking

The way these work influence purposeful player action, target selection, and more

Players have to learn how to manage enemies around them

Challenge

- not all enemies have all hit states, stronger ones immune to some
- adds element of progression to the game

Game Feel

Responsiveness

- input response time / input latency
- Animation blending
    - Shorter blend times feel more responsive
- Locomotion response
    - reduce acceleration period to increase responsiveness
- Favor player input
    - players can interrupt various actions
        - players break out of almost any action with a defensive response

Animation

- Rely on core principles of animation
    - 80s Disney book on animation a good reference

Important principles

- Anticipation and Follow through
- Secondary and overlapping action
- Slow in / Slow Out - helps create beliable weight
- Exaggeration & strong posing
- Translate Forward - bake some translation into the move, not just in place

Enemy animation

- Make the output of their action as readable as possible (telegraphing)
    - Clear poses
    - Strong Anticipation
    - Sound Design
    - Visual Effects
    - Proper Hierarchy
    - Bake recovery time into the animation, feels more natural
- Players must realize they have the information they need to defeat the enemy by watching them, judging their attacks and making choices
- Creating enemy animation is not the same as player animation, which is to make things FEEL as good as possible

Hit Reactions

- Instant Reaction - no blending, happens right away
- Exaggerated Poses
    - rotate enemy towards direction of hit instead of animating every possiblity - probably good enough!
- Hit Reactions sell strength of player action!
    - One size doesnt fit all, larger enemies might respond differently
- Variety sells interactivity

Cheat for your player 

- Aim Assist
    - recommended to make these configurable, players may need a stronger aim assist
- Magnetism
    - makes sure the attack hits an enemy
- Hit Impulse
- Input Buffering
    - register early inputs for combos. be generous so players can land complex moves easier
- Generous Hit Boxes

Juice it up - game feel!

- Hit Flash
- Hit Reaction
- Hit Impulse
- Hit Stop / Time Dilation
- Hit VFX
    - Power, type and style of attack important consideration for the fx
- Screen Shake
    - screen shake on player much less than screen shake hitting an enemy
- Controller Feedback
- Beefy Audio
- Procedural Enemy Shake

May be important to allow player to turn all of these things off. Makes it more accessible?

General Keys To Success

- view work through a neutral lens
    - divorce personal attachment, focus on what player experiences
- trimming doesnt mean eliminating
    - think features, MVP of some of these
- focused on goal, rather than solution

Test often and Iterate Cheaply

- Need a feedback pipeline
- Open to testing drastic changes
- Do just enough to evaluate outcome of these

Invest in workflows that use prior knowledge

Leverage outside assets

build on others learnings

Loading Scenes - best ways to do so?

[I beg you: Dont use the buildindex for loading Scenes](https://www.reddit.com/r/Unity3D/comments/1888oax/i_beg_you_dont_use_the_buildindex_for_loading/)

Added Flow Fx - try it out in game