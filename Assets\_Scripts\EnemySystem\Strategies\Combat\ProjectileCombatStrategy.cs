using UnityEngine;
using System.Collections.Generic;
using BTR;
using BTR.EnemySystem;

namespace BTR.EnemySystem
{
    /// <summary>
    /// Combat strategy that fires projectiles at targets.
    /// Migrated from ProjectileCombatBehavior to the new strategy pattern.
    ///
    /// Configuration Behavior:
    /// - Fields marked with [ProjectileReadOnly] are overridden by EnemyConfiguration when present
    /// - Inspector values serve as fallbacks when no configuration is assigned
    /// - Custom inspector shows which values come from configuration vs. inspector
    /// </summary>
    public class ProjectileCombatStrategy : CombatStrategy
    {
        // Using Unity Debug.Log for logging

        [Header("Projectile Settings")]
        [SerializeField] private Transform[] projectileOrigins;
        [ProjectileReadOnly][SerializeField] private float projectileSpeed = 10f; // Overridden by configuration
        [ProjectileReadOnly][SerializeField] private float projectileDamage = 10f; // Overridden by configuration
        [ProjectileReadOnly][SerializeField] private float projectileLifetime = 5f; // Overridden by configuration
        [ProjectileReadOnly][SerializeField] private float projectileScale = 1f; // Overridden by configuration
        [SerializeField] private int projectilesPerAttack = 1;
        [SerializeField] private float spreadAngle = 0f;
        [ProjectileReadOnly][SerializeField] private bool useHoming = false; // Overridden by configuration

        [Header("Configuration")]
        [SerializeField] private EnemyConfiguration configuration;

        [Header("Audio Settings")]
        [SerializeField] private string attackSoundEvent = "enemy_projectile_fire";

        // Projectile tracking
        private List<GameObject> activeProjectiles = new List<GameObject>();
        private int currentOriginIndex = 0;

        protected override void PerformCombatInitialization()
        {
            // Using Unity Debug.Log for logging

            // Apply configuration settings if available
            ApplyConfigurationSettings();

            if (projectileOrigins == null || projectileOrigins.Length == 0)
            {
                // Create default origin at entity position
                projectileOrigins = new Transform[] { entityTransform };
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs)
                {
                    Debug.Log($"Using entity transform as default projectile origin on {gameObject.name}");
                }
#endif
            }

            // Initialize projectile tracking
            activeProjectiles.Clear();
            currentOriginIndex = 0;

#if UNITY_EDITOR || DEVELOPMENT_BUILD
            if (enableDebugLogs)
            {
                Debug.Log($"Initialized on {gameObject.name} - Damage: {projectileDamage}, Speed: {projectileSpeed}, Lifetime: {projectileLifetime}s, Scale: {projectileScale}, Homing: {useHoming}");
            }
#endif
        }

        protected override void PerformCombatActivation()
        {
            // Clear any existing projectiles
            CleanupProjectiles();

#if UNITY_EDITOR || DEVELOPMENT_BUILD
            if (enableDebugLogs)
            {
                Debug.Log($"Combat activated on {gameObject.name}");
            }
#endif
        }

        protected override void PerformCombatDeactivation()
        {
            // Clean up projectiles when deactivating
            CleanupProjectiles();

#if UNITY_EDITOR || DEVELOPMENT_BUILD
            if (enableDebugLogs)
            {
                Debug.Log("Combat deactivated");
            }
#endif
        }

        protected override void PerformCombatCleanup()
        {
            // Clean up all projectiles
            CleanupProjectiles();

#if UNITY_EDITOR || DEVELOPMENT_BUILD
            if (enableDebugLogs)
            {
                Debug.Log("Combat cleaned up");
            }
#endif
        }

        protected override void PerformCombatUpdate()
        {
            // Validate ProjectileManager availability
            if (ProjectileManager.Instance == null)
            {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs)
                {
                    Debug.LogWarning($"ProjectileManager.Instance is null on {gameObject.name}");
                }
#endif
                return;
            }

            // Debug combat state every few seconds
#if UNITY_EDITOR || DEVELOPMENT_BUILD
            if (enableDebugLogs && Time.time % 3f < 0.1f) // Log every 3 seconds
            {
                string targetName = currentTarget != null ? currentTarget.name : "NULL";
                string range = currentTarget != null ? Vector3.Distance(entityTransform.position, currentTarget.position).ToString("F1") : "N/A";
                Debug.Log($"Combat State - InCombat: {InCombat}, CanAttack: {CanAttack}, Target: {targetName}, Range: {range}/{attackRange}");
            }
#endif

            // Clean up destroyed projectiles
            CleanupDestroyedProjectiles();
        }

        protected override bool ShouldAttack()
        {
            // Attack if we have a target and ProjectileManager is available
            bool shouldAttack = currentTarget != null && ProjectileManager.Instance != null;

            if (!shouldAttack)
            {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs)
                {
                    // Enhanced debugging for ProjectileManager issues
                    string pmStatus;
                    if (ProjectileManager.Instance != null)
                    {
                        pmStatus = $"Available (Initialized: {ProjectileManager.Instance.IsInitialized}, Active: {ProjectileManager.Instance.IsActive})";
                    }
                    else
                    {
                        // Check if there's a ProjectileManager GameObject in scene
                        var pmInScene = FindFirstObjectByType<ProjectileManager>();
                        if (pmInScene != null)
                        {
                            pmStatus = $"Found in scene but Instance is NULL (GameObject: {pmInScene.gameObject.name}, Active: {pmInScene.gameObject.activeInHierarchy})";
                        }
                        else
                        {
                            pmStatus = "No ProjectileManager found in scene";
                        }
                    }

                    string targetName = currentTarget != null ? currentTarget.name : "NULL";
                    Debug.Log($"ShouldAttack = false on {gameObject.name} - Target: {targetName}, ProjectileManager: {pmStatus}");
                }
#endif
            }
            else
            {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs)
                {
                    Debug.Log($"ShouldAttack = true on {gameObject.name} - Target: {currentTarget.name}");
                }
#endif
            }

            return shouldAttack;
        }

        protected override void ExecuteAttack()
        {
            if (ProjectileManager.Instance == null || currentTarget == null)
            {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs)
                {
                    Debug.LogWarning("Cannot execute attack - missing ProjectileManager or target");
                }
#endif
                return;
            }

            // Fire projectiles
            for (int i = 0; i < projectilesPerAttack; i++)
            {
                FireProjectile();
            }

            // Play attack sound
            PlayAttackSound();

#if UNITY_EDITOR || DEVELOPMENT_BUILD
            if (enableDebugLogs)
            {
                Debug.Log($"Fired {projectilesPerAttack} projectiles at {currentTarget.name}");
            }
#endif
        }

        private void FireProjectile()
        {
            // Validate ProjectileManager
            if (ProjectileManager.Instance == null)
            {
                Debug.LogError("ProjectileManager.Instance is null!");
                return;
            }

            // Get projectile origin
            Transform origin = GetNextProjectileOrigin();
            if (origin == null)
            {
                Debug.LogWarning("No valid projectile origin found");
                return;
            }

            // Calculate direction to target
            Vector3 direction = (currentTarget.position - origin.position).normalized;
            Quaternion shootRotation = Quaternion.LookRotation(direction);

            // Apply spread if configured
            if (spreadAngle > 0f)
            {
                float randomAngle = Random.Range(-spreadAngle, spreadAngle);
                direction = Quaternion.AngleAxis(randomAngle, Vector3.up) * direction;
                shootRotation = Quaternion.LookRotation(direction);
            }

            // Spawn projectile using ProjectileManager unified interface (supports new system)
            var projectileInterface = ProjectileManager.Instance.SpawnProjectileInterface(
                origin.position,
                shootRotation,
                projectileSpeed,
                projectileLifetime,
                projectileScale,
                projectileDamage,
                useHoming,
                null, // material
                currentTarget,
                false // isPlayerProjectile
            );

            if (projectileInterface != null)
            {
                // Track projectile GameObject for cleanup
                activeProjectiles.Add(projectileInterface.GameObject);

#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs)
                {
                    Debug.Log($"Projectile spawned successfully ({projectileInterface.GetProjectileSystemType()}) - Speed: {projectileSpeed}, Damage: {projectileDamage}, Lifetime: {projectileLifetime}s");
                }
#endif
            }
            else
            {
                Debug.LogWarning("Failed to spawn projectile from ProjectileManager");
            }
        }

        private Transform GetNextProjectileOrigin()
        {
            if (projectileOrigins == null || projectileOrigins.Length == 0)
                return entityTransform;

            Transform origin = projectileOrigins[currentOriginIndex];
            currentOriginIndex = (currentOriginIndex + 1) % projectileOrigins.Length;

            return origin != null ? origin : entityTransform;
        }



        private void CleanupProjectiles()
        {
            foreach (GameObject projectile in activeProjectiles)
            {
                if (projectile != null)
                {
                    Destroy(projectile);
                }
            }
            activeProjectiles.Clear();
        }

        private void CleanupDestroyedProjectiles()
        {
            activeProjectiles.RemoveAll(projectile => projectile == null);
        }

        private void PlayAttackSound()
        {
            if (string.IsNullOrEmpty(attackSoundEvent))
                return;

            // Try to play FMOD sound if available
            try
            {
                // This would integrate with your existing FMOD system
                // For now, just log the sound event
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs)
                {
                    Debug.Log($"Playing sound: {attackSoundEvent}");
                }
#endif
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"Failed to play sound {attackSoundEvent}: {e}");
            }
        }

        // Public API for configuration
        public void SetProjectileOrigins(Transform[] origins)
        {
            projectileOrigins = origins;
            currentOriginIndex = 0;
        }

        public void SetProjectileSpeed(float speed)
        {
            projectileSpeed = Mathf.Max(0f, speed);
        }

        public void SetProjectileDamage(float damage)
        {
            projectileDamage = Mathf.Max(0f, damage);
        }

        public void SetProjectilesPerAttack(int count)
        {
            projectilesPerAttack = Mathf.Max(1, count);
        }

        public void SetSpreadAngle(float angle)
        {
            spreadAngle = Mathf.Clamp(angle, 0f, 180f);
        }

        public void SetProjectileScale(float scale)
        {
            projectileScale = Mathf.Max(0.1f, scale);
        }

        public void SetUseHoming(bool homing)
        {
            useHoming = homing;
        }

        /// <summary>
        /// Apply settings from EnemyConfiguration if available
        /// </summary>
        private void ApplyConfigurationSettings()
        {
            if (configuration != null)
            {
                // Apply projectile settings from configuration
                projectileSpeed = configuration.projectileSpeed;
                projectileDamage = configuration.projectileDamage;
                projectileLifetime = configuration.projectileLifetime;
                projectileScale = configuration.projectileScale;
                useHoming = configuration.enableHoming;

                // Apply combat settings from configuration
                SetAttackCooldown(configuration.attackCooldown);
                SetAttackRange(configuration.attackRange);

#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs)
                {
                    Debug.Log($"Applied configuration settings - Speed: {projectileSpeed}, Damage: {projectileDamage}, Lifetime: {projectileLifetime}s, Scale: {projectileScale}, Homing: {useHoming}, Cooldown: {attackCooldown}s, Range: {attackRange}");
                }
#endif
            }
            else
            {
                // Try to find configuration from parent entity
                var entity = GetComponent<CombatEntity>();
                if (entity != null && entity.Configuration != null)
                {
                    configuration = entity.Configuration;
                    ApplyConfigurationSettings(); // Recursive call with found configuration
                    return;
                }

#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs)
                {
                    Debug.Log($"No configuration found, using serialized values - Homing: {useHoming}, Cooldown: {attackCooldown}s, Range: {attackRange}");
                }
#endif
            }
        }

        /// <summary>
        /// Set the configuration for this strategy
        /// </summary>
        public void SetConfiguration(EnemyConfiguration config)
        {
            configuration = config;
            ApplyConfigurationSettings();
        }

        // Debug information
        public int ActiveProjectileCount => activeProjectiles.Count;
        public bool IsHomingEnabled => useHoming;
        public EnemyConfiguration CurrentConfiguration => configuration;

#if UNITY_EDITOR
        [ContextMenu("Test Fire Projectile")]
        private void TestFireProjectile()
        {
            if (Application.isPlaying && currentTarget != null)
            {
                FireProjectile();
            }
        }

        [ContextMenu("Debug Configuration Status")]
        private void DebugConfigurationStatus()
        {
            string configName = configuration != null ? configuration.name : "NULL";
            string configHoming = configuration != null ? configuration.enableHoming.ToString() : "N/A";

            Debug.Log($"Configuration Status on {gameObject.name}:\n" +
                     $"- Configuration: {configName}\n" +
                     $"- Homing Enabled: {useHoming}\n" +
                     $"- Config Homing: {configHoming}\n" +
                     $"- Speed: {projectileSpeed}\n" +
                     $"- Damage: {projectileDamage}");
        }
#endif
    }

    // Interface for damage dealing (would be defined elsewhere in your system)
    public interface IDamageDealer
    {
        void SetDamage(float damage);
    }
}
