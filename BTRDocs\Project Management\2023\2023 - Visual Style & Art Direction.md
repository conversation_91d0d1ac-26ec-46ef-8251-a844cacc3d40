

*   **January 10th**:
    *   Thinking about visuals - light, fog, design, etc.
    *   Great video on constructing beautiful scenes using lighting.
    *   Added Beautify and Voulmetric Fog to project.
    *   Volmetric fog doesn’t look that great - may be better assets to try for this look
    *   Trying BOXOPHOBIC’s fog
    *   Thinking about buying BUTO fog asset because it looks good in videos
    *   How do these things interact with GPU Instancer?
    *   Seems to work, not super elegant looking but works ok - maybe a solid temporary solution
    *   Switched to Kronnect’s Volumetric Fog, seems better! Sticking with this for now
    *   Trying to extract shader of Monk animation from Pilgrimmage
    *   Model Joost like character - use ai possibly to achieve this
    *   AI not working out I think - tried metshape
    *   Use Fspy in Blender and model myself? Might not take very long
    *   Character Art Inspo - solar ash designs

*   **January 11th**:
    *   Getting reacquianted with <PERSON><PERSON>der - working with <PERSON> model to imitate a Joost Egggermoent design.
    *   Subdivide - Fractal is a very cool effect
    *   UV unwrapping
*   **January 12**:
    * Test colour of faces on model
    * Subdivision Surface - Round’s out elements - look into this for certain elements
    * Test colour of faces on model
    * Painted Faces, issue with exporting model WITH all it’s materials
    *Rough particle effect happening for running forwards
    * Look for a better Particle effect for footsteps

*   **February 19th**:
    *   colour palette - develop one!
    *   shadows never black - blue?
    *   look at color theory
    *   adobe colour
    * Depth Testing in this shader - do this effect?
    *   Fixed sizing on light trails of diamond enemies
*   **February 22**:
    * Geometry - Enemy Shapes - Combine these + behaviour in different ways
* Recursion - Serpinski Triangle!
* Recursion in Music -  - Geometry of music?
*   **March 2**:
    *   Tower of Babylon Concept
*
    * Breaking through some kind of layer? sort of like AT Field?
*   **March 6**:
Need to fix enemy and bullet stat colors to be the same what’s on the radar

*   **March 8**:
    * UI To scale properly to different aspect ratios
 Need to fix Radar for this as well
*   **March 10**:
Need to learn shader:
texture maps
Normal maps
Emission Maps

Need to add something to look cooler
How should 
Do I need different Materials for different colors of same shader? Just use GPU Instancing?

Added alternative control schemes
Not sold any on any yet but I think they’re moving in the right direction

Ophanim Rings turn into tower pieces many rings into structure?
Rings split off into many pieces?

GAMMA SPACE

*   **March 13**:
Adding SRDebugger for more efficient on-device debugging

Tying this into my UI

*   **March 14**:
Make these like Control - need sound effect - when transitioning to different part or level
-   Make the design distinct from other levels
-   Added alternative control schemes
    *   Not sold any on any yet but I think they’re moving in the right direction

*Need to disable some Post Processing effects on during transitions - like motion blur
Added this is camera switch feature, motion blue should be disabled and re enabled on next wave
This is working! May just want to lower motion blur instead of disable - not sure yet!

Combine these things for interesting visual effects - reference Solar Ash possibly
*   **March 15**:
  Added Unity built-in distance fog to have things fade off into distance better
New “Slow” parameter in Fmod

*   **March 19**:
Trying to add Koreographer to the speeds of the Fast Glitch

Concepts Ideas for levels / bosses

Metatron as a celestial scribe and archangel, could be a powerful boss with the ability to manipulate reality itself
void Behemoth: This boss could be a monstrous, shadowy creature that lurks in the emptiness of space. It could attack by using its massive claws to swipe at the player and summoning waves of darkness to obscure the player's vision

Jungian Ouroboros: In Carl Jung's theory of psychology, the Ouroboros represents the process of individuation, or the journey of self-discovery and integration of the psyche. It is a symbol of the eternal cycle of death and rebirth and the integration of the shadow self.
Each boss could represent a different aspect of human nature, such as pride, ambition, or fear.
As the tower is split, and things are divided, different aspects of the tower manifested into different beings / bosses

-Levels are:

1.  The Fall of Babel
2.  Echoes of the Past
3.  The Timekeeper's Domain
4.  The Infinite Loop
5.  The Ascension
6.  Requiem for Babel
7.  The Infinite Loop

*   **March 22**:
    *   Alpha clipping on projectiles.
    * Alpha clipping on projectiles - will this solve my issues with them looking better / fading out when they get farther away? Use a better outline shader - 2022 options?
    *   Check if HDR on and off makes a big difference to steam deck performance / pc performance.
*   **March 25**:
    * Elimated screen similar to rollerdrome Use the full screen shader effect
    *    Tried a new outline shader - full screen effect in Unity 2022, pretty cool!
*   **March 28**:
    * Also working on Creatues in Blender. 
    * Need to keep in mind, Roller Drome objectives list
*   **May 1:**
        May need to make some scenes better
*   **May 2:**
* Need to change these to enemies and get shoot and scoot working Or another attack / move combo

*   **May 5:**

   After some tehcnical issues- back in action!

  Trying to fix this with Cinemachine Camera BlendingNever found a good way to do it, trying to just script it all now 

New features and designs:
*Need to free up the Y value of the camera - cant look up enough. Important in this level especially  

 * Also only operation on forward facing at the moment. Caused problems when in reverse, need to program this to work in all four directions.
*   **May 18**:
* To improve
* I need this because in order to
This is all the info the code need to

*   **May 19**:
*Try X ray effect ideas
Highlights in the scene
What is a
*    I am going to create
A system that allows the user to create a
Which allow

* **May 23**
* To improve:
      * Lightening settings
 *    * Need to resolve this projecitle issue
 * Make it more visual

* **May 24**:
To take advantage of new features

* **May 25**
* I want the following

* **September 1**:
   * Implement gravity - what does it do to help the game code,
       It does: helps by allowing the models to feel a certin type of connection
* To ToDo
 * Design, can be applied to this as well, design does the same but with a lot more, what does it do to
 What is it and how can I improve
*   **September 6:**
*   Improve the game better, create something that is unique with high quality that I can
-       -       And then I will work it through
*   **September 2**
   * To Make Better:
 - The player more attractive to a level as they are able to complete the different elements in the world
 * Improve with code and design

* To ToDo

* **November**
*Look at new levels with Ophanim approach
    * What type of level does this cause
 * Look at the different types of levels

*   **November 5**:
*Lighting theory for Games Video
Diffuse Maps - what is it?
Keep lighting separate when possible, you dont want two lights affecting the same area
Get lots of references!! and settle on a key concept/style

  In general, mixing diffuse colors is not a good idea
  Color banding issues, need to watch out for (also called color bleeding)
  Neutral colors help avoid these issues
* They also can handle both cold and warm lighting

*   **November 6**:
 * Figured out how the datamosh stuff works / why it wasnt working in my project
* Have a few variations saved in Beat Remake now

