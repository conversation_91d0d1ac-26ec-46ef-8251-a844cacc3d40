# Wave System - Level Progression

*Created: 2025-07-19*
*Status: Current - Core System*
*System Location: `Assets/_Scripts/Management/`, `Assets/UltimateSpawner2.0-WavesAddOn/`*

## Overview

The **BTR Wave System** is a comprehensive level progression framework that combines **music-synchronized gameplay**, **node-based wave management**, and **dynamic scene progression**. Built around the concept of musical sections driving gameplay, it provides a unique rhythm-action experience where wave completion triggers both scene transitions and musical changes.

## System Architecture

```mermaid
flowchart TB
    %% Core Management
    SGM[SceneManagerBTR<br/>Scene Progression Core]
    WEC[WaveEventChannel<br/>Event Coordination]
    SM[ScoreManager<br/>Progression Tracking]
    
    %% Wave Management
    WSC[WaveSpawnController<br/>UltimateSpawner Core]
    WC[WaveConfiguration<br/>Node-Based Design]
    WN[Wave Nodes<br/>Visual Editor]
    
    %% Configuration Assets
    CA[Configuration Assets<br/>Data-Driven Design]
    SG[SceneGroup<br/>Level Sequences]
    WCA[Wave Config Assets<br/>Enemy Patterns]
    SS[Song Sections<br/>Musical Progression]
    
    %% Progression Systems
    PS[Progression Systems<br/>Game Flow]
    ST[Section Transitions<br/>Musical Progression]
    SC[Scene Changes<br/>Level Advancement]
    EP[Enemy Progression<br/>Dynamic Spawning]
    
    %% UI and Feedback
    UI[UI and Feedback<br/>Player Communication]
    WHT[WaveHUDTimed<br/>Wave Status Display]
    WTM[WaveTextManager<br/>Animated Feedback]
    LS[Loading Screens<br/>Transition Management]
    
    %% Integration Systems
    IS[Integration Systems<br/>Framework Connection]
    FMOD[FMOD Audio<br/>Music Synchronization]
    Stylo[Stylo.Cadance<br/>Beat Synchronization]
    ES[Enemy System<br/>Spawn Coordination]
    
    %% Performance Monitoring
    PM[Performance Monitoring<br/>System Health]
    MO[Memory Optimization<br/>Resource Management]
    EM[Error Management<br/>Recovery Systems]
    PT[Performance Tracking<br/>Metrics Collection]
    
    %% Relationships - Core Flow
    SGM --> WEC
    SGM --> SM
    WEC --> WSC
    WSC --> WC
    WC --> WN
    
    %% Configuration Flow
    SGM --> CA
    CA --> SG
    CA --> WCA
    CA --> SS
    
    %% Progression Integration
    WEC --> PS
    PS --> ST
    PS --> SC
    PS --> EP
    
    %% UI Integration
    WSC --> UI
    UI --> WHT
    UI --> WTM
    SGM --> LS
    
    %% System Integration
    SGM --> IS
    IS --> FMOD
    IS --> Stylo
    IS --> ES
    
    %% Performance Integration
    SGM --> PM
    PM --> MO
    PM --> EM
    PM --> PT
    
    %% Styling
    classDef core fill:#f9f,stroke:#333,stroke-width:2px
    classDef wave fill:#bbf,stroke:#333,stroke-width:2px
    classDef config fill:#bfb,stroke:#333,stroke-width:2px
    classDef progression fill:#fbb,stroke:#333,stroke-width:2px
    classDef ui fill:#ffb,stroke:#333,stroke-width:2px
    classDef integration fill:#fbf,stroke:#333,stroke-width:2px
    classDef performance fill:#bff,stroke:#333,stroke-width:2px
    
    class SGM,WEC,SM core
    class WSC,WC,WN wave
    class CA,SG,WCA,SS config
    class PS,ST,SC,EP progression
    class UI,WHT,WTM,LS ui
    class IS,FMOD,Stylo,ES integration
    class PM,MO,EM,PT performance
```

## Core Components

### **SceneManagerBTR** (Scene Progression Core)
- **Role**: Central coordinator for scene and musical progression
- **Pattern**: Singleton with async scene management
- **Location**: `Assets/_Scripts/Management/SceneManagerBTR.cs`

**Key Features**:
- Scene group management with musical synchronization
- Progressive scene loading with memory optimization
- Spline-based track progression
- Integrated error recovery and performance monitoring

### **WaveSpawnController** (UltimateSpawner Core)
- **Role**: Node-based wave management and enemy spawning
- **Pattern**: SpawnController with event-driven architecture
- **Location**: `Assets/UltimateSpawner2.0-WavesAddOn/Scripts/WaveSpawnController.cs`

**Key Features**:
- Visual node editor for wave design
- Dynamic wave progression with conditions
- Enemy spawn tracking and coordination
- Integration with BTR event systems

### **WaveEventChannel** (Event Coordination)
- **Role**: Central event hub for wave lifecycle management
- **Pattern**: Event channel with UnityEvents integration
- **Location**: `Assets/_Scripts/Events/WaveEventChannel.cs`

**Key Features**:
- Wave lifecycle events (Start, Complete, Custom)
- Section management events
- Enemy spawn coordination
- Cross-system communication

### **ScoreManager** (Progression Tracking)
- **Role**: Score and progression metrics management
- **Pattern**: Singleton with time-based mechanics
- **Location**: `Assets/_Scripts/Management/ScoreManager.cs`

**Key Features**:
- Time-based health decay system
- Wave progress tracking across scenes
- Kill streak and scoring integration
- Performance metrics collection

## Music-Synchronized Progression

### **Section-Based Progression**
The BTR Wave System is uniquely built around musical sections that drive gameplay progression:

```csharp
[System.Serializable]
public class SongSection
{
    [Tooltip("Human-readable name for this section")]
    public string name;
    
    [Tooltip("Musical section timing (0.0 = start, 1.0 = end)")]
    public float section;
    
    [Tooltip("Expected number of waves in this section")]
    public int waves;
    
    [Tooltip("Minimum time to spend in this section")]
    public float minSectionTime = 0f;
    
    [Tooltip("Visual effects intensity for this section")]
    public float intensityMultiplier = 1f;
}
```

### **Musical Integration Architecture**
```csharp
// Scene progression tied to musical timing
public async UniTask ProgressToNextSection()
{
    var currentSection = GetCurrentMusicSection();
    var nextSection = GetNextMusicSection();
    
    // Wait for minimum section time
    if (Time.time - sectionStartTime < currentSection.minSectionTime)
    {
        await UniTask.WaitUntil(() => 
            Time.time - sectionStartTime >= currentSection.minSectionTime);
    }
    
    // Trigger musical transition
    AudioManager.Instance.ChangeSongSection(sceneGroup, nextSection.section);
    
    // Update visual intensity
    SetVisualIntensity(nextSection.intensityMultiplier);
    
    // Progress wave system
    WaveEventChannel.Instance.OnSectionStarted?.Invoke(nextSection.name);
}
```

### **Stylo.Cadance Integration**
```csharp
// Beat-accurate enemy spawning
public class MusicSyncedWaveSpawning : MonoBehaviour
{
    [SerializeField] private BeatDetector beatDetector;
    [SerializeField] private WaveSpawnController waveController;
    
    private void OnBeatDetected(BeatInfo beatInfo)
    {
        // Spawn enemies on specific beat patterns
        if (beatInfo.beatInBar == 1) // Spawn on downbeat
        {
            waveController.TriggerNextWaveSegment();
        }
        
        // Synchronize wave progression with musical timing
        if (beatInfo.barCount % 4 == 0) // Every 4 bars
        {
            CheckWaveProgressionTrigger();
        }
    }
}
```

## Node-Based Wave Management

### **Wave Configuration System**
The system uses UltimateSpawner's visual node editor for designing complex wave patterns:

```csharp
public class WaveConfiguration : NodeGraph
{
    // Visual node-based wave design
    public WaveStartNode GetStartNode();
    public WaveParameterNode GetParameterNode();
    public int GetConnectedNodeCountOfType<T>() where T : WaveNode;
    
    // Wave flow configuration
    public enum WaveNodeType
    {
        None = 0,
        Start,      // Initial wave trigger
        Condition,  // Conditional branching
        Delay,      // Time-based delays
        Event,      // Custom event triggers
        Loop,       // Repeating patterns
        Wave,       // Main wave definition
        SubWave,    // Nested wave patterns
    }
}
```

### **Dynamic Wave Progression**
```csharp
public class WaveSpawnController : SpawnController
{
    // Event-driven wave management
    public UnityEvent OnWaveStarted;
    public UnityEvent OnWaveEnded;
    public UnityStringEvent OnWaveCustomEvent;
    public event Action<Transform> OnEnemySpawned;
    
    // Wave state tracking
    public int CurrentWave { get; }
    public int TotalWavesCount { get; }
    public int CurrentWaveSpawnedItemCount { get; }
    public int CurrentWaveDestroyedItemCount { get; }
    
    // Dynamic wave execution
    public void StartWave(int waveIndex)
    {
        currentNode = waveConfig.GetStartNode();
        
        // Navigate through node graph
        while (currentNode != null)
        {
            ProcessCurrentNode();
            currentNode = GetNextNode();
        }
    }
}
```

### **Wave Configuration Assets**
The system includes pre-configured wave patterns for different game sections:

- **Ouroboros Series**: `WaveConfig - Ouroboros - First/Second/Third Wave Series - V1.asset`
- **Boss Encounters**: `WaveConfig - Ouroboros - Midboss - V1.asset`
- **Advanced Patterns**: `WaveConfig - Ophanim - First Wave Series - V1.asset`

Each configuration defines:
- Enemy spawn patterns and timing
- Conditional progression logic
- Custom event triggers
- Loop and branching behaviors

## Scene and Level Progression

### **SceneGroup Management**
```csharp
[System.Serializable]
public class SceneGroup
{
    [Tooltip("Human-readable name for this scene group")]
    public string name;
    
    [Tooltip("Scenes in this group, in order")]
    public SceneListData[] scenes;
    
    [Tooltip("Musical sections for this group")]
    public SongSection[] songSections;
    
    [Tooltip("Total expected waves across all scenes")]
    public int totalExpectedWaves;
    
    [Tooltip("Performance parameters for this group")]
    public ScenePerformanceConfig performanceConfig;
}
```

### **Progressive Scene Loading**
```csharp
public class SceneManagerBTR : MonoBehaviour
{
    // Async scene progression
    public async UniTask LoadNextSceneAsync()
    {
        // Performance monitoring
        var startTime = Time.realtimeSinceStartup;
        
        // Memory optimization
        await OptimizeMemoryBeforeLoad();
        
        // Preload assets
        var preloadTask = PreloadSceneAssetsAsync();
        
        // Show loading screen with progress
        var loadingTask = ShowLoadingScreenAsync();
        
        // Load scene with error recovery
        try
        {
            await UniTask.WhenAll(preloadTask, loadingTask);
            await LoadSceneWithErrorRecovery();
        }
        catch (Exception ex)
        {
            await HandleSceneLoadError(ex);
        }
        
        // Performance validation
        ValidateSceneLoadPerformance(startTime);
    }
    
    // Memory optimization before scene loads
    private async UniTask OptimizeMemoryBeforeLoad()
    {
        // Clean up previous scene resources
        Resources.UnloadUnusedAssets();
        System.GC.Collect();
        
        // Wait for cleanup to complete
        await UniTask.Yield();
        
        // Optimize audio memory
        AudioManager.Instance.OptimizeMemoryUsage();
    }
}
```

### **Spline-Based Track Progression**
```csharp
// Visual track progression tied to musical sections
public class SplineProgressionController : MonoBehaviour
{
    [SerializeField] private SplineComputer splineTrack;
    [SerializeField] private Transform playerVisualization;
    
    private void UpdateTrackProgression()
    {
        // Get current musical section progress
        var musicProgress = AudioManager.Instance.GetCurrentSectionProgress();
        
        // Convert to spline position
        var splinePosition = ConvertMusicProgressToSplinePosition(musicProgress);
        
        // Update visual representation
        playerVisualization.position = splineTrack.EvaluatePosition(splinePosition);
        
        // Trigger visual effects based on progression
        if (HasCrossedSectionBoundary(splinePosition))
        {
            TriggerSectionTransitionEffects();
        }
    }
}
```

## Performance and Optimization

### **Memory Management**
```csharp
public class WaveSystemMemoryOptimizer : MonoBehaviour
{
    [SerializeField] private float memoryCheckInterval = 5f;
    [SerializeField] private float memoryPressureThreshold = 0.8f;
    
    private void Update()
    {
        if (Time.time % memoryCheckInterval < Time.deltaTime)
        {
            CheckMemoryPressure();
        }
    }
    
    private void CheckMemoryPressure()
    {
        var memoryUsage = GetCurrentMemoryUsage();
        
        if (memoryUsage > memoryPressureThreshold)
        {
            // Optimize wave system memory usage
            OptimizeWaveSystemMemory();
            
            // Clean up inactive enemies
            CleanupInactiveEnemies();
            
            // Reduce visual effects quality
            ReduceVFXQuality();
        }
    }
}
```

### **Performance Monitoring**
```csharp
public class WaveSystemPerformanceMonitor : MonoBehaviour
{
    // Performance metrics
    public float AverageFrameTime { get; private set; }
    public int ActiveWaveCount { get; private set; }
    public int TotalEnemiesSpawned { get; private set; }
    public float MemoryUsagePercent { get; private set; }
    
    // Performance alerts
    public event Action<PerformanceAlert> OnPerformanceAlert;
    
    private void MonitorPerformance()
    {
        // Track frame time
        AverageFrameTime = CalculateAverageFrameTime();
        
        // Monitor wave system health
        ActiveWaveCount = GetActiveWaveCount();
        TotalEnemiesSpawned = GetTotalEnemiesSpawned();
        MemoryUsagePercent = GetMemoryUsagePercent();
        
        // Check for performance issues
        if (AverageFrameTime > targetFrameTime * 1.5f)
        {
            OnPerformanceAlert?.Invoke(new PerformanceAlert
            {
                type = AlertType.FrameTime,
                severity = AlertSeverity.Warning,
                message = $"Frame time elevated: {AverageFrameTime:F1}ms"
            });
        }
    }
}
```

## UI and Player Feedback

### **Wave Status Display**
```csharp
public class WaveHUDTimed : MonoBehaviour
{
    [SerializeField] private TextMeshProUGUI waveText;
    [SerializeField] private TextMeshProUGUI nextWaveText;
    [SerializeField] private float waveTextDisplayTime = 2.0f;
    
    private void OnWaveStarted()
    {
        // Animated wave start feedback
        waveText.text = "<shake>ACTIVATE</shake>";
        waveText.enabled = true;
        
        StartCoroutine(DisableWaveTextAfterTime(waveTextDisplayTime));
        
        // Show progression hint
        if (currentWaveNumber > 1)
            StartCoroutine(ShowNextWaveHint());
    }
    
    private IEnumerator ShowNextWaveHint()
    {
        nextWaveText.color = Color.white;
        nextWaveText.enabled = true;
        
        yield return new WaitForSeconds(waveTextDisplayTime);
        
        // Fade out animation
        while (nextWaveText.color.a > 0)
        {
            var temp = nextWaveText.color;
            temp.a -= 0.05f;
            nextWaveText.color = temp;
            yield return new WaitForSeconds(0.1f);
        }
    }
}
```

### **Loading Screen Management**
```csharp
public class LoadingScreenManager : MonoBehaviour
{
    [SerializeField] private CanvasGroup loadingCanvasGroup;
    [SerializeField] private TextMeshProUGUI progressText;
    [SerializeField] private Image progressBar;
    
    public async UniTask ShowLoadingWithProgress(IProgress<float> progress)
    {
        // Fade in loading screen
        await FadeInLoadingScreen();
        
        // Monitor progress updates
        progress.ProgressChanged += OnProgressChanged;
        
        // Wait for completion
        await UniTask.WaitUntil(() => progressBar.fillAmount >= 1.0f);
        
        // Fade out loading screen
        await FadeOutLoadingScreen();
    }
    
    private void OnProgressChanged(object sender, float progress)
    {
        progressBar.fillAmount = progress;
        progressText.text = $"Loading... {progress * 100:F0}%";
    }
}
```

## Integration Points

### **Stylo Framework Integration**
- **Stylo.Cadance**: Beat-accurate wave spawning and musical synchronization
- **Stylo.Epoch**: Time manipulation effects during wave transitions
- **Stylo.Flux**: Visual effects synchronized with wave progression
- **Stylo.Reservoir**: Efficient enemy and projectile pooling

### **FMOD Audio Integration**
- **Musical Section Changes**: Automatic audio transitions based on wave completion
- **Dynamic Audio**: Intensity changes based on wave difficulty
- **Spatial Audio**: 3D positioned audio for wave events
- **Audio Performance**: LOD system for wave-related audio

### **Enemy System Integration**
- **Dynamic Spawning**: Enemy configurations change based on wave progression
- **Behavior Adaptation**: Enemy AI adapts to current wave context
- **Performance Scaling**: Enemy complexity scales with hardware capabilities
- **Audio Coordination**: Enemy audio synchronized with wave system

## Configuration and Usage

### **Basic Wave Setup**
```csharp
// Configure scene group with wave progression
[SerializeField] private SceneGroup ouroborosGroup = new SceneGroup
{
    name = "Ouroboros Campaign",
    scenes = new SceneListData[]
    {
        CreateSceneData("Ouroboros_Section_1", 4),
        CreateSceneData("Ouroboros_Section_2", 6),
        CreateSceneData("Ouroboros_Section_3", 8),
        CreateSceneData("Ouroboros_Section_4", 10)
    },
    songSections = new SongSection[]
    {
        new SongSection { name = "Intro", section = 0.0f, waves = 4 },
        new SongSection { name = "Build", section = 0.25f, waves = 6 },
        new SongSection { name = "Climax", section = 0.5f, waves = 8 },
        new SongSection { name = "Resolution", section = 0.75f, waves = 10 }
    }
};
```

### **Wave Event Handling**
```csharp
public class WaveProgressionHandler : MonoBehaviour
{
    private void Start()
    {
        // Subscribe to wave events
        WaveEventChannel.Instance.OnWaveStarted += OnWaveStarted;
        WaveEventChannel.Instance.OnWaveCompleted += OnWaveCompleted;
        WaveEventChannel.Instance.OnSectionStarted += OnSectionStarted;
    }
    
    private void OnWaveStarted(int waveNumber)
    {
        Debug.Log($"Wave {waveNumber} started!");
        
        // Trigger visual effects
        VFXManager.Instance.TriggerWaveStartEffect();
        
        // Update UI
        UIManager.Instance.UpdateWaveDisplay(waveNumber);
        
        // Adjust audio intensity
        AudioManager.Instance.SetIntensityParameter(
            Mathf.Lerp(0.3f, 1.0f, waveNumber / (float)totalWaves));
    }
    
    private void OnWaveCompleted(int waveNumber, bool wasSuccessful)
    {
        if (wasSuccessful)
        {
            // Progress to next section if appropriate
            CheckForSectionProgression();
            
            // Update score and progression
            ScoreManager.Instance.AddWaveCompletionBonus();
        }
        else
        {
            // Handle wave failure
            HandleWaveFailure();
        }
    }
}
```

### **Custom Wave Node Creation**
```csharp
// Create custom wave behaviors using the node system
[CreateAssetMenu(menuName = "BTR/Wave Nodes/Music Sync Node")]
public class MusicSyncWaveNode : WaveNode
{
    [SerializeField] private float beatMultiplier = 1.0f;
    [SerializeField] private bool syncToDownbeat = true;
    
    public override void OnEnterNode(WaveState state)
    {
        // Wait for musical synchronization
        StartCoroutine(WaitForMusicalSync(state));
    }
    
    private IEnumerator WaitForMusicalSync(WaveState state)
    {
        var beatDetector = FindObjectOfType<BeatDetector>();
        
        if (syncToDownbeat)
        {
            // Wait for next downbeat
            yield return new WaitUntil(() => beatDetector.IsDownbeat);
        }
        
        // Apply beat multiplier timing
        yield return new WaitForSeconds(beatDetector.SecondsPerBeat * beatMultiplier);
        
        // Continue to next node
        state.GoToNextNode();
    }
}
```

## Best Practices

### **Wave Design**
- **Musical Timing**: Align wave transitions with musical sections
- **Progressive Difficulty**: Gradually increase challenge through sections
- **Performance Awareness**: Monitor enemy counts and system performance
- **Visual Feedback**: Provide clear progression indicators to players

### **Scene Management**
- **Memory Optimization**: Clean up resources between scene transitions
- **Error Recovery**: Implement fallback systems for failed loads
- **Progress Persistence**: Save progression state across sessions
- **Performance Monitoring**: Track load times and memory usage

### **Integration**
- **Event-Driven Design**: Use WaveEventChannel for system communication
- **Async Patterns**: Implement non-blocking progression systems
- **Configuration-Driven**: Use ScriptableObjects for easy content iteration
- **Performance-First**: Optimize for target platform capabilities

## Future Enhancements

### **Planned Features**
- **AI-Driven Wave Generation**: Procedural wave creation based on player skill
- **Cross-Platform Synchronization**: Multi-device wave progression
- **Advanced Analytics**: Detailed progression tracking and optimization
- **Dynamic Difficulty Adjustment**: Real-time challenge scaling

### **Performance Improvements**
- **Burst Compilation**: High-performance wave calculations
- **Job System Integration**: Multithreaded wave processing
- **Memory Pooling**: Advanced resource management for wave assets
- **Predictive Loading**: AI-driven scene preloading

## Related Systems

- **[[Stylo.Cadance - Music Synchronization Framework]]** - Beat-accurate wave timing
- **[[FMOD Advanced Audio Integration]]** - Musical progression synchronization
- **[[Enemy System Architecture]]** - Dynamic enemy spawning coordination
- **[[Stylo.Reservoir - Object Pooling System]]** - Efficient resource management

## Notes

The **BTR Wave System** represents a **unique approach to level progression** that seamlessly blends rhythm-action gameplay with traditional wave-based progression. Its strength lies in the **tight integration between musical timing and gameplay mechanics**, creating an experience where the music truly drives the action.

The system's **node-based visual editor** combined with **music-synchronized progression** provides both technical flexibility and artistic control, enabling designers to create compelling wave patterns that feel natural and engaging within the musical context.