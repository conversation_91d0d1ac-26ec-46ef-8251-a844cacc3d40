---
title: RAYCASTING Made Insanely Fast for Collision Detection
tags: [Unity, Performance, Raycasting, Jobs, CollisionDetection]
date: 2025-01-20
---

# [[RAYCASTING Made Insanely Fast for Collision Detection]]

## [[Overview]]
This implementation demonstrates how to optimize raycasting and collision detection in Unity using Jobs and Burst. The techniques shown can be adapted to various game types, particularly those with many fast-moving objects like FPS or bullet hell games.

## [[Implementation]]

### [[Projectile Spawning]]
```csharp
public class BulletManager : MonoBehaviour {
    public int bulletsPerShot = 10;
    public float bulletSpeed = 20f;
    public float maxDistance = 50f;
    public LayerMask collisionMask;
    public Transform spawnOrigin;
    public GameObject bulletPrefab;
    public GameObject impactEffect;

    private ObjectPool<Bullet> bulletPool;
    private List<Bullet> activeProjectiles = new List<Bullet>();
    private List<Bullet> bulletsToReturn = new List<Bullet>();

    void Start() {
        bulletPool = new ObjectPool<Bullet>(
            createFunc: () => {
                GameObject obj = Instantiate(bulletPrefab);
                obj.SetActive(false);
                return obj.GetComponent<Bullet>();
            },
            actionOnGet: (bullet) => bullet.gameObject.SetActive(true),
            actionOnRelease: (bullet) => bullet.gameObject.SetActive(false),
            maxSize: 1000
        );
    }
}
```

### [[Movement with Jobs]]
```csharp
[BurstCompile]
struct BulletMoveJob : IJobParallelForTransform {
    public float deltaTime;
    public float speed;

    public void Execute(int index, TransformAccess transform) {
        Vector3 direction = transform.rotation * Vector3.forward;
        transform.position += direction * speed * deltaTime;
    }
}
```

### [[Batch Raycasting]]
```csharp
public class RaycastBatchProcessor {
    private const int MAX_RAYCASTS = 10000;

    public void PerformRaycast(
        Vector3[] origins, 
        Vector3[] directions, 
        LayerMask mask,
        Action<RaycastHit[]> callback
    ) {
        int rayCount = Mathf.Min(origins.Length, MAX_RAYCASTS);
        
        using (var commands = new NativeArray<RaycastCommand>(rayCount, Allocator.TempJob))
        using (var results = new NativeArray<RaycastHit>(rayCount, Allocator.TempJob)) {
            for (int i = 0; i < rayCount; i++) {
                commands[i] = new RaycastCommand(
                    origins[i], 
                    directions[i], 
                    maxDistance: 10f,
                    layerMask: mask
                );
            }

            JobHandle handle = RaycastCommand.ScheduleBatch(
                commands, 
                results, 
                minCommandsPerJob: 1
            );
            handle.Complete();

            if (callback != null) {
                callback(results.ToArray());
            }
        }
    }
}
```

### [[Substepping for Accuracy]]
```csharp
void Update() {
    const int SUBSTEPS = 4;
    float substepTime = Time.deltaTime / SUBSTEPS;

    for (int i = 0; i < SUBSTEPS; i++) {
        // Movement
        using (var transforms = new TransformAccessArray(activeProjectiles.Count)) {
            for (int j = 0; j < activeProjectiles.Count; j++) {
                transforms.Add(activeProjectiles[j].transform);
            }

            var moveJob = new BulletMoveJob {
                deltaTime = substepTime,
                speed = bulletSpeed
            };

            JobHandle moveHandle = moveJob.Schedule(transforms);
            moveHandle.Complete();
        }

        // Collision detection
        HandleCollisions();
    }
}
```

## [[Key Optimization Techniques]]
1. **[[Object Pooling]]**: Reduces GC pressure
2. **[[Transform Access Array]]**: Efficient parallel processing of transforms
3. **[[Batch Raycasting]]**: Processes multiple raycasts in parallel
4. **[[Substepping]]**: More accurate collision detection for fast-moving objects

## [[Best Practices]]
1. Use `SphereCastCommand` or `CapsuleCastCommand` for better collision detection
2. Adjust number of substeps based on performance needs
3. Consider using `NativeArray` and `NativeList` for better memory management
4. Profile implementation to find optimal balance between accuracy and performance

## [[Additional Resources]]
- [[Unity Documentation: Physics Jobs]]
- [[Object Pooling Patterns]]
- [[Advanced Collision Detection Techniques]]
- [Unity Physics Optimization Guide](https://docs.unity3d.com/Manual/PhysicsOptimization.html)
- [Job System Best Practices](https://example.com/job-system-best-practices)