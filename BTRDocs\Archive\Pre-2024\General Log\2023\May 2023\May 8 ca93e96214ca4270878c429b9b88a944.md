# May 8

**Navmesh Test Scene - Ouroboros 3 - More complex Behaviors**

Integrated <PERSON><PERSON>’s feedback on how to get AI to stick to Navmesh

StickToNavmesh script on enemies

Sort of working? They seem to stick but behavior can be odd, stuck in places, model appears to be flashing at times. Monitoring….

Seems like it’s intermittenly flashing the position, so this is buggy approach. 

When I disable tick to navmesh script still having flashing position issues - interesting!

Something else is the issue

Projectiles are rarely coming into contact with enemies when fired at them. Enemies are not really firing projectiles at me either. Need to adjust values, get balanced. 

<aside>
💡 Bug!

</aside>

Seems like some of the positioning / rotation of elements on enemies is causing the flashing, due to floating point precision. Zero out or round off some of this for performance

Disbale StickToNavmesh script to see if it was the issue - didnt appear to be! 

Trying out a new Projectile script to clear things up

Error with Playter Target being set to null - disbaled some code to fix this 

```csharp
//This was causing issues with the player target being set to null constantly - need a fix!
        //else if (other.gameObject.CompareTag("PlayerTarget") && !locked)
        //{
        //    projectileTarget = null;
        //    Debug.Log("Hit PlayerTarget - Projectile Target should be set to null now");
        //}
```

Setting up another mobius strip

Figuring out workflow again

Unity

- Prefab Painter for waypoints - make sure rotation is take into account

Then use WaypointGenerator to convert those to CinemachineSMoothPath points

Also made WaypointVisualizer script to help with adjusting Roll on any waypoint

Spawn Points not working properly, need to dig into Spawn Master again and see what’s up