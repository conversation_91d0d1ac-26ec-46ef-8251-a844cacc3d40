# Stylo.Flux - Visual Effects Framework

*Created: 2025-07-18*
*Status: Current - Core System*
*System Location: `Assets/Stylo/Flux/`*

## Overview

**Stylo.Flux** is an advanced real-time visual effects framework designed specifically for **Unity 6 URP (Universal Render Pipeline)** that provides **authentic datamoshing**, **compression artifacts**, and **pixel trailing effects**. Built on Unity's Render Graph architecture, it delivers high-performance visual corruption effects optimized for both mobile and desktop platforms.

## System Architecture

```mermaid
flowchart TB
    %% Core Components
    FE[FluxEffect<br/>Volume Component]
    FRF[FluxRendererFeature<br/>URP Integration]
    FP[FluxPreset<br/>Configuration Storage]
    FPM[FluxPresetManager<br/>Preset Management]
    
    %% Rendering Pipeline
    RP[Render Pipeline<br/>5-Pass System]
    DS[Downscale Pass<br/>Resolution Reduction]
    EN[Encode Pass<br/>Compression Simulation]
    DE[Decode Pass<br/>Decompression Artifacts]
    US[Upscale Pass<br/>Motion Integration]
    CP[Copy Pass<br/>Temporal Storage]
    
    %% Shader System
    SS[Shader System<br/>Optimized Pipeline]
    MS[Main Shader<br/>URP_Flux.shader]
    SI[Shared Include<br/>Common Functions]
    KS[Keyword System<br/>160 Variants]
    
    %% Motion Processing
    MV[Motion Vectors<br/>Unity 6 Integration]
    TP[Temporal Processing<br/>Frame Storage]
    MT[Motion Trails<br/>Pixel Flow]
    MA[Motion Amplification<br/>Effect Enhancement]
    
    %% Quality System
    AQ[Adaptive Quality<br/>Performance Scaling]
    MO[Mobile Optimization<br/>Platform Specific]
    PF[Performance Monitor<br/>Frame Time Tracking]
    QS[Quality Scaling<br/>Real-time Adjustment]
    
    %% Editor Tools
    ET[Editor Tools<br/>Development Support]
    EI[Enhanced Inspector<br/>Custom UI]
    DT[Debug Tools<br/>Visualization]
    PW[Preset Wizard<br/>Creation Tools]
    
    %% Relationships
    FE --> FRF
    FRF --> RP
    
    RP --> DS
    DS --> EN
    EN --> DE
    DE --> US
    US --> CP
    
    FRF --> SS
    SS --> MS
    SS --> SI
    SS --> KS
    
    FRF --> MV
    MV --> TP
    MV --> MT
    MV --> MA
    
    FRF --> AQ
    AQ --> MO
    AQ --> PF
    AQ --> QS
    
    FE --> FP
    FP --> FPM
    
    FE --> ET
    ET --> EI
    ET --> DT
    ET --> PW
    
    %% Styling
    classDef core fill:#f9f,stroke:#333,stroke-width:2px
    classDef pipeline fill:#bbf,stroke:#333,stroke-width:2px
    classDef shader fill:#bfb,stroke:#333,stroke-width:2px
    classDef motion fill:#fbb,stroke:#333,stroke-width:2px
    classDef quality fill:#ffb,stroke:#333,stroke-width:2px
    classDef editor fill:#fbf,stroke:#333,stroke-width:2px
    
    class FE,FRF,FP,FPM core
    class RP,DS,EN,DE,US,CP pipeline
    class SS,MS,SI,KS shader
    class MV,TP,MT,MA motion
    class AQ,MO,PF,QS quality
    class ET,EI,DT,PW editor
```

## Core Components

### **FluxEffect** (Volume Component)
- **Role**: Post-processing volume component for URP integration
- **Pattern**: VolumeComponent with parameter overrides
- **Location**: `Stylo/Flux/Universal/Runtime/FluxEffect.cs`

**Key Features**:
- Complete VolumeComponent implementation with parameter overrides
- Profile management with easy preset saving and loading
- Global/Local volume support for scene-wide and localized effects
- Animation support for all parameters via Timeline or scripts

### **FluxRendererFeature** (URP Integration)
- **Role**: URP Renderer Feature implementing the rendering pipeline
- **Pattern**: ScriptableRendererFeature with Render Graph integration
- **Location**: `Stylo/Flux/Universal/Runtime/FluxRendererFeature.cs`

**Key Features**:
- Seamless URP pipeline integration with configurable render pass events
- Dynamic input requirements based on enabled features
- Mobile optimizations with automatic render pass event adjustment
- Render Graph optimization for Unity 6 performance

### **FluxPreset** (Configuration Storage)
- **Role**: ScriptableObject for effect configuration storage
- **Pattern**: ScriptableObject with reflection-based parameter application
- **Location**: `Stylo/Flux/Shared/FluxPreset.cs`

**Key Features**:
- Complete serialization of all effect parameters
- Cross-pipeline support (URP and Built-in RP)
- Runtime type-safe parameter application
- Category organization with hierarchical preset structure

### **FluxPresetManager** (Preset Management)
- **Role**: Static utility for preset management and validation
- **Pattern**: Static utility class with caching system
- **Location**: `Stylo/Flux/Shared/FluxPresetManager.cs`

**Key Features**:
- Efficient preset discovery and caching
- Performance validation with automated conflict detection
- Auto-fix system for intelligent parameter conflict resolution
- Default preset generation with performance tier support

## Visual Effects Pipeline

### **Multi-Pass Rendering Architecture**
Flux uses a sophisticated 5-pass rendering pipeline optimized for Unity 6 Render Graph:

#### **Pass 1: Downscale**
```csharp
// Reduces resolution for performance optimization
// Configurable downscale factor (1-10x)
// Prepares data for block-based compression simulation
```

#### **Pass 2: Encode**
```csharp
// Implements DCT-based JPEG compression simulation
// Color quantization and block-based encoding
// Authentic compression artifacts (ringing, mosquito noise)
```

#### **Pass 3: Decode**
```csharp
// Reconstructs image from compressed representation
// Introduces controlled decompression artifacts
// Maintains temporal coherence for datamoshing effects
```

#### **Pass 4: Upscale**
```csharp
// Returns to full resolution with motion vector integration
// Pixel trailing and temporal reprojection
// Selective stencil-based rendering support
```

#### **Pass 5: Copy to Previous**
```csharp
// Stores current frame for temporal effects
// Enables I-frame simulation and error accumulation
// Required for authentic datamoshing behavior
```

## Effect Types and Features

### **Core Datamoshing Effects**

#### **True Datamoshing**
- **JPEG-style compression**: Authentic DCT calculations with block processing
- **I-frame simulation**: Configurable reset frequency for temporal effects
- **Motion vector corruption**: Realistic compression errors
- **Temporal error accumulation**: Frame-to-frame corruption buildup

#### **Compression Artifacts**
- **Ringing artifacts**: High-contrast edge artifacts
- **Mosquito noise**: Textured region noise simulation
- **Chroma subsampling**: 4:2:0 sampling simulation
- **Quantization noise**: Luminance and chrominance channel artifacts

### **Enhanced Visual Effects**

#### **Pixel Flow & Trailing**
```csharp
// Configuration example
fluxEffect.trailIntensity.value = 0.8f;        // Dynamic trail intensity
fluxEffect.trailSmoothness.value = 0.5f;       // Blocky to smooth trails
fluxEffect.trailPersistence.value = 0.9f;      // Temporal behavior
fluxEffect.flowSpread.value = 1.2f;            // Organic motion patterns
```

#### **Artistic Corruption**
- **Multi-scale corruption**: Different block sizes (2x2 to 32x32)
- **Glitch transitions**: Dramatic moment effects
- **Feedback loops**: Recursive visual artifacts
- **Custom corruption masks**: Selective application

#### **JPEG Quality Control**
```csharp
// Quality settings like real compression
fluxEffect.compressionQuality.value = 75;      // Variable quality (1-100)
fluxEffect.luminanceQuantization.value = 1.2f; // Separate Y/UV quantization
fluxEffect.edgeSensitivity.value = 0.8f;       // Artifact placement
fluxEffect.brightnessMask.value = true;        // Realistic behavior
```

## Motion Vector Integration

### **Unity 6 Motion Vector Support**
- **Explicit binding**: Manual motion vector texture binding for Render Graph
- **Mobile fallback**: Graceful degradation when motion vectors unavailable
- **Validation system**: Runtime checks for motion vector availability
- **Debug visualization**: Built-in motion vector visualization tools

### **Temporal Processing**
```csharp
// Motion vector configuration
fluxEffect.motionAmplification.value = 2.0f;   // Unified motion enhancement
fluxEffect.motionThreshold.value = 0.1f;       // Performance optimization
fluxEffect.motionSmoothing.value = 0.3f;       // Jitter reduction
fluxEffect.cameraMotionWeight.value = 0.7f;    // Camera vs object balance
```

### **Previous Frame Storage**
- **Persistent texture management**: Automatic resource cleanup
- **Temporal coherence**: Frame-to-frame consistency
- **I-frame simulation**: Periodic resets mimicking video compression
- **Error accumulation**: Gradual corruption buildup over time

## Performance Optimization

### **Adaptive Quality System**
```csharp
// Performance monitoring and adjustment
public class FluxQualityController
{
    [SerializeField] private float targetFrameTime = 16.67f; // 60 FPS
    [SerializeField] private bool enableAdaptiveQuality = true;
    [SerializeField] private QualityTier currentTier = QualityTier.Auto;
    
    // Automatic quality scaling based on performance
    private void UpdateQualityBasedOnPerformance()
    {
        float avgFrameTime = GetAverageFrameTime(60); // 60-frame average
        if (avgFrameTime > targetFrameTime * 1.2f)
        {
            ReduceQuality();
        }
        else if (avgFrameTime < targetFrameTime * 0.8f)
        {
            IncreaseQuality();
        }
    }
}
```

### **Mobile Optimization Strategy**
- **Aggressive downscaling**: 2-3x higher downscaling on mobile
- **Feature limitation**: Expensive effects disabled on weak hardware
- **Block size constraints**: Maximum 8x8 blocks on mobile platforms
- **TBDR optimization**: Pass ordering optimized for tile-based renderers

### **Shader Performance**
- **Keyword reduction**: From 1024 to 160 shader variants
- **Feature grouping**: Logical parameter organization
- **Mobile macros**: Conditional compilation for platform-specific optimizations
- **Texture format optimization**: Consistent R32G32B32A32_SFloat usage

## Configuration and Usage

### **Basic Setup**
```csharp
// Add Flux to URP Renderer
var rendererData = GetComponent<UniversalRendererData>();
var fluxFeature = ScriptableObject.CreateInstance<FluxRendererFeature>();
rendererData.rendererFeatures.Add(fluxFeature);

// Configure Volume Component
var volume = GetComponent<Volume>();
var fluxEffect = volume.profile.Add<FluxEffect>();
fluxEffect.enabled.value = true;
```

### **Preset Application**
```csharp
// Apply preset to effect
FluxPreset preset = Resources.Load<FluxPreset>("Presets/DatmoshingIntense");
FluxPresetManager.ApplyPreset(fluxEffect, preset);

// Create preset from current settings
FluxPreset newPreset = FluxPresetManager.CreatePresetFromComponent(fluxEffect);
AssetDatabase.CreateAsset(newPreset, "Assets/MyPreset.asset");
```

### **Runtime Control**
```csharp
// Dynamic effect control
public class FluxController : MonoBehaviour
{
    [SerializeField] private FluxEffect fluxEffect;
    
    private void Update()
    {
        // Audio-reactive intensity
        float audioLevel = GetAudioLevel();
        fluxEffect.compressionIntensity.value = audioLevel * 0.8f;
        
        // Motion-based corruption
        float playerSpeed = GetPlayerSpeed();
        fluxEffect.motionAmplification.value = Mathf.Lerp(1f, 3f, playerSpeed / maxSpeed);
        
        // Conditional I-frame reset
        if (Input.GetKeyDown(KeyCode.Space))
        {
            fluxEffect.triggerIFrame.value = true;
        }
    }
}
```

## Editor Integration

### **Enhanced Inspector UI**
- **Preset integration**: Direct preset application from inspector
- **Parameter validation**: Real-time conflict detection and warnings
- **Auto-fix buttons**: One-click parameter conflict resolution
- **Performance warnings**: Visual indicators for expensive combinations

### **Debug Tools**
```csharp
// Debug visualization options
fluxEffect.debugMode.value = FluxDebugMode.MotionVectors;
fluxEffect.debugIntensity.value = 1.0f;
fluxEffect.debugOverlay.value = true;

// Available debug modes
public enum FluxDebugMode
{
    None,
    MotionVectors,
    CompressionBlocks,
    TemporalDifference,
    PerformanceMetrics
}
```

### **Workflow Tools**
- **Preset creation wizard**: Guided preset creation from current settings
- **Batch preset validation**: Project-wide preset health checking
- **Default preset generation**: Automatic sample preset creation
- **Integration testing**: Automated URP integration validation

## Shader Pipeline Architecture

### **Optimized Keyword System**
```hlsl
// Keyword organization (160 variants total)
#pragma multi_compile _ FLUX_BLOCK_2X2 FLUX_BLOCK_4X4 FLUX_BLOCK_8X8 FLUX_BLOCK_16X16 FLUX_BLOCK_32X32
#pragma multi_compile _ FLUX_REPROJECTION
#pragma multi_compile _ FLUX_COMPRESSION_ARTIFACTS
#pragma multi_compile _ FLUX_MOBILE_OPTIMIZATION
#pragma multi_compile _ FLUX_DEBUG_MODE
```

### **Authentic Compression Simulation**
```hlsl
// DCT-based compression simulation
float4 ApplyJPEGCompression(float4 color, float2 uv, float quality)
{
    // Discrete Cosine Transform implementation
    float4 dctCoeffs = DCTransform(color, blockSize);
    
    // Quantization based on quality level
    float4 quantized = QuantizeCoefficients(dctCoeffs, quality);
    
    // Inverse DCT with artifacts
    float4 compressed = IDCTransform(quantized, blockSize);
    
    return compressed;
}
```

### **Motion Vector Processing**
```hlsl
// Motion-based pixel trailing
float4 ApplyMotionTrails(float4 currentColor, float2 uv, float2 motionVector)
{
    // Sample previous frame with motion compensation
    float2 prevUV = uv - motionVector;
    float4 prevColor = SamplePreviousFrame(prevUV);
    
    // Blend based on motion magnitude
    float motionMagnitude = length(motionVector);
    float blendFactor = saturate(motionMagnitude * trailIntensity);
    
    return lerp(prevColor, currentColor, blendFactor);
}
```

## Integration Points

### **Stylo Ecosystem Integration**
- **Cadance Audio System**: Audio-reactive parameter modulation
- **Epoch Time System**: Temporal effect synchronization with time manipulation
- **MenUI System**: Runtime effect control through UI
- **Reservoir Pooling**: Efficient resource management for VFX

### **Unity System Integration**
- **Volume Framework**: Native post-processing volume integration
- **Render Graph**: Full Unity 6 Render Graph optimization
- **Timeline System**: All parameters animatable through Timeline
- **Input System**: Runtime control through new Input System

### **Third-Party Compatibility**
- **FMOD Integration**: Audio-reactive effects through FMOD events
- **Cinemachine Support**: Camera-based effect triggering
- **Post-Processing Stack**: Compatibility with other post-effects

## Best Practices

### **Performance**
- Monitor frame time with adaptive quality enabled
- Use appropriate block sizes for target platform
- Configure mobile-specific presets for optimization
- Test extensively on target hardware

### **Visual Design**
- Start with preset templates and customize
- Use audio reactivity for dynamic effects
- Combine with particle systems for enhanced impact
- Consider temporal coherence for smooth effects

### **Integration**
- Apply effects through Volume components
- Use Timeline for cinematic sequences
- Implement audio-reactive controls for music games
- Design fallbacks for unsupported platforms

## Future Enhancements

### **Planned Features**
- **Real-time ray tracing integration**: RT-based motion vectors
- **AI-enhanced compression**: ML-based artifact generation
- **Advanced temporal effects**: Multi-frame error accumulation
- **Custom block patterns**: Non-rectangular compression blocks

### **Performance Improvements**
- **Compute shader pipeline**: GPU-accelerated DCT calculations
- **Variable rate shading**: Quality-based shading rate control
- **Temporal upsampling**: AI-enhanced resolution reconstruction

## Related Systems

- **[[Stylo.Epoch - Time Manipulation Framework]]** - Temporal effect synchronization
- **[[Stylo.Cadance - Music Synchronization Framework]]** - Audio-reactive effects
- **[[VFX System Architecture]]** - Visual effects coordination
- **[[Radar System Architecture]]** - UI overlay integration

## Notes

Stylo.Flux represents a **cutting-edge visual effects framework** that combines authentic datamoshing algorithms with modern Unity 6 optimization techniques. Its comprehensive preset system, adaptive quality scaling, and deep URP integration make it suitable for both indie developers and AAA productions.

The system's strength lies in its balance of **authenticity** (matching real compression artifacts) and **artistic flexibility** (enhanced visual effects), backed by robust tooling and optimization systems that ensure reliable performance in production environments.