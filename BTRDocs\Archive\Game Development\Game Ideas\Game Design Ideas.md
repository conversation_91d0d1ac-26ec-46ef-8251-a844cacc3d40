# Game Design Ideas
  2 |   
  3 | ## January 2024
  4 | 
  5 | ### January 5th, 2024
  6 | - Strategy for creative look of bosses - several phases?
  7 |     - use a base shape as point to construct boss
  8 |     - use geometry nodes to make a deconstructed look for the boss
  9 |     - third phase? Unique to each boss?
 10 | 
 11 | ### January 10th, 2024
 12 | - **Enemy Inspiration:** Use Gravity Rush enemies as inspiration for my enemies. Look up the various types.
 13 | - **Visual Style:** Apply a certain visual style (from a Twitter link) to text and UI in Unity. ([https://twitter.com/ashlee3dee/status/1745524506872176970](https://twitter.com/ashlee3dee/status/1745524506872176970))
 14 | - **Visual Style Reference:** Look at Ace Combat, which emulates a similar visual style.
 15 | 
 16 | ### January 15th, 2024
 17 | - **Gameplay Pacing:** Make the game more fast-paced, feels a bit slow.
 18 | - **Bullet Visuals:** Make bullets glow. Look into this. (Links provided to tutorials on bloom and glow effects in Unity).
 19 | - **Selective Bloom:** Apply bloom to specific objects. (Links provided to tutorials on selective bloom in Unity).
 20 | - **Radar Style:** Alternate radar? (Link provided to a tutorial on RPG Radar Chart in Unity).
 21 | 
 22 | ### January 18th, 2024
 23 | - **Radar Icons:** Need different radar icons for enemies vs bullets in perspective radar. 
 24 | - **Graphics Variation:** Think on idea - graphics variation - maintain interest this way.
 25 | 
 26 | ### January 22nd, 2024
 27 | - **Bullet Visuals:** Remove indicators from bullets - too messy and cooler when they’re not there.
 28 | - **Phase Transition:** Should expand scene reload to jumping phases? or just kill enemies to quickly move to next phase?
 29 | 
 30 | ### January 24th, 2024
 31 | - **Gameplay Pacing:** When playing, too much down time. Things are too slow. Need more activity and movement! more effects! Avoid down time - need more to do?
 32 | 
 33 | ### January 26th, 2024
 34 | - **Game Design Philosophy:** Why can't video games be more unexplained? Visceral in this way? Use simple things to set the tone and vibe, makes later choices more impactful. Consider how to apply this... horror games probably lean into these ideas. Beginning of "Under the Skin" as a "Birth"?
 35 | - **Beat Remake Ideas:** Controls are wonky at start, slowly become more sensible, it's being born, it's learning, getting adjusted.
 36 | - **Bullet Obstacle Avoidance:** Need to give them obstacle avoidance, at least for ground? Apply this for both enemy shooting and player shooting?
 37 | - **Static Enemies:** Place static enemies along the snake to also take out? Gives more to do.
 38 | 
 39 | ### January 27th, 2024
 40 | - **Reticle Visuals:** Looked into Shapes, may be a worthwhile replacement on reticle. 
 41 | - **Font Change:** Changes fonts to something more interesting - WipeOut Influenced.
 42 | - **Score Particle Effects:** Have particle effects on Score? Replicate Rez tracking every action. Have each thing that affects score show the numbers of what's happening and particle effects. Like - 100, + 500 etc. 
 43 | - **UI Considerations:** Considering UI, what might work best.
 44 | 
 45 | ### January 29th, 2024
 46 | - **Visual Flourishes:** Trying to add some visual whizz bang to the game, flourishes or emphasizing things more.
 47 | - **Level Structure:** Interesting setup for having multiple levels. (Link to YouTube video: [https://www.youtube.com/watch?v=YacTpE4ZP_w](https://www.youtube.com/watch?v=YacTpE4ZP_w))
 48 | 
 49 | ### January 31st, 2024
 50 | - **Game Reference:** "Death in Abyss" game - a little similar? Neat! (Link to Twitter: [https://twitter.com/Agelvik](https://twitter.com/Agelvik))
 51 | - **Visual Style Reference:** Link to Twitter user "christa lee" for visual style inspiration. (Link to Twitter: [https://twitter.com/OhPoorPup/status/1752440548852674984](https://twitter.com/OhPoorPup/status/1752440548852674984))
 52 | 
 53 | ## February 2024
 54 | 
 55 | ### Feb 1st
 56 | - **Ring World Reference:** "Ring World Using Random Flow" YouTube video, useful for Ophanim. (Link to YouTube video: [https://www.youtube.com/watch?v=YHkhEyh5G68](https://www.youtube.com/watch?v=YHkhEyh5G68))
 57 | - **Game Design Resources Spreadsheet:** Link to "Game Design Resources full spreadsheet - lots of good links!". (Link to Google Sheet: [https://docs.google.com/spreadsheets/d/1QhFyPfYSjHv7PjibGrslF3mNW_CIDXWv9o-iQgLbu1o/edit#gid=1404087630](https://docs.google.com/spreadsheets/d/1QhFyPfYSjHv7PjibGrslF3mNW_CIDXWv9o-iQgLbu1o/edit#gid=1404087630))
 58 | - **Enemy Character Inspo:** "A multitude of Empty Slots (for Commissions) on Twitter / X". Cool enemy character inspo - check out guys other stuff too! (Link to Twitter: [https://twitter.com/ZloNoNameSov/status/1716610855125799378](https://twitter.com/ZloNoNameSov/status/1716610855125799378))
 59 | 
 60 | ### Feb 6th
 61 | - **Character Room/Tutorial:** Birth of character room… ‘tutorial’ section… Use "Water Caustics Effect from Blade Runner 2049" effect? (Link to YouTube video: [https://www.youtube.com/watch?v=Oh21hYx_Jbk](https://www.youtube.com/watch?v=Oh21hYx_Jbk))
 62 | - **Projectile Logic:** Suspect bullets need to go faster and maybe go through walls? Maybe don't shoot from above, gather bullets at reticle? Shoot from reticle?
 63 | 
 64 | ### Feb 13th
 65 | - **Hyperpassive Music/Interactivity Rubric:** "Hyperpassive" as a rubric for the music and interactivity in Beat Remake. (Links to YouTube videos related to hyperpassive music/interactivity).
 66 | - **Level Design - Spatial Communication:** "Spatial Communication in Level Design" YouTube video. (Link to YouTube video: [https://www.youtube.com/watch?v=AKeUZVikPV8](https://www.youtube.com/watch?v=AKeUZVikPV8))
 67 | 
 68 | ### Feb 24th
 69 | - **Infinite Track Ouroboros Section Idea:** Infinite track applied to game. Definitely could be a cool idea for section of Ouroboros. Also could use as part of travelling inside of one.
 70 | 
 71 | ### Feb 25th
 72 | - **Infinite Track Snake Head:** Added Snake head to infinite track, so it’s like you’re being chased by ouroboros. Cool idea! Needs refinement, likely doesn't need to be on its own spline.
 73 | - **Ouroboros Level Ideas/Structure:** 
 74 |     - Several ouroboros figures.
 75 |     - Back of snake - chased - shoot something…. not sure what!
 76 |     - Interior Snake? - enemies inside running along interior? - is this infinite or a structure?
 77 |     - Two-headed snake boss.
 78 | - **Ophanim Level Ideas:** Running along moving rings. How could infinite track be applied here??
 79 | 
 80 | ### Feb 27th
 81 | - **Sin & Punishment Attach Phase Ideas:** Play / watch some Sin & Punishment for idea of attach phases here.
 82 |     - Enemies move up the snake’s back?
 83 |     - Snake’s eyes do something?
 84 | 
 85 | ### Feb 28th
 86 | - **Snake Infinite Track - Charging Attack:** Snake infinite track - charging attack that you have to hit enough times before it's dismissed.
 87 | 
 88 | ### Feb 29th
 89 | - **Infinite Track Gameplay Style:** Infinite track working better now, but need a solution to gameplay style that is compelling. 
 90 | - **Tail Animator/Physics Animation System:** Maybe tail animator can have snakes that follows but move with the physics? Maybe use player plane as a base and do more traditional rail shooter waves etc? Why look back at snake? to shoot? What is that system? Same people who make animancer have a tail / physics animation system that seems quite good. May be useful for snake models.
 91 | 
 92 | ## March 2024
 93 | 
 94 | ### March 6th
 95 | - **Design Docs Advice - Setting Questions:** Questions to consider for a good setting: 1. Is it easy to explain? 2. Can you tell an interesting story? 3. Can you write interesting quests? 4. Is the setting evocative? 
 96 | 
 97 | ### March 12th
 98 | - **Static Shooter Trail Idea:** Maybe a longer trail / any trail on the static shot ones would be good? Something to showcase their directionality.
 99 |  99 | 
100 | 100 | ### March 26th - Game Design Philosophy - Music
101 | 101 | - **Music and Meaning:** Music for the game should be driven by the second to second gameplay and systems, not just procrastination or generic sound generation. Meaning and feeling should drive the creation of music, especially in rhythm games like this, to go beyond just fading between stems and battle tracks and explore deeper, more focused sound design.
102 | 102 | 
103 | 103 | ### March 28th
104 | 104 | - **Buto Renderer Order Level Idea:** Buto renderer order fix was kind of cool, worth considering for a level.
105 | 105 | 
106 | 106 | ### April 4th
107 | 107 | - **Infinite Track Following Spline Idea:** Idea of Infinite track but following a spline. This is potentially how we can have some randomization in the boss battle but restrain to a specific area. May be useful in general!
108 | 108 | 
109 | 109 | ### April 5-6th
110 | 110 | - **Twin Snakes Boss Ideas:** Working on boss ideas of Twin Snakes / Time.
111 | 111 | 
112 | 112 | ### April 8-9th
113 | 113 | - **Twin Snakes Movement - A* Idea:** Possible idea - use A* for snakes on a platform if moving around to follow player? Move slow. Maybe not A* for this, not sure if truly a benefit though it does work.
114 | 114 | - **Twin Snakes Boss Fight Time Rewind Mechanic:** Twin Snakes boss fight concept - Time rewind mechanic in action + music reacting.
115 | 115 | - **Ouroboros Boss Battle Randomized Path:** Attempting a roughly set / randomized path for the boss fighting. A variation on OuroborosInfinite.
116 | 116 | 
117 | 117 | ### April 10th
118 | 118 | - **Boss Stage Planning:** Need better planning for the boss stage - gameplay and sequence.
119 | 119 | - **Power Ups Idea:** Power ups like Rez - upgrade character?
120 | 120 | - **Visual Inspo - Sentinel Fight:** Inspired by X-Men Sentinel fight, flashes of color and light in time with the music - dark and violent.
121 | 121 | 
122 | 122 | ### April 16th - Game Design Philosophy - Music
123 | 123 | - **Music and Meaning Reflection:** Reflecting on previous journal entry from Mid-March about avoiding making music for the game and the importance of meaning in game music.
124 | 124 | 
125 | 125 | ### April 12-14th
126 | 126 | - Twin snake - on each side
127 | 127 | - Polyrhythm shooting tied with music!
128 | 128 | - 3 against two?
129 | 129 | - when landing on the beat - dodge!
130 | 130 | - otherwise can shoot back
131 | 131 | - parry option that fires in direction instead of lock on?
132 | 132 | 
133 | 133 | ### April 15th
134 | 134 | - Snake as rings wrapped around a planet - ouroboros
135 | 135 | - Think ouroboros - surround an orb - shoot any bullets at that orb to take out the health of the current orb and when you are successful a kill all enemies event occurs , per wave
136 | 136 | - Ouroboros - A serpentine creature that encircles the player, creating an ever-shrinking arena. Its attacks are wave-based and must be dodged to the beat.
137 | 137 | - Should i have a speed dash that affects tempo / music?
138 | 138 | 
139 | 139 | ### April 16th - Game Design Philosophy - Music
140 | 140 | - **Music and Meaning Reflection:** Reflecting on previous journal entry from Mid-March about avoiding making music for the game and the importance of meaning in game music.
141 | 141 | 
142 | 142 | ### April 22nd
143 | 143 | - Plan out game and levels
144 | 144 | - Include more rhythm elements??
145 | 145 | - Find the fun!!
146 | 146 | 
147 | 147 | ## May 2024
148 | 148 | 
149 | 149 | ### May 1st, 2024
150 | 150 | 
151 | 151 | #### Enemy Behavior
152 | 152 | 
153 | 153 | **Attack Styles**
154 | 154 | 
155 | 155 | - single shots
156 | 156 | - several small projectiles (3 at a time)
157 | 157 | - Area of effect attacks (explode)
158 | 158 | - Counterattacks
159 | 159 |     - think risk / reward with trying certain things on enemies
160 | 160 | 
161 | 161 | **Movement Patterns**
162 | 162 | 
163 | 163 | - difficult to target (snake like movement?)
164 | 164 |     - orbital movement (electrons / nucleus)
165 | 165 | - chase and retreat?
166 | 166 |     - combine with explosive enemies - need to take them out quick?
167 | 167 | - vertical dive and rise
168 | 168 | - lateral movement
169 | 169 | 
170 | 170 | **Defensive Abilities**
171 | 171 | 
172 | 172 | - Enemies with shields under certain conditions
173 | 173 |     - Use this for snake bosses / time forward and reverse?
174 | 174 | - Camouflage / Stealth
175 | 175 |     - Temporary invisibility
176 | 176 |     - Only visible right before an attack?
177 | 177 |         - Would need line of sight in order to attack?
178 | 178 | 
179 | 179 | **Group Tactics**
180 | 180 | 
181 | 181 | - Enemy formations
182 | 182 | 
183 | 183 | **Environment Manipulation**
184 | 184 | 
185 | 185 | - Interact with the environment to create obstacles or new paths. For instance, an enemy might destroy part of the track, forcing the player to take a different route temporarily.
186 | 186 |     - Rewind time to reverse? Required or you will instantly die?
187 | 187 | 
188 | 188 | #### Boss Fight Ideas
189 | 189 | 
190 | 190 | [Ouroboros Boss Fight Concept 1](May%201%20038edfee9455402aa54b8dce20ccf465/Ouroboros%20Boss%20Fight%20Concept%201%20ca713811ac604c3bb0e5a8aaa3ef6596.md)
191 | 191 | 
192 | 192 | [Ouroboros Boss Fight Concept 2](May%201%20038edfee9455402aa54b8dce20ccf465/Ouroboros%20Boss%20Fight%20Concept%202%2061807addf3974aa29b5b9baa0f675caf.md)
193 | 193 | 
194 | 194 | #### Player Platform Ideas
195 | 195 | 
196 | 196 | - Constantly falling
197 | 197 |     - Moving through the portals seen in the background previously
198 | 198 |     - Loops through falling and appearing from top to bottom
199 | 199 |         - This can just be a spline but feels like falling
200 | 200 |         - Allow reversing of movement along spline here?
201 | 201 |             - May reuse this idea in other levels
202 | 202 | 
203 | 203 | #### Ophanim Enemy Design
204 | 204 | 
205 | 205 | - Test enemy movement along internal rings
206 | 206 |     - May be better with new A* improvements
207 | 207 | 
208 | 208 | ### May 7-9th, 2024
209 | 209 | 
210 | 210 | - Need to get out of the bug trench and into gameplay trench a bit more!
211 | 211 | 
212 | 212 | ### Gameplay Mechanics - May 2024
213 | 213 | 
214 | 214 | #### May 13th, 2024
215 | 215 | 
216 | 216 | Idea 
217 | 217 | 
218 | 218 | Time / music / health
219 | 219 | 
220 | 220 | All the same
221 | 221 | 
222 | 222 | Music dies when time stops!
223 | 223 | 
224 | 224 | Build curiosity to make level repayable
225 | 225 | 
226 | 226 | X # of secrets found 
227 | 227 | 
228 | 228 | What are the rewards for replaying a level?
229 | 229 | 
230 | 230 | Make it so game plays has an Obvious approach + many non obvious options
231 | 231 | 
232 | 232 | How can there be endless mastery? Think devil daggers and others 
233 | 233 | 
234 | 234 | Speed running is a popular approach but how does that align with my game at all?
235 | 235 | 
236 | 236 | Need to think through how to better intermingle all my game mechanics
237 | 237 | 
238 | 238 | Instead of 5 different options, have them exist as multipliers for each other to create whole new actions 
239 | 239 | 
240 | 240 | Could I do a pacifist run?
241 | 241 | 
242 | 242 | Watch people play a game to see how they make their own fun
243 | 243 | 
244 | 244 | ### Playtesting Feedback - May 14th, 2024
245 | 245 | 
246 | 246 | - feels a bit slow in general?
247 | 247 | - Mechanics need to interact better with each other
248 | 248 |     - time glitch
249 | 249 |         - could highlight where the enemies are located
250 | 250 |         - could allow you to move faster or slower while enemies move differently?
251 | 251 |             - or maybe just enemy shot bullets are slowed drastically
252 | 252 |     - slow time
253 | 253 |         - allows you to move backwards?
254 | 254 | - Enemy types on wave scene 3 need adjusting, need more projectiles flying around
255 | 255 | 
256 | 256 | ### Ouroboros Level Design - June 2024
257 | 257 | 
258 | 258 | Figuring out all Ouroboros levels
259 | 259 | 
260 | 260 | Section 1
261 | 261 | 
262 | 262 | - Mobius Tube 2 Variation 2
263 | 263 | - Mobius Tube 2
264 | 264 | 
265 | 265 | Section 2
266 | 266 | 
267 | 267 | - Infinite Track 1
268 | 268 | 
269 | 269 | Section 3
270 | 270 | 
271 | 271 | - Mobius Tube Multi Var 1
272 | 272 | - Mobius Tube 5
273 | 273 | 
274 | 274 | Section 4
275 | 275 | 
276 | 276 | - Infinite Track 2
277 | 277 | 
278 | 278 | Section 5
279 | 279 | 
280 | 280 | - 
281 | 281 | 
282 | 282 | Need many more enemies per wave
283 | 283 | 
284 | 284 | But only a few die to advance
285 | 285 | 
286 | 286 | MUbe they're one hit?
287 | 287 | 
288 | 288 | The. Use multi tied enemies for more hits
289 | 289 | 
290 | 290 | ### Enemy Bullet Patterns - June 2024
291 | 291 | 
292 | 292 | #### June 6th, 2024
293 | 293 | 
294 | 294 | Thoughts on having many bullets flynig at player
295 | 295 | 
296 | 296 | Make a system where they once they reach a certain radius, they hit the player in time? 
297 | 297 | 
298 | 298 | more rhythmic? Look into this if it feels necessary - may be other ways of implementing as well
299 | 299 | 
300 | 300 | Example - enemies all shoot in a pattern but it’s controlled which and when. for example projectile manager is talking to enemies, controlling when each gets its chance to shoot, this is done in rhythm. 
301 | 301 | 
302 | 302 | ### Gameplay Mechanics - June 2024
303 | 303 | 
304 | 304 | #### June 6th, 2024
305 | 305 | 
306 | 306 | Idea - time is running down / health is running down
307 | 307 | 
308 | 308 | No limit on lock on 
309 | 309 | 
310 | 310 | Just get as many as possible and go
311 | 
312 | ### May 1st, 2024
313 | - Really need to go minimal viable product with my game - make a place - do it!
314 | 314 | ### May 13th, 2024
315 | 315 | - Idea
316 | 316 | - Time / music / health
317 | 317 | - All the same
318 | 318 | - Music dies when time stops!
319 | 319 | - Build curiosity to make level repayable
320 | 320 | - X # of secrets found 
321 | 321 | - What are the rewards for replaying a level?
322 | 322 | - Make it so game plays has an Obvious approach + many non obvious options
323 | 323 | - How can there be endless mastery? Think devil daggers and others 
324 | 324 | - Speed running is a popular approach but how does that align with my game at all?
325 | 325 | - Need to think through how to better intermingle all my game mechanics
326 | 326 | - Instead of 5 different options, have them exist as multipliers for each other to create whole new actions 
327 | 327 | - Could I do a pacifist run?
328 | 328 | - Watch people play a game to see how they make their own fun
329 | 329 | ### June 6th, 2024
330 | 330 | - Thoughts on having many bullets flynig at player
331 | 331 | - Make a system where they once they reach a certain radius, they hit the player in time? 
332 | 332 | - more rhythmic? Look into this if it feels necessary - may be other ways of implementing as well
333 | 333 | - Example - enemies all shoot in a pattern but it’s controlled which and when. for example projectile manager is talking to enemies, controlling when each gets its chance to shoot, this is done in rhythm. 
334 | 334 | - Idea - time is running down / health is running down
335 | 335 | - No limit on lock on 
336 | 336 | - Just get as many as possible and go
337 | 337 | ### January 3rd, 2024
338 | 338 | **Jan 3rd**
339 | 339 | 
340 | 340 | Need to make a plan, finalize what things are. Less aimless experimentation!
341 | 341 | 
342 | 342 | Bosses / Enemies 
343 | 343 | 
344 | 344 | What are they made of? What are the materials? How does this impact their structure? Rigid vs curverd lines?
345 | 345 | 
346 | 346 | Can I take smooth things, make them geometric, then reduce and apply generators to them? Make them interesting and different? 
347 | 347 | 
348 | 348 | Can bosses be made of different things then enemies? 
349 | 349 | 
350 | 350 | Twin Snakes
351 | 351 | 
352 | 352 | - Inverter selection of seperated geometry is the model of the second snake
353 | 353 | - together they are whole model ?
354 | 354 | 
355 | 355 | Blender Tissue for tesselation?
356 | 356 | 
357 | 357 | Voulme to mesh and Geometry Nodes for this as well, in YT vidds
358 | 358 | ### January 5th, 2024
359 | 359 | - Strategy for creative look of bosses - several phases?
360 | 360 | - use a base shape as point to construct boss
361 | 361 | - use geometry nodes to make a deconstructed look for the boss
362 | 362 | - third phase? Unique to each boss?
363 | 363 | ### January 10th, 2024
364 | 364 | - **Enemy Inspiration:** Use Gravity Rush enemies as inspiration for my enemies. Look up the various types.
365 | 365 | - **Visual Style:** Apply a certain visual style (from a Twitter link) to text and UI in Unity. ([https://twitter.com/ashlee3dee/status/1745524506872176970](https://twitter.com/ashlee3dee/status/1745524506872176970))
366 | 366 | - **Visual Style Reference:** Look at Ace Combat, which emulates a similar visual style.
367 | 367 | ### January 15th, 2024
368 | 368 | - **Gameplay Pacing:** Make the game more fast-paced, feels a bit slow.
369 | 369 | - **Bullet Visuals:** Make bullets glow. Look into this. (Links provided to tutorials on bloom and glow effects in Unity).
370 | 370 | - **Selective Bloom:** Apply bloom to specific objects. (Links provided to tutorials on selective bloom in Unity).
371 | 371 | - **Radar Style:** Alternate radar? (Link provided to a tutorial on RPG Radar Chart in Unity).
372 | 372 | ### January 18th, 2024
373 | 373 | - **Radar Icons:** Need different radar icons for enemies vs bullets in perspective radar.
374 | 374 | - **Graphics Variation:** Think on idea - graphics variation - maintain interest this way.
375 | 375 | ### January 22nd, 2024
376 | 376 | - **Bullet Visuals:** Remove indicators from bullets - too messy and cooler when they’re not there.
377 | 377 | - **Lock-on Indicators:** Fixing lock on inidicators on shooter - currently cant see them, finding a solution
378 | 378 |     - Still not great, need to look at new material or different rendering methods
379 | 379 |         - Make slightly emissive?
380 | 380 | - **Phase Transition:** Should expand scene reload to jumping phases? or just kill enemies to quickly move to next phase?
381 | 381 | ### January 24th, 2024
382 | 382 | - **Gameplay Pacing:** When playing, too much down time. Things are too slow. Need more activity and movement! more effects! Avoid down time - need more to do?
383 | 383 | ### January 26th, 2024
384 | 384 | - **Game Design Philosophy:** Why can't video games be more unexplained? Visceral in this way? Use simple things to set the tone and vibe, makes later choices more impactful. Consider how to apply this... horror games probably lean into these ideas. Beginning of "Under the Skin" as a "Birth"?
385 | 385 | - **Beat Remake Ideas:** Controls are wonky at start, slowly become more sensible, it's being born, it's learning, getting adjusted.
386 | 386 | - **Bullet Obstacle Avoidance:** Need to give them obstacle avoidance, at least for ground? Apply this for both enemy shooting and player shooting?
387 | 387 | - **Static Enemies:** Place static enemies along the snake to also take out? Gives more to do.
388 | 388 | ### January 27th, 2024
389 | 389 | - **Reticle Visuals:** Looked into Shapes, may be a worthwhile replacement on reticle.
390 | 390 | - **Font Change:** Changes fonts to something more interesting - WipeOut Influenced.
391 | 391 | - **Score Particle Effects:** Have particle effects on Score? Replicate Rez tracking every action. Have each thing that affects score show the numbers of what's happening and particle effects. Like - 100, + 500 etc.
392 | 392 | - **UI Considerations:** Considering UI, what might work best.
393 | 393 | ### January 29th, 2024
394 | 394 | - **Visual Flourishes:** Trying to add some visual whizz bang to the game, flourishes or emphasizing things more.
395 | 395 | - **Level Structure:** Interesting setup for having multiple levels. (Link to YouTube video: [https://www.youtube.com/watch?v=YacTpE4ZP_w](https://www.youtube.com/watch?v=YacTpE4ZP_w))
396 | 396 | ### April 8-9th, 2024
397 | 397 | - **Twin Snakes Boss Fight Time Rewind Mechanic:** Twin Snakes boss fight concept - Time rewind mechanic in action + music reacting.
398 | 398 | 396 | ### January 31st, 2024
399 | 399 | 397 | - **Game Reference:** "Death in Abyss" game - a little similar? Neat! (Link to Twitter: [https://twitter.com/Agelvik](https://twitter.com/Agelvik))
400 | 400 | 398 | - **Visual Style Reference:** Link to Twitter user "christa lee" for visual style inspiration. (Link to Twitter: [https://twitter.com/OhPoorPup/status/1752440548852674984](https://twitter.com/OhPoorPup/status/1752440548852674984))
401 | 401 | 401 | ### August 2nd, 2024
402 | 402 | 402 | - **Bounce sound improvement:** Bounce sound could use more note choices, more of a sense of harmony and surprise? Drive some melody? Or refer to unfold Gabor Lazar for change ideas
403 | 403 | 403 | ### August 3-5th, 2024
404 | 404 | 404 | - **Second Snake Encounter Ideas:** Consider second snake encounter - maybe not the crazy graphics? WHat else ot make it different
405 | 405 | 405 | - **Enemy Flashing Effect:** Enemies flashing could be good, whats a good way to implement that? let’s look into it.
406 | 406 | 406 | ### August 8th, 2024
407 | 407 | 407 | - **Chained Enemy Type:** Want chained enemy type in Section 3
408 | 408 | 408 | - **Orbital Enemies:** Makeing them move in a snake like fasion…. unsure this can happen. Will require further deep experiments. Right now it might make the most sense to try something in line with what works right now. have the other parts spn around? use PrimeTween? Setting them up as orbitals! testing Seems the previous snake prefab works ok, need to figure out quick turning or other issues, but could potentially do something cool with this. Use both enemy type in different areas, play with these concepts Types - Snakes - Orbitals
409 | 409 | 409 | - **New Enemy Ideas:** More ideas
410 | 410 | 410 |     - Fractured - destroying it breaks into multiple smaller enemies - or projectiles! That are flynig towards the player to hit them.
411 | 411 | 411 |     - Tesselated Wall - A large enemy composed of many interlocking polyhedrons that form a wall or barrier. As the player attacks, the polyhedrons detach and become independent attackers.
412 | 412 | 412 |     - Elastic - think vertices - A shape that stretches and morphs its shape, making it difficult to predict its movement. It can extend its sides to form spikes or turn into a flat sheet to dodge attacks. Also maybe these vertices are the only lock on poitns?
413 | 413 | 413 |     - Mines - these need to be taken out before the player hits them and gets hurt
414 | 414 | 414 |     - Phased movement - disappear and reappear in different spots
415 | 415 | 415 |     - Large guardians - maybe static in their positon and slow to move, but deal big damage
416 | 416 | 416 |     - Reflective shields - attacks boucne off them at differnet times. this could be the time ability in action or just a timed shield based on the music
417 | 417 | 417 |     - Cloaked Stalkers: move between invisible and visible! radar comes in handy for this especially
418 | 418 |
419 |
