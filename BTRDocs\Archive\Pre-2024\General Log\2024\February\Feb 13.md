# Feb. 13

Still working on PSB

Also updating several assets

- A* looks to have massive improvements so updated to *.95
- Curvy Spline has a newer version, hoping it fixes my issues with gizmos not showing up for the rotations

Player Movement, or other script, seem to be causing the player object to rotate gradually, spinning around on the y axis. still unsure why. updated scripts to fix this but doesnt seem like it did anything

Hyperpassive as a rubric for the music and interactivity in Beat Remake

[https://www.youtube.com/watch?v=S4pHJN8YclE](https://www.youtube.com/watch?v=S4pHJN8YclE)

[https://www.youtube.com/watch?v=woLgWVZPMk4](https://www.youtube.com/watch?v=woLgWVZPMk4)

[https://www.youtube.com/watch?v=osGJGqDDby8](https://www.youtube.com/watch?v=osGJGqDDby8)

[https://www.youtube.com/watch?v=ZytOQ4NSciU](https://www.youtube.com/watch?v=ZytOQ4NSciU)

[Spatial Communication in Level Design](https://www.youtube.com/watch?v=AKeUZVikPV8)

[Unity VFX Graph：Ribbons and Balls (Event control)](https://www.youtube.com/watch?v=h9ApA9tHiqk&list=WL&index=12)

[SIMPLE Tip For Better Unity Game Architecture](https://www.youtube.com/watch?v=mAfpfUYhpAs)

[Practical generators for blender](https://www.youtube.com/watch?v=wBT0GGui75I)

[3 Game Programming Patterns WE ACTUALLY NEED.](https://www.youtube.com/watch?v=BwA36em_DnA&list=WL)

In game settings

[https://github.com/xZenvin/UnitySettingsFramework](https://github.com/xZenvin/UnitySettingsFramework)?