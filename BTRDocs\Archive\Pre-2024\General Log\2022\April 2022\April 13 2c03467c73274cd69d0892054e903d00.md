# April 13

Upgraded to Unity 2021 LTS - seem to be no issues! This appear to work fine

Before today all builds used 202.3.30f

Uninstalled earlier version - can bring back if needed!

**Debugging particle error**

Particle System can only spawn from mesh if all submeshes use the same primitive type

Appears to be Birth Particles of Enemy that is causing this issue?

> set Mesh to None and that will solve the problem... well partially because the mesh that is causing the problem still won't be usable.
> 

> You need to check all your meshes and see if they have more than one material. Emitting a mesh that has more than one material is more often than not the cause of this warning message.
> 

![Untitled](April%2013%202c03467c73274cd69d0892054e903d00/Untitled.png)

Set shape properties of particle system to not rely on single material OR use mesh colors - no error!

May need to revisit this though based on above answer

ERROR 

When enemy reused, they do not spawn properly

ERROR

Enable / Shoot particles of bullet are going in opposite or very different direction then the bullet

These are parented to the bullet, so they dont move with the enemy. These should probably just come from the enemy. 

ERROR

Time Rewind seems to break randomly?

ERROR 

Bullet trails not in correct direction

[https://www.youtube.com/watch?v=agr-QEsYwD0&t=4s](https://www.youtube.com/watch?v=agr-QEsYwD0&t=4s)

Run through this whole thing for proper implementation?

Changing Unlockable Bullet weight to 0 for now! Not using it in current testing

Lofelt Vibrations - is this what I should use?