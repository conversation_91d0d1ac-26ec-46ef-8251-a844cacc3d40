
## January 2022
* All Items
https://www.youtube.com/watch?v=P7cYVg5fAvY

## April 2022

#### April 5

*   Cleaning up hard drive to install UE5
*   Moving Landforms to hard drive - worked fine
*   Watching Unity Twitch GDC stream for info - looking for Tunic Post Processing info
*   ShaderForge used for the texture
*   Automatic UV in Pro Builder largely does the job
Need to thoroughly look at my wave spawner for gameplay issues

#### April 6
*   Random Flow for Blender - thought occurs, all these metallic textures and materials i see pop up for sci fi stuff. Play around with them! See what kind of things I can create. Could possibly bring a bit more of a textured look to characters and things like Tunic does

#### April 13

*   Upgraded to Unity 2021 LTS - seem to be no issues! This appear to work fine
*   Before today all builds used 202.3.30f
*   Uninstalled earlier version - can bring back if needed!

#### April 14
https://www.youtube.com/watch?v=agr-QEsYwD0&t=4s
https://www.youtube.com/watch?v=BD4Mpq8BqQk

#### April 15

*   [https://www.youtube.com/watch?v=v5_RN8o1b3g](https://www.youtube.com/watch?v=v5_RN8o1b3g)
*   Watching this about performance enhancements - lots of good tips!

---

#### April 18

*   Trying to document aspects of the game in Figma instead of Miro

    Not feeling like this is super beneficial

## April 19

*   Look over - make a solid plan. Evaluate. Move forward! Important to do this tomorrow.

##April 28

*   *Limiting Creativity
"Limitations to stoke creativity - remember a game is never done, only released
  *   [https://www.youtube.com/watch?v=Xd7u6r5IvGQ

## May 2

*   Charles Amat Advice
OOP Techniques, SOLID priciples
* Jason Storey videos - cleanly structuring the code vids
What do i want
*   "Tooling & Workflow","Software Architecture https://www.youtube.com/watch?v=sh7f4K9Wbj8&t=1s

* Local functions "https://www.youtube.com/watch?v=D7Kf1CY1PNQ

* The better you make things the more you will get done" - - the more people will respect it
Do I have some good code? Is it a game

## MAY 6

Need to fix for 3D scene
https://qa.fmod.com/t/assistance-using-settimelineposition-in-unity-studioeventemitter/16846
    https://www.youtube.com/watch?v=PjZLIiupIsQ&t=596s

https://www.magcloud.com/browse/issue/1820005" -What is happening?

##July 6

*   Where does this happen or come from and what did you need

""Need consistent event ID’s for Koreographer"

[https://www.youtube.com/watch?v=pMOPgoTNYFk](https://www.youtube.com/watch?v=pMOPgoTNYFk)
You never say and
Where are you on this action""
### Where do you go to get better code or ideas, https://game dev and this video of a girl

How do I improve performance
Can not use all at once -
Add new audio clips on every scene, so the player is always listening to something"""""
What can i get better at and what do you enjoy?"""

*   What can you use"""""
Why do you start there"

What would you say
There has been a very easy tutorial on things:
https://m.youtube.com/watch?v=Cq_Nnw_Lwnl ""You Died or pause Screens""

### May 10
Does this cover my 3D scene
[Better Coding in Unity With Just a Few Lines of Code](https://www.youtube.com/watch?v=r-RCfmQqLA0
https://www.youtube.com/watch?v=7EZ2F-TzHYw&

Doesnt appear to be all in one place so what do you have to do??
Do this type of game and now you’re in 3-d"https://www.youtube.com/watch?v=mCKeSNdO_S0
https://guavaman.com/projects/rewired/docs/ControlMapper.html
What are the main ideas
*   Have new code and the video be easy enough""
https://.youtube.com/watch?v=Cq_Nnw_LwnI

###MAY 12
Are there more or do you need more or just make more.

What am I doing with these videos
Should have and AutoLoad for game

###May 13
Need the most
This can help solve the problem though
Profiler auto connects to build - does so by IP. Just open it in Unity Editor

### 15 MAY
*   [Object Pooling in Unity 2021 is Dope AF](https://www.youtube.com/watch?v=7EZ2F-TzHYw&)
Use Recenter the Origin and
AutoLOD
Mesh -> Cleanup

### 16 MAY
*   [20 Mins of GREATNESS - BEST #unitytips](https://www.youtube.com/watch?v=dQnAc2mEDI0)
Need a RESET of reticle and camera position on transition phase
Should consider, current more popular genre are movement shooters"

### June 10
Node canvas vs Behaviour Designer

### June 14

""BuildingSettings

## June 28
Back to -1 and .44s
*   [Where should i be in June"]

## July 12
Did the check

##Sept 13

What do i need to get going:
*   The game
Check code and set up
Where is the code for those""

*Is the game getting started or not?*"*"

*Is this making it hard?
*   Then the next idea

## September 15
Dynamic Crosshair

##October13

Make updates on what’s happening,
 walk through ideas of what needs to be done

make bullet lock on more obvious
* Visual effecf"