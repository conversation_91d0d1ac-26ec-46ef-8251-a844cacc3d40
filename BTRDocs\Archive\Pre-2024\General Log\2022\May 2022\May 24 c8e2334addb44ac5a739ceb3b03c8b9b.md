# May 24

Testing dolly switching and debugged a movement issue

Jittery playe rmovement issue seesm to be from going far out of play area - causes math errors

Keep new levels / areas close - stack them veritcally? Just out of eyesight aswell?

Trying out new level designs with GPUInstancer in scene as well

debuggig multiple enemy lock on too

Importing modern Sci Fi building back to Blender file for Level Design

Need a real Concept / Level Design day where I assess modelling options ideas an lay out a plan