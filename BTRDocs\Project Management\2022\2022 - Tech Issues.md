
# 2022 - Tech Issues

This document consolidates all reported bugs, technical challenges, and debugging processes from the 2022 development logs.

---

## April 2022

#### April 5

*   Also this error seems to happen upon spawning

    ![Wave Spawner Error](April%205%20a010455306bb40c0b1be2dcf1a6ccff4/Untitled%207.png)

---

#### April 7

*   Works! Sort of...

    ![Spawner Issue 1](April%207%208026b6a3f195439a81bc192bcd0cd6b7/Untitled.png)
*   Not changing to a new Wave - need to see why!

---

#### April 10

*   Debugging WaveSpawner issues - why wont waves advance?
*   ALSO - double shooting issues appears to be the particle system flying off lol? WHat’s happening there i wonder

---

#### April 12

*   IMP - when an enemy is reused it doesn’t seem to properly come back. Some refinement of the enemy class and instantiation / make active will possibly need to be made
*   IMP - don’t forget the double shot from enemies - this is likely the particle system shooting off in the opposite direction, will have to verify and fix

---

#### April 13

*   Particle System can only spawn from mesh if all submeshes use the same primitive type

    ![Particle Shape Error](April%2013%202c03467c73274cd69d0892054e903d00/Untitled.png)
*   Set shape properties of particle system to not rely on single material OR use mesh colors - no error!
*   ERROR
*   When enemy reused, they do not spawn properly
*   ERROR
*   Enable / Shoot particles of bullet are going in opposite or very different direction then the bullet
*   These are parented to the bullet, so they dont move with the enemy. These should probably just come from the enemy.
*   ERROR
*   Time Rewind seems to break randomly?
*   ERROR
*   Bullet trails not in correct direction

---

#### April 14

*   Respawn Error

    ![Respawn Error](April%2014%201633eaf26d58423e8d180043fcca492a/Untitled.png)
*   Quibli Shaders - Cannot get alpha of outline to work
*   Transparency not working when bullet locked - trying to fix

---

#### April 18

*   Bug found - LockedList on Shooting is empty but Canvas number says 1
*   Bug found - Enemy continually rising? maybe an A\* / RichAI Issue
*   Bug found - if bullets are heading for target and it dies first, they will get stuck!
    *   Need to evaluate that target is still active, and if it isn’t just continue forward and reinitiate lifetime
*   Bug found - bullets rotating with enemies still happening
*   Bug found - Issues with Chrono persist in Level Test 4

---

## Bug collection

*   Bug found - LockedList on Shooting is empty but Canvas number says 1
*   Bug found - Enemy continually rising? maybe an A\* / RichAI Issue
*   Bug found - if bullets are heading for target and it dies first, they will get stuck!
    *   Need to evaluate that target is still active, and if it isn’t just continue forward and reinitiate lifetime
*   Bug found - bullets rotating with enemies still happening
*   Bug found - Issues with Chrono persist in Level Test 4

---

## Crosshair / Projectile bug Fixing

![Crosshair Error 1](Crosshair%20Projectile%20bug%20Fixing%2036853ac3d16c4df08caaf2ae442ac77b/Untitled.png)

Koreo related errors here

![Crosshair Error 2](Crosshair%20Projectile%20bug%20Fixing%2036853ac3d16c4df08caaf2ae442ac77b/Untitled%201.png)

*   Player is locked on to current EnemyTarget
*   EnemyTarget is still active - not dead
*   Targets list is 0
*   Locks count is 3
*   LockedList showing 3 items - projectiles
    *   1 is parented to horizontal plane
    *   Has no projectileTarget
    *   Shows both Locked and Released

![Crosshair Error 3](Crosshair%20Projectile%20bug%20Fixing%2036853ac3d16c4df08caaf2ae442ac77b/Untitled%202.png)

*   2 Next projectile on list is inactive!
    *   Has died - everything disbaled and no projectileTarget
    *   Has NEGATIVE lifetime!
    *   Still has LockedOnPrefab
*   3 Exactly the same as #2
    *   Negative lifetime and all other attributes
*   If lifetime < 0 it just kills itself
    *   Probably should pause lifetime if locked?
    *   Also only kill itself if it’s not locked - need to look at if my locked states are setup properly
        *   CHANGED THIS TO LAUNCHING - not LOCKED state
            *   UPDATE Launching does not appear to be the correct approach either
*   Still a bunch of inactive bullets in lockedlist
*   Error appears to be Cross trying to launch inactive bullets

---

## Feb 1

*   Bug found - LockedList on Shooting is empty but Canvas number says 1
*   Bug found - Enemy continually rising? maybe an A\* / RichAI Issue
*   Bug found - if bullets are heading for target and it dies first, they will get stuck!
    *   Need to evaluate that target is still active, and if it isn’t just continue forward and reinitiate lifetime
*   Bug found - bullets rotating with enemies still happening

---

## Feb 2

*   Bug found - Issues with Chrono persist in Level Test 4
*   Bug found - bullets not reparenting properly, still attached to enemy at times when they’re locked by the player
*   Bug found - bullets circle back on itself

    ![Bullet Loop Error](Feb%202%20182aecade6f441df91692d4fba03671b/Untitled%203.png)
    *   Negative lifetime but still circling

---

## Jan 29

*   Outline of key things to fix in Level Test 4 / in General
    *   Not in order of importance
        *   Chronos time control no longer working
        *   Bullets dont hits targets / loop in circles
        *   Error in Crosshair class break everything - tied to Koreographer
            *   Unregister for events necessary?
        *   Wave Spawner not working correctly
            *   Tie this to muscial cue / viz fx once working
        *   Bullet visibility still a big issue
        *   A\* Graph or enemies have problems when moving

---

## Jan 30

*   Weird Emerald AI Projectile class issues - I THINK - deleted EmeraldAI to try and rectify this
*   Bullet homing issues - can this be solved easily?
*   Pause button not working??

---

## Jan 31

*   Error popping up here!

    ![Error Log 1](Jan%2031%204e4bdfce8fd0416abce16f6b903570fd/Untitled.png)
*   Now this is the issue

    ![Error Log 2](Jan%2031%204e4bdfce8fd0416abce16f6b903570fd/Untitled%201.png)

---

## March 2

Found an issue -

all enemies need to reregister attack capabilitiles with currently playing track
[Koreo Trouble 1](March%202%206d0124c221e8460398062c22150ceb01/Untitled.png)

---

## March 3

Seems like collision to change song section is unrealiable - other possible methods?

Bullets are coming out of both ends of enemy? Not sure, possibly collision issue with bullets

---

## March 8

A\* broke for some reason? Reverting to old unity, hopefully dont break anything else ☹️
*FIXED* Enemy Plane was out of wack and causing the issue
Build Note: transition area - removing transition area to fix the drop out - is this a common problem?
Enemy Lock on aiming - problem

---

## May 3
*   FMOD Error - Global paramters howing unavailable in Events in Unity, set to Local

## May 5

*   Seems every problem bullet has a negative lifetime. Recycle bullets with negative life time?

## May 8

*   A\* broke for some reason? Reverting to old unity, hopefully dont break anything else ☹️

## May 9

*   Bullet shoot from wrong side of enemy often? what is going on here?

## May 13

*   What’s the best LOD group Fade Mode?
 *       Verify Crossfade is working - may not be in URP. The video has instructions on how to deal with this
  *       Doesn’t seem to work to me! May need work around

## May 17
 *   Enemies getting stuck - is this due to colliding with buildings?
 ## May 20-22

*   Reticle bouncign around - error is within Shooter Movement script
*  Debugging multiple enemy lock on too
*  Floating Origin Point - Cant use Gameplay Plane - What else?

[Bullet Stuck State](May%2020-22%2043c0298f9a4646d6aaa3672536ff3002/Untitled%201.png)
*   All seem to have same attributes generally - negative lifetime, etc
## May 23
[Bullet Error Out of Bounds](May%2025%2098d127f8725e40448db18110e5748904/Untitled.png)

*   Comes from various Enemy Shooting Particles
* [floating Bullets](May%2023%2030145d0228ee4f4f857f0925c57eafd6/Untitled%201.png)
## May 25

*   Breaking error that happens

[Bullet Error Out of Bounds](May%2025%2098d127f8725e40448db18110e5748904/Untitled.png)
* Comes from various Enemy Shooting Particles

## June 10

*   Keep hitting this error once the level progressess

    ![BD AI Issue 1](June%2010%206e1131e0df804ca19f1cbc3716c29ae6/Untitled.png)

    ![BD AI Issue 2](June%2010%206e1131e0df804ca19f1cbc3716c29ae6/Untitled%201.png)

    ![BD AI Issue 3](June%2010%206e1131e0df804ca19f1cbc3716c29ae6/Untitled%202.png)
---

## June 14
 * You can do into shaders and see how many variants they have
Build time errors]

## June 17
Object is breaking things? Not sure why
## June 29
*   Render Camera broken - this is for the UI overlay