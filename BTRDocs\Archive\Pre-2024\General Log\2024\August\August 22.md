# August 22

Various optimizations to Projectile Manager and ProjectileStateBased

Maybe worth not having timelines on every Projectile? Maybe that’s not correct

Tonight, should architect a better transition system. 

Consider all parts, work it out - will make long run easier!!

Strangely, performance is showing a locked 60 and i hav no ida why. there are dips but gnereally it’s at 60, seems strange to me. Requires investigating. 

**August 23**

This seems to have disappeared. Interesting!

Need a better transition system, not happy wiith the current one