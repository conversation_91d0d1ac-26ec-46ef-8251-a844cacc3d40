# Short Stories Lecture

**<PERSON>**

What is a short story?

It’s about delivering a specific emotional punch 

Novels are about immersion !

Readers are picking it up to have an type of experience, they want different things from each!

Olympics Novel- Could watch every bit of content surrounding it, interviews, history, ties ins, and THEN see event, then the post-game. 

Olympics Shortstory - get a youtube clip of the exact exciting moment in the routine where they do the good stuff and it ends.

You just want the PUNCH - that QUICK fix

The mice quotient 

MICE 

Milieu, Inquiry, Character, Event

![Untitled](Beat%20Game%20Notion/Learning%20and%20Research/GDC%20talks/Untitled.png)

![Untitled](Beat%20Game%20Notion/Projects/June%2027/Untitled%201.png)

Around the world in 80 Days, Gull<PERSON> travels, etc

Conflicts stop your character from reaching their goal

As a writer, you figure out what you charcter needs to do, and then systematically figure out what prevents them from reaching their goal 

Once the goal is reached, the story is over

![Untitled](Beat%20Game%20Notion/Projects/June%2027/Untitled%202.png)

All the stuff in the middle is about difficulty navigating the place, struggling with the place you’re in 

**Inquiry Stories**

![Untitled](Beat%20Game%20Notion/Projects/June%2027/Untitled%203.png)

Start with a question, end when it’s answer!

<PERSON>, Murder Mysteries

![Untitled](Beat%20Game%20Notion/Projects/June%2027/Untitled%204.png)

Your goal as a writer is to keep the character from finding the answer

Redherrings, etc. 

**CHaracter Stoires**

![Untitled](Beat%20Game%20Notion/Projects/June%2027/Untitled%205.png)

Driven by angst, a character is unhappy with themselves, and end when they’re happy. 

They begin with an identity shift, with how the character self defines 

They end when the characters self defintino solidifies

![Untitled](Beat%20Game%20Notion/Projects/June%2027/Untitled%206.png)

Coming of age stories, romances

As a writer, you prevent the character from changing, thigns backfire, etc.

**Event Stories**

![Untitled](Beat%20Game%20Notion/Projects/June%2027/Untitled%207.png)

When the sense of normal is disrupted, status quo interrupted, the story starts.

It ends when there is a new status quo 

Very much an external threat

![Untitled](Beat%20Game%20Notion/Projects/June%2027/Untitled%208.png)

As a writer, you prevent the character from establishing the status quo, you have fights scenes, you have chase scenes, explosions, more disruptions to status quo

It is easy to confuse the following types!

![Untitled](Beat%20Game%20Notion/Archive/Old%20Research/GDC%20talks/Untitled%209.png)

But Interior VS Exterior is how you delinate the difference

Most stories are made up of multiple threads!

![Untitled](Beat%20Game%20Notion/Archive/Old%20Research/GDC%20talks/Untitled%2010.png)

You embed threads within each other to achieve this. You have to close interior threads befor eyou can close exterior ones, etc. Very code like! 

Put the inquiry ones inside the large box so they’ll fit inside the large one of the milieu

An example!

![Untitled](Beat%20Game%20Notion/Archive/Old%20Research/GDC%20talks/Untitled%2011.png)

Lord of the Rings

![Untitled](Beat%20Game%20Notion/Archive/Old%20Research/GDC%20talks/Untitled%2012.png)

**Flash Fiction Exercise**

Opening - we meet our charcter and make promises to our readers

Establish in any order - Who, Where, Genre - First Three Sentences

Temp guidelines - Science Fiction, Jockey, Coaster

Link location to a sensory detail

For character you want to use point of view. Define a short hand for the character like ‘ sexist boss’ , ‘ angsty teenage jockey’ - something that gives you an idea of what their attitude is

Define who using action - what is the thing that they’re doing 

For genre- get in a genre sepcific detail (specific & unique) as fast as possible. 

Tyler’s: Jessica leaned over the mix desk, looking out to the crowd for any detail that might spark her MusicMind playlist database. Cybernetic hair piece, hologrpahic jacket; magneticly floating drink coaster, she spotted nothing all that interesting to probe the search engine for a uniquely ‘jessica’ track. She needed something to stand out or the label rep in the back would be sure to leave after their watery gin and tonic.    

For this exercise

**Two Characters - One location** 

This is because

![Untitled](Beat%20Game%20Notion/Archive/Old%20Research/GDC%20talks/Untitled%2013.png)

Each character or stage you add, has the protential to add AVG 750 words to your story

Each mice quoteint thread has the ptoential to make the story HALF AGAIN as long - because you have to keep it alive all the way through that story 

Next sentence in the story should introduce our conflict. Trying to achieve the goal and failing to achieve it - try / fail cycle!

In short story, you want it in first 13 lines - in flash ficiton, you get two sentences.

What is your character trying to do and why. Next, what is stopping them. 

Tip: You can imply that some of this happened before our scene starts 

Tylers: This was her last shot at a big break: she didnt have enough credits to keep touring around the entertainment planets like this, playing to half empty back rooms. It’s back to serving tables halfway across the galaxy and the thought made her sink like a stone. 

Are they trying to escape from something? Are they trying to navigate in a place? If so we have a milieu. 

Are they trying to answer a question? That’s an inquiry story. 

Are they unhappy with themselves / angsty? THat’s a character story 

Are they trying to change the status quo / normal? That’s an event story 

As the author, you take this an know the kind of conflict to throw at them. Milieu story, stop them from getting to wherever they’re trying to go.

If it’s Inquiry, block them from answering question.

Character, making them unhappier

Event story, make more things go wrong

WHen something fails, they try a differnet appraoch.  As author , knock the character down and pick them up again. Doesn’t have to be a big try / fail cycle, asking a question and getting snubbed counts. 

Each action the character takes should have a consequence. 

The format is YES, but…. NO, and…..

Yes→ make some progress. but pushed back a bit.

No → DIdn;t make progress, and were pushed back abit

![Untitled](Beat%20Game%20Notion/Short%20Stories%20Lecture/Untitled%2014.png)

Star Wars example. Rescuing the princess is a milieu thread. 

Being chased by storm troopers → Are they gonna get out?

What do they try? jump down garbage chute. Yes, but…… new trap.

They try blaster, does it work? No, and it wakes up a creature beneath them. 

Can they deal with the creature? Yes, but only because the walls of the trash come in. 

We are looking for a try / fail cycle for our character.

Try for failure AND things get worse. 5 whole sentences to do this!

Tyler’s: Jessica spots a dragon tattoo poking out from the agent’s sleeve: definitely a symbol from the JDJ X-Racers. This is exactly the ammo she needs; focusing on the X-Racer catalogue and watching the tempo of the dancer’s across the front row, she mashes it up with what she remembers from the sexy new designs of the latest issue of fashionNOW, mixed with the feeling of charts of the latest META stock performance. The MusicMind responds and pumps out a intensly resonant sub bass pulse, igniting the front row in cheers. They dance harder than they have the whole set. The agent perks up, looks at her dead in the eyes towards the booth, and draws a line straight across his neck. She played exactly the wrong thing and blew it. 

Proportionally, we spent more words on try / fail cycle then the setup. 

Now we start into the end of the middle. Try / Cycle is the middle

It’s about the 2/3 to 3/4 mark in most things. You ask questions, you open up problems, you make things worse. and then you start to resolve them, close those story questions.

This is why you always bog down around here - because you’re changing mode. 

Psychological phenomenon - When doing something, it’s about the 3/4 of doing something is when it seems like you can’t possibly finish it. Mentally, you assume you have much further to go then you actually do. But also it’s because you’re changing modes into ‘ending things’

The resolutions are YES, AND - NO, BUT

Movement towards the goal and contiuation towards the goal. 

![Untitled](Beat%20Game%20Notion/Short%20Stories%20Lecture/Untitled%2015.png)

Yes and would be - Someone is hungry, are they able to buy a lunch? Yes, and it came with an extra order of churros!

No but would be - They’re trying to diffuse the bomb are they able to? No, but when it explodes it goes out the window and lands on the bad guy!

We’re switching to Closing Mode, so this next Try / Fail cycle is a Try / Succeed. 

In a story, you would do multiple of these iterations to proceed towards the end goal. 

In Flash Fiction like here, we just solve the problem right away.

5 sentences. 

Tyler’s: She lets the track play out anyway to close off her set, audience enjoying the beat and giving it their all. The last song of her set and she’ll play the hell out of it, agent approval or not. He looks her way again, eyes widening ashe downs the rest of the drink and slams it on the table. “Good one, innit! Dance away little babies, it’s my new hit and you’re all gonna love it with a lil max sparkle across the top eh” The crowd cheers and dances harder and Jessica doesn’t know what to make of it. I guess he likes it? Maybe there’s a shot after all. 

Ending! Now we need to satisfyingly close out the big elements. 

You’re mirroring the ending with the beginning. We need to know WHO, WHERE and GENRE / MOOD

THings have shifted over the story, so we want to ground the reader to understand what has shifted, drawing a line under it. Hitting those points agian will help us see the change. 

WHO - action or reflection

Where - sensory detail

Genre / mood - specific and unique

Often the tone has changed, so we want to highlight this. Express this is the words and structure you use. 

Tylers: The agent staggers up to the mix desk as the song comes to a finsih and the next dj jacks in with his hyper visor. He leans toward her ear, stating “Well you forced me to play that one up, not what I had in mind tonight but you’re a spunky one and i like that. How about you and me grab a drink and you tell me why you’re reminding me of the biggest mistake I ever made. ” Well it wasn’t the fawning adoration she hope for but this was her chance, Jessica could stay in the game just a bit longer. 

MY THOUGHTS: not much of an ending, but soemwhate captures the point of the lecture.  

The sentence length is a rule of thumb, but often its a bit more flexible then that remember.

These ideas can be applied at a longer length.  

**Q&A**

What happens when the goal changes part way through the story.?

Signpost that this might happen early in the story, by having something that is thematicly linked to what they will eventually solve. 

Make sure the initial one is still closed out in some way. 

In longer form would you still establish genre so quickly?

Yes. Really set the stage and ground them. It’ll be too slow if you take too long to ground them. But it doesnt have to be superspecific. 

Follow up 

[https://www.youtube.com/watch?v=EgFE2W9qICo](https://www.youtube.com/watch?v=EgFE2W9qICo)

Symptoms - This is how i reacted to the story - This is what we are mostly looking for from people

Diagnosis - This is how I reacted and here is why I did - Trickier becuase they may misdiagnosis things 

Prescirption - Telling you how to fix it - go to very few people for this! Way harder to do than most people think. 

So mostly look for symptoms. 

4 Primary ones to get from people!

 Disbelief, didn’t care, confused, things they thought were cool

First threw focus on the things that throw people out of the story 

Diagnosis from VERY trusted readers.

When you’ve made adjustments, give to FRESH readers without preconception. You want to avoid leading questions. 

**Receiving Critique**

Don’t have too much editor brain while writing, will stop you too much!

BUT - if you train your internal editor to work WITH your internal writer, can help!

As you receive critiques, internal editor can get the symptoms from others and then diagnose them 

Some reactions to symptoms!

DOH - It was obvious and I know how to fix that

Different problem - I see what you mean but I think the problem identifed is actually a differnet thing

I disagree - This usually occurs when someone wants the story to be different than the one you’re telling.

WTF - Reaction is so off base you dont know what to think of it. FInd out if they can explain why they think that - keep that question clean and straightforward as possible. May be something you overlooked!