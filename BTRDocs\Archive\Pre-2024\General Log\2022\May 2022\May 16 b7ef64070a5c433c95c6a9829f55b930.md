# May 16

[20 Mins of GREATNESS - BEST #unitytips](https://www.youtube.com/watch?v=dQnAc2mEDI0)

General unity tips video

Controllable SKybox? 

May be useful in setup - or just look up this kind of thing

[https://assetstore.unity.com/packages/vfx/shaders/retrowave-skies-dynamic-synthwave-skybox-asset-pack-197258](https://assetstore.unity.com/packages/vfx/shaders/retrowave-skies-dynamic-synthwave-skybox-asset-pack-197258)

Need to think - what am i making? What is the context?

**Think Rez**

The stages represent the evolution of human civilization, 

The bosses represent the evolution of life on earth, 

Your avatar represents stages of "enlightenment" as you level up. 

What does the world look like?

Digital corruption - buildings destroyed - based around infrastructure

Japan loves wires and lightpoles and such - very aesthetic!!

Thinking quite abstract  - but makes sense to base this around real environments and destruct from there

What about serial experiments lain? 

“Today, <PERSON>n’s story resonates more so as an allegory about the perils of forging one’s identity—an alternative identity, however false, misguided, perverse, delusional—using the internet”

What if you’re excavating digital memories? 

Talk about things in Layers liek Serial Experiments Lain Layer01, Layer02

What are the scenery / objects I have access to?

Design video on this! 

[Why Is It So Hard To Make Games On Rails?](https://www.youtube.com/watch?v=mUjZUPPrz-o)

1) Density of action and spectacle is necessary - WOW setpieces needed

- Camera work to liven things up
- Changing up attack strategies to keep things interesting

2) Good pacing! A lot like a roller coaster

- house of the dead and time crisis are not as much spectacle but paced well
- very much a thrill ride
- quick action!

Sense of progression - im flying around but where am i headed?

Consider the slower pace of Pokemon Snap - and how it features a puzzle-ish element added to it

Sayonara Wild Hearts is a QTE / Rail Shooter vibe. Big Spectacle!

Ring Fit Adventure is sort of a rail shooter as well!

Ex Zodiac - modern star fox 

[Game Templates - Rail Shooter Preview](https://www.youtube.com/watch?v=nhivR66tT5k)

Should I implemenent an aim system more similar to this?

[MORE Movement Shooters - FUNKe Study](https://www.youtube.com/watch?v=oRTh2aMgPY4)

Should consider, current more popular genre are movement shooters

Rail shooter games throguhout the ages

[Evolution of Rail Shooter games (1978-2021)](https://www.youtube.com/watch?v=DsI-Bte1ZYI)

Highlights 

- Starblade - 1991 - Visual Inspo
- Wild Guns - 1994 - How to dodge bullets?
- Starfox 64 - Look for full level layouts for design
- Panzer Dragoon Orta - Tunnel gameplay and levels
- Killer 7 - weird interesting game
- Sin & Punishment Star Successor - spectacle, wild!

Starfox Design

[The Design Secrets Behind Star Fox 64](https://www.youtube.com/watch?v=PnvYCc-I8w0)

![Untitled](May%2016%20b7ef64070a5c433c95c6a9829f55b930/Untitled.png)

Layout for moving through Starfox systems

Star fox on rail VS all range mode - would alternate movement modes be good?

<aside>
💡 Figure out why enemies are glitching / Editor view - is it path recalculation?

</aside>

<aside>
💡 Is it the speed of the latform moving? Is it the turning of the platform messing them up?

</aside>

<aside>
💡 Make them move faster as well!

</aside>