# May 22

Implementing the X Ray URP feature as a good start to the day

[X-ray Stealth Vision in Unity Shader Graph and Universal Render Pipeline](https://www.youtube.com/watch?v=eLIe95csKWE&t=1s)

Implemented on enemies. Didn’t do the animated version, worth considering!

Will alter look of the effect

Projectiles now working! Refining movement for better accuracy

Simplified by removing 

Rewinding time messes with Enemy Dodeca script that places movement directly on the navmesh (stop from flying off)

Testing this as well - is the error and actual problem?

<aside>
💡 Bug!

</aside>

Time Rewind can cause enemy to fall off - is a real issue! should I freeze enemy position in time?

Maybe if it goes on too long - need time rewind restriction again?

Attempting to fix projectile movement, but enemy gimbal making things difficult to see, so fixing this first. This appears to be fixed now, may need some adjusting.

For setting dolly points on the camera transition, added the following event which appears to be logically working - increment dolly on camera switch

![Untitled](May%2022%20f1e6a7e6c4024735a87ab77a2ce26e3c/Untitled.png)

Looking into issues of direction / roll of player not being accurate when switching to another structure

Seems like it’s Horitonal Rotate that needs to be reset to default values, adding this in the transition 

SOmething is setting rotation values on horizontal rotate at start of transition, not sure what! But need to track this down and fix the issue 

Editied PlayerFacingForward script so that it properly resets horizontal platform to 0 rotation, working now. 

<aside>
💡 Bug!

</aside>

Checking GroundCheck on Player to see if it’s causing player to fly off screen when transition happens. It is! Ground check needs improvement, disabled for now. 

<aside>
💡 Idea!

</aside>

Re-lock on needed for taking out most enemies? Kind of boring to maintain lock on, means you have less to do. Enabling this and movign forward with it

Physics.Boxcast taking a lot of resources - is this just a health collecatable mode issue or issue in geenral?

Issue - Some Projecitles are not hitting their TargetCube targets AND they’re not dying after an enemy is already dead / lose their target. Add these options in the script

Groundcheck fixed up a bit. Seems better

I think issues exist in Launchback with recent Projectile errors

Mayeb not! Seems like related, but moreso about reuse of a bullet, setting details/states appropriately

THINGS TO DO

Fix Death / FX of Enemies - Parts are busted