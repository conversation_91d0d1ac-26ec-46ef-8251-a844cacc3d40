# January 17th

Adjusting Reticle control to try and fix jerky issues

Have Interpolate off right now to make it more responsive, unsure if that’s actually helping

But in general, it’s working better than before by having adjusted the code to better use the new input system - existing outside the Update method I believe. 

Testing reticle movement in build to verify changes occur there.

Need to figure out how to get camera to rotate up/down more when reticle moves towards edges, necessary for looking around. Thought I solved this problem already????

Need to find a way to reduce build times - Assembly Definitions? Look into it

Need to fix various small problems with game play, audio, that i thought were already fixed???