# Jan 6

Made some progress over christmas with refining performance and systems. 

But it means I have to reimplment some basic features, which is coming along well. 

Some new things

- Using Shapes for some UI
    - Added enemy UI that needs to be turn on/off depending on time features
        - Finish implementing
- Player AI for playtesting automation
    - Basics implemented but needs further refining