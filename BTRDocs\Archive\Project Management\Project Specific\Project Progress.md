# Project Progress

## January 2024

### January 17th, 2024
- Reticle Control: Adjusting reticle control, using new input system.
    - Existing outside the Update method.
    - Working better than before.
    - Testing reticle movement in build to verify changes.
- Bug Fixes: Need to fix gameplay and audio bugs that were thought to be fixed.

### January 22nd, 2024
- **Lock-on Projectile Behavior:** Changing lock-on projectile behavior.
    - If you attempt to lock on within a certain radius, it just shoots at the target enemy right away.
    - Need to also address behavior if there are no target enemies - ideally it just fires off into the distance.
    - Code is in there but needs to be verified, and lockableRadius needs to be tested.
- **Scene Reload:** Added press R to reload scene, for hot reload testing.

### January 23rd, 2024
- **Shooter Movement:** Adjusted shooter movement to be separated from frame rate.
    - Connected to Timeline of Chronos - Constant Clock.
- **Camera Adjustments:** Looking into camera adjustments.

### January 24th, 2024
- **Steam Devkit Upload:** Steam Devkit upload system setup - can easily upload to Steam Deck now.
- **Camera Movement:** Camera movement when reticle moves to edges also setup - this is working ok.

### January 25th, 2024
- **Shooting Movement Adjustment:** Adjusted shooting movement rotations to prevent camera going through ground, center of screen adjusted, player now lower in view.
- **Audio Filter Bug Fix:** Filter to audio no longer applied when hitting start. Fixed, all exists on Pause Menu Manager now.
- **Reach Task:** Updated Reach to and trying to quickly fix things. Seems good so far!

### January 26th, 2024
- **UI Adjustment:** Did more adjusting the UI, basically fixed it.

### January 28th, 2024
- **Projectile Obstacle Avoidance:** Added some code for projectiles to avoid ground, likely needs to be tweaked.
- **SDF Effects:** SDF stuff is adding some interesting effects, not final but interesting experiment.
- **Steam Deck Build:** Trying out build on Steam Deck.

### January 29th, 2024
- **Enemy Material:** Changing enemy material, using Ultimate Lit Shader then back to regular Lit shader.
- **Enemy Object Pooling:** Changed enemies to use Better Object Pooling for Birth/Death FX.
- **Dodging Fixes:** Fixed dodging issues, was dotween being killed in the movement. removed the material swap when dodging cause it’s not currently working - will come back to this

### January 30th, 2024
- **Static Shooting Enemy:** Adding a static shooting “enemy” - not killable currently but emits bullets from terrain.
- **Wave Spawner Removal:** Removing all the wave spawner stuff.
- **Cinemachine Camera Switching Fix:** Changed Cinemachine Camera Switching to disable player model and reticle instead of whole player object.
- **Snake Eyeball Prefab:** Snake Eyeball Prefab is made.

### April 1st, 2024
- **Jittery Issue Smoothed Out:** Seemed to have smoothed out the jittery issue with lerping on ground detection.
- **Rotation Issue Kinematic Rigidbody Attempt:** Enabling the rigidbody as kinematic has seemed to fix my intermittent rotation issues. 
- wel… not quite! still debugging
- seems to help only if i have player movement script disabled
- A* stuff seems to not effect this issue

### April 2nd, 2024
- **Player Rotation Fix:** Adjusted ensure player upright to be relative to parent object and that seems to have fixed seemingly random rotation of Player game object on wave/level object change.

### April 4th, 2024
- **Infinite Track Following Spline Idea:** Idea of Infinite track but following a spline. This is potentially how we can have some randomization in the boss battle but restrain to a specific area. May be useful in general!
- `OuroBossInfiniteTrack` script created, needs more logic from original script

### April 5-6th, 2024
- **Ouroboros Boss Battle Randomized Path:** Attempting a roughly set / randomized path for the boss fighting. A variation on OuroborosInfinite
- Not fully working but making progress
- **Twin Snakes Rough Layout:** Rough things laid down for the twin snakes
- **Twin Snakes Time Feature Work:** Working on the rough forward / reverse time feature of the twin snakes
- Issue with the snakes not finding their time line components properly, need to company to wroking example and see whats wrong here
- **April 6**
- **Twin Snakes Time Feature Implementation:** Made adjustments for Controller and Enemy Twin Snake Boss so that they can tie into the time keeper / global clock and move forward and backward

### April 8-9th, 2024
- **Prefab/Pooling Organization Adjustment:** Some adjustments to organization for prefabs and pooling
- **Twin Snakes Rotation Implementation:** Have a successful rotation of twin snakes looking at player but they do intersect in weird ways. 
- **Twin Snakes Movement - A* Idea:** Possible idea - use A* for snakes on a platform if moving around to follow player? Move slow
- Maybe not A* for this, not sure if truly a benefit though it does work.

### August 1st, 2024
- Minor adjustments - most importantly, need a proper asset list !
- Priority for tomorrow / the weekend. 
- While still available

### August 2nd, 2024
- Feel like i need to inject a bit more speed in the game
- Movement being one - rest to follow
- Made things faster in first ouroboros level, aslo adjusting camera
- higher lookahead time seems to help with camera in reverse position 

### August 3-5th, 2024
- Minor bug fixing. Issues with FMOD that i’m addressing
- Speeding some things up in the game to increase intensity
- Snake shouldn’t do hit anaimation and then death animation, just death
    - Done!
- Disbale Y rotate direction in cahsed by snake level
    - Done! Disable Player Features script
- Cant shoot in section after snake, Koreogrpaher issue?
    - Beleive i fixed this once before
- Section 3 music not looping properly
- Section 3 need the enemies and bullet speeds adjusted for faster movement. 

### August 8th, 2024
- COnversion of Crosshair to three speerate scripts seems to mostly work!
- Montioring…..
- Need to also monitor previous error of being stuck on reverse time
- Adjusting see through material for enemies - making it less visible

### August 14th, 2024
- Transfer of time control to game manager seem to be working for Crosshair functionality. 
- Other classes may need adjustment. 
- Slowly making it throgu hthem
- Also fixing ricochet dodge
- WOrks again now! Refine again
- Odd issue where Gme Widnow has a differnet camera angel in editor depending on where and how it’s placed. Really strange!
- Uploading build with all these adjustments changes, though need to test a bit more

### August 19th, 2024
- Fixing looping issues with Section 3 - FMOD adjustments needed
- Do these same fixes help with Shooting? It isnt workign for some reason
- No! SOme other reason i cant shoot. Can lock on to enemies and bullets but wont fire?
- These are snake enemies, so something could be amiss

### August 21st, 2024
- Working on Projectile accuracy, first improving the debugger to track what the accuracy is
- SHow 8 and 15 percent as hit rate, seems really low
- THis has increased to roughly 50%, working on making it even better
- Significant improvements, at around 70% now, something worht monitorign moving forward. 
- Still trying to improve this as it drops to 50% at times.
- Enemy Snakes are working, but need refinement. Refinign the damagable parts aspect to work cleaner
- Changing Wave patterns for shorter delays
- ENemy bullets still have trouble hitting at times, 
- Cmaera not acting very well, attempt to fix with old setup
- Lookahead attribute is the big factor, cant be too much, screws things up
- Adding a score / time boost when cahnging waves / sections. 
- Adding gizmos to projecitles to see whats going wrong, what adjustmetns i need to make so that the enemies always get hit. 
- Need to make the variance in projecitle colours more efficent, instead of assigning new materials need ot chagne the colours of current ones i thinkl
- Made things less efficient, need to try to get back to efficiency. I thoguht I kept burst for projectiles… need to revisit these issues. Run a diff one some uploads with cursor
- Deleted Projecitel Path gizmo due to potential errors in physics calculations, 
- Not entirely sure whats hogging performance, tracking it down - try not to waste too much time on this

### August 22nd, 2024
- Various optimizations to Projectile Manager and ProjectileStateBased
- Maybe worth not having timelines on every Projectile? Maybe that’s not correct
- Tonight, should architect a better transition system. 
- Consider all parts, work it out - will make long run easier!!
- Strangely, performance is showing a locked 60 and i hav no ida why. there are dips but gnereally it’s at 60, seems strange to me. Requires investigating. 
- August 23
- This seems to have disappeared. Interesting!
- Need a better transition system, not happy wiith the current one

### December 4th, 2024
- Have made several major optimizations over the past few days
- Need to test gameplay but seems functional so far!
- Ricochet may not be working - need to verify
    - Is it the spatial optimziation system messing this up?
- Also need to optimize rendering - this is the bottleneck now 
- Made a texture analyser - use to delete unused textures. 
- Player Movement split to Player Movement and Player Rotation
- Bug test this. Also putting ricochet dodge improvements/fixing within
- ricochet dodge implemented and needs testing
- Turning Enemies into more managable structure of EnemyBase and EnemyBehavior
- Review structure and implement in game
- For Rendering optimization, try windsurf with CatlikeCoding docs, see what suggestions we can implement in our case. what would majorly improve performance - look at frame debugger for this as well
- May also be a good idea to debloat project at this point - can import new assets if needed, keep some of the old ones around for future level development