# March 2 2023

Tower of Babylon Concept

![Untitled](March%202%202023%203db3abdae5d7443291dc07f96bdf7f00/Untitled.png)

Start on worker building tower of babylon - simple note / rhythm 

![tower of babylon.png](March%202%202023%203db3abdae5d7443291dc07f96bdf7f00/tower_of_babylon.png)

Zoom out to reveal full tower ascending to the clouds - lots of notes / rhythms

- Do we see many other workers?

![Untitled](March%202%202023%203db3abdae5d7443291dc07f96bdf7f00/Untitled%201.png)

Camera pointed up to show some of the great height into the stars - sound more distant?

![Untitled](March%202%202023%203db3abdae5d7443291dc07f96bdf7f00/Untitled%202.png)

![Untitled](March%202%202023%203db3abdae5d7443291dc07f96bdf7f00/Untitled%203.png)

We see some pieces falling off the tower - introduce atonality? weird rhythms?

![Untitled](March%202%202023%203db3abdae5d7443291dc07f96bdf7f00/Untitled%204.png)

![Untitled](March%202%202023%203db3abdae5d7443291dc07f96bdf7f00/Untitled%205.png)

Camera zooms way up past the many pieces falling, seeing components of the tower fly by

- Do we see any workers falling?

Cut to black when getting close up on a specific piece

![Untitled](March%202%202023%203db3abdae5d7443291dc07f96bdf7f00/Untitled%206.png)

Cut to the destroyed tower, pieces falling off, 

![brasss old solar system.png](March%202%202023%203db3abdae5d7443291dc07f96bdf7f00/brasss_old_solar_system.png)

![solar system.png](March%202%202023%203db3abdae5d7443291dc07f96bdf7f00/solar_system.png)

Zoom out to various areas / sections / levels that area represented in some type of solar system structure

Our character is floating close up to the camera as we make a selection of first level

- How many choices do we want available here?
- Whats the best way to integrate a tutorial?

Move through several levels, rebuilding the tower

![Untitled](March%202%202023%203db3abdae5d7443291dc07f96bdf7f00/Untitled%207.png)

Destroyed again, but this time it’s more apparent what destroyed it.

It’s rebellion among those who don’t want it? It’s a mysterious force that denies perfection?

Does the pursuit of new tech allegory connect with the art allegroy? Maybe not

Pieces fly around and new levels are a remix of previous ideas

This battle with enemies is call and response

You’re responding to the ideas being thrown at you

This is represented through music

Through quick action and instinct, it is like jamming or improvisation  

Remix Concept

- Art and communication as a struggle of persuing perfection and seeing it not work out?

Akka Arrh Concept

- Shoot enemies, they subdivide. Note or musical divisions?

Some Concepts

- God as this pure sound, elaborate construction
- Ophanim as supports for the throne imply the throne of god cannot hold itself up
    - The weight of the idea of god does not sustain itself? Not this version
- Music as systems

IDEA: Breaking through some kind of layer? sort of like AT Field?

![Untitled](March%202%202023%203db3abdae5d7443291dc07f96bdf7f00/Untitled%208.png)

Enemies

What do they represent?

- What prevents sound from being music?
    - deliberate and intentional organization of sound that creates a sense of rhythm, melody, and harmony, and communicates a particular artistic message or emotional expression.
    - Who decided what is deliberate and intentional in the absence of the artist? Is this universal? What is the communicated message?
- What enables sound to be music?

Representations

- Pieces of the tower - exhibit different musical ideas
- Pieces of something holding back the construction of the tower
    - Destroying them and saving the ophanim allows the tower to be built

Mechanics

- Geometric shapes that shoot things which contribute to the sound
- External points of the enemy relate to the range of notes
    - What does this mean mechanically? Strength of bullets?
- What if enemies blow apart into smaller enemies? Make this equivalent to musical systems
    - Like breaking down time signatures

IDEAS

- do we see any enemy types as workers?
- ophanim as rings of the tower?

Timnit Gebru

> Resentment for - Nobody said go build us a god, try to build us a god, but they decided it was a priority for everyone to do and go do that, then the rest of us are stuck cleaning up rather than trying to implement our vision of what useful things we can build with our skills and our little amounts of funding.
> 

Reconstruct tower - the turn is not everyone wanted this in the first place

God did not strike it down, others did 

[CHatGPT Prompts](March%202%202023%203db3abdae5d7443291dc07f96bdf7f00/CHatGPT%20Prompts%2011ccd4c7f00a4577b8e8ba677e630981.md)

Look into using Flexalon

[Flexalon Update 3.0: Infinite Curves, 3D Grids, and More!](https://www.youtube.com/watch?v=kt3O9AgH8WY)

Many interaction ideas form the demos here! Worth using

Sacred Geometry for enemies

Kabbilistic Imagery - Jewish mysticisim

Tower of Babel as rings (ophanim) idea

Brekaing apart enemies as adding time signautre

- then certian enemy types would represent time sigs (points or edges)

Destorying enemies is acutally returning them to the strucutre (show in tutotiral, impied later that this occurs)