# Play Mode Entry Optimization Guide

## Overview

This guide provides specific steps to reduce your 20-25 second play mode entry time to 2-5 seconds by addressing initialization bottlenecks.

## Current Status

✅ **Already Optimized:**
- Domain reload disabled (`m_EnterPlayModeOptions: 2`)
- <PERSON><PERSON><PERSON><PERSON> configured for zero-allocation logging
- BTR scripts have proper domain reload handling

⚠️ **Needs Optimization:**
- Heavy FindObjectOfType calls during initialization
- Third-party asset InitializeOnLoad attributes
- Synchronous manager initialization

## Immediate Actions

### 1. Use the New Profiling Tools

**Step 1:** Add the PlayModeInitializationProfiler to your base scene
```csharp
// The profiler will automatically start when play mode begins
// Check Console and persistent data path for detailed reports
```

**Step 2:** Run the InitializeOnLoad Auditor
- Go to `BTR > Performance > Initialize On Load Auditor`
- Click "Scan Project" to identify all InitializeOnLoad attributes
- Focus on third-party packages with heavy initialization

### 2. Manager Initialization Optimization

**SceneManagerBTR** has been optimized to defer FindObjectOfType calls:
- SplineManager lookup now happens asynchronously
- Scene loading no longer blocks on manager discovery

**Recommended for other managers:**
```csharp
// Instead of this in Awake/Start:
var manager = FindFirstObjectByType<SomeManager>();

// Use this pattern:
StartCoroutine(InitializeManagerAsync());

private IEnumerator InitializeManagerAsync()
{
    yield return null; // Wait one frame
    var manager = FindFirstObjectByType<SomeManager>();
    // Initialize manager...
}
```

### 3. Third-Party Asset Configuration

**GPU Instancer Pro:**
- Check if demo components are in your scenes (they use ExecuteInEditMode)
- Consider disabling auto-initialization in editor settings
- Review package loading settings in GPUIDefines

**Altos (OccaSoftware):**
- The dependency checker runs on every compilation
- Consider disabling if not actively developing with Altos

**Reach UI:**
- Has 2 InitializeOnLoad attributes
- Check if initialization can be deferred

### 4. Logging Optimization

Ensure all debug logging uses conditional compilation:
```csharp
#if UNITY_EDITOR || DEVELOPMENT_BUILD
if (enableDebugLogs)
{
    _logger?.LogDebug("Debug message: {Parameter}", value);
}
#endif
```

## Performance Monitoring

### Using the Initialization Profiler

1. **Automatic Profiling:** The profiler runs automatically in play mode
2. **Manual Step Logging:** Use `PlayModeInitializationProfiler.LogStep()` to track custom operations
3. **Report Analysis:** Check the generated reports for slow steps (>50ms)

### Key Metrics to Watch

- **Total initialization time:** Target <5 seconds
- **Individual step times:** Target <50ms per step
- **Memory allocation:** Minimize GC pressure during startup
- **FindObjectOfType calls:** Should be <5 during initialization

## Specific Optimizations Applied

### SceneManagerBTR Changes

1. **Deferred SplineManager Lookup:**
   - Moved from synchronous Start() to async coroutine
   - Prevents blocking main thread during initialization

2. **Optimized Scene Loading:**
   - FindObjectOfType calls now happen after scene load
   - Reduced blocking operations in critical path

### Recommended Next Steps

1. **Profile Current State:**
   - Run the initialization profiler
   - Identify your specific bottlenecks
   - Use the InitializeOnLoad auditor

2. **Optimize Heavy Managers:**
   - Apply async initialization pattern to GameManager
   - Defer non-critical component lookups
   - Use object pooling for frequently created objects

3. **Third-Party Asset Audit:**
   - Review each package's initialization impact
   - Disable unnecessary editor-only features
   - Consider alternatives for heavy packages

4. **Scene Setup Optimization:**
   - Minimize objects in base scenes
   - Use prefab variants instead of complex hierarchies
   - Defer VFX and audio initialization

## Troubleshooting

### If Play Mode Entry Is Still Slow

1. **Check the profiler reports** for specific bottlenecks
2. **Use Unity Profiler** during play mode entry
3. **Disable packages temporarily** to isolate issues
4. **Check for infinite loops** in initialization code

### Common Issues

- **Multiple FindObjectOfType calls:** Use caching and async patterns
- **Heavy asset loading:** Defer non-critical assets
- **Complex scene hierarchies:** Simplify base scene setup
- **Excessive logging:** Use conditional compilation

### Performance Regression Prevention

1. **Regular profiling:** Run initialization profiler weekly
2. **Asset review:** Audit new packages before integration
3. **Code review:** Check for blocking operations in initialization
4. **Automated testing:** Include startup time in CI/CD pipeline

## Expected Results

After implementing these optimizations:

- **Play mode entry:** 2-5 seconds (down from 20-25 seconds)
- **Reduced blocking:** Smoother editor experience
- **Better debugging:** Clear profiling data for future optimization
- **Maintainable code:** Async patterns prevent future regressions

## Tools Created

1. **PlayModeInitializationProfiler:** Automatic startup profiling
2. **InitializeOnLoadAuditor:** Third-party asset analysis
3. **Optimized SceneManagerBTR:** Async initialization patterns

## Next Phase Recommendations

1. **Apply async patterns** to remaining managers
2. **Optimize asset loading** in critical scenes
3. **Implement lazy initialization** for non-critical systems
4. **Consider dependency injection** for better manager coordination

## Monitoring and Maintenance

- **Weekly profiling:** Check for performance regressions
- **Asset updates:** Re-audit when updating third-party packages
- **Code reviews:** Ensure new code follows async initialization patterns
- **Performance budgets:** Set and enforce startup time limits
