

## Tooling and Workflow

**January 10th**
*   Used AssetStudio Gui in attempt to extract shader of Monk animation from Pilgrimmage.

**January 11th**
*   <PERSON><PERSON> using Blender.

**January 12**
*   Discovered Blender Split tool.

**February 14**
*   Experimented with Steam Asset Ripper to extract assets from Sayonara Wild Hearts.
*   Investigated various rotate scripts for object movement and control.
*   Researched compilation shader variants and attempted fixes via Project Settings > Graphics and preloading shaders.
*   Switched from Gamma to Linear color space in Unity to address washed-out colors in builds.
*   Disabled Optimize mesh data to speed up build times.

**February 19th**
*   Explored color palettes using Adobe Color.
*   Deleted scenes in project, tried to find unused assets.

**March 2 2023**
*   Look into using Flexalon.

**March 6 2023**
*   Added Screen resolution script to Manager<PERSON>.
*   Trying Hot Reload for unity!

**March 8 2023**
*   Deleted Magic Arsenal and MeshBaker from project to remove a possible error.

**March 10 2023**
*   Added SRDebugger for more efficient on-device debugging
*   Tying this into my UI
*   Need to learn shaders

**March 13 2023**
*   Adding SRDebugger for more efficient on-device debugging
*   Tying this into my UI.

**March 14 2023**
*   Added video gameplay recorder to builds for bug tracking
*   Interesting resource that may help me solidify my game!

**March 15 2023**
*   Made refactor and cleanup updates to Crosshair, Enemy Basic Setup, Shooter Movement and Projectile setup scripts.

**March 19 2023**
*   Grabbed Fast Glitch for some effects during some function - possibly Half speed?

**March 21 2023**
*   Upgraded out of LTS to Unity 2022.2.11
*   Removed A* and GPU Instancer due to errors.
*   Disabled Burst Compilation and Optimizations
*   Added Quibli package again - DONE!
*   Added GPU Instancer back, seems to work.
*   Added Buto - not working yet. All pink.

**March 22 2023**
*   A* Now working!! Not problematic in scene anymore
*   Only issue now is Buto - waiting on resolution here.
*   Burst is disabled, not sure if this effects anything for me, seems like it doesn’t?
*   Need to run a full asset / render pipeline conversion but dont have enough space on main drive.

**March 23 2023**
*   Buto not working 100% so adjusted to work in a manageable state
*   Tried to fix setting UI menu controller system
*   Experimenting with Settings UI
*   Used a new camera for settings

**March 25 2023**
*   Dynamic Mesh Cutting
*   Tried a new outline shader - full screen effect in Unity 2022

**March 27 2023**
*   Tried Rokoko animation importing - nothing interesting here, works fine
*   Tried AUto Rig in Blender - Mixamo hates it. Not sure what’s wrong, but need to more thoroughly look at Blender → Unity pipeline, and bringing in mixamo aniamtions to blender.
*   Relearned Random Flow - it’s awesome! Finsih BT docs and consider environments with these tools

**March 29 2023**
*   Black Hole VFX

**March 30 2023**
*   Older method in ChatGPT 3.5 was working best

**May 1 2023**
*   Navmesh scanning work again with beta 61 of A star pathfinding.

**May 9 2023**
*   Completely reinstalling Ultimate SPawner  + Waves add on

**May 11 2023**
*   Adding

**May 16 2023**
*   Have the browsing functionality in chat gpt and tried AGAIN to make a datamosh effect but it wasnt a much better situation

**May 17 2023**
*   Is there anything in Projectile Toolkit I could use?

**Sept 1 2023**
*   Switched to Direct X 11 and the memory leaks went away - likely a Unity issue
*   Switch haptics to the interhaptics razer suite? Added but maybe not working properly

**Sept 20 2023**
*   Compared to the RDR mix and Jam file.
*   Investigating camera options for better reticle look around movement

**Sept 27 2023**
*   Added UModeler X for AI texturing, but thinking this might be better in blender? May be case by case basis for these things. Need to go through tutorials

**Oct. 2nd 2023**
*   Using prefab generator to try and make a basic structure for player to walk across on the snake. Most of the way along, hope to figure this out tomorrow.

**Oct 3rd 2023**
*   Used Magic Light Probes for placing light probes?
*   Successfully worked
*   Light Probes
*   Rough snake mode working - issues primarily with camera

**Oct 4th 2023**
*   Adding Curvy integration package to Behavior Designer
*   Trying more basic - Behavior Desginer and A* - Patrol implementation

**Oct 5th 2023**
*   Upgraded Buto to 2022 version
*   Changed light to Bakery setup

**Oct 11 2023**
*   Used Unity Muse for better suggestions.

