---
title: Management Systems Documentation
date: 2024-01-15
tags: [management, systems, core, unity]
aliases: [ManagementSystem, CoreSystems]
---

# Management Systems Documentation

## Overview
The management systems form the backbone of BTR, handling core game functionality, scene management, audio, time control, and debugging. These systems provide essential services to all other game systems.

## Related Documents
- [[SystemsDesignAnalysis|Systems Design Analysis]]
- [[PlayerSystem|Player System Documentation]]
- [[ProjectileSystem|Projectile System Documentation]]
- [[Changelog]]

## Project Structure
The management systems are organized into several key categories:

### Core Management
- [[GameManager]] - Central game state and systems coordination
- [[SceneManagerBTR]] - Scene loading and transitions
- [[TimeManager]] - Global time control and manipulation
- [[EventManager]] - Event system and message passing

### Audio and Music
- [[AudioManager]] - FMOD audio system integration
- [[MusicManager]] - Music playback and transitions
- [[WaveEventSubscriptions]] - Wave-based event handling
- [[WaveCustomEventSorter]] - Custom event sorting

### Scene Management
- [[SceneGroup]] - Scene grouping and organization
- [[SceneConfiguration]] - Scene-specific settings
- [[SceneLoadActivationController]] - Scene activation control
- [[SceneSwitchCleanup]] - Scene transition cleanup
- [[SceneStarterForDev]] - Development scene utilities

### Visual and Effects
- [[GlobalVolumeManager]] - Post-processing volume control
- [[SplineManager]] - Spline-based movement and effects
- [[SparkManager]] - Particle effect management
- [[ShaderPrewarmer]] - Shader initialization

### Debug and Development
- [[DebugControls]] - Development controls and cheats
- [[DebugSettings]] - Debug configuration
- [[ScoreManager]] - Score tracking and statistics

## Core Systems

### Game Management
The central game management system:

```csharp
public class GameManager : MonoBehaviour
{
    // Core game state
    public GameState currentState;
    public bool isPaused;
    public float gameTime;
    
    // System references
    public SceneManagerBTR sceneManager;
    public TimeManager timeManager;
    public EventManager eventManager;
    
    // Game flow control
    public void StartGame()
    {
        currentState = GameState.Playing;
        eventManager.TriggerEvent("GameStart");
    }
    
    public void PauseGame()
    {
        isPaused = true;
        timeManager.PauseTime();
        eventManager.TriggerEvent("GamePaused");
    }
}
```

### Scene Management
Advanced scene loading and management:

```csharp
public class SceneManagerBTR : MonoBehaviour
{
    // Scene loading configuration
    public float minLoadTime = 0.5f;
    public float fadeOutDuration = 1f;
    public float fadeInDuration = 1f;
    
    // Scene tracking
    private List<string> loadedScenes;
    private Dictionary<string, SceneGroup> sceneGroups;
    
    // Async loading with visual feedback
    public async Task LoadSceneAsync(string sceneName)
    {
        await FadeOut();
        await LoadSceneInternal(sceneName);
        await FadeIn();
    }
}
```

### Time Management
Global time control system:

```csharp
public class TimeManager : MonoBehaviour
{
    // Time control parameters
    public float globalTimeScale = 1f;
    public float fixedTimeStep = 0.02f;
    
    // Time zones
    private Dictionary<string, TimeZone> timeZones;
    private List<TimeAffectedObject> timeAffectedObjects;
    
    // Time manipulation
    public void SetTimeScale(float scale, string zoneId = "global")
    {
        if (timeZones.TryGetValue(zoneId, out TimeZone zone))
        {
            zone.SetTimeScale(scale);
            UpdateAffectedObjects(zone);
        }
    }
}
```

### Event System
Robust event management:

```csharp
public class EventManager : MonoBehaviour
{
    // Event handling
    private Dictionary<string, Action<EventData>> eventDictionary;
    private Queue<EventData> eventQueue;
    
    // Event registration
    public void AddListener(string eventName, Action<EventData> listener)
    {
        if (!eventDictionary.ContainsKey(eventName))
            eventDictionary[eventName] = listener;
        else
            eventDictionary[eventName] += listener;
    }
    
    // Event triggering
    public void TriggerEvent(string eventName, EventData data = null)
    {
        if (eventDictionary.TryGetValue(eventName, out Action<EventData> thisEvent))
            thisEvent.Invoke(data);
    }
}
```

## Configuration

### Scene Configuration
```csharp
public class SceneConfiguration : ScriptableObject
{
    // Scene settings
    public string sceneName;
    public LoadPriority loadPriority;
    public bool preloadAssets;
    
    // Scene dependencies
    public List<string> requiredScenes;
    public List<AssetReference> preloadedAssets;
    
    // Post-load actions
    public UnityEvent onSceneLoaded;
    public UnityEvent onSceneUnloaded;
}
```

### Debug Settings
```csharp
public class DebugSettings : ScriptableObject
{
    // Debug flags
    public bool showDebugUI = false;
    public bool enableCheats = false;
    public LogLevel logLevel = LogLevel.Warning;
    
    // Performance monitoring
    public bool showFPS = false;
    public bool showMemoryUsage = false;
    public bool enableProfiler = false;
}
```

## Best Practices

### 1. Scene Management
- Use async loading for smooth transitions
- Implement proper cleanup
- Handle dependencies correctly
- Provide loading feedback
- Validate scene states

### 2. Event System Usage
- Use strongly typed events
- Implement proper unsubscription
- Handle event errors gracefully
- Document event contracts
- Avoid event loops

### 3. Time Management
- Use time zones for isolation
- Handle fixed timestep properly
- Implement proper cleanup
- Cache time calculations
- Handle edge cases

### 4. Audio Management
- Use FMOD events properly
- Handle audio device changes
- Implement proper fallbacks
- Manage memory usage
- Handle concurrent sounds

## Implementation Examples

### Custom Scene Transition
```csharp
public class CustomSceneTransition
{
    private SceneManagerBTR sceneManager;
    private GlobalVolumeManager volumeManager;
    
    public async Task TransitionToScene(string sceneName)
    {
        // Fade out
        await volumeManager.FadeOut(1f);
        
        // Load scene
        await sceneManager.LoadSceneAsync(sceneName);
        
        // Setup scene
        await SetupNewScene();
        
        // Fade in
        await volumeManager.FadeIn(1f);
    }
}
```

### Time Zone Implementation
```csharp
public class TimeZone
{
    private float timeScale = 1f;
    private List<ITimeAffected> affectedObjects;
    
    public void AddObject(ITimeAffected obj)
    {
        affectedObjects.Add(obj);
        obj.UpdateTimeScale(timeScale);
    }
    
    public void SetTimeScale(float scale)
    {
        timeScale = scale;
        foreach (var obj in affectedObjects)
            obj.UpdateTimeScale(scale);
    }
}
```

## Testing Checklist
- [ ] Scene loading works smoothly
- [ ] Time control functions properly
- [ ] Events are handled correctly
- [ ] Audio plays properly
- [ ] Debug tools work
- [ ] Performance is acceptable
- [ ] Memory usage is stable
- [ ] Error handling works
- [ ] Scene cleanup is proper
- [ ] State persistence works

## Known Issues and Solutions

### Scene Loading Issues
**Issue**: Scene dependencies not properly loaded
**Solution**: Implement dependency tracking
```csharp
private async Task LoadDependencies(SceneConfiguration config)
{
    foreach (var dependency in config.requiredScenes)
    {
        if (!IsSceneLoaded(dependency))
            await LoadSceneAsync(dependency, LoadSceneMode.Additive);
    }
}
```

### Event System Memory Leaks
**Issue**: Event listeners not properly unsubscribed
**Solution**: Implement automatic cleanup
```csharp
public class EventSubscription : IDisposable
{
    private readonly string eventName;
    private readonly Action<EventData> listener;
    
    public void Dispose()
    {
        EventManager.Instance.RemoveListener(eventName, listener);
    }
}
```

## Future Improvements
- [ ] Add scene streaming system
- [ ] Implement advanced time zones
- [ ] Enhance event system
- [ ] Add performance monitoring
- [ ] Improve debug tools
- [ ] Add scene validation
- [ ] Enhance audio management
- [ ] Optimize memory usage
- [ ] Add state serialization
- [ ] Implement crash recovery 