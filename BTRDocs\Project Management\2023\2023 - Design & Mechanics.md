
*   **August 11**:
    *   Design is constraint. What can you do that’s interesting within constraints?
    *   How can I expand creativity?
* **February 14**:
*   Positive game loop - dont punish for being off beat, but reward for being on beat
*   Alternative gameplay modes for center of Ophanim
*   When player is located on a ring
    *   Ring only rotates for fake forward motion
    *   Other rings rotate in many directions
*  *Rez bosses inspo
**February 19**:
*   Adjust radius in which bullets lose targetting> May be an issue
    * Should I have enemy shooting Particles always face the player? 
    * Doesn't effect things - need to try earlier version and see the difference - must have changed some value that is important!
    * Geometric enemies with points reduced as we move forward.
    * Square to triangle to line etc
* **February 22**:
* Attention Economy
    * No movement - what replaces this?
* Spectacle can replace free movement
    * visually and new patterns emerging constantly
* Pacing is key!
* What to learn from Starfox 64?
* secret paths and goals to discover
dodge mechanic?
variety of paths to reach end
combo and high score options
power ups
stage medals - acheive specific things like rollerdrome?
* Collect coins or powerups or something along the way?
* Is there any way to controll HOW many bullets you shoot at a time?
* Types of Progression
Intrinsic progression
* bring your own ability to the game
* tough for beginners
Neon white brings speed running to beginners?
* Extrinsic Progression
set goals within the game - obvious ones!
Level up system is an example
cosmetic items are an example
* Be careful - don’t want things to feel like a treadmill!
* Use skill trees or advancement to increase musical palette? 
* Bullet types for different enemies? Ikaruga method? 
Souls like method for score - retrieve your score when you die and retry level?
* invincibility when you find your corpse?
* Combined designs are the addition of these things
This also defines their shooting pattern
but NOT movement pattern? 
* Light / Dark / Digital
* Is the journey - not the destination - something we can consider recursively? 
*Like the idea of making the perfect music - as if there was some perfect sound that would feel like touching god?
* Mechanics
* Geometric shapes that shoot things which contribute to the sound
* External points of the enemy relate to the range of notes What does this mean mechanically? Strength of bullets?
* What if enemies blow apart into smaller enemies? Make this equivalent to musical systems Like breaking down time signatures
Do we see any enemy types as workers?
* ophanim as rings of the tower?
They are the base of the throne of god - or move it around
* Tower of Babylon - what does it mean?
* **January 23:**
    *   Consider modern game / genres and how they apply to rhythm shooter
    *   Rail-shooter along rings of the ophanim
*   **January 23**
    * Parrying as a Dodge mechanic?
    * Vampire Survivors short form design
*    Concept - enemy is blind until you do a certain thing / that thing is necessary
* * What if facing the opposite direction, time moves backwards?
This would require

* **March 2**:
 * Mechanics
Destorying enemies is acutally returning them to the strucutre (show in tutotiral, impied later that this occurs)
* **March 5**:
    * Core Ideas
    * Call and Response: Enemy Shoots - I send it back
    * "The call and response musical structure can serve as an allegory for the artistic process"
*   **March 6**:
    *  Add Enemies locked?
    * Quality Settings
    * Implement graphics setting in UI
    * Need to adjust to make coherent with UI design 
      What Makes Good Rhythm Game UX?
* Change Control scheme - Use Triggers for main actions, buttons for time rewind etc
*   **March 7**:
*   "What Makes Good Rhythm Game UX?" 
*   Use Triggers for main actions, buttons for time rewind etc
* Managed to get rotation around sphere working through fake gravity unity video
*   **March 8**:
Need to fixe Radar for this as well
- Quality Settings
* Do I need the depath texture and opaque textures on?

*   **March 10**:
    * Fix Enemy UI - update when enemy dies?
    * Add MAP scaling
Maybe represent differently? Is it important?
*Need to fix Enemy UI - update when enemy dies?
*Bullet absorb mode - change music to something more chill when this happens? 

*   **March 14**:
    * Mechanics → Tone → Themes
* Mechanics
    * Dodgeball-like Shooting
    * Move to the music / affect the music
    * 4 quadrant turning
    * Lock on to enemies
    * Lock on the Projectiles
    * You can rewind time
    * You can pause time
    * What if enemies can control time as well? 
        *They can be unaffected by the time rewind
        *They are unaffected by the slow down
*No luck with Gravitational Waves Spacetime, need to figure out how to scale the mesh up larger
  Or use a different mesh?
* **March 15**:
  Does time SLowing the objects change everything
*   **March 16**:
Implemented this - New “Slow” parameter in Fmod
  Datamosh effects
*   **March 19**:
    *Tried writing new Boss4Attacks4Positions Behavior Designer, issues with 4 attack types and extending the tactical agent class. Likely easier to track attack types in a seperate script, Behavior Designer can just track when an attack is allowed to occur.
* * Snake as rings wrapped around a planet - ouroboros
* Each boss could represent a different aspect of human nature, such as pride, ambition, or fear
* Levels can be any order, like Starfox

* **April 5**:

* *How do you make a game where you don’t use a lot of movement
* *You are moving with the movement of the wheel but this takes the active movement away and gives you time
* *Is this fun? It has to feel good.
* *Look up video from game maker toolkit
* *Look at game design theory
* **May 2**:
    * May need to implement Object Placer with ALign to Surface feature.
* Should I freeze enemy position in time? 
*   Enemies are shooting but not moving
*Ouroboros 3 - have music and other things working. Best template so far - can refence others for minor issues
*Some things to fix
Enemies are shooting but not moving
Player running anim direction gets wonky - why?
Crosshair Reticle sideways and wonky - why?
1) Enemies moving - but fly off easily. Need to clamp them to navmesh properly.  
    * Crosshair reticle issue.
*  Using kinematic
*  Can get Torodial working, not sure why! Something to do with the game object? What am I missing? 
*SHOOT AND SCOOT AND MOVEMENT
*   **May 3**:
    * Integrated Aron Granberg’s feedback on how to get AI to stick to Navmesh
* Enemeis do not stay on
* **May 5**:
*   Projectiles are rarely coming into contact with enemies when fired at them
Navmesh Test Scene - Ouroboros 3 - More complex Behaviors
Character not rotating properly or animation not changing properly  
  Using 8 lock on
*   **May 9**:
*Can I have a snake look like it’s unwinding from an ouroboros? What does that camera shot look like? You fly off and land on a snake moving forward? 
*   What does this mean for the world design
 *  Not really working either with grid graph.   
* Trying out a new Projectile script to clear things up
  Disbale StickToNavmesh script to see if it was the issue
  To Do list
*   **May 10**:
 * Seperate game into component parts to more easily move things between scenes?
What are limitations
Using Cinemachine Path for straight lines - No need to use CInemachine Smooth Path in these scenarios!
 Have a PathDetector script for character movement that looks at direction of reticle and tries to choose the best path.
  Lock there verticality and only do 2D movement in this scenario? Look into this.
 What about shooting and the look

*  The AI on the level
* Get an idea of mechanics design / technical limitations
* **May 16**:
       Enemies appear to be working on mesh!
* *Can I set specific spawn points for specific waves?
Added Conditional Debug static class

* **May 17**:
     Should Projectile Kit  be used

* **May 18**:

* To ground
  -  Need to adjust projectile behavior - not hitting enemies most of the time, behave too erratically. Would improve gameplay loop drastically if fixed.
   What should be my gameplay

*   **May 19**:
 X ray effect ideas
*Implement the movement with code
*   * To create 257s, the most recent change has been 2

*May 22*:
Need to GroundCheck Issues, fix this!
* * To ToDo

* **May 23**:

      Recent bug report, this seems to be the solution* Time Rewind, Time Slow,
What can I do to make the snake better
*  Implement the movement with code
*   Have that time can be set up with code

* **May 25**:
what is the player doing?
Fix the rewind of player on enemy death

* May 30
Snake can do movement
*

* **Nov 13**:
 Why does it appear as if the bullets are bound within a box?

*To do

* **March 29**
*Black Hole VFX issues

* **July 18**:
 * Problems with movement and code
 * What makes it better

