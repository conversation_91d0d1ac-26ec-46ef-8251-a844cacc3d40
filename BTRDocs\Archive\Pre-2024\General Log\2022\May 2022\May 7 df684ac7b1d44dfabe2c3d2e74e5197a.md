# May 7

<PERSON>on course

Promise, Progress, Payoff -twists, Plot Types

**Promises** - Tone (consistent, understandable)

Arc - signify what kind of arc the character will be going on, following through on that structure

Plot - Umbrella Plot and Core Plot

- Ex. Core plot is a romance, but Umbrella plot is ‘we need to do X`  and while thats happening, you care about the romance
- Sometimes they’re the same thing, sometimes split off and takes a bit of time - but you’re making a promise of what these things are going to be

These promises are all in the introduction of the book 

- short story, first couple paragraphs
- massive fantasy - could be many chapters deep

Character Arc promise is how they char will change over course of story - they’re gonna change or their situation is gonna change and give them what they want. <PERSON> being told by o<PERSON><PERSON> he has to become a jedi, take part in war we saw at beginning of movie

Plot promise - Get the plans to the rebels so we can defeat the empire

How not to be too predictable with these?

Plot can be more predictable than you think if the setting and characters are a bit more interesting

Pretty much every plot has been done, and they way to buck the trend is to do something unexpected that breaks your promises - can bea feature of the story, but mostly you want to do subtle inversions of your promises

The ‘Strange attractor` idea for a story - you want it to be familiar but new. twist on an old idea or mash up two ideas that dont feel like theyd make sense, but is intrigiung

Do all good characters need arcs? <PERSON> / <PERSON> is a good example of a character who needs no arc but works because they other elements are strong. Tone and Plot promises are much more important there

**Progress**

This is the most important part

Classic example of Inferno and putting a Map at the beginning, allowing the reader to see how we progress through the book toward the center of hell

Reader is always watching a timebomb, seeing how they progress through the book

It’s an illusion of progress of course, you can control how fast things move forward in writing - you have complete control of time

Why are people turning the page? What questions are they looking to be answered?

Promises can totally shift the view of the progress - diversion story from lecture about this. 

Progress - think about how every scene advances the main points - Star Wars example

![Untitled](May%207%20df684ac7b1d44dfabe2c3d2e74e5197a/Untitled.png)

Look at your plot / outline - what are small increments i can make along this path that will be interesting and show the reader we’re progressing. Can backslide to and be interesting but need to be very careful about it. Step backs are useful in basically every story in relation to twists, but very common in romance stories.

But important - the reader sees the step back differently than the characters, so it may seem major in the book but you know its smaller than it looks

Identify what type of plot you have - very useful for understanding how to progress

Progress should involve problems arising and dealing with them

Nesting plots like you think of code - this is in relation to sub plots

![Untitled](May%207%20df684ac7b1d44dfabe2c3d2e74e5197a/Untitled%201.png)

**Payoff**

You make good on all this, the trick is you don’t entirely make good on it all

Star wars uses plot expansion for its twist

Ex. You promise your kid a toy car

They wait until christmas like they’re supposed to 

They a brand new real car for christmas

Star wars promise at the beginning is smaller than what is delivered at the end - lukes arc becomes defeat the entire empire

Giving the reader more then they expected

While you were sleeping (film) is a great example of a substitution plot - convince the reader that you actually want something else. This can be hard to do!

**Lecture 3 Part 2**

How do we make an outline and construct a plot?

Whats an outline? For editors, 3 - 5 pages telling your story. A Summary

Outline looks like this

Character

- Main Char Name
    - Intro, and Paragraph of who they are
    - Arc 1
    - Arc 2
- Side Characters
    - One paragraph on each or so
    

Setting - Each category below written defining terms, encyclopedicly written 

- Magic /Tech
- Worldbuilding physical setting
- Cultural setting

Plot - Plot Archtypes!

Heist Plot - Mystborn was going to be a heist - this was the main framework

Master/Apprentice plot was also decided

Info Plot - History about characters teased as clues

Relatioship plot - a romance subplot between two chracters

How do we make all this into a story?

Brandon watched a bunch of heist films to determine his achetype there

Being able to strip these down helps you to understand your progress

How does Brandon make an outline?

Relationship

- Char A and B are a couple at the ene
- Bullet points taking them from the beginning of this until the end
    - Scene showing how Char A is rally competent in one area but  also missing something
    - Scene showing Char B also competent, but is missing something different (reader will realize these people compliment each other here)
    - Next you introduce the issue/conflict that demonstrates why they’re not together. They’re intereactions are disasters for various reasons
    - Scene where they are working together (braiding roses) the metaphor is great - the thorns that kept them apart will be the throns that prevent others from breaking them apart when they are together
    - Ex bullet point - Char A sees Char B with their sister, showing Char A that they can be a caring person to others
    

Mystery

- Discover X - explain why discovering X is gonna be awesome
- Bullet points of clues that will help us along the way to achieving this

Outline is ordered by section

When we start writing, start grabbing bullet points and organize them into the sections of how the story will progress - chronological order

Building scenes out of them as we imagine where they’re going to be

Sit down a writing means “you need to write a scene that will achieve A, B, and C

Plot brainstorming - a lot of simmering on it before even writing it

9 point story, 7 pointy story ,save the cat screenwriting, lots of books on this stuff

Dan Wells 7 point story structure video

Hero’s journey has issues, dont want to apply to everything or be to slavish towards it

Discovery writing - Yes but No and

Taking a character, throwing them into a bad situation, think of the best thing they could do in that situation, and see if that works. Depending on how that’s resovled, see how you can make things worse, and where things go from there

A short note on Brandon Sandersons