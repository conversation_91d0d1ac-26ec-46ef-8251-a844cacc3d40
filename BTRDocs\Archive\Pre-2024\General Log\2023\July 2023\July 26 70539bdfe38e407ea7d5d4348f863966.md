# July 26

Error with this - can’t find it. Not sure why! 

![Untitled](July%2026%2070539bdfe38e407ea7d5d4348f863966/Untitled.png)

Removing to see if I can fix this problem

Had to readd the default input module as seen above, also reimported the UI and have things working now. Need to redo customization of UI as well - something to do when i dont want to do other stuff

Kill All Enemies script being attached to double click scoring is no longer working due to removal of mouse input from Default Controls. Look into work around for this.

Trying to verify why screen flashes and I cant run on any monitor than the first one when i play a build. Not sure I had this problem before - using a development build to troubleshoot the issue. 

On 60hz display i dont get the flashing and issues i do on my laptop screen. Need to add support for alternate refresh rates. 

Need to fix aiming system, thinking turret model could help? would this adjust camera? 

Started on implementing a rough turret rotation aiming system to add to the vcam. 

Partially working. Disabled for now, but need to come back to this. 

Testing issue with Shoot and Scoot stopping. Enemies are seeing targets as 0, so checking if health/score/time is the issue, when it hits 0/negative does this cause them to stop?

seems to have worked! ALl is well if health is positive