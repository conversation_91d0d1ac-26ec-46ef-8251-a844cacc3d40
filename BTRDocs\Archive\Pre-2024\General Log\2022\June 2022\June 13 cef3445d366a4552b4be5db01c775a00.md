# June 13

Scan effect as time reverse?

[Released an update to my FREE post-processing scan effect for URP (GitHub link inside).](https://www.reddit.com/r/Unity3D/comments/vai8f3/released_an_update_to_my_free_postprocessing_scan/)

Work on AI issues

AI Issues

What is possible with the moving AI system? 

Do I have to restrict myself to this?

Cant seem to get many features working with moving platform AI

Disabled rotation! Just does not seem to work properly

Some other issues - Agents getting stuck on other agents

Local Avoidance - This can be done with RVO - “RVOController to a GameObject with an AIPath or RichAI component.” 

[Local Avoidance](https://arongranberg.com/astar/docs/localavoidance.html)

Two approaches come to mind for enemy out of sight issues

- Make AI only go to certain locations in the gameplay plane
- Widen the perspective / allow horizontal movement to see corners better

Tagging is a good way to restrict walkable areas

[Working with tags](https://arongranberg.com/astar/docs/tags.html)

Boomerang X does not have crazy AI - reference this !

Looking at camera movement possibilities. 

<PERSON><PERSON>le now looking at Aim Wall to free up connection with Camera

Increased Repeat Attack Delay on Diamonds to 2 for less frantic bullet hell!

Changed Reticle - Not sure what I think! SOmething like this could good butnot totally there yet

RIGHT glitch as BOMB that destroys current bullets on playing field

For all bullets that are not locked - Death

Narrative - Judeo-CHristian stories

[Ophanim](https://the-demonic-paradise.fandom.com/wiki/Ophanim)

Use the Kitbash Spaceships models in Blender and decimate a bit, make them look a bit more interesting 

Take concept from reboot a bit - travelling systems - surfing the net? These are the environments

Sci Fi Industrial buidlings will work well for a level - editing this now