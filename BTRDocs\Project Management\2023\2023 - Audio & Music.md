
Okay, here's the summarized output with only the entries that contain references to audio and music-related aspects:

**CHatGPT Prompts.md**

*   Contains a Tower of Babylon allegory where music is central to the story. This includes:
    *   The Tower is built by musicians.
    *   Each level of the Tower is dedicated to a different aspect of music (rhythm, melody, harmony, etc.).
    *   The failure to communicate musically leads to confusion and isolation.
    *   Music can bring people together but only with effective communication.

**February 14.md**

*   Positive game loop - dont punish for being off beat, but reward for being on beat
*   Enemies wont shoot. Bringing in music and files from Resources folder to see if this will fix it!!!

**February 19th.md**

*   Rez Analysis file referenced.
*   The potential use of <PERSON><PERSON> as a reference.
*   Sound Issues?

**February 22.md**

*   Thinking about structure of game - Music as a representation of systems
*   Recursion in Music - [https://www.youtube.com/watch?v=4ydZrKcvejU](https://www.youtube.com/watch?v=4ydZrKcvejU)
*   Eileen - “different enemies could be variations on a theme (musically and visually) that maybe represent parts of the player character’s journey through life (and death?)”
*   Music and visuals as language
*   music as language
*   Tower as pure sound
*   Like the idea of making the perfect music - as if there was some perfect sound that would feel like touching god?

**January 12.md**

*   Audio research category created
*   <PERSON> work as well as references to other books marker
*   Read some of <PERSON> work
*   Casino machines giving ‘winning’ sounds when the player hasn’t won - interesting!

**January 23.md**

*   Consider modern game / genres and how they apply to rhythm shooter

**March 1 2023.md**

*   How can I handle varying levels of gameplay speed - liek thumper and it's increasing difficulty?
*   Start on worker building tower of babylon - simple note / rhythm

**March 10.md**

*   Bullet absorb mode - change music to something more chill when this happens?
*   Have a rhythm track of hi hat drop out when locking on / firing, replaced with player behaviour?
*   What if the rings ARE the tower?
*   What if the rings exist around planets? You’re attempting to reach the center of each

**March 15.md**

*   May be worth changing lock on / shoot sounds. Hi hats like Rez? Percussive elements? Have a rhythm track of hi hat drop out when locking on / firing, replaced with player behaviour?

**March 16.md**

*   Testing alternate sounds playing for slow down
*   Got this working! New “Slow” parameter in Fmod

**March 19.md**

*   The relationship between sound and silence that matters, and it takes new forms as time moves onward
*   Levels and Bosses: • The Maestro navigates through a mesmerizing galaxy, following the trail of the Ophanim, celestial beings with rings of divine fire. The on-rail shooter aspect is justified as the Maestro moves along a pre-determined path through the rings, shooting musical projectiles to match the rhythm of the music and avoid obstacles.
*   Music and art: • According to some interpretations, Metatron was originally a human prophet who was elevated to the status of an angel after his death. He is sometimes associated with the tower builders of Babel, who were punished for their arrogance by having their languages confused. In this context, Metatron could be seen as a symbol of human ambition and the dangers of overreaching.  • associated with the music of the spheres and the divine harmony that underlies the universe. He is sometimes depicted as a kind of celestial scribe who records the music of the spheres and the secrets of the divine order. In this context, Metatron could be seen as a figure who embodies the mystical knowledge and wisdom that is needed to understand the deeper truths of the universe.
*   One way to relate the Tower of Babel to the planets and the music of the spheres is to view the tower as a representation of the structure of the solar system. In this interpretation, the different levels of the tower could represent the different planets and their corresponding celestial spheres, while the builders of the tower sought to understand and control the forces of the universe through their construction.

**March 20.md**

*   All of this builds towards some kind of musical idea
*   A world of silence and sound

**March 5.md**

*   The call and response musical structure can serve as an allegory for the artistic process in many ways

**March 6.md**

*   Adding ability to see come control mapper features in the UI Controller settings windows
*   Added Master volume control to Settings
*   May be worth changing lock on / shoot sounds. Hi hats like Rez? Percussive elements? Have a rhythm track of hi hat drop out when locking on / firing, replaced with player behaviour?

**March 7.md**

*   Added code for Unity Debug console visibility on/off
*   Added ability to see come control mapper features in the UI Controller settings windows
*   [What Makes Good Rhythm Game UX?](https://www.youtube.com/watch?v=nRJ4iVjIMAc)

**May 2.md**

*   "Sound design as a language"
*   "Music as a representation of structure.

**May 22.md**

*   Implementing the X Ray URP feature as a good start to the day

**May 23.md**

*   "Use a sound every time an enemy dies. Consider changing the pitch to coincide with kill streaks"
*   "Incorporate audio that helps the player understand what part of the beat they're attacking on."

**May 8.md**

*   "Use a different sound for hitting an enemy. Test a deep synth bass or 808 sample"
*   More musicality to the project
*   Think of systems that build and fade through the game

**May 9.md**

*   "Mix and match different kinds of sounds to fit player progression through area
*   Incorporate different audio to compliment different levels of difficulty."

**November.md**

*   "All enemies have unique voice sounds. "
*   "All enemies have unique death screams."
*   Added Light Flasher script, for void level. Looking at timing a light to the beat.
*    Could also time a light to locking on / shooting enemies
*    Beefy Audio
•Procedural Enemy Shake

**Rez Analysis.md**

*   Many cool UI FX when locked on
*   Explosive lighting FX

**Rhythm Ascension.md**

*   The title of the game Rhythm Ascension
*   The ultimate goal of the game is to achieve the ultimate state of musical art
*   Is that ultimate art just a song.

**Sept 18.md**

*   Music - is movement - is action - is ?

**Sept 20.md**

*   Need the be more musical
*   Use audio to signal when the player can attack

**Sept 21.md**

*   "Use synthwave or electronic music to complement game"

**Symphony of Celestial Rebirth.md**

This focuses solely on music and sound!

*   Levels in game based on certain musical principles
*   Level and world based on elements of music
*   The need to create audio that is a major part of the story"

**Symphony of the Cosmos.md**

Same as before Symphony and music are important things here.
