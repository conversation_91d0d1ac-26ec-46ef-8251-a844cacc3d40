# August 20

Built extended debugging tools for ConditionalDebug

Can select specific classes and see debug messages

Might go further with this or just look into standard unity debugger to see if im reiventining the wheel

Point of this is to see properly if Projectiles are hitting enemies. Might build some of those messages into the tool? 

- Did this, basic attributes for testing added

Some extended features like Debug Time Control (not working)