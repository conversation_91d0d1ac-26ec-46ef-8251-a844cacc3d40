# May 18

Trying a recompile of the game in release mode to see how it affects performance

Wondering if this will flag editor scripts? 

Made no difference, need to place editor scripts in editor folder, not properly ignored otherwise

Thinking about protagonist

<PERSON><PERSON><PERSON> - angels - you are a repurposed piece of metatron

Intro section / learning mechanics  - could be a version of these levels

A piece of the tower that has been repurposed to help reassemble itself

Build works!

Made adjustments to ground check script, work better with some errors. Added UI Lock on effect. Added script to Shooting to prevent it from going through the ground. Likely needs adjustment. Successfully fixed errors to allow a build. Attempted fix with errors for lock on when ground is in the way, seems to be working. Need to adjust projectile behavior - not hitting enemies most of the time, behave too erratically. Would improve gameplay loop drastically if fixed.