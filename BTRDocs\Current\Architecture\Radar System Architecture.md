# BTR Radar System Architecture

*Created: 2025-07-18*
*Status: Current*
*System Location: `Assets/_Scripts/Radar/`*

## Overview

The BTR Radar System is a **high-performance, job-based solution** for tracking and visualizing entities in 3D space on a 2D radar display. It uses Unity's Job System with <PERSON><PERSON><PERSON> compilation to achieve optimal performance while maintaining real-time tracking of projectiles, enemies, and other dynamic objects.

## System Architecture

```mermaid
flowchart TB
    %% Core Components
    RM[RadarManager]
    RJ[RadarJobs]
    RST[RadarSharedTypes]
    
    %% Integration Points
    PJS[ProjectileJobSystem]
    EE[EnemyEvents]
    PM[Player Manager]
    
    %% UI Components
    IRadarUI[IRadarUI Interface]
    Canvas[Canvas UI]
    BlipPool[Blip Pool]
    
    %% Data Structures
    RD[RadarData]
    RED[RadarEntityData]
    RER[RadarEntityRef]
    
    %% Special Features
    RAV[RadarAreaVolume]
    RT[RadarTarget]
    
    %% Job System
    RPJ[RadarProjectileJob]
    RGJ[RadarJob]
    
    %% Relationships
    RM --> RJ
    RM --> RST
    RM --> IRadarUI
    RM --> BlipPool
    
    RJ --> RPJ
    RJ --> RGJ
    
    RST --> RD
    RST --> RED
    RST --> RER
    
    %% Integration
    PJS --> |Projectile Data| RM
    EE --> |Enemy Events| RM
    PM --> |Player Position| RM
    
    %% Special Components
    RAV --> |Area Effects| RM
    RT --> |Entity Registration| RM
    
    %% UI Integration
    IRadarUI --> Canvas
    BlipPool --> Canvas
    
    %% Styling
    classDef core fill:#f9f,stroke:#333,stroke-width:2px
    classDef integration fill:#bbf,stroke:#333,stroke-width:2px
    classDef ui fill:#bfb,stroke:#333,stroke-width:2px
    classDef jobs fill:#fbb,stroke:#333,stroke-width:2px
    classDef data fill:#ffb,stroke:#333,stroke-width:2px
    
    class RM,RJ,RST core
    class PJS,EE,PM integration
    class IRadarUI,Canvas,BlipPool ui
    class RPJ,RGJ jobs
    class RD,RED,RER,RAV,RT data
```

## Core Components

### **RadarManager** (Central Hub)
- **Role**: Primary system coordinator and singleton manager
- **Pattern**: Singleton pattern with dependency injection
- **Location**: `Assets/_Scripts/Radar/RadarManager.cs`
- **Execution Order**: -100 (runs after ProjectileJobSystem at -200)

**Key Features**:
- Implements `IRadarProjectileSystem` interface for projectile integration
- Manages entity registration/unregistration lifecycle
- Handles UI display and blip pooling optimization
- Coordinates job scheduling and completion
- Provides area-based radar modifications

### **RadarJobs** (Job System Integration)
- **Role**: High-performance entity processing
- **Pattern**: Job System pattern with Burst compilation
- **Location**: `Assets/_Scripts/Radar/RadarJobs.cs`

**Key Components**:
- **RadarProjectileJob**: Processes projectile positions and visibility
- **RadarJob**: Handles general entity tracking and range calculations
- **Burst Compilation**: All computational jobs are Burst-compiled for maximum performance
- **Parallel Processing**: Uses `IJobParallelFor` for entity processing

### **RadarSharedTypes** (Data Architecture)
- **Role**: Unified data structures for radar system
- **Pattern**: Shared data types for job system compatibility
- **Location**: `Assets/_Scripts/Radar/RadarSharedTypes.cs`

**Key Types**:
```csharp
// Core radar information
public struct RadarData
{
    public Vector3 position;
    public int iconIndex;
    public bool isActive;
    public float lastUpdateTime;
}

// Burst-compatible entity data
public struct RadarEntityData
{
    public Vector3 position;
    public int iconIndex;
    public bool isActive;
    public bool isDynamic;
}

// Managed entity reference
public class RadarEntityRef
{
    public Transform transform;
    public Vector3 staticPosition;
    public int iconIndex;
    public bool isDynamic;
}
```

## Integration Architecture

### **ProjectileJobSystem Integration**
- **Automatic Discovery**: RadarManager automatically finds and integrates with ProjectileJobSystem
- **Data Flow**:
  - Positions from `projectileSystem.Positions`
  - Homing flags from `projectileSystem.HomingFlags`
  - Active state from `projectileSystem.ActiveFlags`
- **Icon Differentiation**:
  - Homing projectiles use base icon index
  - Non-homing projectiles use base icon index + 2
- **Job Synchronization**: Proper dependency chaining prevents race conditions

### **Enemy System Integration**
- **Event-Driven Architecture**: Uses `EnemyEvents` for automatic enemy tracking
- **Lifecycle Management**:
  - `OnEnemySpawned`: Registers enemies for radar tracking
  - `OnEnemyDied`: Removes enemies from radar
  - `OnEnemyDespawned`: Handles cleanup
- **Icon Assignment**: Enemies use dedicated `enemyBlipIndex` (default: 1)

### **Player Integration**
- **Radar Origin**: Uses player position as radar center
- **Camera Integration**: Radar display orientation matches camera forward/right vectors
- **Area Volumes**: Player triggers modify radar behavior in specific zones

## Visual System

### **UI Architecture**
- **Interface**: `IRadarUI` defines contract for radar display implementations
- **Built-in Management**: RadarManager includes integrated UI handling
- **Blip Pooling**: Efficient object pooling for radar blips

### **Prefab Structure**
- **EnemyjRadar.prefab**: Enemy radar blip visualization
- **ProjRadar.prefab**: Standard projectile radar blip
- **ProjRadarAmbient.prefab**: Ambient projectile radar blip

### **Rendering Features**
- **Camera-Relative**: Radar display rotates with camera orientation
- **World-to-Radar Conversion**: Sophisticated 3D to 2D projection
- **Range Visualization**: Configurable radar range with visual feedback
- **Distance-based Fading**: Configurable alpha fading with distance

## Performance Optimizations

### **Job System Architecture**
- **Burst Compilation**: All computational jobs are Burst-compiled
- **Parallel Processing**: Uses `IJobParallelFor` for entity processing
- **Batch Size Configuration**: Tunable batch sizes (default: 64)
- **Memory Management**: Persistent NativeArrays with proper disposal

### **Object Pooling**
- **Dynamic Pool Growth**: Pools expand when needed
- **Component Caching**: Cached RectTransform and Image components
- **Efficient Reuse**: Minimal object creation/destruction

### **Update Optimization**
- **Configurable Update Intervals**: Adjustable frame skipping
- **Selective Updates**: Only updates changed entities
- **Cached Values**: Frequently used values cached for performance

## Configuration System

### **Range Settings**
```csharp
[Header("Range Settings")]
public float maxRange = 50f;           // Maximum radar detection range
public float visibilityRadius = 100f;  // UI display range
public float areaMultiplier = 1f;      // Base range multiplier
public float currentAreaMultiplier = 1f; // Runtime range modification
```

### **Performance Settings**
```csharp
[Header("Performance Settings")]
public int maxEntities = 500;          // Maximum trackable entities
public int jobBatchSize = 64;          // Job processing batch size
public int updateInterval = 1;         // Update frequency control
public bool useFixedUpdate = false;    // Physics-based vs frame-based updates
```

### **Visual Settings**
```csharp
[Header("Visual Settings")]
public bool fadeWithDistance = true;   // Distance-based alpha fading
public float fadeStartDistance = 30f;  // Fade range start
public float fadeEndDistance = 50f;    // Fade range end
public float blipScaleMultiplier = 1f; // Blip size scaling
public float radarScale = 1f;          // Overall radar scaling factor
```

## Special Features

### **RadarAreaVolume** (Area Modifications)
- **Location**: `Assets/_Scripts/Radar/RadarAreaVolume.cs`
- **Purpose**: Creates zones that modify radar behavior
- **Features**:
  - Configurable area multipliers
  - Automatic reset on exit
  - Trigger-based activation
  - Visual debugging support

### **RadarTarget** (Entity Registration)
- **Location**: `Assets/_Scripts/Radar/Examples/RadarTarget.cs`
- **Usage**: Drop-in component for making objects radar-trackable
- **Features**:
  - Automatic registration/cleanup
  - Static vs dynamic object support
  - Visual debugging aids

### **Debug Features**
- **Visual Debugging**: Gizmos for radar range, blip positions, origin
- **Performance Logging**: Detailed system state information
- **Grid Visualization**: Debug grid overlay
- **Color-coded States**: Different colors for different entity states

## Usage Examples

### **Basic Setup**
```csharp
// Scene setup requires:
// 1. RadarManager on GameObject
// 2. Canvas for UI
// 3. Blip prefabs assigned
// 4. ProjectileJobSystem in scene

// Configure radar settings
RadarManager.Instance.maxRange = 100f;
RadarManager.Instance.maxEntities = 1000;
RadarManager.Instance.jobBatchSize = 128;
```

### **Entity Registration**
```csharp
// Dynamic entities (enemies, moving objects)
RadarManager.Instance.RegisterEntity(transform, Vector3.zero, iconIndex, true);

// Static entities (pickups, waypoints)
RadarManager.Instance.RegisterEntity(null, staticPosition, iconIndex, false);

// Unregister entities
RadarManager.Instance.UnregisterEntity(entityId);
```

### **Area-based Modifications**
```csharp
// Radar enhancement zones
RadarManager.Instance.SetAreaMultiplier(1.5f);  // Increase range by 50%
RadarManager.Instance.ResetAreaMultiplier();    // Reset to normal

// Check current multiplier
float currentMultiplier = RadarManager.Instance.GetCurrentAreaMultiplier();
```

## System Dependencies

### **Required Dependencies**
1. **ProjectileJobSystem**: For projectile tracking integration
2. **Canvas**: For UI display rendering
3. **Camera**: For orientation reference
4. **EnemyEvents**: For enemy lifecycle integration

### **Optional Dependencies**
1. **RadarAreaVolume**: For area-based modifications
2. **RadarTarget**: For manual entity registration
3. **Custom IRadarUI**: For specialized UI implementations

## Integration with Core Systems

### **Player System**
- Uses player position as radar center
- Integrates with player camera for orientation
- Responds to player movement and rotation

### **Enemy System**
- Automatic enemy registration via events
- Lifecycle management (spawn/death/despawn)
- Visual differentiation for enemy types

### **Projectile System**
- High-performance projectile tracking
- Differentiation between homing and non-homing projectiles
- Real-time position updates

### **UI System**
- Integrated blip pooling system
- Canvas-based radar display
- Configurable visual settings

## Best Practices

### **Performance**
- Use appropriate `maxEntities` limits for target performance
- Monitor Job System profiler for bottlenecks
- Adjust `jobBatchSize` based on entity count
- Consider update intervals for non-critical tracking

### **Configuration**
- Test radar range with actual game environments
- Balance visual quality with performance requirements
- Use area multipliers for gameplay enhancement
- Implement proper entity lifecycle management

### **Integration**
- Register entities through proper event channels
- Implement cleanup for dynamically created entities
- Use `RadarTarget` component for simple objects
- Consider radar state in game pause/resume logic

## Future Enhancements

### **Potential Additions**
- **Signal Strength**: Distance-based signal intensity
- **Sector Scanning**: Directional radar scanning effects
- **Multi-Layer Display**: Support for multiple radar layers
- **Entity Filtering**: Runtime entity type filtering
- **Jamming Effects**: Radar interference mechanics

### **Performance Improvements**
- **LOD System**: Multiple detail levels for distant entities
- **Culling**: Smart entity culling based on importance
- **Batching**: Improved UI batching for large entity counts
- **Caching**: Enhanced caching for frequently accessed data

## Related Systems

- **[[Projectile System Architecture]]** - High-performance projectile tracking
- **[[Enemy System Architecture]]** - Enemy lifecycle integration
- **[[Player System Architecture]]** - Player-centered radar display
- **[[UI System Architecture]]** - Radar display and interaction

## Notes

The BTR Radar System represents a **well-architected, performance-optimized solution** that effectively balances functionality with efficiency. It provides real-time tracking capabilities essential for the game's tactical gameplay elements while maintaining high performance through Unity's Job System and Burst compilation.

The system's strength lies in its seamless integration with existing game systems and its ability to handle hundreds of entities efficiently. The modular design allows for easy customization and extension while maintaining optimal performance characteristics.