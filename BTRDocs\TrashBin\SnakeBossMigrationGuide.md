---
title: Snake Boss Migration Guide
date: 2024-01-16
tags: [enemy, boss, migration, guide]
aliases: [SnakeMigration, BossMigration]
---

# Snake Boss Migration Guide

## Overview
This guide details the process of migrating from the legacy `EnemySnakeMidBoss` and `EnemyTwinSnakeBoss` scripts to the new modular Enemy System. The migration should be done in stages to ensure stability.

## Prerequisites
- Backup your current scene and prefabs
- Ensure all required behaviors are in the EnemySystem:
  - TimelineControllerBehavior
  - MusicSyncedCombatBehavior
  - ProjectileCombatBehavior
  - DamageVisualizationBehavior
  - TwinBossControllerBehavior (for twin boss)

## Migration Steps

### 1. Configuration Setup

#### Single Snake Boss
1. Create a new ScriptableObject:
   ```
   Right Click > Create > BTR > Enemy System > Snake Boss Configuration
   ```
2. Copy values from existing boss:
   - Health and damage settings
   - Projectile configurations
   - Timeline settings
   - Music sync IDs
   - Visual effect settings

#### Twin Snake Boss
1. Create a new ScriptableObject:
   ```
   Right Click > Create > BTR > Enemy System > Twin Snake Boss Configuration
   ```
2. Copy additional values:
   - Individual snake positions
   - Clock keys
   - Eye collider names
   - Animation trigger names

### Default Configurations
Pre-made configurations are available as starting points:
- `DefaultSnakeBossConfig.asset` - Single snake setup
- `DefaultTwinSnakeBossConfig.asset` - Twin snake setup

These include recommended values for:
- Health thresholds
- Phase multipliers
- Animation triggers
- Sound events
- Eye system setup

### New Configuration Features

#### Phase System
```yaml
phaseHealthThresholds: [75, 50, 25]
phaseAttackSpeedMultipliers: [1.2, 1.5, 2.0]
phaseAnimationTriggers: ["Phase2Start", "Phase3Start", "Phase4Start"]
phaseSoundEvents: ["event:/Boss/Snake/Phase2", ...]
```

#### Model Setup
```yaml
modelPrefab: SnakeModel
animatorController: SnakeAnimator
projectileOrigins: ["Mouth", "Tail"]
```

#### Rewind Integration
```yaml
rewindTimeScale: 0.25
rewindPitchModulation: 0.5
```

### 2. Prefab Migration

#### Single Snake Boss
1. Create new prefab "SnakeBoss_New"
2. Add required components:
   - EnemyCore
   - TimelineControllerBehavior
   - MusicSyncedCombatBehavior
   - ProjectileCombatBehavior
   - DamageVisualizationBehavior
3. Copy over:
   - Model/mesh
   - Animations
   - Colliders
   - Visual effects

#### Twin Snake Boss
1. Create new prefab "TwinSnakeBoss_New"
2. Add core components:
   - EnemyCore
   - TwinBossControllerBehavior
   - TimelineControllerBehavior
   - MusicSyncedCombatBehavior
   - ProjectileCombatBehavior
   - DamageVisualizationBehavior
3. Setup snake instances:
   - Create child GameObjects for each snake
   - Copy models and animations
   - Setup eye colliders
   - Configure projectile origins

### 3. Scene Updates

1. **Test Scene Setup**
   - Create a test scene copy
   - Replace old boss with new prefab
   - Verify all references

2. **Reference Updates**
   - Update any scripts referencing the old boss
   - Check event listeners
   - Update UI references

3. **Timeline Integration**
   - Verify Chronos timeline setup
   - Check clock assignments
   - Test time manipulation

### 4. Testing Protocol

#### Basic Functionality
- [ ] Health and damage system
- [ ] Movement and positioning
- [ ] Projectile attacks
- [ ] Visual effects
- [ ] Audio cues

#### Advanced Features
- [ ] Music synchronization
- [ ] Time manipulation
- [ ] Eye locking system
- [ ] Phase transitions
- [ ] Death sequences

#### Twin-Specific Tests
- [ ] Individual snake control
- [ ] Coordinated attacks
- [ ] Independent health tracking
- [ ] Eye system per snake
- [ ] Combined death sequence

#### Phase System Tests
- [ ] Phase transitions trigger at correct health thresholds
- [ ] Speed multipliers apply correctly
- [ ] Animations play on phase change
- [ ] Sound effects trigger properly

#### Events Integration
- [ ] OnDamaged events fire
- [ ] OnPhaseChange events trigger
- [ ] OnDeath events execute
- [ ] OnRewindStateChange events work

### 5. Cleanup

1. **Code Cleanup**
   ```csharp
   // Add to old scripts
   [System.Obsolete("Use new EnemySystem components instead. See SnakeBossMigrationGuide.md")]
   public class EnemySnakeMidBoss : MonoBehaviour
   ```

2. **Asset Management**
   - Move old prefabs to "Legacy" folder
   - Update scene references
   - Document any manual fixes needed

### 6. Validation Checklist

#### Configuration
- [ ] All values properly transferred
- [ ] Validation rules working
- [ ] Default values appropriate

#### Components
- [ ] All required components present
- [ ] References properly set
- [ ] Event connections working

#### Performance
- [ ] No duplicate behaviors
- [ ] Proper cleanup on disable/destroy
- [ ] Memory usage optimized

## Troubleshooting

### Common Issues

1. **Missing References**
   - Symptom: Null reference exceptions
   - Fix: Check component initialization order
   - Prevention: Use proper dependency injection

2. **Timeline Sync**
   - Symptom: Desynced animations/attacks
   - Fix: Verify clock assignments
   - Prevention: Use timeline validation

3. **Eye System**
   - Symptom: Eye colliders not registering
   - Fix: Check naming conventions
   - Prevention: Use explicit registration

### Emergency Rollback
1. Keep old scripts until new system is verified
2. Maintain backup prefabs
3. Document any scene-specific changes

### Phase System Issues
- Symptom: Phases don't transition
  - Check health threshold values
  - Verify event connections
  - Validate array lengths

### Model Loading
- Symptom: Models don't appear
  - Check prefab references
  - Verify hierarchy setup
  - Confirm animator assignment

### Event System
- Symptom: Events don't fire
  - Check event registration
  - Verify subscriber setup
  - Validate event parameters

## Related Documents
- [[SnakeBoss|Snake Boss Documentation]]
- [[TwinSnakeBoss|Twin Snake Boss Documentation]]
- [[EnemySystemGuide|Enemy System Guide]]
- [[SystemsDesignAnalysis#Migration|Systems Migration Analysis]] 