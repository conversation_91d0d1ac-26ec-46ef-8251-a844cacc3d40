# March 10

Fixing the prefab pooling of Snake Eyes on infinite track generation

Wasnt talking to pool properly

Editied Better Object Pooling Instance - ensure this isnt reset!

Added StopAllCoroutines to it. 

Still havent figured this out.

Coroutine issues still happening

 BUT

- prefabs are being properly despawned now
- doesn’t seem to effect gameplay?

Need to still figure out why i can extend the tail in the example scene, however in my scene it’s not working properly, im always at the end of it. it seems this becomes a problem over time - needs to be addressed, for visual reasons and others

Changed snake eyeballs to shoot from their forward. 

Enemies need to shoot a different colour bullet, rectify this