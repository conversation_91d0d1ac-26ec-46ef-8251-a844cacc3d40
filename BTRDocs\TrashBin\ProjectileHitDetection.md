# Projectile Hit Detection System Documentation

## Current Implementation Overview

Our current hit detection system uses multiple complementary approaches to detect when projectiles hit the player:

1. **OnTriggerEnter Physics-Based Detection**: 
   - Primary detection through Unity's built-in physics system
   - Relies on collider intersections reported by the physics engine

2. **Enhanced Sphere Casting**:
   - Secondary detection using Physics.SphereCast
   - Attempts to catch high-speed projectiles that might miss trigger detection
   - Uses expanded radius and velocity-based prediction

3. **Spatial Grid Proximity Checks**:
   - Tertiary system using a custom spatial partitioning grid
   - Performs additional proximity checks for projectiles close to the player
   - Uses distance-based and trajectory-based detection

4. **Queued Damage System**:
   - Processes hits sequentially through a queue
   - Designed to handle multiple simultaneous hits
   - Uses invincibility frames after processing a hit

5. **Job System Integration**:
   - Uses Unity's job system for projectile movement
   - Tracks projectile-player proximity in native arrays
   - Attempts to do hit prediction based on trajectories

## Current Challenges

Despite these layered approaches, we're still experiencing issues with hit detection:

1. **"Tunneling" Effect**: 
   - Fast projectiles may pass through colliders in a single frame
   - Physics engine can miss these intersections entirely

2. **Inconsistent Registration**:
   - Some hits visually appear to connect but don't register
   - Other hits register multiple times or inconsistently

3. **Multiple Hit Scenarios**:
   - When multiple projectiles hit simultaneously, some are not registered
   - Invincibility frames may prevent registering subsequent hits

4. **Performance Overhead**:
   - Multiple detection systems create substantial overhead
   - Many raycasts and distance checks per frame

5. **Race Conditions**:
   - Thread safety issues with concurrent hit processing
   - Collection modification during iteration

## Alternative Approaches

### 1. Manual Trajectory Prediction System

Instead of relying on Unity's physics, implement a pure custom solution:

```csharp
void Update() {
    // For each projectile
    foreach (var projectile in activeProjectiles) {
        Vector3 currentPos = projectile.transform.position;
        Vector3 nextPos = currentPos + (projectile.velocity * Time.deltaTime);
        
        // Check if line segment from currentPos to nextPos intersects with player bounds
        if (LineIntersectsSphere(currentPos, nextPos, playerPosition, playerRadius)) {
            RegisterHit(projectile);
        }
    }
}
```

**Benefits**:
- Complete control over hit detection logic
- No reliance on Unity's physics timing
- Can be optimized specifically for projectile-player interactions

**Drawbacks**:
- Must implement custom spatial optimization
- Would need to handle complex collider shapes

### 2. Fixed-Timestep Physics Simulation

Use a fixed, smaller timestep specifically for projectile movement and hit detection:

```csharp
// Use a smaller fixed timestep just for projectiles
const float PROJECTILE_PHYSICS_TIMESTEP = 0.005f; // 5ms, much smaller than default

void UpdateProjectilePhysics() {
    float remainingTime = Time.deltaTime;
    
    while (remainingTime > 0) {
        float dt = Mathf.Min(PROJECTILE_PHYSICS_TIMESTEP, remainingTime);
        
        foreach (var projectile in activeProjectiles) {
            // Move in smaller steps
            projectile.transform.position += projectile.velocity * dt;
            
            // Check for collision at this sub-step
            if (IsCollidingWithPlayer(projectile)) {
                RegisterHit(projectile);
            }
        }
        
        remainingTime -= dt;
    }
}
```

**Benefits**:
- More accurate for high-speed projectiles
- Simulates continuous collision detection
- Less likely to miss intersections

**Drawbacks**:
- Performance cost of multiple steps per frame
- Still needs spatial optimization

### 3. Position History & Backtracking

Track position history and perform retroactive collision checks:

```csharp
// Each projectile keeps a short history of positions
void UpdateProjectile(Projectile p) {
    // Store previous position
    p.previousPositions.Add(p.transform.position);
    if (p.previousPositions.Count > HISTORY_LENGTH) {
        p.previousPositions.RemoveAt(0);
    }
    
    // Update position normally
    p.transform.position += p.velocity * Time.deltaTime;
    
    // If player position changed significantly
    if (Vector3.Distance(playerLastCheckedPosition, playerCurrentPosition) > threshold) {
        // Backtrack and check for missed collisions
        for (int i = 0; i < p.previousPositions.Count - 1; i++) {
            if (LineIntersectsSphere(
                p.previousPositions[i], 
                p.previousPositions[i+1],
                playerCurrentPosition,
                playerRadius)) {
                RegisterHit(p);
                break;
            }
        }
        playerLastCheckedPosition = playerCurrentPosition;
    }
}
```

**Benefits**:
- Can catch collisions missed in previous frames
- Works well for fast-moving players or projectiles
- Especially useful for near-misses that should have been hits

**Drawbacks**:
- Memory overhead for position history
- Additional computation for backtracking

### 4. Custom CollisionWorld Using BVH (Bounding Volume Hierarchy)

Implement a specialized collision system optimized for many small projectiles against few large targets:

```csharp
class ProjectileCollisionWorld {
    BVHTree playerBVH;  // Bounding volume hierarchy for player(s)
    List<Projectile> activeProjectiles;
    
    void Update() {
        // Update player BVH
        playerBVH.Update();
        
        // Test each projectile against the BVH
        foreach (var projectile in activeProjectiles) {
            Ray projectileRay = new Ray(
                projectile.previousPosition,
                projectile.transform.position - projectile.previousPosition
            );
            
            if (playerBVH.Raycast(projectileRay, out RaycastHit hit, 
                Vector3.Distance(projectile.previousPosition, projectile.transform.position))) {
                RegisterHit(projectile, hit);
            }
            
            projectile.previousPosition = projectile.transform.position;
        }
    }
}
```

**Benefits**:
- Highly optimized for the specific case of many projectiles vs. few targets
- Efficient spatial partitioning
- Can implement true continuous collision detection

**Drawbacks**:
- Complex implementation
- Separate from Unity's physics system

### 5. GPU-Based Collision Detection

For extremely high projectile counts, offload collision detection to the GPU:

```csharp
// CPU-side code
ComputeBuffer projectileBuffer;
ComputeBuffer playerBuffer;
ComputeBuffer resultBuffer;

void UpdateCollisions() {
    // Update buffers with current positions
    UpdateProjectileBuffer();
    UpdatePlayerBuffer();
    
    // Dispatch compute shader
    collisionShader.Dispatch(kernelHandle, projectileCount / 64, 1, 1);
    
    // Read back results
    CollisionResult[] results = new CollisionResult[resultCount];
    resultBuffer.GetData(results);
    
    // Process hits
    foreach (var result in results) {
        if (result.collided) {
            ProjectileStateBased p = projectileLookup[result.projectileId];
            RegisterHit(p);
        }
    }
}
```

**Benefits**:
- Can handle thousands of projectiles efficiently
- Parallelized collision detection
- Can implement sophisticated detection algorithms

**Drawbacks**:
- Complex to implement
- Requires compute shader support
- Buffer transfer overhead

## Recommended Approach

Based on analysis of our current issues, I recommend implementing a hybrid approach combining:

1. **Fixed-Timestep Simulation** for high-speed projectiles to prevent tunneling
2. **Position History & Backtracking** to catch near-misses
3. **Simplified Colliders** to improve performance and reduce edge cases

Most importantly, I recommend removing several of our current overlapping systems to reduce complexity and potential conflicts. The current multi-layered approach may be creating race conditions where systems interfere with each other.

### Implementation Plan

1. Refactor to a single, authoritative hit detection system
2. Implement sub-stepping for high-speed projectiles
3. Add selective backtracking for projectiles that pass near the player
4. Ensure all hit registration happens in a single code path
5. Add extensive debugging visualization to identify missed hits

This simplified but more robust approach should improve hit detection reliability while reducing overhead and potential conflicts. 