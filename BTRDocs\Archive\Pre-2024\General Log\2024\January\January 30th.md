# January 30th

Need to fix Scaling issues of bullets, they’re wildly off depending on what object shoots them. Need to set them a certain size and not inheirit scale from parent objects

Adding a static shooting “enemy” - not killable currently but emits bullets from terrain 

Using FindObjectOfType in the class, this could be a problem. Need to refine this so the code is better 

The on wave stated issue didnt fix my problem seems likely it more of a pooling issue, with projectile pool being created and some particular reference being required. 

Issue is finding tag with player when player is currently disbaled - lol
”_projectile.currentTarget = GameObject.FindWithTag("Player").transform;”

Removing all the wavespawner stuff, not necessary?

Changed Cinemachien Camera Switching so that it disables player model and reticle, rather than the whole playe object, that way game SHOULD start without errors about finding the player game object

Snake Eyeball Prefab is made, should be good to place around multiple areas of the Snake.