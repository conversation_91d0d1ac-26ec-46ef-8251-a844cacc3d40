# Summary of 2023 - Research & Inspiration.md

This file is a log of research and inspiration gathered throughout 2023 for the game project. Key areas of focus include:

*   **Color Palette & Visuals:** Research into color theory, developing a color palette (blue shadows), and experimenting with shader effects like depth testing. Inspiration from Solar Ash's visual design.
*   **Recursion & Geometry:** Exploring recursion concepts (<PERSON><PERSON><PERSON> triangle) and their relation to music and game geometry.
*   **Game Design Principles:** Studying shmup principles, rhythm game UX, level design, and game design theory. Analyzing games like Starfox 64, Rez, and Bioshock for design insights.
*   **Mechanics & Theme Relationship:**  Focus on practical tools for linking game mechanics to themes, emphasizing "Mechanics → Tone → Themes" approach.
*   **Time Manipulation:** Researching time manipulation mechanics and their implementation in games (Chronos engine).
*   **Accessibility:**  Considering accessibility in game design.
*   **Narrative & Mythology:**  Exploring the Tower of Babylon allegory, Ophanim, Ouroboros, and Jungian concepts for narrative and thematic depth.
*   **Audio & Music Research:** Investigating <PERSON>' work on game audio and the role of music in games like TUNIC.
*   **Shader Research:**  Extensive research into shaders, including lighting techniques, black hole shaders, and general shader theory.
*   **Lighting & Visuals:** Studying lighting theory for games, techniques for creating beautiful scenes, and specific lighting approaches (Magic Light Probes, Bakery setup).
*   **Level Creation & Design:**  Looking into efficient level creation methods (Hang Line GDC talk) and level design principles.
*   **Camera & Controls:** Researching camera control techniques (Cinemachine Free Look Camera) and aiming mechanics.
*   **Game Vision & Pillars:**  Reflecting on the game's vision, defining core pillars, and establishing a unique game concept.
*   **Optimization Research:**  Briefly touching on optimization methods for A* pathfinding and general performance.
*   **Workflow & Tools:**  Exploring tools like Flexalon, Unity Muse, UModeler X, and Curvy integration for Behavior Designer.

The log demonstrates a broad research approach, covering visual style, game mechanics, narrative themes, and technical implementation. It reflects an iterative process of gathering inspiration from various sources, analyzing successful games, and exploring different tools and techniques to refine the game's design and development.