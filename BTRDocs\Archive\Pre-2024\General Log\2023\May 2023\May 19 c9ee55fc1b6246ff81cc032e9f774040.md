# May 19

Removed parent settings from projectile - seems unecessary now

Working on a redo of Projectile class

Made state based - not complete , lock on not working, need to look at it more. 

X ray effect ideas

[Highlight Plus](https://assetstore.unity.com/packages/tools/particles-effects/highlight-plus-134149)

Probably the best approach

[X-ray Stealth Vision in Unity Shader Graph and Universal Render Pipeline](https://www.youtube.com/watch?v=eLIe95csKWE)

Time rewind seems to screw up enemies know their position (getnearest planting them firmly on mesh)

Need to look into if it’s a one off or actually causing problems.