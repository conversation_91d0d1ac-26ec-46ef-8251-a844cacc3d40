# May 25

Disabled vertical movement of player, just left and right movement exists for dodging

Breaking error that happens

![Untitled](May%2025%2098d127f8725e40448db18110e5748904/Untitled.png)

Comes from various Enemy Shooting Particles

Lots of bullets in bullet pool so unsure that that is the issue

Coming from active enemies - they still seem to be shooting

I think this slows down the game as well

Code referenced

![Untitled](May%2025%2098d127f8725e40448db18110e5748904/Untitled%201.png)

Shooting is always looking at camera, but maybe best to have it look at a wall instead? That way I can switch camera angles and have it looking in different directions based on wall position. f

Dynamic Crosshair 

[How to Create a Simple Dynamic Crosshair (Unity3D, C#, Game Design)](https://www.youtube.com/watch?v=-7DIdKTNjfQ)

Need a real Concept / Level Design day where I assess modelling options ideas an lay out a plan

Made documents Mechanics Refernces and Scenario Ideation