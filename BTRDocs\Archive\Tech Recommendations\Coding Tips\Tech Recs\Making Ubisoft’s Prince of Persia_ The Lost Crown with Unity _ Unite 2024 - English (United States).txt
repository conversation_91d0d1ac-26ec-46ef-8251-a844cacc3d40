Making Ubisoft’s Prince of Persia: The Lost Crown with Unity
    


[BENOÎT] Hello, everyone. On behalf of our entire team, we are very happy to be here today to share some of the work we have done on "Prince of Persia: The Lost Crown." In this presentation, we will share insights into the game's development process, the challenges we faced, and the optimization strategies we used to ensure a smooth experience on all platforms. I'm <PERSON><PERSON>, a 3D programmer on the project. My focus has been on rendering performance, especially for the Nintendo Switch™. [Nintendo Switch is a registered trademark Of Nintendo]. I'm with <PERSON>. [GUILLAUME] Hello. [BENOÎT] A programmer on the project. He has been working on various aspects of the game related to memory, tracking, and consoles. We will first give you some quick context about the game and talk about its production. We will list the key optimizations we put in place to improve performance and how we manage to fit the game into memory. But first, let us introduce our game. "Prince of Persia: The Lost Crown" is an action adventure Metroidvania with a side-scrolling camera. From the very beginning, our team had a gameplay-first mentality, which is why achieving a smooth frame rate on all platforms was very important to us. The game was developed using Unity 2021 with URP. It was released in January, and we have been very happy with all the positive feedback from the players. The new DLC, "Mask of Darkness," just released a few days ago and we hope that many players will enjoy it too. All games shipped on many different platforms at the same time, but the Nintendo Switch was our main target. We really wanted to ensure that this version would not be seen as a downgrade by players. This is why it runs at native resolution, even though other versions support up to 4K resolution and 120 FPS. But before we continue, let us watch a short trailer of the game. (intense music) (sword clashing) (slow dramatic music) (electricity crackling) (singing in a foreign language) (intense music) [QUEEN THOMYRIS] Be my guests and suffer. (metal clanging) Sargon. (Queen Thomyris laughs) (intense music) (electricity crackling) (intense music) You will bend and crack. Your bones will ask for mercy. Lullaby so short and sweet. Fast asleep, no time to weep. (character grunting) (intense music) You're just another dog in the litter. You still deserve death. (intense music) (audience clapping) [GUILLAUME] Thank you very much. Hello everyone. I'm Guillaume Gernez, programmer on the project. One of the key challenges of this production was scaling to the large game scope on the size of our team. This was also the first time our team developed a game of this scale for multiple platform using Unity. To create this game, we first needed a hierarchical work structure that would fit the requirements of a Metroidvania game. We had to adapt data and features for scalability on our different target platforms and we will show some of the tools we used to streamline production. A world is a set of levels connected to each other. A level is made up of several units like art, design, sound, cinematics, and lighting. The setup makes it easier for the team to collaborate, allowing them to work together on the same level with less conflicts. We also have C data layers that are dynamically loaded when needed. This is also useful for patching where we can add script to delete, modify, or create existing GameObjects. A seamless game experience requires a streaming system. On "Prince of Persia", it's a simple and standard solution. When the player enters the level, we begin loading the next level. We let Unity load scenes on vendors. Most of the data is shared between levels of the same biome, but we also have specific data for each level, like baked light on meshes. We customize streaming settings like how many neighbor levels are loaded depending on the platform. On high performance devices, we use texture streaming to maximize texture quality - otherwise, it is disabled to save performance. We use Houdini to help artists create whole levels. It's the very first way to design levels for a platform game while maintaining complete control over the level of design. The Houdini part was managed by artists with minimal intervention from the programmers. The art team created graphic kits for each biome and the levels, designed by a level designer, are graphed automatically. The main drawback of this modular approach, is that it creates a lot of objects. For example, the columns consist of three parts in their C+ form. With over 2,000 Unity scenes, you have an idea of the game's scope. These figures provide an estimate of the game scale and the volume of data we needed to produce. We use Perforce with a single repository that contains all files for managing all platforms. Users sync only what they need for their job. Each platform has different quality settings to run the game at the highest resolution and frame rate possible and we adjust these settings during production to achieve the best compromise. We wanted to keep the setting transparent for the production team so they could focus on making a good game. Let's talk about texture scaling. In "Prince of Persia", textures are made by artists in 4K to 8K resolution. You assign presets using rules based on naming conventions, and the tools allow us to apply the settings to all applicable textures. Artists can revise those settings, but they usually don't need to except for very specific cases. This way, we can trust that relevant size and format are used for all platforms. We changed the way the texture importer downscales textures to use a divider instead of max size. For example, when we wanted to change light back resolution for the Nintendo Switch to improve quality, we could do it in a single click using this tool. Automatic tools are mandatory when dealing with huge amounts of data. For example, there are over 12,000 textures and 6,000 animations. One of the key philosophies for the project is to facilitate testing so anyone can run and quickly see the results on console. We customized the scriptable build pipeline for our needs. With a few clicks, the user can select the part of the game they want to build as well as several rebuild configurations. They can also specify data or code shelves to use for their custom version. Builds are processed on Ubisoft Cloud to free users' computers and to take advantage of the daily cache to provide a faster build. For automatic testing, we have a bot that travels through the level. Moving the bot's axis is quite simple. It runs every night on console with a debug on the release version. We have different models and different iterations to monitor long term stability. The bot plays through the game world twice and the resolution from a fresh boot, it monitors levels in real collision without previous influence. Error reports, memory usage, leaks collect CPU/GPU timing for the area. It logs warnings and errors, and generates a quality report overview and it creates bug reports for tracking. Once the process is complete we receive a summary with the problems that need to be tackled quickly. Let's see a tool we developed around it: the Performance Map Tool. The purpose of this tool is to display real-time data, specifically about manual usage on CPU/GPU performance in context for every location in the game. We developed a little tool to analyze the data after our editors, linking to the actual levels and assets in the editor. A very useful feature of this tool was its history so we could compare all the changes between builds. Here's a short video of the tool in action. On the right, you can see the different game builds. We have an overview of the gameworld, and color helps us quickly identify areas that need optimization. It's also possible to obtain more detailed information about the asset loaded at specific locations. These areas can be quickly analyzed further by the quality assurance team, allowing them to fill out bug reports with more details. To summarize, we established a production philosophy focused on delivering the best player experience by making gameplay the central purity in all decisions. We support rapid updates and changes to enable content creators to work efficiently and adapt quickly. We streamlined the workflow by adding technical complexities, allowing the team to concentrate on creativity and innovation. We ensure a smooth process for deploying and testing on console to maintain high quality and performance throughout development. Finally, we ensure the game is always playable. And now, I'll hand over to Benoît to talk about performance. [BENOÎT] Thank you, Guillaume. (audience clapping) So, let's now move on to the performance section. Reaching 60 FPS for a game on the Nintendo Switch was a challenge, and to be honest, at the beginning, some people thought it would be impossible, which of course only motivated us even more. To reach our goal, we started by doing some benchmarks so as to identify the key areas for optimization. First, we quickly found that rendering our levels took a lot of time both on the CPU and the GPU. Materials and lighting were expensive to set up. Animation and skinning were also quite costly and post-processing effects were just too expensive for a GPU budget. So in this performance section we will first explore how we optimized all levels. Then in the second part we will quickly look at other areas that required extra work to improve performance. First, why do we need to crunch all levels? The main reason is to reduce the CPU costs. We also want to optimize geometry and lightmaps so as to minimize build size, to improve memory usage and shorten loading times. And we want to achieve all of this while improving GPU performance if possible, not make it worse. There is a way to optimize static geometry in Unity called static batching. However, the procedural generation used in all levels still created too many separate instances and we were above our performance budget so we had to explore beyond static batching to see how we could improve performance even more. We used an Octree to divide all scene geometry into chunks of up to 64K vertices using Burst Jobs and we adjusted the parameters so as to try to find the right balance between a reduced CPU workload by having fewer objects and an increased GPU cost by having to process more offscreen geometry. Dynamic geometry was also combined when possible, depending on the context. In this map, for example, we could reduce potential draw calls by a factor of five. Static batching was still enabled but we now had much fewer objects. This made rendering the levels faster but we now had another issue: the size of the static batching data. So one of the first things we did to reduce the size was to enable mesh compression in player options. We could do it for all attributes except the position attribute because of precision issues. Compatibility with static batching led to constraints on the vertex format we could use. Initially we tried to use a custom format but then we had worse performance. Indeed the static batching wasn't working anymore because the position wasn't a float3 or a float4 attribute. So instead we packed texture coordinates into the existing attributes. We used the extra components available when a float3 normal attribute became a Alpha 4 after compression to store the first part of the UV and we stored the second part of the UV in the same component as a tangent sine, shifting the values so as to be able to store both the positive value and a sine into the same ALF components. It sounds like a very small change but it had a significant impact and saved 500 mb on build size. Another thing we did early to reduce the static batching data size was to remove back-facing triangles from build. It saved up to 30% of the triangles in our maps and we thought that it would be good enough to ship the game but a few weeks left in the project, our game still did not fit within our cartridge size constraints and static batching geometry was still the most significant budget, even before textures and animations. So we had to quickly find a way to optimize geometry even more and started wondering how we could really have only the triangles that are actually visible in the game in our builds. Fortunately, a GPU is perfect for this kind of task and build machines already add GPUs to compute lightmaps. So we also use them to compute visibility. We render the levels from several viewpoints to textures with a simple shader that outputs a unique value per triangle. Then we could read the textures back on the CPU, merge all the results and now we had a list of all the potential visible triangles of the levels in-game. Let's see how it works on the sample scene. On the left we can see the camera sampling positions and on the right the corresponding rounder outputs. As it's using the GPU, the process is super fast. It takes a few seconds per map during baking and that's only because the merge part is done on the CPU. Using GPU triangle visibility, back-facing triangles have still been removed, but now every triangle occluded by another object or inside an object has also been removed and because of the way we built our levels, we frequently add a lot of such hidden geometry. Now let's check the results on real game maps. This was a significant improvement for us. In the first example, using back-face culling saved about 20% while GPU triangle visibility saved 44% and even more in the second example on the right, a map which features a lot of organic geometry. Back-face culling was performing very poorly, saving only 15% of the geometry, whereas GPU triangle visibility saved 50%. As you can imagine, we were very pleased to save several gigabytes of data just days before a deadline and more importantly, without any impact on quality. Graphics engines usually draw objects front to back for a very good reason: to improve Z-buffer efficiency. We saw having fewer but larger objects was very beneficial for the CPU but it can also hurt GPU performance because it means there is less opportunity for hierarchical Z-buffer rejects, and per-object sort doesn't help much anymore. The good news with that, we had a 2.5D game, so we could actually sort the triangles front to back once for all when baking the scenes and as we had no see-through pass, this helped reduce opaque overdraw. Indeed, with only per-object sorting, all large chunks of triangles were rendered in no specific order. By sorting triangles front-to-back during baking, we could reduce opaque overdraw and ultimately minimize the number of fragments the GPU needed to shade. For lightmaps, we use Unity's non-directional lightmaps with baked ShadowMasks. In order to save memory and improve GPU performance, we used a custom lightmap format. So instead of having two textures - one HDR lightmap, and another one supporting up to four ShadowMasks - we used a single sRGB texture and stored the ShadowMasks in the alpha channel. When editing levels, artists and designers need to work with the original version of the maps. However, we also want to work with the optimized version as soon as we enter Play Mode. So we needed a way to compute only one set of lightmap data that would work for both modes. Lightmaps were computed using the combined version because it made everything simpler. We didn't need to assign lightmap IDs and we got more optimal UV packing. Initially, each separate object has its own unwrapped UV. So when combining several objects together, the first step is to ensure that every triangle in the new mesh will have a unique lightmap UV. To achieve this, we compute an additional transform for the UV coordinate that depends only on the objects indexed within its group. This way, we could reuse the same lightmaps onto the original objects, just a matter of computing the lightmap scale and bias parameters by inverting the additional transform. Despite merging many geometries offline, we still have a lot of objects to cull again the camera frustum - every frame - and this can be an issue especially in large levels. So to reduce this cost, we used the 2D grid, based on camera position, and pre-computed object visibility while processing scenes. Cells store delta with neighbors so that at runtime when the camera entered the new cell we could toggle many renderers on and off. We use this system not only for static meshes but also for some non-moving renderers like particle systems. To summarize, this is a process we use to optimize all levels. First we sort meshes to see what can be combined together. Then we combine all geometries using an optimized format but without any culling at this point. We use this version to compute lightmaps and light probes. We perform triangle visibility and remove all the unnecessary triangles. We sort the remaining triangles front-to-back for the GPU. We optimize the index and vertex buffers, roughly preserving the triangle order, and then we pre-compute the object visibility grid. In the game build, the static batching will still be enabled on top of these optimizations. The whole process was transparent for the users. The optimized version of the level was automatically updated every night on the build machines, but could also be backed on demand if needed. During production we needed to monitor levels closely so we developed a tool to track budgets across all platforms and issue automatic warnings. Among other information, this tool provided the static batching data on lightmap sizes for every level on every platform. It really helped us refine our optimization process. The level art team also used it to quickly identify levels that required more optimization and to ensure they stay within their allocated budgets. In conclusion, the process was effective for us and 98% of our level geometries were optimized using it. This improvement not only boosted CPU and GPU performance, but also reduced the size of the static batching data by around 50%, helping us stay within our cartridge size constraints. Now I will quickly talk about other optimizations. The first one is about materials. Our project originally used Shadergraphs. They provided a great way to prototype our materials and achieve the look of the game. However, we had some issues. First, we had too many differentiators, with some of them being almost identical. This is not good for batching and it wastes a lot of memory. Second, we knew that we would have many effects in the game that would need to be shared by almost every material. Like, for instance, the accessibility features. And finally, after the first profiling sessions, we knew that we would have to squeeze every bit of GPU performance possible. For these reasons, we decided to move from Shadergraph to HLSL shaders, replacing all existing Shadergraphs with a more optimized Übershader. Of course, we didn't want to add extra work for the artists, so we used an automatic conversion. This transition allowed us to have better control over performance and features. For example, we were now able to pre-compute more optimized shader constants in the ShaderGUI, and more easily balance between having more specialized shaders but more variants, or less optimized shaders with fewer variants. With a unified interface, it also became easier for anyone to work with materials like driving values from gameplay code or cinematics. This Übershader approach was used for every material in the game, for level art, for characters, for VFX. It greatly improved not only performance but also our production pipeline. During the profiling session, the setup of lighting data was also identified as one of the potential bottlenecks. It used a uniform array of size 256 slides introducing a small CPU cost for every draw call. However, since we had decided early in the project to fork URP to adapt it to our needs, this was something we could address. First, reducing the max visible light constant to only 16 already saved alpha milliseconds, decreasing the time spent copying shader uniforms constants. But then we discovered that URP had existing code to use structured buffers instead of uniforms. Using structured buffers saved an additional 0.6 ms on the CPU but there's a drawback: the GPU was now slower. So we modified this existing code so as to use constant buffers instead of structured buffers, so as to force scalar reads on the GPU. This allowed us to have the same CPU speed improvement as we structured buffers but without the negative impact on the GPU. To maximize performance, we also used baked shadows a lot. We limited ourselves to a single baked shadow per level to save memory and simplify the shaders. Most maps use directional sunlight as the main light. In this case, we didn't use shadow cascades so as to save shader instructions, but we had performance issues on the very few maps that were using a spotlight as a main light because it triggered the additional light shadows pass which is much more expensive to render. To address this, we had to introduce a new shader keyword to handle this case specifically. We also disabled dynamic shadows on distant objects, very unlikely to receive them and used unlit map painting for backgrounds wherever possible. Animations also frequently stood out as a major issue. during our performance analysis. We first noticed that our game performed better using the CPU skinning jobs instead of GPU compute skinning. To speed up these jobs, we removed tangents from skinned meshes during import and reconstructed tangent space for normal maps in the pixel shader using the famous DDX DDY instructions. For a small GPU cost, it made all the CPU skinning jobs about 30% faster because now they only had to skin position and normals. We also had an issue with the performance of morph targets. Originally we used a single combined mesh per skin, but morph targets were only used for heads in our game. So in a note to French history, we had to separate heads from bodies so as to fix all the issues, and it reduced the cost of morph targets. In this context, when the designers wanted animated crowds in the background for some levels, we simply didn't have the CPU budget for that and we had to find another solution with zero CPU cost. So we baked animations into textures as a cheaper alternative. Positions and normals were read from the textures in the vertex shader and these textures could be compressed like any regular texture. In this example from one of the early levels of the game, all the enemies in the background are actually playing the same animation baked in a 2 mb texture. Of course, we added some randomness in the shader using the instance position as a random seed. We also had a very tight budget for post-processing effects, actually less than 2 ms in 1080p on the Nintendo Switch and this was including anti-aliasing. So we profiled the game running with all the effect we needed and our conclusion was that a 60 fps game couldn't afford 1.4 ms for bloom effects or 1.6 ms for FXAA. So we got rid of the existing stack and decided early that we had to implement our own so as to get only the effects we needed, and more importantly, for as cheap as possible. Our custom post-processing stack included several optimizations to optimize performance. First, we had a common downscale pass before all effects. This pass not only downscaled the source image but also other values such as the stencil mask or the bloom Brightpass result using multiple render targets. This made all the following effects much cheaper to compute. For example, the bloom effect was now optimized down to 0.3 ms. All effects were then applied during the only full resolution draw call directly to the back buffer to avoid an intermediate copy. During this same pass, we also used dynamic branching so as to apply FXAA only where needed, using a simple edge-detection test based on the first FXAA samples. With this optimization, a final pass was now cheaper than the original anti-aliasing alone and we were able to fit within our 2 ms budget. A key factor in achieving performance was providing the artists with all the graphics tools they requested, such as an overdraw heat map for the VFX artists, or vertex density mode for the level artists. The artists on our game put a lot of effort into ensuring that the data was as optimized as possible and these tools were there to support them. In summary, these are the strategies we used to optimize performance. First, we established budgets early on. They were inaccurate, but it's still better to have a budget you can refine than to not have one. Second, leveraging game-specific opportunities was instrumental. For example, several optimizations were only possible because we had 2.5D game. Third, we never assumed that an optimization just worked. We took a lot of time to measure accurately. We also avoided profiling too much in the editor. Instead, we focused on the release build without VSync on the Nintendo Switch so as to get more relevant results. And lastly, we used all the tools available. We used Unity tools, we used native tools, and we developed our custom solutions when needed to get a better understanding of our data and its impact on performance. And now Guillaume will talk about memory. Thank you. [GUILLAUME] Thank you, Benoît. (audience clapping) Some of our platforms have limited memory, and because of our game scope, we had to be extra careful. About memory usage, this is something the team always has in mind; the fact that the game has to run within the console constraints. We use benchmarked levels majorly in the project to define our first memory budget. Creating a memory budget is always challenging. You might be too optimistic and run out of memory or too cautious and downgrade frame quality. That is why during the production we constantly monitored memory usage as the game evolved to refine our budget. We had to balance memory usage on our ambitions on quality, on frame rate. The fact we wanted to offer a seamless experience also put some additional pressure on memory. Before optimizing memory, it's essential to have a comprehensive overview. We used an API provided by a third party to complete Unity's built-in statistics. Achieving 100% visibility into allocations is almost impossible, but we have tried to address the gaps. For example, we modified the geometry overrides to retrieve allocation statistics. We also used a third-party memory profiler to help identify unreported allocations. We customized the memory profiler to add our own statistics and help to fill the untracked parts. We can also export views to other tools for deeper and faster analysis. In the game, to increase our chance of tracking issues, we can capture memory snapshot on demand or automatically when a certain trigger is exceeded. For example, if the management model reached 300 mb, we could get an automatic capture for future analysis. We also expanded the snapshot content with a metadata collect API to gather additional information. To continue memory profiling, we created our own resource viewer using the player connection API to make memory tracking easier with the live connection to the console. The tools display detailed information about Unity assets and external assets using real-time capture or generated reports. It was very useful to track leaks by comparing scenes on GameObject resources between runs and to track temporary resource spikes. Using this tool we could create our own category to facilitate memory budget and track major offenders. Here's a demonstration of the resource viewer. We connect to the console and capture the data. You can view each category in detail, like textures. We can see properties and we can perform searches. Making a game in C# was a challenge and something new for a good part of the team. We had to learn the good practices regarding memory allocation, understand how the garbage collector works, and forget runtime allocation from the C++ we were used to. We chose Addressables to handle the streaming, to ensure there are no loading screens between levels, and to guarantee efficient streaming, all the data has been optimized. With over 300 levels to run, and numerous back and forth the memory was put under pressure. Dealing with managed memory was not easy and removing frame rate allocation was a hard task, believe me. And sometimes our allocation created big spikes, leading us to fragmentation on increased memory usage. We had to rewrite signification portions of code to address these issues. In managed memory, there is a dedicated memory pool. Too much memory pressure can trigger unexpected garbage collection and the memory might never return to the application. It will depend on the platform you are running it on. Even in C#, we also experience leaks. Leaks can slow down future allocation, contribute to fragmentation, increase the garbage creation process and eventually cause the red "Out of memory" error. It was a challenge for us. A single C# reference not properly released could generate a cascading effect and create a severe memory leak. For example, texture or animation still referenced by an NPC. To help us we had daily memory usage reports about potential memory leaks. Even if these reports did not cover 100% of the leaks, they are helpful for uncovering things like spawning on loading. We use the memory profiler to understand the root cause of the leak and looked for managed shell objects, but it was time consuming. Most of those issues come from events that are not properly on register or objects not removed from container. A few C# optimization we did... We used pooled or cached buffers when possible. We optimized strings by removing duplicates using a reference dictionary. We had more strings in the project than we expected. We converted all GUID strings into struct with reduced memory, or no longer need referencing. We optimized C# class by packing variables. We use struct instead of class when possible, especially in errors. And we remove useless fields in build sometimes from temporary data from the build process. These are all recipes that still work for us, saving a few megabytes here and there and helping garbage collection. Addressables help us handle streaming. In-Editor, the Addressable Asset System is not visible or used by the team. No one interacts with it. During the build process, everything is created automatically. Asset dependencies are used to dispatch data into different bundles in an optimal way. We have asset bundles that we share between levels, and we have two bundles per level, one for assets and one for scenes. In game, we have a tight budget for loading scenes in the background. We also preload assets that will be required by gameplay like the enemies that will be spawned in the next levels. As we split levels into multiple Unity scenes, we can continue loading faster when needed. Addressables lead us to asset bundles. To save space on the cartridge and help to reduce loading times, we compress the data using the LZ4 compression provided by Unity. Beside the data content and asset bundle, we store a preload table with multiple object entries. In some cases it's big and inefficient if you only need a few entries, because it uses more memory and increases loading time. Part of the memory to the asset bundle is a remapper field available in the memory profiler. We needed to be careful because part of this memory is full or never returned to the system in certain situations. Since we can't afford any spikes, we need to keep this remapper pool stable throughout the game. We frequently adjusted our Addressable rules on how we handle data duplication to address these issues. This strategy saved at least 150 mb from the main memory. To maintain visual quality while balancing memory usage, data optimization is required. Here are a few actions to consider. We selected the best compression format for our assets. Memory versus quality. We created a tool to split levels in an easy way to rebalance memory usage. I'm going to say something obvious, but one of the best memory optimizations is the data we don't load. We spent time to check if every asset was truly necessary. Finally, we added scene layers to ease level loading and lower pressure on memory. Every improvement in memory helped reduce loading times on the Nintendo Switch. Now we had optimized levels with the best possible compromises, we believed we were on the right track. Unfortunately, the reality is more complicated. These days, players no longer turn off the console. The game is running for 10, 20 hours without rebooting. Fragmentation and expanding pools become issues. Here are some approach we use. Reuse an [inaudible].. pools with several adjustments for the customization of allocators. It's available in the memory section in project settings. To save time, you can also look through the boot config file to make changes. You manage to save some memory, but it's crucial to check the game over a full game session. What seems like a great improvement on the first run can turn out to be catastrophic on an entire play. We found that some pools do not release allocated memory despite my CD allocation and no apparent leaks. In some cases, the total size of the pool could cause "Out of memory" errors. We have to identify them, reduce their usage or manage their spike. Since the game has been released, we now have a clear idea of what we would do differently about memory and the lessons that we have learned from our first Unity game. We underestimated the cost of the particle system in [terms of] volume. Even though a single particle system uses little memory, when there are many of them, like 10,000, the costs become perceptible. Unfortunately, by the time we realized this, it was too late to implement a safe pulley system to recycle these particles. Ideally, we should also avoid using strings. Next time, we should have a string ID that leads to a flat data structure. Combine small scriptable assets into larger ones if they are persistent to reduce references on the locations. Use unmanaged memory to replace managed memory for temporary access. And find a better way to detect unresolved memory leaks. Since combined meshes were not shared between levels, we could have a sort bundle per level containing only static batching data. Currently, everything is loaded with the scene. With this approach, we could control when to load and when unload the data to save memory. To summarize this topic, we kept a close eye on memory usage rate production. It's better to fix problems early rather that letting them grow, which can make the game unplayable for the team and lead to poor decisions. We are careful with managed memory leaks and we try to use C# best practices to reduce risk as investigating these issues can take a lot of time. Try to keep a good balance of assets in each level to avoid unexpected memory spikes and inefficient loading. We use tools to identify assets at runtime. We are surprised by the unnecessary attempts of sync and loading. Last, but not least, we monitor the release version to have the big picture on the current number, so we can make the right decisions. A concrete example of this: Once you start playing, there is a difference of 500 mb between the real release and the debug version. Both the program size and allocation patterns are very different. And now, I move over to Benoît for the conclusion. (audience clapping) [BENOÎT] So it's already time to conclude this presentation. Before moving on to the Q&A, we would like to share with you the lessons we learned while making this game. First, production went smoothly despite the game scope and our team was happy with the tools provided, which allowed them to be very productive and meet our schedule. Second, achieving 60 fps on the Nintendo Switch is definitely possible, but it needs to be considered from the very beginning as it requires a lot of optimization work, impacts the way data is created, and almost every technical choice. For the graphic part, being able to customize URP made a great difference and it was easy. Third, memory optimization took a lot of time to implement, but memory usage on this side could increase very quickly at the end of production because we were a large team adding a lot of new content every day, so it's very important to keep a safe margin and to involve everyone. And last, running automatic tests every night really helped us. It was complimentary to the work of our QA team, especially for performance and memory issues as it could detect them very quickly and it provided us an always up-to-date overview of the game state. We would like to thank everyone involved in making this game. It has been an exceptional experience to work with such talented people. So we would like to thank first the "Prince of Persia" team in the Ubisoft Montpellier studio. We'd also like to thank the Ubisoft partner studios in Bucharest, Kyiv, and Pune. And we would like to thank Unity ISS developer support as well as Nintendo for the close support during this production. [Nintendo Switch™ is a registered trademark of Nintendo.]
