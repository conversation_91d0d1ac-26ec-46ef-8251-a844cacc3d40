# Summary of 2023 - Design & Mechanics.md

This file contains notes and ideas related to game design and mechanics throughout 2023. Key points include:

*   **Core Mechanics:** Focus on "call and response" gameplay, where the player reflects enemy attacks. Dodgeball-like shooting, time manipulation (rewind, pause), and lock-on mechanics are central.
*   **Level Design & Structure:**  Rings of Ophanim and the Tower of Babylon are recurring themes, with ideas of levels being structured around rings, planets, or tower segments. Levels can be in any order, like Starfox, affecting the game's "ending."
*   **Enemy Design:** Geometric enemy shapes evolving (square to triangle to line), enemies breaking into smaller units (like musical time signatures), and bosses representing aspects of human nature (pride, ambition, fear).
*   **Progression Systems:** Exploration of intrinsic progression (player skill) and extrinsic progression (in-game goals, level-up systems, cosmetics). Skill trees or musical palette advancement tied to progression.
*   **Inspiration & References:** Drawing inspiration from Rez bosses, Starfox 64 level design (secret paths, variety), Vampire Survivors (short-form design), and Rollerdrome (stage medals, objectives).
*   **Movement & Camera:**  Limited player movement, emphasis on spectacle over free movement, camera shots for snake-like movement, and potential for time to move backward when facing the opposite direction.
*   **UI/UX:**  Consideration of rhythm game UX, control schemes (triggers for actions, buttons for time rewind), and UI for graphics settings.
*   **Technical Design:**  Navmesh implementation for enemy movement, projectile behavior adjustments, and addressing issues like enemies flying off-screen or projectiles not hitting targets.
*   **Themes:** Mechanics driving tone and themes, exploring themes of the Tower of Babel, music of the spheres, and the artistic process as "call and response."
*   **Open Questions & Problems:**  Recurring issues with enemy AI sticking to navmeshes, projectile accuracy, and general movement/code problems. Questions about how to make a game fun with limited movement and how mechanics relate to musical systems.

The notes reveal a game concept focused on rhythm-based shooting, geometric aesthetics, and a blend of arcade-style action with deeper thematic elements inspired by mythology and music theory. The design process involves iterative refinement of core mechanics, level concepts, and enemy behaviors, alongside ongoing technical problem-solving.