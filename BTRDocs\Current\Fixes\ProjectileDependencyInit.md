# Fix: Projectile Dependency Initialization During Shutdown

## Issue

Intermittent errors occurred on scene start: `[ProjectileTrackingManager] Required dependencies not found after retries! ProjectileManager or JobSystem missing`. Analysis revealed:

1. Race condition in dependency initialization
2. Coroutine continued execution during application shutdown
3. Null references when accessing JobSystem during quit

## Solution

Implemented a coordinated shutdown sequence:

1. **Shutdown State Propagation**:

   - Centralized shutdown flag in `ProjectileEvents`
   - `ProjectileEventDispatcher` sets the flag during its `OnShutdown`

2. **Initialization Checks**:
   - Updated `IsEventSystemReady()` to check shutdown state
   - Modified dependent classes to use the updated check

```csharp
// ProjectileEventDispatcher.cs
protected override void OnInitialize()
{
    ProjectileEvents.SetShutdownState(false);
    // ... existing initialization ...
}

protected override void OnShutdown()
{
    ProjectileEvents.SetShutdownState(true);
    // ... cleanup ...
}
```

```csharp
// ProjectileEvents.cs
private static bool isShuttingDown = false;

public static void SetShutdownState(bool shuttingDown)
{
    isShuttingDown = shuttingDown;
}

public static bool IsEventSystemReady()
{
    if (isShuttingDown)
    {
        return false;
    }
    // ... existing dispatcher checks ...
}
```

```csharp
// ProjectileEntity.cs
private IEnumerator RegisterEventsWithRetry()
{
    int attempts = 0;
    while (!ProjectileEvents.IsEventSystemReady() && attempts < MAX_ATTEMPTS)
    {
        attempts++;
        yield return new WaitForSeconds(RETRY_DELAY);
    }
    if (ProjectileEvents.IsEventSystemReady())
    {
        ProjectileEvents.Subscribe<ProjectileCollisionEvent>(OnCollision);
    }
}
```

## Verification

1. Tested scene transitions (no errors)
2. Verified clean shutdown during application quit
3. Confirmed event unsubscription during shutdown
4. Stress-tested with 50+ consecutive scene transitions

## Related Files

- [`ProjectileEventDispatcher.cs`](Assets/_Scripts/Projectiles/Events/ProjectileEventDispatcher.cs)
- [`ProjectileEvents.cs`](Assets/_Scripts/Projectiles/Events/ProjectileEvents.cs)
- [`ProjectileEntity.cs`](Assets/_Scripts/Projectiles/ProjectileEntity.cs)
- [`ProjectilePool.cs`](Assets/_Scripts/Projectiles/ProjectilePool.cs)
- [`ProjectileTrackingManager.cs`](Assets/_Scripts/Projectiles/ProjectileTrackingManager.cs)
