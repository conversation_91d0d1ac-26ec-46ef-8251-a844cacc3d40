using UnityEngine;
using Stylo.Cadance;

namespace BTR
{
    [CreateAssetMenu(fileName = "StaticShooterConfig", menuName = "BTR/Enemy/Static Shooter Configuration")]
    public class StaticShooterConfiguration : ScriptableObject
    {
        [Header("Cadence Settings")]
        [SerializeField, EventID, Tooltip("The Cadence event ID that triggers shooting")]
        private string cadenceEventID = "Enemy Shoot";

        [Header("Projectile Settings")]
        [SerializeField] private float shootSpeed = 25f;
        [SerializeField] private float projectileLifetime = 3f;
        [SerializeField] private float projectileScale = 1f;
        [SerializeField] private float projectileDamage = 10f;
        [SerializeField] private bool enableHoming = false;

        [Header("Visual Settings")]
        [SerializeField] private Color mainColor = Color.white;
        [SerializeField, ColorUsage(true, true)] private Color fresnelColor = new Color(0.69f, 1f, 0.98f, 1f);
        [SerializeField, Range(1, 10)] private float selfIllumination = 1f;
        [SerializeField] private float fresnelIntensity = 4f;
        [SerializeField] private float fresnelPower = 4f;
        [SerializeField, Range(0, 1)] private float fresnelBias = 0f;
        [SerializeField, Range(0, 1)] private float opacity = 1f;

        [Header("Safety Settings")]
        [SerializeField] private float minShotInterval = 0.1f;
        [SerializeField] private bool enableDebugLogs = false;

        [Header("Epoch Integration")]
        [SerializeField] private string epochClockKey = "Global";

        // Public properties
        public string CadenceEventID => cadenceEventID;
        public float ShootSpeed => shootSpeed;
        public float ProjectileLifetime => projectileLifetime;
        public float ProjectileScale => projectileScale;
        public float ProjectileDamage => projectileDamage;
        public bool EnableHoming => enableHoming;
        public Color MainColor => mainColor;
        public Color FresnelColor => fresnelColor;
        public float SelfIllumination => selfIllumination;
        public float FresnelIntensity => fresnelIntensity;
        public float FresnelPower => fresnelPower;
        public float FresnelBias => fresnelBias;
        public float Opacity => opacity;
        public float MinShotInterval => minShotInterval;
        public bool EnableDebugLogs => enableDebugLogs;
        public string EpochClockKey => epochClockKey;

        private void OnValidate()
        {
            // Validate numeric values
            shootSpeed = Mathf.Max(0f, shootSpeed);
            projectileLifetime = Mathf.Max(0f, projectileLifetime);
            projectileScale = Mathf.Max(0.1f, projectileScale);
            projectileDamage = Mathf.Max(0f, projectileDamage);
            minShotInterval = Mathf.Max(0.01f, minShotInterval);

            // Validate visual settings
            selfIllumination = Mathf.Max(1f, selfIllumination);
            fresnelIntensity = Mathf.Max(0f, fresnelIntensity);
            fresnelPower = Mathf.Max(0f, fresnelPower);
            fresnelBias = Mathf.Clamp01(fresnelBias);
            opacity = Mathf.Clamp01(opacity);
        }
    }
}
