# April 14

Ordered Razer haptic headphones used for testing - can send back to amazon!

Working on Error

Bullet trails not in correct direction

[https://www.youtube.com/watch?v=agr-QEsYwD0&t=4s](https://www.youtube.com/watch?v=agr-QEsYwD0&t=4s)

Run through this whole thing for proper implementation?

Did this - things looks better but not perfect - need to reconsider ideas

Particles are on a moving platform, i want it all relative to that space, NOT to the world where it’s speeding by 

Custom space - relative to bullet object

Not working! Hmmmmmmm

Quibli Shaders - Cannot get alpha of outline to work

According to docs, Flat Kit has better Outline options, look into this!

Transparency not working when bullet locked - trying to fix

Can make object transparent but outline will not go transparent

Experimenting with Lofelt Haptics studio - doesnt support my phone

Use it as integrated with Nice Vibrations I think? Does Feel have this whole package?

Error Respawning Enemies

![Untitled](April%2014%201633eaf26d58423e8d180043fcca492a/Untitled.png)

Textures on low poly models

[https://github.com/eldskald/unity-support-textures-generators](https://github.com/eldskald/unity-support-textures-generators)

This reminds me of the tunic talk

Adding grit to low poly

Quibli Shader has an option i think? 

For reference - removed this from TOrus

![Untitled](April%2014%201633eaf26d58423e8d180043fcca492a/Untitled%201.png)

Need to learn what these maps mean 

![Untitled](April%2014%201633eaf26d58423e8d180043fcca492a/Untitled%202.png)

Look at [https://www.youtube.com/watch?v=BD4Mpq8BqQk](https://www.youtube.com/watch?v=BD4Mpq8BqQk)

Quibli Shaders tutorial? Look at tea set examples for ref