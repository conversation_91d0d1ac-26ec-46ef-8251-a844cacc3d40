
---

# Assement - BT

SpaceGraphics - Ripple Well or other wells

![Untitled](Assess%20Assement%20-%20BT%207a0806227fbf48568d8535fbc4488bad/Untitled.png)

Twist well

![Untitled](Assess%20Assement%20-%20BT%207a0806227fbf48568d8535fbc4488bad/Untitled%201.png)

Styles

![Untitled](Assess%20Assement%20-%20BT%207a0806227fbf48568d8535fbc4488bad/Untitled%202.png)

Galaxy Materials for Ouroboros area

[Galaxy Materials (Skybox Update)](https://assetstore.unity.com/packages/vfx/shaders/galaxy-materials-skybox-update-191773)

---

## From: August 11 f0f30aea52f24bef9fed76b88e7f2348.md

# August 11

Design is constraint. What can you do that’s interesting within constraints?

How can I expand creativity?

---

## From: August 15 75ce4d291494477d9be14a92d2095fa4.md

# August 15

Reviewing debugging approaches - Breakpoints

Seem useful! More context than debug statement

[3 Essential Unity3D Debugging Techniques](https://www.youtube.com/watch?v=bdOoQ_Xdxz8)

---

## From: Big List of Cursor Suggestions d497fcc7d478486091f8876625d3614e.md

# Big List of Cursor Suggestions

1. Use LayerMasks for Physics Queries: If you're doing a lot of physics queries (like Raycast or BoxCast), consider using LayerMasks to only check against certain layers. This can improve performance by avoiding unnecessary checks.
2. Use Coroutines for Delayed or Time-Based Code: If you have code that needs to run after a delay or over time, consider using coroutines. They are more efficient and easier to manage than using Update or FixedUpdate for this purpose.
3. Use Vector3.Distance for Distance Checks: Instead of manually calculating the distance between two points, use Vector3.Distance. It's more readable and less error-prone.
4. Avoid Using GetComponent with a String Parameter: This method uses reflection and is slower than the generic version of GetComponent. Use GetComponent<T>() instead.
5. Avoid Using Debug.Log in Update or FixedUpdate: Debug.Log is slow and can cause performance issues if used frequently. Use it sparingly, or consider using a custom logging system that can be enabled or disabled easily.
6. Use Proper Encapsulation: Avoid making all fields public or serialized. Only expose what's necessary and use properties or methods to control access to the fields.
7. Use foreach Instead of for When Possible: foreach is more readable and less error-prone than for when iterating over collections.
8. Use null-conditional Operators to Avoid NullReferenceExceptions: Use ?. and ?[] to safely access members and elements.
9. Use Early Return to Reduce Nesting: If you have a condition that, when met, should stop the execution of a method, use return to exit early. This can reduce nesting and make the code more readable.
10. Use Proper Error Handling: Don't ignore exceptions or errors. Handle them properly to make your code more robust and easier to debug.
11. Keep Classes Small and Focused: Each class should have a single responsibility. If a class is doing too many things, consider breaking it up into smaller classes.
12. Use Interfaces to Define Behavior: If you have multiple classes that share common behavior, consider using interfaces. This can make your code more flexible and easier to test.
13. Use Proper Unity Lifecycle Methods: Use Awake for initialization of references, Start for initialization that depends on other objects, and Update or FixedUpdate for things that need to happen every frame.
14. Avoid Using Invoke or InvokeRepeating: These methods use reflection and are slower than other methods of scheduling code. Consider using coroutines or Update instead.
15. Use TryGetComponent Instead of GetComponent: TryGetComponent is more efficient and safer than GetComponent. It doesn't throw an exception if the component doesn't exist, and it doesn't allocate memory if the component isn't found.
16. Avoid Using SendMessage or BroadcastMessage: These methods use reflection and are slower than direct method calls or using events.
17. Use CompareTag Instead of == for Tag Comparisons: CompareTag is more efficient than == for comparing tags.
18. Avoid Using Instantiate and Destroy in Update or FixedUpdate: These methods are slow and can cause performance issues if used frequently. Consider using an object pooling system instead.
19. Avoid Using transform in Update or FixedUpdate: Accessing the transform property is slower than accessing a cached reference to the Transform component. Cache the reference in Awake or Start and use the cached reference instead.
20. Avoid Using gameObject in Update or FixedUpdate: Accessing the gameObject property is slower than accessing a cached reference to the GameObject. Cache the reference in Awake or Start and use the cached reference instead.
21. Avoid Using Camera.main in Update or FixedUpdate: Accessing Camera.main is slow because it uses FindGameObjectWithTag under the hood. Cache a reference to the main camera in Awake or Start and use the cached reference instead.
22. Avoid Using Input in Update or FixedUpdate: Accessing Input is slower than using the new Unity Input System. Consider migrating to the new Input System for better performance and flexibility.
23. Avoid Using PlayerPrefs for Frequent Read/Write Operations: PlayerPrefs is slow and can cause performance issues if used frequently. It's better to use it for storing persistent data that doesn't change often, like settings or high scores.
24. Avoid Using Resources.Load or Resources.UnloadUnusedAssets in Update or FixedUpdate: These methods are slow and can cause performance issues if used frequently. It's better to manage your assets in a way that you don't need to load or unload them at runtime.
25. Avoid Using Time.timeScale for Slow Motion Effects: Changing Time.timeScale affects all time-based operations, including animations and physics. It's better to implement slow motion effects in a way that doesn't affect the global time scale.
26. Avoid Using Physics.IgnoreCollision or Physics.IgnoreLayerCollision

---

## From: Boomerang X Abilities List cb6d59f175a640a8ba25d0d594b6fcb6.md

# Boomerang X Abilities List

1. **Slowing Down Time**: As you aim, you gain the ability to slow down time, enhancing precision and control.
2. **Launch Towards Boomerang**: An ability to launch yourself towards your boomerang as it glides through the air, effectively using the boomerang as a makeshift teleport.
3. **Continuous Airborne Movement**: You can use these abilities to stay airborne almost indefinitely, flying through the air using the boomerang as both a weapon and a means of movement.
4. **Chain Attacks for Additional Abilities**: If you manage to take out two enemies in one throw, you gain an ability to blast out multiple boomerangs at once, similar to a short-range shotgun. Taking out multiple enemies with this ability allows you to fire a precise needle shot that can penetrate multiple enemies in a row. Another ability lets you slam into the ground with force after killing three enemies in the air, wiping out several ground-based opponents.
5. **Teleportation with Boomerang**: You can throw your boomerang and then teleport to its location with a button tap. This ability adds a unique movement style to the game, allowing you to zip across arenas without touching the ground.
6. **Shotgun Blast and Laser Beam**: Later in the game, you unlock two new projectile abilities - a shotgun blast and an instant-kill laser beam. Killing two enemies at once grants you the shotgun blast, and killing three enemies at once with the shotgun blast then allows you to use the laser beam. This risk & reward system challenges you to decide between quick single kills or lining up multi-kills for advanced projectiles.
7. **Flux**: An ability to slow down time, which when combined with the Slingshot power, offers a "bullet-time hyper-precision" flow state, greatly enhancing gameplay precision and experience.
8. **Alternate Fires**: The game includes alternate firing modes for the boomerang, adding a broader range of combat options and strategies.

---

## From: CHatGPT Prompts 11ccd4c7f00a4577b8e8ba677e630981.md

# CHatGPT Prompts

tower of babylon that is an allegory for how music works

> 
> 
> 
> Once upon a time, there was a great city called Babylon, and at the center of the city stood a towering structure known as the Tower of Babylon. The tower was so tall that it seemed to touch the sky, and people came from far and wide to marvel at its beauty.
> 
> The Tower of Babylon was built by the people of Babylon, who were all musicians. They loved music more than anything else in the world and had dedicated their lives to creating beautiful melodies and harmonies.
> 
> The tower was built to represent the power and beauty of music, and each level of the tower was dedicated to a different aspect of music. The first level was dedicated to rhythm, the second to melody, the third to harmony, and so on.
> 
> The people of Babylon believed that by building the tower, they could reach the heavens and create the most beautiful music that had ever been heard. They worked tirelessly, day and night, to build the tower higher and higher.
> 
> But as the tower grew taller, something strange began to happen. The people of Babylon realized that they could no longer understand each other's music. Each level of the tower had its own unique rhythm, melody, and harmony, and the music played on one level sounded completely foreign to the people on the other levels.
> 
> The people of Babylon tried to communicate with each other, but they found that their music had become so different that they could no longer understand each other. They had built the tower to create the most beautiful music in the world, but instead, they had created a tower of babel, a tower of confusion.
> 
> As the tower grew taller and taller, the people of Babylon became more and more isolated from each other. They could no longer work together or create beautiful music together, and the tower became a symbol of their failure.
> 
> But despite their failure, the people of Babylon continued to love music. They continued to create beautiful melodies and harmonies, but they did so in small groups, where they could understand each other and work together to create something truly beautiful.
> 
> And so, the tower of Babylon remained as a reminder of how music works. Just as the tower had become a symbol of confusion and isolation, music can also bring people together and create something truly beautiful, but only if we work together and communicate effectively.
>

---

## From: February 14 59e0f8fb56844d94bc6fc295e968d7fd.md

# February 14

Compilation of notes over last couple weeks below

Go over notes again - What approach do I want? What do I want in a demo?

Break down design of Hi Fi Rush - how can I have multiple moves? Is this achievable? 

Assets that need VFX

- Bullet being shot

Float down - laser - floating animation - level loading and 

START

moving forward through barren landscape

come upon city

move forward through city

come upon data center / guardian / ophanim

move along each ring  - several layers

zap into center!

different gameplay mode - not moving forward as much - things coming at you from all sides

Walls closing in? attack with bullets?

Puzzle element here? 

ESCAPE TIME

moving through city faster???

Look at destruction in blender for rings

look at blender tool for making robotic looking rings and such

Sayonara Wild Hearts - think about different modes provided here - do gameplay analysis

Use Steam Asset Ripper on this game - figure out if it works

Jan 30th

Ophanim rings

Test this as on rails shooter - how will enemy waves work? 

What is the variation in gameplay?

Combat types? 

Can do square and triangle rings as well?

Talk to Eileen !!!

Spinning issues

![Untitled](February%2014%2059e0f8fb56844d94bc6fc295e968d7fd/Untitled.png)

Cause current wheel dolly to spin forward? and only rotate all other wheels when not in use? 

Can keep player in exact same position - level moves around them

Then I don’t have to deal with Camera flipping issues?  

Am I delaying issues that might come up in the future anwyay? 

Is there a way to have everything rotate and the play can exist on it without issue

SCRIPTS - Rotate Around, other rotate basics

Positive game loop - dont punish for being off beat, but reward for being on beat 

Do I need moving A* for the ophanim rings? 

Looks like some events are not syncing up due to moving elements around!!!

If enemies not moving, try snapping A* scan to scene bounds. Likely scanning in the wrong area!

When player is located on a ring

- Ring only rotates for fake forward motion
- Other rings rotate in many directions

Using Dreamtek ‘Rotate’ script. Will have to edit this for more features eventual!!

Successfully moving between dolly points now - but need to control dolly’s during transitions. Working on this!

Remember - if something exists in a seperate namespace is may be inaccessible! Check for these things

Test out transition between rings when i’ve returned! Add more dollys, look at spinning in scene view 

Alternative gameplay modes for center of Ophanim

- Static in center - surrounded by walls to shoot things

Rez bosses inspo

- moving through hallways of enemy

Working on functional menu system with controller

may need to update shooting script/player movement to disable input processing when paused!!!!

No matter how I set it up…. UP on the UI seems to trigger pause menu on and off, just like escape

Why?? Unsure

Switch back to D Pad and see what happens with more troubleshooting   

Fixed this! Weird listings in Rewired due to prefab vs in scene version of the rewired manager

Need to keep an eye on this issue

Compiling shader varaints → An approach to solving this issue!

here is a fix to this. Go to Edit->Project Settings->Graphics. Go to the very bottom. It will state the number of shaders and shader variants. Make sure that seems about right to you. There is a button called 'Save to asset...'. Use that to save a compiled shader variants file. In the graphics tab you're still in, drag that saved file from your Project window into the 'Preloaded Shaders' list. That will decrease the built time dramatically.

Washed out colors in Unity builds, but not in editor play view. Attempting to fix this

Changing from Gamma to Linear color space - may fix this 

Anti-Aliasing part of the issue? Trying FXAA and Linear color space

Disabled Optimize mesh data to speed up build times

[UNITY SHADER VARIANTS - TIPS to Speed Up Your Build Time!](https://www.youtube.com/watch?v=3i2V8Q7SsOM)

Height fog global is possibly causing the coloring issues

[Atmospheric Height Fog | Optimized Fog for Consoles, Mobile and VR | Fullscreen & Camera Effects | Unity Asset Store](https://assetstore.unity.com/packages/vfx/shaders/fullscreen-camera-effects/atmospheric-height-fog-optimized-fog-for-consoles-mobile-and-vr-143825)

Look into settings or find another fog solution

Trigger FLoating animation when transition step is happening!

- Need to reset player viewpoint and animation once transition is over
- Need to fix all menu controller issuesson

Object Spawner issues seem to be tanking things once level moves along - figure out what the issue is here

```jsx
AssertionException: Assertion failure. Value was False
Expected: True
UnityEngine.Assertions.Assert.Fail (System.String message, System.String userMessage) (at <1332c534a8a44623b2e5cc943a0b790e>:0)
UnityEngine.Assertions.Assert.IsTrue (System.Boolean condition, System.String message) (at <1332c534a8a44623b2e5cc943a0b790e>:0)
UnityEngine.Assertions.Assert.IsTrue (System.Boolean condition) (at <1332c534a8a44623b2e5cc943a0b790e>:0)
HohStudios.Tools.ObjectParticleSpawner.ObjectParticleSpawner.LateUpdate () (at Assets/HoH Studios/Packages/Tools/Runtime/Object Particle Spawner/Spawner/ObjectParticleSpawner.cs:303)
```

Matrix Notes - • [Seraph](https://en.wikipedia.org/wiki/List_of_Matrix_series_characters#Seraph) is a supporting character in the second and third films of The Matrix Trilogy. Seraph is an exile program who is seen acting as a "guardian angel" of the Oracle, and is described as the personification of a sophisticated challenge-handshake authentication protocol which guards the Oracle.

The Bible later makes mention of a third type of angel found in the merkabah called *"[seraphim](https://en.wikipedia.org/wiki/Seraph)"*
 (lit. "burning") angels. These angels appear like flashes of fire continuously ascending and descending. These *seraphim* angels power the movement of the chariot. In the hierarchy of these angels, *hayyoth* are the highest, that is, closest to God, followed by the *ophanim*, which are followed by the *seraphim* The chariot is in a constant state of motion, and the energy behind this movement runs according to this hierarchy. The movement of the *ophanim* is controlled by the "Living creatures", or *Hayyot*, while the movement of the *hayyot* is controlled by the *seraphim*. The movement of all the angels of the chariot is controlled by the "Likeness of a Man" on the Throne.

[Angels in Judaism - Wikipedia](https://en.wikipedia.org/wiki/Angels_in_Judaism#Angelic_hierarchy)

Shadow of the colossus story style…show up in a land. 

How does it all relate to music? 

World without sound?

rumbling….

Solar Ash

- voidrunner jumps into a black hole

Shadow of the colossus

- world establishing shot following a bird - nighttime
    - showcases the vast land
    - shows nature - a living world
- we see a man on horse, on mountain, headed somewhere - has something with him
- moves through forest

IDEA - start with enticing scope of world shot - the 4 ophanim / bosses - looks huge!

Zoom down in scale towards our protagonist - start level

Enemy Shooting Particles appears to have the error 

DEBUG of OPS error

OPS line 303

```jsx
// Error catching assertions.. not included in build
            if (_numberOfAliveParticles < _aliveParticles.Length)
                UnityEngine.Assertions.Assert.IsTrue(_aliveParticles[_numberOfAliveParticles].remainingLifetime < 0.001f);
            if (_numberOfAliveObjects < _aliveObjects.Length)
                UnityEngine.Assertions.Assert.IsTrue(_aliveObjects[_numberOfAliveObjects] == null || !_aliveObjects[_numberOfAliveObjects].IsInitialized);
        }
```

OPS line 158

```jsx
// Error catching assertions.. not included in build
            if (_numberOfAliveObjects < _aliveObjects.Length)
                UnityEngine.Assertions.Assert.IsTrue(_aliveObjects[_numberOfAliveObjects] == null || !_aliveObjects[_numberOfAliveObjects].IsInitialized);
```

Assertion Failure! This are showing up false 

Issues with the bullet pool 

NOTE - Seems to happen when ENEMIES are what needs to be reused, not bullets ???

Verify this finding. Still issues with bullet pool i think but…. needs more testing

Trying with enemy preload amount of 40 instead of 20

Appears to be true! Made it to Wave 7 until error occured

As soon as 1st two enemies are reused the Assertion failure occurs. Pooling is not working correctly for enemies. 

Doesn’t seem to be a big deal for bullets - Though may be worth cleaning out inactive game objects? Also how might I combine all enemies pools to work together? Doesn’t seem to do functioning 

Need to change redirection of player to front so that it’s END OF TRANSITION not START OF WAVE

Do I need to use ALINE ? Am I using it? Potential performance gaing from getting rid of it

Moving from mono ot il2cpp back end made significant differnece in frame rate!!! stick with this

IL2CPP takes a long time to build but works twice as fast in the build! Even more! Some issues though

Aspect ratio wrong - need to set this? Add resolution as well?

Using Asset Hunter to slim down project and improve build times

Enemies evolve as iterations on a theme

Geometric

Move through rocky landscape

approach ophanim / city

More variation among sections??

What about the center?

Glitched out area? 

Ophanim rings… shoot or collect something along the interior? 

Need to use enemy bullets to do this!

Get to center - circle around it

DO SOMETHING

Circle / Square / Triangle 

Add a FOV slider 

Look into Volumetric Lights and Fog using Shader Graph!

[https://www.youtube.com/watch?v=rihJzWq7sE4](https://www.youtube.com/watch?v=rihJzWq7sE4)

Dynamic Portals Asset?

[Dynamic Portals](https://assetstore.unity.com/packages/tools/physics/dynamic-portals-235818#reviews)

Beautify not working with QUibli scene - Looking at rendering pipeline for QUibli again - may be setup wrong!

Looking at URP overview - SRP information
Reflection Probe Basics
[https://www.youtube.com/watch?v=NIAG2QsLqsA](https://www.youtube.com/watch?v=NIAG2QsLqsA)

Chroma - Pro Shaders Gradient! 

Bought this - use it! Test it out!

Playing with VFX graph effects and Chrome today!

Chrome looks great but need to find right instances for it

Likely characters and enemies and the like

Error with Enemy Spawn pool - this may be the pooling issue i think is occuring to crash the game?

```jsx
SpawnPool SpawnPool1 (Enemy Diamond): You turned ON Culling and entered a 'Cull Above' threshold greater than the 'Preload Amount'! This will cause the culling feature to trigger immediatly, which is wrong conceptually. Only use culling for extreme situations. See the docs.
UnityEngine.Debug:LogWarning (object)
PathologicalGames.PrefabPool:PreloadInstances () (at Assets/Plugins/PathologicalGames/PoolManager/SpawnPool.cs:1823)
PathologicalGames.SpawnPool:CreatePrefabPool (PathologicalGames.PrefabPool) (at Assets/Plugins/PathologicalGames/PoolManager/SpawnPool.cs:374)
PathologicalGames.SpawnPool:Awake () (at Assets/Plugins/PathologicalGames/PoolManager/SpawnPool.cs:196)
```

Fixed this - no improvement

Brought project into another instance - less fat!

Big thing to fix - enemy aiming! Collecting bullets is ok but enemy aiming is quite bad 

What's the problem?

SPent a couple hours trying to get a good fog going with Fog Volume 3

Maybe less time

Not going to work, seems it interferes with render layers???

Trying Aura 2

Doesn’t Work

Installed Buto - seems to work a bit. Some Errors, need to investigate proper use in demo scene

Read up on documetnation 

Feb 17th

enemies are -20 lowere and not seeing graph properly - dont move graph, seems to be a good place?

Enemies slowly move up. Freezing Position in Y on rigid body to counter this. Need to find out the actual error?

Enemies wont shoot. Bringing in music and files from Resources folder to see if this will fix it!!!

RezV6 backed up on NVME - D, trying to scale down Rez V6 and them might reduce assets

Fix Rotate RIng issues!!!
May need to setup this script all over again

Adjust Radar to match new scale

Can I switch basck to Visual Studio 2022??

---

## From: February 19th bfdc80c324114c19b31dc1378c46a9b2.md

# February 19th

colour palette - develop one!
shadows never black - blue?
look at color theory
adobe colour

know what not to polish!

- feature creep

every step of the way, ask 'why is the character doing that' and revise - this is how to write bullet proof script

Point lights for color chaging??

Depth Testing in this shader - do this effect?

[https://www.youtube.com/watch?v=nKMTU6TbUF0](https://www.youtube.com/watch?v=nKMTU6TbUF0)

Adjust radius in which bullets lose targetting> May be an issues

Fixed sizing on light trails of diamond enemies

How to remove unnecessary assets? Export approach caused many issues

Delete most scenes, than use something that finds uneeded assets

[Rez Analysis](February%2019th%20bfdc80c324114c19b31dc1378c46a9b2/Rez%20Analysis%20a9b6367f4da44d0aaeb196d14ce0e7f3.md)

Bullet issues not always firing at player? May be enemy rotation issues

No real need to use A* movement path finding on Ophanim level - change to standard? But things are moving around me, so may still be necessary

SHould I have enemy shooting Particles always face the player? 

Doesnt effect things - need to try earlier version and see the difference - must have changed some value that is important!

Geometric enemies with points reduced as we move forward?

Square to triangle to line etc

---

## From: February 22 6ea902ce56a14171b898aba7d4c765d0.md

# February 22

Thinking about structure of game

Music as a representation of systems

Geometry - Enemy Shapes

![Untitled](February%2022%206ea902ce56a14171b898aba7d4c765d0/Untitled.png)

Combine these + behaviour in different ways

Recursion - Serpinski Triangle!

Recursion in Music - [https://www.youtube.com/watch?v=4ydZrKcvejU](https://www.youtube.com/watch?v=4ydZrKcvejU)

Use imagery such a triptych?

Geometry of music?

![Untitled](February%2022%206ea902ce56a14171b898aba7d4c765d0/Untitled%201.png)

Like notes of a chord or overlapping rhythms

A system 

Eileen - “different enemies could be variations on a theme (musically and visually) that maybe represent parts of the player character’s journey through life (and death?)”

[Why Is It So Hard To Make Games On Rails?](https://www.youtube.com/watch?v=mUjZUPPrz-o)

Attention Economy

- No movement - what replaces this?

Spectacle can replace free movement

- visually and new patterns emerging constantly

Pacing is key!

What to learn from Starfox 64?

- secret paths and goals to discover
- dodge mechanic?
- variety of paths to reach end
- combo and high score options
- power ups
- stage medals - acheive specific things like rollerdrome?

Collect coins or powerups or something along the way?

Is there any way to controll HOW many bullets you shoot at a time?

Types of Progression

Intrinsic progression

- bring your own ability to the game
- tough for beginners
- Neon white brings speed running to beginners?

Extrinsic Progression

- set goals within the game - obvious ones!
- Level up system is an example
- cosmetic items are an example

Be careful - don’t want things to feel like a treadmill!

Use skill trees or advancement to increase musical palette? 

Bullet types for different enemies? Ikaruga method?

Souls like method for score - retrieve your score when you die and retry level?

- invincibility when you find your corpse?

motifs 

- elemental systems

what system can i use the re inforces game mechanics? what would help define enemies better? 

How do my elements play off each other?

light / dark - holy / death 

Rock paper sciccors loop - would this be good?

- Light / Dark / Digital

Enemy - # of points is # of bullets to kill

Combined designs are the addition of these things

This also defines their shooting pattern

but NOT movement pattern? 

Keep on this - look at how systems could work / how they integrate with ideas

rotation / solar system models

![Untitled](February%2022%206ea902ce56a14171b898aba7d4c765d0/Untitled%202.png)

![Untitled](February%2022%206ea902ce56a14171b898aba7d4c765d0/Untitled%203.png)

![Untitled](February%2022%206ea902ce56a14171b898aba7d4c765d0/Untitled%204.png)

Tower of Hanoi

**What are the points in more jounrey that are interesting that I should follow?**

**That I never would have written?** 

Look into Object Particle Spawner and shoot groups of bullets using this

Shmup Patterns

[Shmup Principles](February%2022%206ea902ce56a14171b898aba7d4c765d0/Shmup%20Principles%20f8fd6182542e41b2aeb09314fe226b11.md)

AM thinking very mechanically - functionally

Is this a bad thing?

Watch Level Design video

Music and visuals as language

Music + Geometry 

Enemies are mixes of basic geometry, and produce different projectiles based on this 

Pattern Recognition

“an attempt to explain the existence of [diverse](https://www.merriam-webster.com/dictionary/diverse) human languages”

- music as language

Tower as pure sound

Crashes down - divided everywhere

According to Genesis, the Babylonians wanted to make a name for themselves by building a mighty city and a [tower](https://www.britannica.com/technology/tower) “with its top in the heavens.” God disrupted the work by so confusing the [language](https://www.britannica.com/topic/language)
 of the workers that they could no longer understand one another. The city was never completed, and the people were dispersed over the face of the earth.

Tower of Babel 

Who are the Ophanim in this story?

They are the base of the throne of god - or move it around 

Tower of Babylon - what does it mean? 

[https://en.wikipedia.org/wiki/Geocentric_model](https://en.wikipedia.org/wiki/Geocentric_model)

[https://en.wikipedia.org/wiki/Celestial_spheres](https://en.wikipedia.org/wiki/Celestial_spheres)

> The Tower of the biblical myth is perhaps the grandest of these cultural reminders. This is clearly how Chiang interprets it. The workers, assembled from many foreign places, share not just a language but a devotion to YHWH. The tower itself is being built to honour him. It is the physical representation of the spiritual search for YHWH that is implicit in its changing content. Humanity is attempting to reach YHWH not out of arrogance but as a matter of devotion.
> 

> Of course, human understanding cannot reach to the exalted heights of YHWH’s existence. But only when their search stops is the covenant in jeopardy. In their continuing physical and intellectual quest for the ultimate reality human beings can put themselves in awkward and life-threatening situations. No matter what happens, however, YHWH can be relied upon not to break his covenant promise - never again will he use water against them, nor let them use it against themselves.
> 

Is this recursive? Is the journey - not the destination - something we can consider recursively? 

Like the idea of making the perfect music - as if there was some perfect sound that would feel like touching god?

building up the tour, it falls, levels are remixed and shapes are remixed

enemies are remixed

---

## From: Game Design Tools; Cognitive, Psychological, and P ********************************.md

# Game Design Tools; Cognitive, Psychological, and Practical Approaches Exercises

Here are some exercises, tools, and strategies that you can apply to improve the game you're currently making:

1. **Documentation**: Keep a detailed record of your game design process. This can help you track your progress, identify areas of improvement, and serve as a reference for future projects.
2. **Production**: Understand the production process and how to manage it effectively. This includes planning, scheduling, and coordinating different aspects of game development.
3. **Evaluation**: Regularly evaluate your game design. This can involve playtesting, feedback collection, and analysis to understand what's working and what's not.
4. **Analysis and Marketing Tools**: Use analysis tools to understand player behavior and preferences. Marketing tools can help you understand your target audience and how to reach them effectively.
5. **Task Guide**: The book suggests completing tasks at the end of each design technique to consolidate your knowledge. Practicing these tasks can help you improve your game design skills.
6. **Iterative Process**: If something goes wrong in your game design, use the iterative process to understand what went wrong and why, in order to provide a better experience the next time.
7. **Manipulation**: Manipulate your players to provide them with the best possible experience. This can involve using design tools to direct users into the best possible experience.
8. **Affordance**: Understand the concept of affordance, which is the relationship between the properties of an object and the capabilities of the agent that determine just how the object could possibly be used.
9. **Respecting Players' Time**: As game designers, we are the custodians of the players' time. Every game makes a promise on an emotion, a tone, or an experience in return for borrowing time from our players' lives. As designers, it is our job to deliver on those promises, ensuring that the transaction of a player's time for our experience is fulfilled, fair, and treated with respect.
10. **Experimentation**: Experiment with different elements of your game design. This can lead to a positive dynamic where you accomplish an action or a negative one where you fail to perform the action. Experimentation can help you understand what works and what doesn't in your game design.
11. **Understanding Game Rules**: Understand the rules of your game and how they affect the player's actions. For example, if a rule says “you can’t drive cars in this part of the map”, players won't be able to drive cars in that part of the map. This understanding can help you design better gameplay experiences.
12. **Design Techniques**: The document suggests various design techniques that can be used to define the initial concept of the game or solve specific problems. These techniques are based on psychological manipulation and should be used wisely.
13. **Dynamic Difficulty Adjustment (DDA)**: This tool can be used when the player is struggling in a certain gameplay segment. Designers can make the game easier, for example by lowering NPCs’ difficulty without telling the player, adjusting the challenge to their skill level.
14. **Forgiveness Mechanics**: These mechanics can make it look like the player succeeded, even if they would have lost. For example, if 20 bullets are going to hit the player, but they can only take 19 before dying, one of those bullets, let's say, missed the player.
15. **Guidance Techniques**: These techniques can guide players into following a path or performing a certain action. For example, if there is an item on a desk that only half the players notice, in the next version of the game, the lamp on the desk that casually points toward the object flickers a bit when players get close.
16. M**Random Number Generators (RNG)**: RNG can be used to overcome a challenge that players didn't have a high enough skill for. For example, while playing any of the Souls series, a player might grind until they are statistically stronger, rather than getting better at the game.
17. **Grading Systems**: Designers can communicate to the players their results with a grade. Even the worst grade can sound cool, motivating players to improve.
18. **Progression Systems**: Players can get a promotion, like a badge or a new title, but nothing really changes in the game. If players can't tell the difference between real progress and a fake, awarding them with a title will make them feel better about their skills.
19. **COM-B**: The COM-B is a design tool of behavioural game design, and it divides the process of performing an action into three categories: capability, opportunity, and motivation. Each one has a checklist of questions you need to answer to find the users’ perception of the action and identify possible flaws.

Remember, these are tools and exercises to help you improve your game design skills. How you apply them will depend on the specific needs and context of your game.

---

## From: Game Design Tools; Cognitive, Psychological, and P 94fd60e9282d48c2a73941138709fbb1.md

# Game Design Tools; Cognitive, Psychological, and Practical Approaches Summary

Book summary of Diego

Here is a summary of advice for game designers from the book:

1. **Democratization of Tools**: With the growth of the gaming industry, there has been an increase in access to learning content and tools. Anyone with an idea and a computer can make a game, leading to an explosion of content. Small teams and even solo developers can build new experiences and bring them to market.
2. **Iterative Process**: Game design is not a one-way process. It involves a series of iterative processes and countless decisions throughout development to bring visions to life and create experiences for players.
3. **Engaging Players**: The designer's job is to engage players right from the moment they launch the game and to keep them engaged in the long term. This involves understanding cognitive load, user experience, and the overall player journey.
4. **Design as Manipulation**: Design, in essence, is a form of manipulation. It involves using design tools to direct users into the best possible experience. This can range from adding a visual effect to show an interactable item, to creating a whole system to make the players live everything the game has to offer.
5. **Affordance**: Understanding affordance, the relationship between the properties of an object and the capabilities of the agent that determine how the object could possibly be used, is crucial. Good design makes it easy for the player to understand how things work.
6. **Clear Goals**: It's important to have a clear goal in mind. Remember why you first wanted to be a game designer and use that as your guiding principle.
7. **Communication**: When describing your game, use specific design language. However, when communicating with players, press, or people who aren't designers, feel free to use more general language that they can understand.
8. **Understanding Feedback**: When receiving feedback from players, use your design tools to understand what they meant. It's like decrypting a code, and with experience, you'll be able to do it quickly in most cases.

Continuing from the previous summary, here are more insights on game design principles from the book:

1. **Experimentation and Iteration**: Game design involves a lot of trial and error. You have an idea, develop a concept, make a prototype, see where people don't perform as expected, and then fix it. No game is born perfect; they just get tested and fixed enough through an iterative process.
2. **Unique Experiences and Emotions**: Game design allows each player to live unique experiences and feel uncommon or rare emotions that other media might struggle to arouse. This is achieved by controlling the player's actions and reactions within the game.
3. **Player Manipulation**: Games often manipulate players to enhance their experience. For example, Gears of Wars gave a bonus to every player every 24 hours, allowing them to feel confident and keep playing. However, players weren't told about this power, as the target of manipulation needs to be unaware of it.
4. **Respect for Players' Time**: As game designers, we are the custodians of the players' time. Every game makes a promise on an emotion, a tone, or an experience in return for borrowing time from our players' lives. It's our job to deliver on those promises, ensuring that the transaction of a player's time for our experience is fulfilled, fair, and treated with respect.
5. **Shared Vocabulary**: Having a shared vocabulary across a spectrum of topics that occur throughout any project from ideation and brainstorming techniques through to understanding player motivations and building mechanics is crucial. This provides a strong foundation for both newcomers and experienced developers.

Continuing from the previous summary, here are more insights on game design strategies from the book:

1. **Creative Freedom**: It's important for game designers to have creative freedom. This might mean needing some extra space to work on a project closer to yourself. You can create your indie studio and develop your own game, but be aware that for every successful company of this kind, there are ten that fail.
2. **Understanding Player Behavior**: Understanding and predicting player behavior is crucial in game design. For example, in League Of Legends, when allies are low, they have an icon standing above their head. This icon instantly shows the user playing Soraka which teammate needs her help, allowing a quicker response time by said user, meaning a better team collaboration and a higher chance of winning the game.
3. **Design Tools**: The book provides a series of transdisciplinary tools to help game designers improve their design pipeline and design output. It covers archetypes, praxeology, behavioral game design, and emotional game design. It also includes chapters on documentation, production, evaluation, analysis, and marketing tools.
4. **Creating Memories**: As designers, always remember we should allow people to live their wildest fantasies, unite with friends, create memories, and bring them back to when they were kids. We are almost their parents when they play.

That's all the information I could extract from the document. I hope this summary provides a comprehensive overview of the advice for game designers from the book.

---

Here is a summary of advice for game designers from the book:

1. **Clear Roadmap**: Keep your project roadmap as simple and short as possible. Rather than focusing on perfect values, talk about what you will accomplish in what time. Be transparent about where the resources will be going to make the project more understandable.
2. **Pitching**: When pitching your game to a publisher, highlight the strengths of your team and your past successes. Be clear about what you need from the publisher and be honest about the game and its timeline. Remember, pitching is about making your idea feel perfect, even if it's for just a second.
3. **Flexibility in Design**: Game design is about finding the best possible compromise among a hundred different things—time, money, vision, players’ satisfaction, etc. A good designer is someone who gets you to the closest possible place to the destination on time, on budget, with everyone still alive—and hopefully happy enough to get ready for the next adventure.
4. **Key Elements**: Define the key elements or pillars of your game. Every single element you add during the development needs to support one of these pillars. If it doesn’t, that element is just a nice to have.
5. **Creativity**: Creativity can be trained and improved. Expand your creativity by experiencing new things, seeing new places, reading new books, living as a different culture, doing things you are too scared to do, and asking yourself the 9 Whys for everything you don’t understand.
6. **Inclusivity in Creativity**: A good designer is someone who sees the creative value in everything—and everyone—around them. A good designer listens to all the ideas around them and understands which can work and which just won’t. Listen to anyone, but don’t say yes to everyone.
7. **Choosing the Best Idea**: Once you gather tons of possibilities, the design team needs to choose the best one for the game, no matter if it comes from the “creative team” or not. Like parents with our babies, we need to do what’s best for them, putting our ego aside.

Continuing from the previous summary, here are more pieces of advice for game designers from the book:

1. **Practice Pitching**: If you need to practice before your first pitch, a simple but effective beginner’s technique is pitching to yourself in the mirror. This allows you to see your level of confidence, how you move, and even if you turn red after talking too much without taking a breath.
2. **High Vision Document**: A high vision document works great because it gives enough guidelines to know what you can and can’t do but not enough to make you feel like you are just following a checklist. It should include the title of the game, a concept brief, key elements or pillars, and primes or objectives.
3. **Combining Objects Creatively**: Combining objects without criteria is more like gambling than creating. To know which objects you should put together you need analysis and data. With the right research, you know in advance which of the thousands of possible combinations will not work and which will be valid.
4. **Improving Creativity**: Creativity can be trained and improved. Go see new places, read new books, live as a different culture, do things you are too scared to do, ask yourself the 9 Whys for everything you don’t understand. Each one of these activities will expand your creativity because it will change—even if just a bit—the way you see the world.
5. **Generating Ideas**: Another way to improve your creativity is by forcing yourself to come up with ideas. Take a problem, give yourself one hour, and come up with 50 possible solutions for the said problem. Over time, you will have more ideas and, most importantly, more valid ones.
6. **Everyone Can Be Creative**: A good designer is someone who sees the creative value in everything—and everyone—around them. A good designer listens to all the ideas around them and understands which can work and which just won’t. Listen to anyone, but don’t say yes to everyone. Once you gather tons of possibilities, the design team needs to choose the best one for the game, no matter if it comes from the “creative team” or not.

---

## From: January 10th 5e4b177a1b2e4552a2f780d27238ee5f.md

# January 10th

Jumping back into project. Thinking about visuals - light, fog, design, etc.

[A New Way to Think About Beauty in Art](https://www.youtube.com/watch?v=MDiysZ4eglc&)

Great video on constructing beautiful scenes using lighting

Added Beautify and Voulmetric Fog to project

Beautify does not seem to be working properly according to demos

Volmetric fog doesn’t look that great - may be better assets to try for this look 

Trying BOXOPHOBIC’s fog 

Thinking about buying BUTO fog asset because it looks good in videos

How do these things interact with GPU Instancer?

Seems to work, not super elegant looking but works ok - maybe a solid temporary solution

Switched to Kronnect’s Volumetric Fog, seems better! Sticking with this for now 

Trying to extract shader of Monk animation from Pilgrimmage

Valid shader files are not extracted using AssetStudio Gui - is there another way to do this?

Model Joost like character - use ai possibly to achieve this 

AI not working out I think - tried metshape

use Fspy in Blender and model myself? Might not take very long

Character Art Inspo - solar ash designs

![Untitled](January%2010th%205e4b177a1b2e4552a2f780d27238ee5f/Untitled.png)

![Untitled](January%2010th%205e4b177a1b2e4552a2f780d27238ee5f/Untitled%201.png)

Joost designs - think of these related to this

Likely need to make these characters in isolation - like joost designs

---

## From: January 11th 31377a7057354853bd7394f57c4ef6e7.md

# January 11th

Tried some video of 3d model → Metashape stuff but didnt work out

Getting reacquianted with Blender - working with Kira model to imitate a Joost Egggermoent design

[Tutorial: Blender MODELLING For Absolute Beginners | Low Poly Girl](https://www.youtube.com/watch?v=sbCW0Cs7aI8)

Subdivide - Fractal is a very cool effect

Made a rough character! Rigging it now 

[Creating a Simple Humanoid Rig in Blender](https://www.youtube.com/watch?v=Yf-2dKkhJB4)

Best for me? 

[Rig and Animate Character in 10 Minutes with Blender 3.1](https://www.youtube.com/watch?v=4z7G4TyKE9g)

Look into Rigify for better rigging in Blender 3?

Need to firgure out how to apply multiple textures / materials to rigged character

[](https://www.youtube.com/watch?v=0ca1JjCEtPw)

do i need to finish my model beofre i rig blender? Has to be a way to address this

What are the point of UV’s? Why UV unwrap?

Save model unrigged, save rig next to it. Have file ready to apply rig if needed?

---

## From: January 12 48130e2ed8bb4a59b03b6aec416c70a1.md

# January 12

Audio research category created

Karen Collins work as well as references to other books marker

Read some of Karen Collins work

Casino machines giving ‘winning’ sounds when the player hasn’t won - interesting!

Blender Split tool - very useful! 

[How to Use the Split Tool in Blender 3.0.0](https://www.youtube.com/watch?v=8zgVMhv5RPQ)

Material Basics

[ADDING MATERIALS in Blender | The BASICS | LeeDanielsART Tutorial](https://www.youtube.com/watch?v=E42OxbroToM)

Can assign materials to faces of a mesh ! This is how to use multiple materials 

UV unwrapping

[[3.x] UV Editing & UV Unwrap In Blender | Learn About UV Maps & Their Uses | Step-by-Step Guide](https://www.youtube.com/watch?v=fthcqufSA3U)

Follow up video mentions how to do multiple UV wraps per object

Other useful Blender tips! Geometry Nodes

[Now Bend Anything With Geometry Nodes | Better & Easier Bending In Blender Using Procedural Nodes](https://www.youtube.com/watch?v=okHTUpG_hSA)

Test colour of faces on model. 

Select all adjoining faces of a piece of the mesh?

Painted Faces, issue with exporting model WITH all it’s materials.

[](https://www.youtube.com/watch?v=CMie1Q11yM0)

Specifically for Mixamo! There is a different routine for Unity

[Blender 3.0 to Unity 2022 - Emissive Materials and AI](https://www.youtube.com/watch?v=abaZAXU02NI)

Subdivision Surface - Round’s out elements - look into this for certain elements

Attemtping Import from Blener to Unity, and applyign mixamo animations

Making an animator that responds to positons of camera perspective! 

Need to program the function in the player movement script - update player positon

Animation controller working - running man is happening

Need to add some FX to footsteps

Looking at adding particle effect to footstep

Rough particle effect happening for running forwards

Find better particle effect for Footsteps

Adjust Footsparks to follow rotation!

Trying VFX graph - want to trigger burst using animation events. 

Learning Animation Events

[How to Use Animation Events [Unity Tutorial]](https://www.youtube.com/watch?v=INJmqquHaz4)

Attempting to tie ANimation Events to Playing on VFX graph - having issues! Won’t play more than once. Investigating !

Including STOP in the Start function appears to have fixed it!

---

## From: January 23 a3e4eb5fb6a843a0a87dbb5e61d83fe8.md

# January 23

Rail-shooter along rings of the ophanim

![Untitled](January%2023%20a3e4eb5fb6a843a0a87dbb5e61d83fe8/Untitled.png)

Consider modern game / genres and how they apply to rhythm shooter

**How To Market a Game / GameDiscoverCo**

[Pay attention to these secretly popular sub-genres](https://howtomarketagame.com/2023/01/23/pay-attention-to-these-secretly-popular-sub-genres/)

Vampire Survivor-like

[Are Vampire Survivor-Likes Dead?](https://howtomarketagame.com/2023/01/09/are-vampire-survivor-likes-dead/)

Extraction Shooter

[How a pixel art shooter achieved a million dollar launch (the ZERO Sievert story)](https://howtomarketagame.com/2023/01/18/how-a-pixel-art-shooter-achieved-a-million-dollar-launch-the-zero-sievert-story/)

Low-fi horror

Popular games!

[Six types of best selling games](https://howtomarketagame.com/2022/11/21/six-types-of-best-selling-games/)

How to apply these to my game?

Chart these out - consider where they go

Thematic ideas

Ophanim - wheel - move along the inside / outside of the wheel

Ophanim carrying god’s throne ?

Concept - enemy is blind until you do a certain thing / that thing is necessary 

Vampire Survivors

[Studying the Success of Vampire Survivors | Dissecting Design, Game Design Analysis](https://www.youtube.com/watch?v=pXBkJN_5PGM)

Short form design

score chasing game - what more does it add ? Reverse bullet hell

Persistence through play throughs 

Beat Traveller Notes

Bullets should be spinning so their movement is more apparent

give them rougher faces so this reads better? Less round?

Think - rails - linear game movement structures

Are there games where you’re stuck in one tower taking down enemies? 

Parrying - as an opposite to current mechanic? 

Do you lock targets, or just parry when close and shoots at the enemy who shot it?

Is parrying a possible DODGE mechanic? 

Accessibility

[https://twitter.com/stevesaylor/status/1618344326312099841](https://twitter.com/stevesaylor/status/1618344326312099841)

What if facing the opposite direction, time moves backwards?

This would require

---

## From: July 18 fd2ba3e27c6344389aceda37c614f51a.md

# July 18

<aside>
💡 FSR plugin cause camera to flicker with upside down image occarionally

</aside>

Disabled for now

Looking through curvy examples to see if there’s a better way to define a moving spline

Looking at snake control point / bone problems to see what i may have missed on previous attempts to fix this

New snake is promising, but wildly affected by speed in a way other level parts are not. Unsure why!

<aside>
💡 Speed much faster on new snake!

</aside>

Important to delete uneeded control points before removing from bone offsets list. Otherwise not populated properly, screws up tracking. 

Keeping dolly/spline and model seperate ise actually useful. Scale affects speed, so keeping a regular scale on dolly/spline and altering only the model appears to be better. Need to finalize things a bit more if that’s the approach

---

## From: July 20 fd492f0eef994caa97225de150cf075b.md

# July 20

Removing second global clock from Timekeeper because it seemed it is not needed

currently disabled, and was named ‘Root’ - if causes issues look back to this

Jumping between snakes ask question - freeze time to jump?

or make this automatic?

Some details on using connections

Using relative position may yield better results - inconclusive right now 

[Connections - Curvy Splines tutorial](https://www.youtube.com/watch?v=8Ok1WoaHQQY&list=PLatR0iPtXq-x8gDP9tNaxC5Pp32DEL6fS&index=12)

---

## From: July 24 3edf6d65e48c47369df28ed1303c1f81.md

# July 24

Update on mesh disappearing at times in game

“Most likely your skinned positions are going outside of the calculated skinned mesh renderer's bounds and therefore getting frustum culled unexpectedly.

Annoyingly, Unity's importer only calculates the extents of a skinned mesh renderer bounds with animations on the same source FBX. So, if that's you're issue, you'll need to update the renderer's bound yourself in script.

[https://docs.unity3d.com/ScriptReference/Renderer-localBounds.html](https://docs.unity3d.com/ScriptReference/Renderer-localBounds.html)

You can also turn on "Update When Offscreen" on your skinned mesh renderers. It's inefficient, but simple fix. Also, if it fixes your problem, then it confirms that your bounds is the issue.

[https://docs.unity3d.com/ScriptReference/SkinnedMeshRenderer-updateWhenOffscreen.html](https://docs.unity3d.com/ScriptReference/SkinnedMeshRenderer-updateWhenOffscreen.html)”

Using the quick fix seems to work!

Broke something in Shooter movement with refactor - need to look at old code to fix

---

## From: July 25 c152e6832ef94d83acffcf50db17305e.md

# July 25

Trying to fix weird issues with shooting reticle movement. Remove Time.deltaTime from the speed determinant in the script, may have helped things

Weird issue with things not working in build with this movement script, versus things working somewhat in editor. This may be a factor?

Fixed UI not appearing on screen. 

Changed game to IL2CPP builds. Seems better? faster builds at least. 

Change Camera system, mostly working ok. Should allow for adjustments to reticle range now in ways i didnt have before. will need tweaking!

Need to fix UI menu, not working properly.

---

## From: July 26 70539bdfe38e407ea7d5d4348f863966.md

# July 26

Error with this - can’t find it. Not sure why! 

![Untitled](July%2026%2070539bdfe38e407ea7d5d4348f863966/Untitled.png)

Removing to see if I can fix this problem

Had to readd the default input module as seen above, also reimported the UI and have things working now. Need to redo customization of UI as well - something to do when i dont want to do other stuff

Kill All Enemies script being attached to double click scoring is no longer working due to removal of mouse input from Default Controls. Look into work around for this.

Trying to verify why screen flashes and I cant run on any monitor than the first one when i play a build. Not sure I had this problem before - using a development build to troubleshoot the issue. 

On 60hz display i dont get the flashing and issues i do on my laptop screen. Need to add support for alternate refresh rates. 

Need to fix aiming system, thinking turret model could help? would this adjust camera? 

Started on implementing a rough turret rotation aiming system to add to the vcam. 

Partially working. Disabled for now, but need to come back to this. 

Testing issue with Shoot and Scoot stopping. Enemies are seeing targets as 0, so checking if health/score/time is the issue, when it hits 0/negative does this cause them to stop?

seems to have worked! ALl is well if health is positive

---

## From: July 28 7bf5cf1160984ec2b13cc13eb655a41a.md

# July 28

---

## From: March 1 2023 c59550993dfc45ddae0d4c958ca523da.md

# March 1 2023

How can I handle varying levels of gameplay speed - liek thumper and it's increasing difficulty?

choice as being almost 50% of what a player projects onto an aparatus rather than what the aparatus is doing

Technical Issues Today

- Map not constrained properly - bullets flying off
    - Mostly Fixed - scaling issue with screen aspect ratio now, i think. check black square / map size as well
- bullets circling enemies, not hitting them. Rotation and other projectile numbers need to be fine tuned?
    - higher rotation speed fixes this
    -use low rotation speed as gameplay mechanic for some projectiles?
- Not transitioning between Ophanim rings properly - implement script for transition again
    - pulled scripts from old backup and fixed this

---

## From: March 10 27982ba4105b47b88a39e9bce3744558.md

# March 10

Mario day

Looking into GPU bound of performance - optimization!

Unity Crashing “Stack trace could not be displayed. See the latest crash report generated in Editor Log by clicking the "Go to crash logs" button”

Optimization - Additional Camera existed that was doing nothing! Taking up resources 😟

Important to check everything

Currently working very well! 60 FPS on steam deck with current settings. BUTO takes a good chunk of rendering resources but not an issues at this point. 

Things to tackle

- Fix functionality of tracking on reticle
- Fix bullet trajectory calculations
- Work on Menu UI more
    - Add HUD Scaling
    - Add MAP scaling
- ~~Find a way to turn dev console on/off~~
- Refine HUD UI
- Try alternative control sceme

Fixed projectile UI being the wrong number

Need to fix Enemy UI - update when enemy dies?

Maybe represent differently? Is it important? 

Bullet absorb mode - change music to something more chill when this happens? 

Instead of a glitch

Absorbing is a bit more relaxed, adds health / time to the clock 

make bullets reduce score / health and try this out

How can i make using the absorb feature more engaging? Is it enough? 

Is there a way to make it somewhat timed? 

**Shaders! Things to do**

Need to learn

- Texture Maps
- Normal Maps
- Emission Maps

How should  I set Alpha Clipping?

Do I need different Materials for different colors of same shader? Just use GPU Instancing?

Playing with texturing and effects on Ophanim 6

Looking at bringing more detail and interest to things - experiments!

Added alternative control schemes

Not sold any on any yet but I think they’re moving in the right direction

Consider FPS / Third Person ideas for best practices here

Concept thoughts

Ophanim Rings turn into tower pieces many rings into structure?

Rings split off into many pieces? 

To rebuild the tower

What if the rings ARE the tower? 

The rings expand outward until you reach the divine… god…. 

What if the rings exist around planets? You’re attempting to reach the center of each 

Section 1 / ring infiltration

Section 2 / Different worlds

GAMMA SPACE

- Think about these categories from the perspective of someone who doesn’t know these things

---

## From: March 13 09e00d91ccf54c06a45d82b98526135e.md

# March 13

Adding SRDebugger for more efficient on-device debugging

Tying this into my UI

---

## From: March 14 af9011757db14bf98b892cce1d86e2ff.md

# March 14

Was very tired yesterday! Very little done. Added custom debugger to game, SRDebugger. Seems cool! 

Thinking about level design. Maybe more distinct variations among rings would liven up each wave.

Variation on enemy per wave as well? 

Updated Vcam2 so it looks a bit better

May need to disable some Post Processing effects on during transitions - like motion blur

Added this is camera switch feature, motion blue should be disabled and re enabled on next wave

This is working! May just want to lower motion blur instead of disable - not sure yet!

Cant seem to figure out rotation issues with Shooting/Reticle. 

Not a game breaking issue but annoying!

Adding video gameplay recorder to builds for bug tracking?

Haven’t found a good way to do this. May not be that important?

Looking at adjusting movement of bullets so they’re properly affected by time changes.

Seems like they aren’t by slow time, but they are by reverse time. 

**Trying an ‘Area Clock’ to slow bullets around the player**

Works! For 0. But also doesn’t work for slow. Revealing the issues with bullet movement implementation. Looking at fixing that

Can’t seem to figure out why it’s not working for slowing down the projectiles. 

Need to revisit this!

Could implement destruction possibly!

[Time Manipulation in DestroyIt with Chronos](https://www.youtube.com/watch?v=B6hk1dNeY50)

Interesting resource that may help me solidify my game!

[Practical Tools for Empowering the Relationship between Theme & Mechanics](https://polarisgamedesign.com/2022/practical-tools-for-empowering-the-relationship-between-theme-mechanics/)

Tone as a means to bridge the communication and conceptual gap between theme and mechanic

> **Now, let’s look at the other side. You have a great mechanic, say, a character that can create a mirror of himself in order to complete the various puzzles in the game. This is fun to play with, and you’ve created a ton of fun puzzles, but the game sort of just feels like a toy, with no underlying meaning or way of really connecting with the player. How do you find a theme to map to this mechanic? Utilize the Tone Bridge to find your way from mechanic to theme. The mirroring mechanic produces lots of tones: Duality, the need for precision, a constant companion, and more, depending on what you see. Putting that mechanic into a game with increasingly-complex puzzles also creates some tones, like an increasing difficulty, a gradual slope up to a natural climax. You can take these tones and see what themes fall out of them for you. Duality, and a constant companion: Maybe a theme of friendship, or perhaps one of rivalry? Increasing difficulty: Perhaps the game takes place within a mountain, traversing symmetrical caverns with your clone or friend, ending with a triumphant crest at the top, all signifying a struggle through adversity, maybe even with yourself? (If this sounds familiar, you’ve probably played Celeste.) Taking each tone, you can define what each means to you thematically, until you have a complete set of thematic ideas which you can then weave into your game’s overall theme. This game’s theme could well be, “You must learn to be your own friend before you can truly conquer your internal demons and break through to an enlightened life.” Or something completely different! Again, the Tone Bridge can possibly reveal different themes and mechanics depending on the person using it, and what they interpret as they go through the exercise.**
> 

Mechanics → Tone → Themes

> **Shooting mechanics are frequently assimilated with more action tones, which narrows down some themes it can relate to.**
> 

**Mechanics**

Dodgeball-like Shooting

- Call and response

Move to the music / affect the music

4 quadrant turning

Lock on to enemies

Lock on the Projectiles

You can rewind time 

You can pause time

**Tone**

Fast / Frantic

Flow state like playing music

In the moment, like a dance club

DJ?

Abstract 

Futuristic

Meditative

Hypnotic

Time can be sped up as well? 

Bullet absorb mode - change music to something more chill when this happens? 

Instead of a glitch

What if enemies can control time as well? 

They can be unaffected by the time rewind

Testing half speed with this… not working! Need to look into how this works again

Can pause bullets briefly

ChatGPT suggestions

- exploration of this digital world, as players uncover its secrets and discover the true nature of the cybernetic rhythms that power it.
- Combo Chains: Players can be awarded extra points for hitting a series of notes in a row. The more notes they hit in a row, the higher the combo chain becomes.
- Power-Ups: Power-ups can be used to help the player hit more notes or gain extra points. For example, a power-up could slow down the song briefly, giving the player more time to hit the notes.

No luck with Gravitational Waves Spacetime, need to figure out how to scale the mesh up larger

Or use a different mesh?

Combine these things for interesting visual effects - reference Solar Ash possibly

---

## From: March 15 39007b90903d486f9f10aaa397cd7d05.md

# March 15

HDR screws up space time graphics assets, disabling HDR to see what I can do

Tried some models in blender, thinking on boss configurations

Made refactor and cleanup updates to Crosshair, Enemy Basic Setup, Shooter Movement and Projectile setup scripts. 

Added Unity built-in distance fog to have things fade off into distance better 

Added feature to slow down objects / time ! It’s working!!

Tried doing some UI things and no luck, need to dig in deep to setup I think

Do what I had previously done

Used Layer Mask to make sure Enemy lock on is not blocked by anything else, only see’s enemy layer. 

Should I do this for projectiles as well?

May be worth changing lock on / shoot sounds. Hi hats like Rez? Percussive elements? Have a rhythm track of hi hat drop out when locking on / firing, replaced with player behaviour?

Some Object Particle Spawner delays occuring - pooling issues?

 

![Untitled](March%2015%2039007b90903d486f9f10aaa397cd7d05/Untitled.png)

---

## From: March 16 4f4311958412484da05afa8844e4d7fe.md

# March 16

Testing alternate sounds playing for slow down

Got this working! New “Slow” parameter in Fmod

Forgot how to set that up in Fmod but more familiar now

Looking at added video effects as well, tried a video feedback but it didn’t work. 

Now trying Datamosh effect. Doing this through ChatGPT 4

---

## From: March 19 c305317965b144e7ac0cdcd4da178425.md

# March 19

Grabbed Fast Glitch for some effects during some function - possibly Half speed?

Looking into how I can apply Koreographer to the speeds of the Fast Glitch 

Have a basic version - Glitch Controller - In Boss Logic Testing

Tried writing new Boss4Attacks4Positions Behavior Designer, issues with 4 attack types and extending the tactical agent class. Likely easier to track attack types in a seperate script, Behavior Designer can just track when an attack is allowed to occur. 

Thinking about a boss fight and A* being uncessary if primarily stationary. Might not be every sceneario but could be some of them 

Concepts Ideas for levels  / bosses

- Snake as rings wrapped around a planet - ouroboros
- Void Emperor: A powerful being that resides within the empty spaces between planets and stars. It can warp reality itself, creating gravity wells, black holes, and other anomalies that can trap players and deal massive damage.
- Solar Flare Seraphim: This boss could be a massive angelic being that harnesses the power of the sun itself, using solar flares and coronal mass ejections as its weapons. It could have multiple stages, each one representing a different phase of the sun's activity, and players would have to dodge and weave through intense bursts of radiation while targeting weak points on the Seraphim's body. As the battle progresses, the Seraphim could summon smaller minions to assist it, like solar flares breaking off from the main body.
- Leviathan: A monstrous sea serpent from mythology, Leviathan could be a difficult aquatic boss in the game. It might have a range of attacks such as powerful water blasts and the ability to call forth smaller sea creatures to aid in the fight. Or maybe Cosmic Leviathan: This boss could be a colossal, tentacled creature that dwells in the icy depths of Pluto. It could attack by using its tentacles to smash into the player and summoning freezing blasts of energy.
    - level that turns into you realizing you’ve been travelling on leviathans back the whole time.
- Metatron: As a celestial scribe and archangel, Metatron could be a powerful boss with the ability to manipulate reality itself. It might have attacks that distort space and time, as well as the power to summon other angels to aid in the fight.

![Untitled](March%2019%20c305317965b144e7ac0cdcd4da178425/Untitled.jpeg)

- Void Behemoth: This boss could be a monstrous, shadowy creature that lurks in the emptiness of space. It could attack by using its massive claws to swipe at the player and summoning waves of darkness to obscure the player's vision.
- Ophanim Prime: This giant, mechanized version of an Ophanim angel could serve as an early boss in the game. It would have multiple arms with laser cannons and missile launchers, as well as the ability to fly around and dodge attacks.
- Titan's Colossus: When you land on Saturn's largest moon, you'd encounter a colossal machine that resembles the Ouroboros symbol. It would have multiple heads and limbs that could be destroyed one by one, and you'd need to avoid its crushing attacks while finding a way to disable its weapons.

Ouroboros as life / death

sounds / silence

- Jungian Ouroboros: In Carl Jung's theory of psychology, the Ouroboros represents the process of individuation, or the journey of self-discovery and integration of the psyche. It is a symbol of the eternal cycle of death and rebirth and the integration of the shadow self.

Each boss could represent a different aspect of human nature, such as pride, ambition, or fear.

As the tower is split, and things are divided, different aspects of the tower manifested into different beings / bosses. 

“take place in a distant future where humanity has built a massive tower that stretches beyond the Earth's atmosphere and into space. The tower represents humanity's hubris and desire for power, echoing the themes of the Tower of Babel. As the player progresses through the game, they encounter various bosses, each representing a different aspect of humanity's desire for power and control. These bosses could be the Angels inspired by Ophanim, with each possessing unique abilities and musical themes.

[Resonance Ascension](March%2019%20c305317965b144e7ac0cdcd4da178425/Resonance%20Ascension%2078f79231f2ed4ef99e667c7667b6edd0.md)

[Rhythm Ascension](March%2019%20c305317965b144e7ac0cdcd4da178425/Rhythm%20Ascension%205f47e6850a5b41848986a4bda6a95d8a.md)

[Symphony of Celestial Rebirth](March%2019%20c305317965b144e7ac0cdcd4da178425/Symphony%20of%20Celestial%20Rebirth%20fdb02267e34a4468a47d0db0637f9212.md)

[Symphony of the Cosmos](March%2019%20c305317965b144e7ac0cdcd4da178425/Symphony%20of%20the%20Cosmos%20dbdf83baaa8a46598881f05040f9d5e1.md)

 Nebula Symphony: The player moves through a colorful, musical nebula that reacts to the rhythm of the music. The nebula's energy currents guide the player, justifying the on-rail shooter mechanics. **The boss, Chorus Astralis, is a massive celestial being that can only be defeated by navigating through its multiple energy layers.**

Infinity symbol: The Ouroboros can also be represented by an infinity symbol (∞), which conveys the idea of eternal cycles or the endless loop of creation and destruction. This version may suggest that everything is interconnected and that life and death are part of a larger cosmic process.

1. Musical scales: In some esoteric traditions, the Ouroboros is associated with the concept of the "music of the spheres," which is the idea that the movement of celestial bodies creates a cosmic harmony that can be heard as music. In this context, the Ouroboros may represent the cyclic nature of the universe and the underlying order that governs it.

Music of the Spheres concept - the movement of the celestial bodies created a harmonious sound that could be heard by the gods. The idea of the music of the spheres was later developed by philosophers and scientists, such as Pythagoras and Johannes Kepler, who believed that the movements of the planets and stars were governed by mathematical and musical laws.

According to this theory, each celestial body emits a unique sound or tone as it moves through space, and the collective harmony of these tones creates a kind of cosmic symphony. The music of the spheres was thought to be a reflection of the divine order and harmony of the universe, and some believed that it could be heard by humans with the right kind of spiritual or mystical training.

In some esoteric traditions, the music of the spheres is seen as a manifestation of the divine order and harmony that governs the cosmos. This order is reflected in the movements of the celestial bodies, which are believed to emit unique tones or frequencies as they move through space.

the Ouroboros is a symbol of cyclicality and unity, representing the idea that everything in the universe is interconnected and part of a larger cosmic process.

They may be two concepts that reflect different aspects of the same underlying order and harmony. In this context, the music of the spheres is seen as a kind of cosmic symphony that reflects the cyclical nature of the universe, while the Ouroboros represents the eternal cycle of life and death that underlies all existence.

IDEA - Babel and Music of the Spheres as counterpoints

In other words, the people of Babel sought to build a physical tower to reach heaven, while the music of the spheres suggests that the key to understanding the divine lies in listening to and understanding the natural world.

Ophanim

- represent the celestial chariots that carry the throne of God, and that their movements create a kind of cosmic symphony that reflects the harmony and order of the universe. In this context, the Ophanim could be seen as a metaphorical representation of the music of the spheres, emphasizing the idea that divine harmony and order are reflected in the movements of the celestial bodies.

Metatron

- a powerful angel in Jewish mysticism, there are various associations with the Tower of Babel and the music of the spheres. According to some interpretations, Metatron was originally a human prophet who was elevated to the status of an angel after his death. He is sometimes associated with the tower builders of Babel, who were punished for their arrogance by having their languages confused. In this context, Metatron could be seen as a symbol of human ambition and the dangers of overreaching.
- associated with the music of the spheres and the divine harmony that underlies the universe. He is sometimes depicted as a kind of celestial scribe who records the music of the spheres and the secrets of the divine order. In this context, Metatron could be seen as a figure who embodies the mystical knowledge and wisdom that is needed to understand the deeper truths of the universe.

One way to relate the Tower of Babel to the planets and the music of the spheres is to view the tower as a representation of the structure of the solar system. In this interpretation, the different levels of the tower could represent the different planets and their corresponding celestial spheres, while the builders of the tower sought to understand and control the forces of the universe through their construction.

---

## From: March 2 2023 3db3abdae5d7443291dc07f96bdf7f00.md

# March 2 2023

Tower of Babylon Concept

![Untitled](March%202%202023%203db3abdae5d7443291dc07f96bdf7f00/Untitled.png)

Start on worker building tower of babylon - simple note / rhythm 

![tower of babylon.png](March%202%202023%203db3abdae5d7443291dc07f96bdf7f00/tower_of_babylon.png)

Zoom out to reveal full tower ascending to the clouds - lots of notes / rhythms

- Do we see many other workers?

![Untitled](March%202%202023%203db3abdae5d7443291dc07f96bdf7f00/Untitled%201.png)

Camera pointed up to show some of the great height into the stars - sound more distant?

![Untitled](March%202%202023%203db3abdae5d7443291dc07f96bdf7f00/Untitled%202.png)

![Untitled](March%202%202023%203db3abdae5d7443291dc07f96bdf7f00/Untitled%203.png)

We see some pieces falling off the tower - introduce atonality? weird rhythms?

![Untitled](March%202%202023%203db3abdae5d7443291dc07f96bdf7f00/Untitled%204.png)

![Untitled](March%202%202023%203db3abdae5d7443291dc07f96bdf7f00/Untitled%205.png)

Camera zooms way up past the many pieces falling, seeing components of the tower fly by

- Do we see any workers falling?

Cut to black when getting close up on a specific piece

![Untitled](March%202%202023%203db3abdae5d7443291dc07f96bdf7f00/Untitled%206.png)

Cut to the destroyed tower, pieces falling off, 

![brasss old solar system.png](March%202%202023%203db3abdae5d7443291dc07f96bdf7f00/brasss_old_solar_system.png)

![solar system.png](March%202%202023%203db3abdae5d7443291dc07f96bdf7f00/solar_system.png)

Zoom out to various areas / sections / levels that area represented in some type of solar system structure

Our character is floating close up to the camera as we make a selection of first level

- How many choices do we want available here?
- Whats the best way to integrate a tutorial?

Move through several levels, rebuilding the tower

![Untitled](March%202%202023%203db3abdae5d7443291dc07f96bdf7f00/Untitled%207.png)

Destroyed again, but this time it’s more apparent what destroyed it.

It’s rebellion among those who don’t want it? It’s a mysterious force that denies perfection?

Does the pursuit of new tech allegory connect with the art allegroy? Maybe not

Pieces fly around and new levels are a remix of previous ideas

This battle with enemies is call and response

You’re responding to the ideas being thrown at you

This is represented through music

Through quick action and instinct, it is like jamming or improvisation  

Remix Concept

- Art and communication as a struggle of persuing perfection and seeing it not work out?

Akka Arrh Concept

- Shoot enemies, they subdivide. Note or musical divisions?

Some Concepts

- God as this pure sound, elaborate construction
- Ophanim as supports for the throne imply the throne of god cannot hold itself up
    - The weight of the idea of god does not sustain itself? Not this version
- Music as systems

IDEA: Breaking through some kind of layer? sort of like AT Field?

![Untitled](March%202%202023%203db3abdae5d7443291dc07f96bdf7f00/Untitled%208.png)

Enemies

What do they represent?

- What prevents sound from being music?
    - deliberate and intentional organization of sound that creates a sense of rhythm, melody, and harmony, and communicates a particular artistic message or emotional expression.
    - Who decided what is deliberate and intentional in the absence of the artist? Is this universal? What is the communicated message?
- What enables sound to be music?

Representations

- Pieces of the tower - exhibit different musical ideas
- Pieces of something holding back the construction of the tower
    - Destroying them and saving the ophanim allows the tower to be built

Mechanics

- Geometric shapes that shoot things which contribute to the sound
- External points of the enemy relate to the range of notes
    - What does this mean mechanically? Strength of bullets?
- What if enemies blow apart into smaller enemies? Make this equivalent to musical systems
    - Like breaking down time signatures

IDEAS

- do we see any enemy types as workers?
- ophanim as rings of the tower?

Timnit Gebru

> Resentment for - Nobody said go build us a god, try to build us a god, but they decided it was a priority for everyone to do and go do that, then the rest of us are stuck cleaning up rather than trying to implement our vision of what useful things we can build with our skills and our little amounts of funding.
> 

Reconstruct tower - the turn is not everyone wanted this in the first place

God did not strike it down, others did 

[CHatGPT Prompts](March%202%202023%203db3abdae5d7443291dc07f96bdf7f00/CHatGPT%20Prompts%2011ccd4c7f00a4577b8e8ba677e630981.md)

Look into using Flexalon

[Flexalon Update 3.0: Infinite Curves, 3D Grids, and More!](https://www.youtube.com/watch?v=kt3O9AgH8WY)

Many interaction ideas form the demos here! Worth using

Sacred Geometry for enemies

Kabbilistic Imagery - Jewish mysticisim

Tower of Babel as rings (ophanim) idea

Brekaing apart enemies as adding time signautre

- then certian enemy types would represent time sigs (points or edges)

Destorying enemies is acutally returning them to the strucutre (show in tutotiral, impied later that this occurs)

---

## From: March 20 e2d6c975f74c4d85980d4daecbf16fff.md

# March 20

Collecting ideas together for the world buidling

Ophanim carry the throne of god

Solar system as ophanim in slow motion? or seen from a different dimension - 3d and not 4d

Take place in a distant future where humanity has built a massive tower that stretches beyond the Earth's atmosphere and into space. The tower represents humanity's hubris and desire for power, echoing the themes of the Tower of Babel. 

The people of this world built the Tower of Babel, a colossal structure representing the unification of sound, as a way to create the perfect symphony and reach God. However, the tower was struck by divine intervention, shattering it into pieces and scattering its musical essence throughout the world.

set in a surreal, ethereal world where music and rhythm dictate the flow of life. The environments are a blend of celestial and terrestrial elements, with levels taking players through the remnants of the Tower of Babel, now shattered across time and space.

As the Tower nears completion, its creators fail to realize the hubris of their actions. The heavens unleash cosmic forces that shatter the Tower, sending its fragments throughout the galaxies. These fragments, each containing pieces of the perfect music and art, corrupt the celestial beings they encounter, morphing them into monstrous guardians.

Level 1: The Fall of Babel
The game begins with a cinematic of the Tower of Babel being destroyed. The Seeker is mysteriously drawn to the ruins and embarks on a quest to rebuild the tower using fragments of the once-great structure.

Level 2: Echoes of the Past
The Seeker traverses a desolate landscape, collecting musical fragments and battling rhythm-based enemies, including the first boss, a seraphim-like creature called Echorythm.

Level 3: The Timekeeper's Domain
The Seeker enters a clockwork realm where the power to manipulate time is strengthened. Here, they face the second boss, Ophanim, a powerful guardian angel made of gears and machinery.

Level 4: The Infinite Loop
The Seeker navigates a level designed like a Mobius strip, symbolizing the cyclic nature of life and death. They encounter the third boss, Ouroboros, an enormous serpent that represents eternity and the never-ending cycle of creation and destruction.

Level 5: The Ascension
Having gathered enough fragments, The Seeker reassembles the Tower of Babel. This level features vertical gameplay as they ascend the tower, battling against gravity and waves of angelic adversaries.

Level 6: Requiem for Babel
As the Tower reaches completion, it is destroyed again, causing the world to be reformed with remixed levels. The Seeker must navigate these familiar yet altered landscapes, confronting the previous bosses in their evolved forms, culminating in a battle against a colossal, Evangelion-inspired angelic figure named Archisynth.

Final:

 The remnants of the Tower of Babel disperse throughout the universe, creating an eternal symphony that connects all living beings to the divine through the power of music.

the cyclical nature of life, death, and rebirth is intrinsically connected to the ebb and flow of sound and silence. It the relationship between sound and silence that matters, and it takes new forms as time moves onward. Wheel of Time? 

the balance between sound and silence, life and death, and creation and destruction. The Maestro accepts the impermanence of the Tower of Babel and embraces the eternal cycle of the universe. With this newfound understanding, they release a cosmic symphony that resonates throughout the galaxies, uniting all beings in the harmony of life, death, and rebirth.

Level 1: Ophanim's Cosmic Rings
The Composer encounters Ophanim, a celestial being composed of numerous interlocking rings. As the Composer's spacecraft traverses through the rings, they must dodge and shoot to the rhythm of the music. Each ring contains a fragment of the Tower. Upon defeating Ophanim, the Composer collects the first fragment and moves on to the next level.

Level 2: Seraphim's Nebula
The Composer ventures into a colorful, digital-looking nebula guarded by Seraphim, a multi-winged, fiery guardian. The Composer's spacecraft is pulled along the trails of fire left by Seraphim, forcing them to dodge and shoot rhythmically. Upon defeating Seraphim, the Composer collects another fragment and continues the journey.

Level 3: Ouroboros' Space-Time Loop
In this level, the Composer encounters Ouroboros, a serpent-like creature consuming its own tail. The spacecraft is caught in the creature's eternal loop. The Composer must navigate the loop and dodge the serpent's attacks, using the time manipulation abilities to their advantage. After defeating Ouroboros, the Composer retrieves another fragment.

Level 4: Rebirth of the Tower
The Composer rebuilds the Tower of Babel, unwittingly causing it to resonate with the cosmic rhythm. The heavens respond, shattering the Tower once more. The levels are remixed, and the Composer must revisit the previous levels, facing more powerful versions of the bosses while the celestial environment has become more hostile.

Final Battle: Celestial Symphony
As the Composer gathers the final fragments, they confront the ultimate guardian, the Celestial Symphony, a divine entity embodying the perfect balance of sound and silence, life and death. In a climactic battle, the Composer's spacecraft navigates the intricate patterns of the Celestial Symphony's attacks, shooting in harmony with the music.

Ending:
Upon defeating the Celestial Symphony, the Composer absorbs the essence of the perfect music and art. The heavens reveal the truth: the divine forces sought to teach humanity humility and the importance of balance. The Composer, now enlightened, transcends their physical form and ascends to a higher plane, becoming a guardian of the celestial realms. The game ends with the reconstructed Tower of Babel standing as a testament to the power of creative expression, a symbol of humanity's capacity for growth, and the delicate balance between life and death, sound and silence.

Levels and Bosses:

1. Ophanim's Rings:
The Maestro navigates through a mesmerizing galaxy, following the trail of the Ophanim, celestial beings with rings of divine fire. The on-rail shooter aspect is justified as the Maestro moves along a pre-determined path through the rings, shooting musical projectiles to match the rhythm of the music and avoid obstacles.
2. Nebula Serpent:
In this level, the Maestro encounters a massive, digital serpent-like creature weaving through a vibrant nebula. The on-rail mechanic requires the Maestro to move along the body of the serpent, defeating smaller enemies and avoiding obstacles as they make their way to the serpent's head for the final confrontation.
3. Ouroboros:
In this level, the Maestro faces a massive, cosmic Ouroboros, the serpent that eternally consumes its own tail. The on-rail aspect involves the Maestro running along the ever-looping body of the Ouroboros while battling smaller enemies and overcoming obstacles in time with the music. The boss fight culminates in the Maestro using their time manipulation abilities to break the eternal cycle of the Ouroboros.

- Snake as rings wrapped around a planet - ouroboros
- Void Emperor: A powerful being that resides within the empty spaces between planets and stars. It can warp reality itself, creating gravity wells, black holes, and other anomalies that can trap players and deal massive damage.
- Solar Flare Seraphim: This boss could be a massive angelic being that harnesses the power of the sun itself, using solar flares and coronal mass ejections as its weapons. It could have multiple stages, each one representing a different phase of the sun's activity, and players would have to dodge and weave through intense bursts of radiation while targeting weak points on the Seraphim's body. As the battle progresses, the Seraphim could summon smaller minions to assist it, like solar flares breaking off from the main body.
- Leviathan: A monstrous sea serpent from mythology, Leviathan could be a difficult aquatic boss in the game. It might have a range of attacks such as powerful water blasts and the ability to call forth smaller sea creatures to aid in the fight. Or maybe Cosmic Leviathan: This boss could be a colossal, tentacled creature that dwells in the icy depths of Pluto. It could attack by using its tentacles to smash into the player and summoning freezing blasts of energy.
    - level that turns into you realizing you’ve been travelling on leviathans back the whole time.
- Metatron: As a celestial scribe and archangel, Metatron could be a powerful boss with the ability to manipulate reality itself. It might have attacks that distort space and time, as well as the power to summon other angels to aid in the fight.
- Void Behemoth: This boss could be a monstrous, shadowy creature that lurks in the emptiness of space. It could attack by using its massive claws to swipe at the player and summoning waves of darkness to obscure the player's vision.
- Ophanim Prime: This giant, mechanized version of an Ophanim angel could serve as an early boss in the game. It would have multiple arms with laser cannons and missile launchers, as well as the ability to fly around and dodge attacks.
- Titan's Colossus: When you land on Saturn's largest moon, you'd encounter a colossal machine that resembles the Ouroboros symbol. It would have multiple heads and limbs that could be destroyed one by one, and you'd need to avoid its crushing attacks while finding a way to disable its weapons.
- Jungian Ouroboros: In Carl Jung's theory of psychology, the Ouroboros represents the process of individuation, or the journey of self-discovery and integration of the psyche. It is a symbol of the eternal cycle of death and rebirth and the integration of the shadow self.

How to travel across moons in a similar way as travelling across direct rails? Or maybe you just travel across a meteor shower / astral belt alongside all of this?

Ouroboros as life / death

sounds / silence

What is a sound without silence? 

What is a life without death?

Each boss could represent a different aspect of human nature, such as pride, ambition, or fear.

As the tower is split, and things are divided, different aspects of the tower manifested into different beings / bosses. 

In some Islamic and Sufi traditions, the angel Israfil is believed to be responsible for blowing the trumpet on the Day of Judgment, signaling the end of the world and the beginning of a new age. This association between music and the end of the world suggests that music has the power to transcend the temporal realm and connect us with the eternal.

---

## From: March 21 4151ffa3a0754b838d270fbcd26a2fe3.md

# March 21

Upgraded out of LTS to Unity 2022.2.11

Some errors! Trying to resolve these

Removed A* and GPU Instancer due to errors. Trying to fix this. A* should get a fix soon, Gpu Instancer, not sure exactly the problem. 

Disabled Burst Compilation and Optimizations

Need to add Quibli package again - DONE!

Added GPU Instancer back, seems to work.

Added Buto - not working yet. All pink.

---

## From: March 22 e158faf2a6ae4e25aa16e1c8ea82ca2c.md

# March 22

A* Now working!! Not problematic in scene anymore

Only issue now is Buto - waiting on resolution here.

Burst is disabled, not sure if this effects anything for me, seems like it doesn’t?

Need to run a full asset / render pipeline conversion but dont have enough space on main drive.

Need to offload this on another drive and try it? Look into this

Alpha clipping on projectiles - will this solve my issues with them looking better / fading out when they get farther away? Use a better outline shader - 2022 options?

Check if HDR on and off makes a big differnece to steam deck performance / pc performance.

---

## From: March 23 b6631bb283e0406ebd76b2e857bde7be.md

# March 23

Buto not working 100% so adjusted to work in a manageable state

Unity crashing, but changed from D12 to D11 and seems fine now

A* Graph issues, pulled in graph from previous scene and now it works

Enemy AI still bugging out, need to fix this

Need to work on controller use in UI menus

Taking a look at previous implementation 

Experimenting with Settings UI - needs camera for cursor selection to work. BUT post processing gets in the way. Dont want to make a new camera if I don’t have to? Maybe not so bad if setting camera active / disabled

---

## From: March 25 588192b57727461cb30b4351b2b2ab22.md

# March 25

<aside>
💡 Dynamic Mesh Cutting

</aside>

Could be an interesting addition - allowing the connection between several bullets to slice objects around the area

Tried a new outline shader - full screen effect in Unity 2022, pretty cool! 

Going to try organizing current materials in a game design document. 

Wleminated screen similar to rollerdrome

Use the full screen shader effect

---

## From: March 27 1cca5b2f1edf48399215960239762d0d.md

# March 27

Tried Rokoko animation importing - nothing interesting here, works fine

Tried AUto Rig in Blender - Mixamo hates it. Not sure what’s wrong, but need to more thoroughly look at Blender → Unity pipeline, and bringing in mixamo aniamtions to blender. 

Relearned Random Flow - it’s awesome! Finsih BT docs and consider environments with these tools 

Working on game docs

Also working on Creatues in Blender. 

Bringing some fantasy creature in to edit and remodel, testing my options here.

---

## From: March 28 6f76d7f2a95d46fdae8c8abff7f9916b.md

# March 28

Working on Blender abilties

Remesh , Subdivision, and Random Flow. Need to look into other tools that I’ve acquired

Random Flow can place a defined mesh in random places across an object. How to use this? 

Did a big narrative dive with JQ, solidified some important things! Got some great boss ideas!

Next steps are coming together. 

Need to keep in mind, Roller Drome objectives list

Need to also assess any Google Keep notes, ideas for moving forward

Some ideas:

completing objectives of a level unlock a hard mode/ variation arcade / endless version of the level?

Look at what Rez does for this

Levels can be any order, like Starfox

Different orders construct the tower differently, give you a different ‘ending’ 

Use narrative to help cement the importance of doing this

what might the true ending be? What can be unlocked?

---

## From: March 29 ab5fdcff663548ba939b703f4808483f.md

# March 29

Black Hole VFX 

[Assess Assement - BT](March%2029%20ab5fdcff663548ba939b703f4808483f/Assess%20Assement%20-%20BT%207a0806227fbf48568d8535fbc4488bad.md)

Tried working on models and in the morning, rigging and such.

Difficult! Need to learn more Blender. 

Tried a different approach for now, 

Taking Mobius Strips and trying to figure out dolly / paths on them

Spent lots of time working on a generative solution for putting paths on these things.

Not perfect! But made some headway on that front, may pursue further

---

## From: March 30 60420580c2da4b3f9d208c7f31183b43.md

# March 30

Older method in ChatGPT 3.5 was working best 

Need to go back to this !

---

## From: March 5 af0621e6511c4d71acbaa91218cd7745.md

# March 5

Core Ideas

Call and Response

Enemy Shoots - I send it back 

> The call and response musical structure can serve as an allegory for the artistic process in many ways. In music, call and response is a technique where one musician or group of musicians makes a musical statement, and another musician or group responds with a contrasting statement. This technique creates a dialogue between musicians, allowing them to communicate and collaborate to create a cohesive piece of music.
> 

In which ways is the response a contrasting statement?

- Fired back

Enemies don’t expect a response

> Similarly, in the artistic process, an artist may begin with an idea or statement, which can be thought of as the "call". The artist then responds to their initial idea with further exploration, experimentation, and refinement, which can be thought of as the "response". This back-and-forth process can continue until the artist has created a finished work of art that is cohesive and communicates their intended message.
> 

> The initial "call" represents the artist's vision or inspiration, while the subsequent "response" represents the artist's willingness to explore new ideas and take risks in order to achieve their creative goals. Ultimately, the call and response process allows artists to create work that is more than the sum of its parts, as it combines different perspectives, ideas, and techniques to create something unique and meaningful.
> 

Hauntology

> hauntology is a complex and multifaceted concept that reflects the idea that the past is always present in some way, and that the present is shaped by the memories and experiences of the past, even if they are not immediately visible or tangible.
> 

> By drawing on the sounds and aesthetics of the past, hauntological music seeks to evoke a sense of nostalgia and longing for a time before the endless stream of digital content.
>

---

## From: March 6 8617ebdc94ee4f0d8993074676cba2c9.md

# March 6

<aside>
💡 Added Screen resolution script to Managers for testing which resolution gets set when game is loaded. Test this on Steam Deck!

</aside>

Considering UI Overhaul

![Untitled](March%206%208617ebdc94ee4f0d8993074676cba2c9/Untitled.png)

**Left Side**

Health - is this needed?

Score / Time

Bullets Locked - currently a number - Use a status Bar?

Current Wave of enemies

Add Enemies locked? Maybe an attribute you get later 

Working on Pause Menu UI - Integrated Shift UI

Changed colours and fonts

Can enable / disable HUD

Implemented Graphics settings

- Field of View
- Draw Distance
- Resolution (maybe works?)

Partially have controller as curson implemented for Pause menu

Cant seem to click anything! Mouse works fine though

Added Rewind Particle effect - cubes everywhere!

**Lighting**

For moving objects, you can use static light maps - "Lightmap static" (or, I think they renamed it to Contribute GI in newer versions) allows you to do this 

Change Control scheme - Use Triggers for main actions, buttons for time rewind etc

---

## From: March 7 6aa367eda20f4da4bd14840325534802.md

# March 7

Disabled Auto Refresh

Trying Hot Reload for unity! Setting up properly

CTRL + R for a manual Reload - necessary sometimes

Added Master volume control to Settings 

Added code for Unity Debug console visibility on/off

- Not sure that it works, needs testing in build

Added ability to see come control mapper features in the UI Controller settings windows

- Need to adjust to make coherent with UI design

[What Makes Good Rhythm Game UX?](https://www.youtube.com/watch?v=nRJ4iVjIMAc)

Rhythm games require great UX - can say this about my portfolio!

Focused on a genre that requires UX more than most!

Some ideas but nothing huge at the moment

Looking at the following video for ideas on fixing aiming

[Accurate Aiming Options Discussed & Implemented | Gun Series 7 | Unity Tutorial](https://www.youtube.com/watch?v=x8ECpNWMmag)

Unsure that it helps but may bring implementation into project for some things

Managed to get rotation around sphere working through fake gravity unity video

[How to Walk Around a Sphere (Walk on Planet) in Unity 3D](https://www.youtube.com/watch?v=cH4oBoXkE00)

Issue with Z value of Shooting Game Object rotating, can’t seem to make it stay locked 

Changing Shooting point of Rayspawn for Bullets AND enemies to be from reticle game object isntead of further back rayspawns

Put Rayspawn game object sunder reticle, using them in shooting, and it’s working MUCH better!

DO I need cone anymore? Disabling

---

## From: March 8 b5677b0fd407410c9e446a291237ba2b.md

# March 8

[QUICK START GUIDE - TO EVERY SHMUP EVER](https://www.youtube.com/watch?v=c_vUM1V_s1A)

Overview of movement and shot types!

Fixed most of UI To scale properly to different aspect ratios

Need to fixe Radar for this as well 

Radar working much better - not perfect but all elements adjust much better than before

![Untitled](March%208%20b5677b0fd407410c9e446a291237ba2b/Untitled.png)

Not sure what this means, but not sure it has had an impact on anything

It’s part of Easy Performant Outlines

- Check if I’m using this anywhere - do I want to?

Removing it for now - will look into later if necessary

Deleting Magic Arsenal and MeshBaker from project to remove a possible error

Didn’t work ! Not seemingly causing issues though 

Quality Settings - Resolution - need to add options for apply and revert for resolution

Updated dev console closing script - should work now

---

## From: March 9 6ca6200d922949ec88ff22bf96a80ffc.md

# March 9

Change Wave to show brefly at start and then disappear

- make this like Control? Need Sound Effect

Integrating target locks stat

Need to make bullet and enemy stat colors same as what’s on the radar

Suggestted to put them on the reticle… there or the player character? 

Implemented this in a basic way. Testing to see how it feels

Integrate other menu quality settings!

Using asset AllSettings to comapre issues

<aside>
💡 May have a better integration of resolution, need to test a build and see!

</aside>

Use Projectile Toolkit to fix bullet issues?

Optimizations needed

- turn off second camera until it’s necessary
- removed altos to gain some performance. will possibly revisit this

Shadow distance in render pipeline - what is ideal?

Do I need the depath texture and opaque textures on?

Enabled Tools for Frame Debugging in Render Pipeline - disbale these if not needed! 

Turned off features of Render Camera to try and get some performance

Optimization

• **Gfx.WaitForPresent**: When the main thread is ready to start rendering the next frame, but the render thread has not finished waiting on the GPU to Present the frame. This might indicate that your game is GPU bound. Look at the Timeline view to see if the render thread is simultaneously spending time in Gfx.PresentFrame. If the render thread is still spending time in Camera.Render, your game is CPU bound and e.g. spending to much time sending draw calls/textures to the GPU.

![Untitled](March%209%206ca6200d922949ec88ff22bf96a80ffc/Untitled.png)

Semaphore.WaitForSignal
17.28ms

Current frame accumulated time:
17.42ms for 21 instances on thread 'Main Thread'
324.38ms for 102 instances over 11 threads

I am GPU bound - need to find out why

[https://docs.unity3d.com/Manual/ProfilerGPU.html](https://docs.unity3d.com/Manual/ProfilerGPU.html)

---

## From: May 1 c961aec9c03746a78be78173fa67c457.md

# May 1

Navmesh scanning work again with beta 61 of A star pathfinding. 

Attemping to get enemy movement working on the Ouroboros Level

Error found! Make sure nothing is at Scale 0, parents or childs, when something ownt move around. Scale 0 can cause this issue !

Movement seems rough / restricted on the ourobors

Need to try smoother surface and see if that works better for navmesh

May stick with that for visuals as well? A/B it and see

Made a Navmesh Testing scene to nail movement on difficult objects. Needs further testing - enemies are floating away from the mesh

Start with Basic example scene - alter to get appropriate behaviour

---

## From: May 10 d86fe672aad94d98994f6b30d6e5a1e8.md

# May 10

Need to do an overview of ideas and progress past month or so 

Get an idea of mechanics design / technical limitations

Ideation on level designs

Ouroboros

- kill required amount while travelling in loop
    - 3 phases

Seraphim 

- kill required amount before you’re devoured
    - 3 phases

Yggdrasil

Seperate game into component parts to more easily move things between scenes?

Metatron Level

Made a Mesh Processor script to automatically generate prefabs at intersections of a mesh

Will turn these into waypoints for Cinemachine 

IDEA

Player just moves around circles while enemies move around complex straight lines?

Using Cinemachine Path for straight lines - No need to use CInemachine Smooth Path in these scenarios!

Have a PathDetector script for character movement that looks at direction of reticle and tries to choose the best path. Somewhat working, but needs fine tuning and debugging.

Need to colour code junction points and available paths for player, too many currently, would be wonky am imprecise.

Enemies movies around on the whole navmesh, but not sticking to it. Can I lock there verticality and only do 2D movement in this scenario? Look into this.

---

## From: May 11 aa66d9071c9946cf9c33d060c0ed3e0a.md

# May 11

Integrating Dreamtek Forever - Collider Run with recent game components for test of travelling level

Integrated controls - roughly! 

Needs polish, but can move through the tunnel. 

Looking into some optimization methods for A* and enemies falling off

Trying all of this 

[https://arongranberg.com/astar/docs/optimization.html](https://arongranberg.com/astar/docs/optimization.html)

Adding

---

## From: May 16 3cb913db9061453f97a1d249ccfd9b61.md

# May 16

Enemies appear to be working on mesh! Kinematic was enabled and this was the problem. 

Need to be more organized to have this information available easily, can reference own files to know if I’ve done everything I can. 

Have the browsing functionality in chat gpt and tried AGAIN to make a datamosh effect but it wasnt a much better situation 

Enemies sticking to mesh, need to fix projectile pooling

Partially fixed thsi pooling, but still seem to build up a lot of inactive projectile game objects.

Need to look closely at when it’s released from the system, how it’s recycled

Can gain performance here i think, or at least memory?

Can I set specific spawn points for specific waves? 

Addressed this with Trigger Volumes! Seems to be working fine

[Optimization Tips](May%2016%203cb913db9061453f97a1d249ccfd9b61/Optimization%20Tips%2095ef9e3b01c1490d869a2aa3567924b8.md)

Added Conditional Debug static class

---

## From: May 17 451c7269371e4c369c230a21303c34dd.md

# May 17

[Game Design Tools; Cognitive, Psychological, and Practical Approaches Summary](May%2017%20451c7269371e4c369c230a21303c34dd/Game%20Design%20Tools;%20Cognitive,%20Psychological,%20and%20P%2094fd60e9282d48c2a73941138709fbb1.md)

[Game Design Tools; Cognitive, Psychological, and Practical Approaches Exercises](May%2017%20451c7269371e4c369c230a21303c34dd/Game%20Design%20Tools;%20Cognitive,%20Psychological,%20and%20P%20********************************.md)

Is there anything in Projectile Toolkit I could use?

---

## From: May 18 c7f4ad55ba864f52b102679ab1bf16f1.md

# May 18

Trying a recompile of the game in release mode to see how it affects performance

Wondering if this will flag editor scripts? 

Made no difference, need to place editor scripts in editor folder, not properly ignored otherwise

Thinking about protagonist

Evangelion - angels - you are a repurposed piece of metatron

Intro section / learning mechanics  - could be a version of these levels

A piece of the tower that has been repurposed to help reassemble itself

Build works!

Made adjustments to ground check script, work better with some errors. Added UI Lock on effect. Added script to Shooting to prevent it from going through the ground. Likely needs adjustment. Successfully fixed errors to allow a build. Attempted fix with errors for lock on when ground is in the way, seems to be working. Need to adjust projectile behavior - not hitting enemies most of the time, behave too erratically. Would improve gameplay loop drastically if fixed.

---

## From: May 19 c9ee55fc1b6246ff81cc032e9f774040.md

# May 19

Removed parent settings from projectile - seems unecessary now

Working on a redo of Projectile class

Made state based - not complete , lock on not working, need to look at it more. 

X ray effect ideas

[Highlight Plus](https://assetstore.unity.com/packages/tools/particles-effects/highlight-plus-134149)

Probably the best approach

[X-ray Stealth Vision in Unity Shader Graph and Universal Render Pipeline](https://www.youtube.com/watch?v=eLIe95csKWE)

Time rewind seems to screw up enemies know their position (getnearest planting them firmly on mesh)

Need to look into if it’s a one off or actually causing problems.

---

## From: May 2 8c45b812e5d54053bba2230b931757fe.md

# May 2

Object Placer - Script
May need to implement this - has a ALign to Surface feature - needs to look at player and do the same?

Clamp them to the navmesh using e.g. AstarPath.active.GetNearest. See AIPath.ClampToNavmesh for an example.

Fixed triangles problem on mesh scanned, used a more detailed mesh, but this did not solve the falling issues exactly. Seems better though?

Tried ClamptoNavmesh script in A* and it cause problems with enemy movement, getting stuck.

Looking into this advice

Clamp them to the navmesh using e.g. AstarPath.active.GetNearest. See AIPath.ClampToNavmesh for an example.

Cannot get Torodial working, not sure why! Something to do with the game object? What am I missing? 

GOT IT WORKING WOOO

Need to change these to enemies and get shoot and scoot working

Or another attack / move combo

Ouroboros 3 - have music and other things working. Best template so far - can refence others for minor issues

Some things to fix

- Enemies are shooting but not moving
- Player running anim direction gets wonky - why?
- Crosshair Reticle sideways and wonky - why?

_ Moonlight - check firewall and port forwarding for UDP 47998, 48000_

---

## From: May 20 2001d2490a2e41bcbacb8ed2ac572edf.md

# May 20

ProjectileStateBased is partially working

Can lock on and they do initial PlayerShotState behaviour, but not changing target / shooting at enemies

Need to fix the way PlayerShotState works

Started X ray effect but not finsihed, will get it working tomorrow

---

## From: May 22 ********************************.md

# May 22

Implementing the X Ray URP feature as a good start to the day

[X-ray Stealth Vision in Unity Shader Graph and Universal Render Pipeline](https://www.youtube.com/watch?v=eLIe95csKWE&t=1s)

Implemented on enemies. Didn’t do the animated version, worth considering!

Will alter look of the effect

Projectiles now working! Refining movement for better accuracy

Simplified by removing 

Rewinding time messes with Enemy Dodeca script that places movement directly on the navmesh (stop from flying off)

Testing this as well - is the error and actual problem?

<aside>
💡 Bug!

</aside>

Time Rewind can cause enemy to fall off - is a real issue! should I freeze enemy position in time?

Maybe if it goes on too long - need time rewind restriction again?

Attempting to fix projectile movement, but enemy gimbal making things difficult to see, so fixing this first. This appears to be fixed now, may need some adjusting.

For setting dolly points on the camera transition, added the following event which appears to be logically working - increment dolly on camera switch

![Untitled](May%2022%20********************************/Untitled.png)

Looking into issues of direction / roll of player not being accurate when switching to another structure

Seems like it’s Horitonal Rotate that needs to be reset to default values, adding this in the transition 

SOmething is setting rotation values on horizontal rotate at start of transition, not sure what! But need to track this down and fix the issue 

Editied PlayerFacingForward script so that it properly resets horizontal platform to 0 rotation, working now. 

<aside>
💡 Bug!

</aside>

Checking GroundCheck on Player to see if it’s causing player to fly off screen when transition happens. It is! Ground check needs improvement, disabled for now. 

<aside>
💡 Idea!

</aside>

Re-lock on needed for taking out most enemies? Kind of boring to maintain lock on, means you have less to do. Enabling this and movign forward with it

Physics.Boxcast taking a lot of resources - is this just a health collecatable mode issue or issue in geenral?

Issue - Some Projecitles are not hitting their TargetCube targets AND they’re not dying after an enemy is already dead / lose their target. Add these options in the script

Groundcheck fixed up a bit. Seems better

I think issues exist in Launchback with recent Projectile errors

Mayeb not! Seems like related, but moreso about reuse of a bullet, setting details/states appropriately

THINGS TO DO

Fix Death / FX of Enemies - Parts are busted

---

## From: May 23 8b930eadf610457da08ccb7dea8897f8.md

# May 23

THINGS TO DO

Fix Death / FX of Enemies - Parts are busted 

Enabled Funnel modifier on enemies - may have perofrmance impact - keep in mind!

Adjusting and cleaning up PorjectileStateBased a bit

Projectiles adjusted, seem to be hitting properly now 

Looking for good animated snake asset for transition

Using dummy snake head for now

Tried adding time slowdown for enemy death, not working. It’s in enemy basics script

Maybe do rewind instead? Implemented this - works oK!

Enemies bug out though - bug found the other day

Disabling Rewindable to see if that helps? DOES NOT

I think I need to disable movement while time rewinds - may require them to not be rewindable as well

Make a call to the RVO Controller to make the units Locked while time rewinds?

Unlock when time is normal

Also disabling rewindable to help this

Looks like this works!!

Need to fix enemy death, here’s the error

![Untitled](May%2023%208b930eadf610457da08ccb7dea8897f8/Untitled.png)

FIxed these, need some adjustments - not ideal effects and maybe too big

<aside>
💡 Bug!

</aside>

Need to resolve this projecitle issue

![Untitled](May%2023%208b930eadf610457da08ccb7dea8897f8/Untitled%201.png)

Recent bug report, this seems to be the solution 

![Untitled](May%2023%208b930eadf610457da08ccb7dea8897f8/Untitled%202.png)

Projectiles are not set to kinematic, so unsure why this is being flagged

Playing with lighting settings, trying to get something more interesting happening.

Need some well places lights around structures for a more dynamic look 

<aside>
💡 Bug!

</aside>

Seems I may need to have a check on time rewind on death, seems if two happen too close together it causes an infinite loop 

Also setting a hard 60 fps limit in game. This is on manager - DemoGameInit

This also sets up resolution and other things on start, may want to change this

Disabling ground check on Shooter Reticle movement because it’s more problems caused than solved

---

## From: May 24 19221765fa9a403fa970969735ffbc76.md

# May 24

Made adjustment to a script for Snake Head movement in Ouroboros level, could be useful to general timed movement. 

Added Ground collision on main camera, seems pretty good!

Also sped things up, trying to balance some gameplay things better

Do I need a ricochet / shield element?

Made a mistake with this and created an element where you can lay traps basically

<aside>
💡 Idea!

</aside>

Seems cool! Keeping this in mind for now - happens when target cube remain target of bullets, they still swarm around it

<aside>
💡 Idea!

</aside>

Key idea - feels like the bullets are chasing you. Important to have proper balance between speed of movement / speed of bullets to achieve this effect - feels like you’re moments from being hit

---

## From: May 25 17661e805a6a41b1be1283eaa1d48e50.md

# May 25

Need a better lock on indicator with enemies

- Changing lock on cube to be wireframe

Projectile Death could look better 

Fix the rewind of player on enemy death 

- Done

Disabling Gimbal script to see how movement is affected. May need some restructuring

Adjusting Enemy collision to continuous, in attempt to allow kill sphere to work

But also, this might help with falling off structure

- Didn;t work!

Adjust order of this - seems to have fixed my issues!

![Untitled](May%2025%2017661e805a6a41b1be1283eaa1d48e50/Untitled.png)

Need to reconsider if stationary enemies are what I want or not, as it may not be necessary

---

## From: May 26 67c42136b420403eab563ad6d9584817.md

# May 26

Enemies dying when i dont even shoot some of the bullets

fix this! is it from falling off or is it bullet collisions

---

---

---

---

---

Metatron level

- move through making different shapes
- path changes colour as you move laong it, make certain shapes
- with each wave, change the dolly shape a player moves through

Tube as inbetween thing on section of level, possibly before boss as well

Performance in tube allows critical hit on boss? 

Killing all bullets when wave ended - done in Level Debugging

Some errors turning up throughout level

Error at 303

```csharp
// Error catching assertions.. not included in build
            if (_numberOfAliveParticles < _aliveParticles.Length)
                UnityEngine.Assertions.Assert.IsTrue(_aliveParticles[_numberOfAliveParticles].remainingLifetime < 0.001f);
            if (_numberOfAliveObjects < _aliveObjects.Length)
                UnityEngine.Assertions.Assert.IsTrue(_aliveObjects[_numberOfAliveObjects] == null || !_aliveObjects[_numberOfAliveObjects].IsInitialized);
        }
```

![Untitled](May%2026%2067c42136b420403eab563ad6d9584817/Untitled.png)

![Untitled](May%2026%2067c42136b420403eab563ad6d9584817/Untitled%201.png)

Error occuring at 158

```csharp
// Error catching assertions.. not included in build
            if (_numberOfAliveObjects < _aliveObjects.Length)
                UnityEngine.Assertions.Assert.IsTrue(_aliveObjects[_numberOfAliveObjects] == null || !_aliveObjects[_numberOfAliveObjects].IsInitialized);
```

Still having enemy time rewind issues

![Untitled](May%2026%2067c42136b420403eab563ad6d9584817/Untitled%202.png)

Order of customAIPathAlignedToSurface does not seem to improve this

Need alternative method for handling time rewind for enemies.

---

## From: May 29 22d654f8ad8f4947b476a35b631d7990.md

# May 29

Making Crosshair script more efficient, can see some issues in the profiler

Largely minor improvements

Also looking into job leak issue, full stack trace leaking enabled

Updating to latest A* Pathfinding for potential perforamnce improvements - 

contains this - helpful?

[FollowerEntity - A* Pathfinding Project (arongranberg.com)](https://arongranberg.com/astar/documentation/beta/followerentity.html#isTraversingOffMeshLink)

Bug - wont rotate right! Why not? Unsure…

![Untitled](May%2029%2022d654f8ad8f4947b476a35b631d7990/Untitled.png)

Had falling off navmesh issues again - seems fixed now. Remember - all was rescan and save graphs when updating A* Pathfinding. WIll have issue otherwise

Improved Ground Check script, appears to working much better now.

though no error, seems on time rewind enemies are glitching out. 

Turning on rewindable to see if it changes anything?

Seems a bit better, but seems if two enemies dies at once it screws things up

Or SOEMTHING is still happening, causing the enemy to teleport

Need to look into this

---

## From: May 3 dbf950e284274b2594e0fec4db2d77e5.md

# May 3

RECAP

Ouroboros 3 - have music and other things working. Best template so far - can refence others for minor issues

Some things to fix

- Enemies are shooting but not moving
- Player running anim direction gets wonky - why?
- Crosshair Reticle sideways and wonky - why?

1) Enemies moving - but fly off easily. Need to clamp them to navmesh properly. 

Using Advice from here to try and fix

["constrain inside graph" problem](https://forum.arongranberg.com/t/constrain-inside-graph-problem/11605/2)

Constrain inside graph does not seem to completely work - likely due to the way it’s implemented

Buto 64 not working - wait for Buto 65 for this to be fixed in several days

2) 

3) Crosshair reticle issue appears to be dependent on Gameplay Plane’s starting position, using Test Waypoint in fully upright position as the starting Dolly Waypoint appears to fix this problem.

---

## From: May 30 8c27e9786ce54d88ba686147033d5de5.md

# May 30

Overhauling Dolly system

Introducing Curvy Splines and adding them to Ouroboros level

Way more functional than cinemachine dolly

Can use them for Behavior Designer movement patterns as well 

Successfully implemented

- Now time freezes for you, technical error but feel like this may be a good idea?
- Allows you to look around a bit

Due to camera movement changes, i am adjusting reticle/shooting position based on z value form player, instead of z value from camera. 

Add disolve effect to projectiles upon death

shader swap? Look at the VFX unity asset that you found

---

---

May 31

Fixed reticle by attaching to game object - need to make look a bit better

Possibly fixed enemies falling off grid using fixed update - more testing needed

Check their movement - affected by this?

Add volumetric clouds for snakes to come out of ?

Using smoothDeltaTime in palce of delta time, may be better for Chronos / TImeline use

Target cube approach might not be working out well, projectiles getting stuck on occasion.

Laucnhable Bullet Locked are getting shot at targetcube’s, why is this? Look at ProjectileStateBased breakdown for answers

Updated Targetcube script to bring in line with other updates

Setting dissolve material, shaders and and more to make projectiles look nicer

alternate color when shot is not working, issue with alpha channel of flatkit shader

eliminated errors with velocity on kinematic object being set - new error that is no longer allowed fully, not needed anyway

Enemy Shooting particles not pooling correctly, need to take a close look 

![Untitled](May%2030%208c27e9786ce54d88ba686147033d5de5/Untitled.png)

Change material on mobius defrom 2 from glaalxy material 9 to see it easier

---

## From: May 4 405a5d8bbf5f4b6fb7f1954d4e8def21.md

# May 4

Updated Buto to fix any issues

May need to reinitialize in scenes again 

Buto Fixed! 

Added a script that attaches reticle spinning to song tempo with adjustments for axis, time, and rotation amount

Added a HUD effect called LockOnEffect - attempting to recreate Panzer Dragoon’s lock on effect. May be interesting! May not be the right approach - need to look into it. 

Time rewind not working properly in Ouroboros level - im not moving backwards - find out what’s up!

---

## From: May 5 26faf16e807441e1bd851d6cf30975da.md

# May 5

After some tehcnical issues- back in action!

Some bugfixing needs to be done. 

**Navmesh Test Scene - Ouroboros 3 - More complex Behaviors**

<aside>
💡 Bug!

</aside>

Character not rotating properly? or animation not changing properly? 

- Appears to fix itself, but slowly. Issue occurs when in total backwards view, character veers in a different direction, to the side a bit. Not an animation Issue
- Suspect this is related to script which tilts player chracter according to ground position, need to verify

Need to free up the Y value of the camera - cant look up enough. Important in this level especially 

Trying this with Cinemachine Camera Blending

Never found a good way to do it, trying to just script it all now 

Adjust elements to be the scaled down size of this scene as well. Many things were made for a larger scale - radar, projectile calculations, etc

<aside>
💡 Bug!

</aside>

**Ground check** doesn’t appear to be altering the height of the player game object - look into this 

Also only operation on forward facing at the moment. Caused problems when in reverse, need to program this to work in all four directions. 

**CameraFollow -vcam1 script**

Made some improvements to the camera system, can look around more freely, removed jittery aspect of that. 

Added a zoom in and out as the reticle looks up and down, but needs to be fine tuned, a bit wonky at the moment but usable. 

Made an ouroboros version of bullet, but needs fine tuning for this scale. Still wraps around enemies aimlessly at times. 

Need to fix enemies to navmesh better, waiting on response from Aron for this, but may hack together my own solution to do appropriate testing.

---

## From: May 8 ca93e96214ca4270878c429b9b88a944.md

# May 8

**Navmesh Test Scene - Ouroboros 3 - More complex Behaviors**

Integrated Aron Granberg’s feedback on how to get AI to stick to Navmesh

StickToNavmesh script on enemies

Sort of working? They seem to stick but behavior can be odd, stuck in places, model appears to be flashing at times. Monitoring….

Seems like it’s intermittenly flashing the position, so this is buggy approach. 

When I disable tick to navmesh script still having flashing position issues - interesting!

Something else is the issue

Projectiles are rarely coming into contact with enemies when fired at them. Enemies are not really firing projectiles at me either. Need to adjust values, get balanced. 

<aside>
💡 Bug!

</aside>

Seems like some of the positioning / rotation of elements on enemies is causing the flashing, due to floating point precision. Zero out or round off some of this for performance

Disbale StickToNavmesh script to see if it was the issue - didnt appear to be! 

Trying out a new Projectile script to clear things up

Error with Playter Target being set to null - disbaled some code to fix this 

```csharp
//This was causing issues with the player target being set to null constantly - need a fix!
        //else if (other.gameObject.CompareTag("PlayerTarget") && !locked)
        //{
        //    projectileTarget = null;
        //    Debug.Log("Hit PlayerTarget - Projectile Target should be set to null now");
        //}
```

Setting up another mobius strip

Figuring out workflow again

Unity

- Prefab Painter for waypoints - make sure rotation is take into account

Then use WaypointGenerator to convert those to CinemachineSMoothPath points

Also made WaypointVisualizer script to help with adjusting Roll on any waypoint

Spawn Points not working properly, need to dig into Spawn Master again and see what’s up

---

## From: May 9 e0e5fbd0b07c4a62865ec3bf2ed18888.md

# May 9

Spawner was not working properply yesterday, trying different approaches

Added Spawner Target script to Player and Shooter to use nearest methods in Ultimate Spawner

Spawnable Items was broken in recent update! RTFM - Documentation is important!!!

Completely reinstalling Ultimate SPawner  + Waves add on

Seem to have it working better now! Stick to Navmesh is way too processor intensive though. 

Trying a singelton manager approach for some efficieny, waiting on Aron for more tips. 

A* Not moving issues - after scanning graph - save to cache! Important!!

Doesn’t exist at runtime otherwise

Need to look at Birth/Death Particles again 

- All screwed up!

Not using Recast graph in current iteration of Ouroboros level - no longer need to have a prefab disable in the scene for graph attachment? Testing this out 

It worked! No need to include in scene

Trying a Gimbal-like approach to movement, due to the jerky nature of the Navmesh movement. 

Using Gimbal script on enemies. 

Added upper bound to Gimbal script, so that the child object quickly moves to new location of parent if they get too far. Not sure it’s working but may be good to workshop.

Issues with level design - moving around mobius strips its very difficult to have enemies to attack all the time

Maybe attack the Snake Head on the strip?

Have a lot of Dodeca’s everywhere, but make the main goal attacking the snake head? 

Trying to do interest AI stuff but it may just be easier to setup strict waypoints for this level, possibly others? 

Need to experiment and think this through. 

Added Snake head to Mobius Strip - placeholder / testing

Idea, snake head slowly moving back and forth on it’s tail, maybe use way points, may a bit of dotween smoothness

---

## From: November 3771dc7bc53b4ede9bf2b86b23be0c37.md

# November

Nov. 4th

Worked on how to create UV maps in Blender

Tex Tools is great - high poly / low poly models, in same location. 

Easy way to create Normal Maps

Look into other maps it can create, may be useful

Smart UV wrap is better in new version of blender, so may work well for now

Plan out levels - Ophanim approach? 

move through rings, enemies coming at you from front / back 

Maybe some shots happening from sides but not part of the waves - static enemies

Move through several rings - enter interior cube - eyes on all sides

Shooting? Take out each eye

DESTROYED

Move to next one?

REMEBER - background must move if current player position isnt

Boss? Ophanim - from a different perspective

More complex, more to deal with 

Ouroboros

Move between multiple snakes that are static in ouroboros form

take out enemies on them

Move to moving snakes, taking out enemies on adjacent snakes backs

travelling - arrive at destination - a boss

the snakes that represent forward and backward time

Nov. 5th

Lighting theory for Games Video

Diffuse Maps - what is it?

Keep lighting separate when possible, you dont want two lights affecting the same area

Know what you want first = work out how later 

Get lots of references!! and settle on a key concept/style

![Untitled](November%203771dc7bc53b4ede9bf2b86b23be0c37/Untitled.png)

[https://jakehicksphotography.com/quick-tips](https://jakehicksphotography.com/quick-tips)

![Untitled](November%203771dc7bc53b4ede9bf2b86b23be0c37/Untitled%201.png)

![Untitled](November%203771dc7bc53b4ede9bf2b86b23be0c37/Untitled%202.png)

In general, mixing diffuse colors is not a good idea

Color banding issues, need to watch out for (also called color bleeding)

Neutral colors help avoid these issues

They also can handle both cold and warm lighting

Nov. 6th

Figured out how the datamosh stuff works / why it wasnt working in my project

Have a few variations saved in Beat Remake now

Nov. 7th

Figuring out needed associations for Player Plane as prefab object

Moved Buto to effects category, added script to parent it to player on awake

Probably need to get rid of the shooting thing where it reverses for the Camera

Expect it’s causing issues 

Consider a

BUG!!!!!!!!!!!!!!

Idea: World is run by machines and not human free will - Deluze and Foucault

[Deleuze - Control Societies & Cybernetic Posthumanism](https://www.youtube.com/watch?v=Hu4Cq_-bLlY)

Machines - Systems of information processing that interpret and remake the world according to their particular logics. Ex. Society is social, politcal, and more machines. 

Machines - a system of interruptions - a break in the flow in relation to the machine it is connected, but at at the same time is a flow itself

work on inputs → produce outputs

The process informations

Outputs of one machine are the inputs of another (usually)

Societies of Sovereignty, discipline, and control 

Discipline - regulate bodies - movement and time enclosed - restricted

Self-regulated - Self-discipline - voluntary to do this - put obvious why you would

The state only interferes with your lie when you’re breaking the rules 

Steering and Cybernetics

cybernetics means systems that use feedback loops to interpret information and elimate deviation over time

Behaviour Steering Machines

![Untitled](November%203771dc7bc53b4ede9bf2b86b23be0c37/Untitled%203.png)

NOv. 8th 

Making this Black Hole Shader

[Remaking the Black Hole Shader from Outer Wilds! | Unity Shader](https://www.youtube.com/watch?v=hNkPHPhzXVA)

Not working in 2022.3 Unit URP LTS, raised issue with dev, will see if I hear back. 

Played with kejiro’s du tone and Aqua effect

Got them into project

Input Actions - Icons of different inputs

[https://github.com/simonoliver/InputSystemActionPrompts](https://github.com/simonoliver/InputSystemActionPrompts)?

Good for controllers - settings - setup

Shader info 

[https://www.mayerowitz.io/blog/a-journey-into-shaders](https://www.mayerowitz.io/blog/a-journey-into-shaders)

Unity SOLID talk - Programming principles

[https://www.reddit.com/r/Unity3D/comments/17icly2/how_using_solid_changed_my_whole_development/](https://www.reddit.com/r/Unity3D/comments/17icly2/how_using_solid_changed_my_whole_development/)

[https://www.youtube.com/watch?v=eIf3-aDTOOA](https://www.youtube.com/watch?v=eIf3-aDTOOA)

GPU Particle System

[https://assetstore.unity.com/packages/tools/particles-effects/ultimate-gpu-particle-system-131718](https://assetstore.unity.com/packages/tools/particles-effects/ultimate-gpu-particle-system-131718)

VFX structuring in Unity

[https://www.reddit.com/r/Unity3D/comments/17bksum/what_is_the_right_approach_for_managing_vfx_and/](https://www.reddit.com/r/Unity3D/comments/17bksum/what_is_the_right_approach_for_managing_vfx_and/)

Alternative Pathfinding Option - GOAP

[https://assetstore.unity.com/packages/tools/behavior-ai/cap-n-3d-pathfinding-using-goap-258946?aid=1101l96nj&pubref=oct23tools&utm_campaign=unity_affiliate&utm_medium=affiliate&utm_source=partnerize-linkmaker](https://assetstore.unity.com/packages/tools/behavior-ai/cap-n-3d-pathfinding-using-goap-258946?aid=1101l96nj&pubref=oct23tools&utm_campaign=unity_affiliate&utm_medium=affiliate&utm_source=partnerize-linkmaker)

Dynamic Mesh Cutter - alternative to RayFire?

[https://forum.unity.com/threads/dynamic-mesh-cutter-a-flexible-run-time-mesh-cutting-solution-discussion-thread.1249558/](https://forum.unity.com/threads/dynamic-mesh-cutter-a-flexible-run-time-mesh-cutting-solution-discussion-thread.1249558/)

Unity Event Bus - implement into my project?

Reflect transparent materials in mirrors etc

[https://github.com/DMeville/RefractedTransparentRenderPass](https://github.com/DMeville/RefractedTransparentRenderPass)

Nov. 11th

Some effects not working in void area when in certain camera positions - investigating

Swirl - and like others, the issue is Render modee - Horizaontal Billboard

Billboards can be problematic with certain camera angles

Adjust to mesh render mode?

Worked!  Mostly!

Now having duplicate key erros with mesh scan A* (i suspect)

Need alternate sphere model i suspect

Seemingly fixed this sphere problem with al sphere model

Looking into why enemy ai are not movign around now

Need to ensure that moving assignment of FMOD to Awake in enemies does not cause errors

Issues with enemies not moving - seems affected by navmesh cuts and recalculate normals on the A star was the problem! At least according to a rebuild based on Ouroboros (that works)

That was the problem!!

Annoying 😟

Found issue with unity crashing

Has to do with Enemy Particle object spawner on emeies and what pools are assigned to it

Containers assigned in scene - maybe cant be assigned to itself?

Issues with projectile render layers - putting void projectile on default due to this

Crashing again! Seems projectiles aren’t moving - problem something to assign

Death Particle system missing from projectile

Nothing checked after it’s shot

Weird behaviour from bullet in void level - no differences found to account for why just yet

THings to try - original standard bullet

Not helping! Need to break down whats going on in the one working project - Ouroboros

Why does it appear as if the bullets are bound within a box?

![Untitled](November%203771dc7bc53b4ede9bf2b86b23be0c37/Untitled%204.png)

Bullets are free to move when I get rid of the navmesh - why????

THis would make sense with the Ouroboros working, because those levels are encapsulated in a box by their form

Unity Crashing - Hard to know whats happening, possibly a Particle System Issue

Removing Hot Reload to see if it is contributing

All bullets are shooting downward now - unsure why? 

THink maybe i Need to remove bottom from halfsphere as well, may be confusing things?

THe bullets are all getting trapped inside here

NOv 13

Why are there box colliders on the dollys? Is this causing an issue? 

It seems not!

Really hard time trying to diagnose this! Need to try other structures

Need to build a whole new level directly from Ouroboros level

Flipped normals for interior sphere works!!

Can refine this

Exterior Black hole

Interior Black Hole

Maybe interior of black hole is very dark, lock on to bullets lights things up?

Nov. 14th

Running through Cursor coder suggestions

[Small List of Suggestions from Cursor](November%203771dc7bc53b4ede9bf2b86b23be0c37/Small%20List%20of%20Suggestions%20from%20Cursor%2010e89b71ec3848adba5f267fa888cd0e.md)

[Big List of Cursor Suggestions](November%203771dc7bc53b4ede9bf2b86b23be0c37/Big%20List%20of%20Cursor%20Suggestions%20d497fcc7d478486091f8876625d3614e.md)

Trying to add ability for projectiles to bounce

Not working - need to investigate further

Made many small improvements to main scripts

Added Game Manager Singleton script to take care of score, will handle more as well in time

ISSUE - Trying to put shooter movement on it’s own global clock / timeline and it’s not working

Need to review this - if I do this, it should mean smoother movement - test and see!

Can Time.deltaTime work at all? Concurrently with Chronos? Need to investigate

Making a Miro Board

Created Light Flasher script, for void level. Looking at timing a light to the beat. 

Could also time a light to locking on / shooting enemies

Need to configure lighting properly

Implemented basic dodge

Need to update logic so holding dodge doesnt just continually allow you to dodge!

Need to add Vibrate on dodge - added using open source Nice Vibrations!

[1. Nice Vibrations developer documentation](https://github.com/Lofelt/NiceVibrations/wiki/1.-Nice-Vibrations-developer-documentation#haptic-components)

Nov. 19th

May need to go back to old rotation script from yesterday or day before.  Adjustments needed for Player Movement rotation . Bug testing!

Fixed, but the player game object is rotating slightly as the game progresses, after several rotations

[Boomerang X Abilities List](November%203771dc7bc53b4ede9bf2b86b23be0c37/Boomerang%20X%20Abilities%20List%20cb6d59f175a640a8ba25d0d594b6fcb6.md)

Nov. 30th

[Dreamscaper: Killer Combat on an Indie Budget](https://www.youtube.com/watch?v=3Omb5exWpd4)

Key Areas to focus on

- Establish Strong Pillars - define feeling of player experience and turn that into actionable things
    - find the right level of specificity “player should feel highly skilled, an assassin, godlike,”
    - if the pillar is too general, the target area is too wide
- Dreamscape Combat Pillars
    - Purposeful Action
    - Improvisational - player freedom, multiple approaches
    - Tough, but fair
    - Dynamic Interactions - create unique situations and solutions
    - Strong Feedback - sound, vfx, aniamtion should do this, make it feel good.
- Player Options
    - options create depth, but also complexity - “mental juggling”
    - cosnider core audience for level of complexity
        - whats appropriate? mid to hardcore for Dreamscape
        - Depth without complexity?
            - Swappable weapons would be an example
    - Consider your pillars
        - How do these options move you towards your design targets?
        - How is it implemented?
            - Dodge - invincible with generous length would be an example
    - Consider your scope
        - Can you hit your quality bar?
        - Is it too big for your team?

Camera

- be careful of camera-genre expectations
    - this will influence player expectations
- Distance, interactivity, and scope

Not every decision will move you towards all goals at once, so find alternatives ways to emphasize parts you feel are neglected

Stack systems for richer combat

Elemental systems used that can be stacked for critical hits

Hit Reactions & Enemy States

Why set up enemy states?

- increases interactivity
- provides added utility and depth to player actions

Some states

- Stun & Stagger
- Flyback - space between player and enemy, resets offensive situation
- Popup - enemy in juggle state, helps encourage offensive action in defensive players
- Knockdown - helps with shifting focus between enemies

Bringing it altogether

- Flyback combined with knockdown for example
- Popup (uppercut) increased damage as continues

Adds depth without complexity

High payoff for player here

Enemy States

Large degree of states adds interactivity

Flow - state interruptions, recovery timing, stun locking

The way these work influence purposeful player action, target selection, and more

Players have to learn how to manage enemies around them

Challenge

- not all enemies have all hit states, stronger ones immune to some
- adds element of progression to the game

Game Feel

Responsiveness

- input response time / input latency
- Animation blending
    - Shorter blend times feel more responsive
- Locomotion response
    - reduce acceleration period to increase responsiveness
- Favor player input
    - players can interrupt various actions
        - players break out of almost any action with a defensive response

Animation

- Rely on core principles of animation
    - 80s Disney book on animation a good reference

Important principles

- Anticipation and Follow through
- Secondary and overlapping action
- Slow in / Slow Out - helps create beliable weight
- Exaggeration & strong posing
- Translate Forward - bake some translation into the move, not just in place

Enemy animation

- Make the output of their action as readable as possible (telegraphing)
    - Clear poses
    - Strong Anticipation
    - Sound Design
    - Visual Effects
    - Proper Hierarchy
    - Bake recovery time into the animation, feels more natural
- Players must realize they have the information they need to defeat the enemy by watching them, judging their attacks and making choices
- Creating enemy animation is not the same as player animation, which is to make things FEEL as good as possible

Hit Reactions

- Instant Reaction - no blending, happens right away
- Exaggerated Poses
    - rotate enemy towards direction of hit instead of animating every possiblity - probably good enough!
- Hit Reactions sell strength of player action!
    - One size doesnt fit all, larger enemies might respond differently
- Variety sells interactivity

Cheat for your player 

- Aim Assist
    - recommended to make these configurable, players may need a stronger aim assist
- Magnetism
    - makes sure the attack hits an enemy
- Hit Impulse
- Input Buffering
    - register early inputs for combos. be generous so players can land complex moves easier
- Generous Hit Boxes

Juice it up - game feel!

- Hit Flash
- Hit Reaction
- Hit Impulse
- Hit Stop / Time Dilation
- Hit VFX
    - Power, type and style of attack important consideration for the fx
- Screen Shake
    - screen shake on player much less than screen shake hitting an enemy
- Controller Feedback
- Beefy Audio
- Procedural Enemy Shake

May be important to allow player to turn all of these things off. Makes it more accessible?

General Keys To Success

- view work through a neutral lens
    - divorce personal attachment, focus on what player experiences
- trimming doesnt mean eliminating
    - think features, MVP of some of these
- focused on goal, rather than solution

Test often and Iterate Cheaply

- Need a feedback pipeline
- Open to testing drastic changes
- Do just enough to evaluate outcome of these

Invest in workflows that use prior knowledge

Leverage outside assets

build on others learnings

Loading Scenes - best ways to do so?

[I beg you: Dont use the buildindex for loading Scenes](https://www.reddit.com/r/Unity3D/comments/1888oax/i_beg_you_dont_use_the_buildindex_for_loading/)

Added Flow Fx - try it out in game

---

## From: Optimization Tips 95ef9e3b01c1490d869a2aa3567924b8.md

# Optimization Tips

https://www.youtube.com/watch?v=ZRDHEqy2uPI&t=1s

Here's a summary of the optimization and performance tips from the video:
1. **Memory Management**: Avoid frequent instantiation and destruction of objects as it's generally slow and can lead to memory fragmentation. Instead, use object pooling to recycle objects.
2. **Garbage Collection**: Minimize the work for the Garbage Collector by reducing the number of destroyed objects. This can be achieved by using object pooling.
3. **Data Structures**: Choose the right data structure for your specific use case. For example, indexing in an array or a list is cheap, but adding or removing elements is more expensive than doing it in a dictionary.
4. **Scriptable Objects**: Use Scriptable Objects to store unchanging data. This can help reduce memory usage as only one copy of the data is stored.
5. **Properties with Getters and Setters**: Avoid using properties with getters and setters in tight loops as it can lead to performance issues. Instead, use Define Symbol or Preprocessing Directive.
6. **Resources Folder**: Avoid using the Resources folder as it can affect the startup time of the game. Instead, use the Addressable Asset System.
7. **Empty Unity Functions**: Remove any empty Unity functions such as Start(), Update(), Awake(), etc. to avoid unnecessary performance overhead.
8. **Heavy Initialization Logic**: Avoid heavy initialization logic in Awake() and Start() functions as it can delay the rendering of the first frame. Instead, activate or instantiate objects right after the first frame is rendered.
9. **Accelerometer Frequency**: Adjust the Accelerometer Frequency to the minimum possible value if your game doesn't require it. This can save CPU processing time in mobile platforms.
10. **Moving GameObjects**: If you want to transform an object that has a Rigidbody component, use the rigidBody class methods instead of transform.Translate().
11. **Adding Components at Runtime**: Avoid adding components at runtime as it can be inefficient. Instead, use Prefabs.
12. **Finding GameObjects and Components**: Avoid using GameObject.Find() and GetComponent() frequently as they can be slow in complex Scenes. Instead, cache the reference to the object or component.
Remember, these are general tips and might not apply to every situation. Always profile your game to see if these optimizations make a significant difference.

https://www.youtube.com/watch?v=EK8sX8oCQbw

Here's a summary of the optimization and performance tips from the video:
1. **Asset Importing Tips**: Optimize import settings for assets like textures and meshes. For textures, use the minimum possible value for the Max Size setting, ensure textures are Power Of Two (POT) in size, atlas your textures, remove Alpha Channels on non-transparent textures, disable Read/Write Enabled option if you don't need to access the data from the texture from code, and examine if a lower format like 16-bit is enough instead of a larger 32-bit color format. For meshes, optimize the mesh, use more aggressive mesh compression, disable Read/Write Enabled option, disable rigs if you're not using animations, disable Blendshapes if you're not using them, and disable Normals and Tangents if your material won't use them.
2. **Graphics Tips**: Use batching techniques to reduce draw calls to the GPU. Dynamic batching helps reduce draw calls on moving objects, and static batching attempts to batch GameObjects with the Static flag enabled. Disable shadow casting for objects whose shadows won't add any relevant detail to the scene. Use culling masks for lights to ensure each light only affects objects in specific Layers. Avoid mobile native resolution if it causes performance issues, and lower your game's default resolution with Screen.SetResolution() function.
3. **UI Tips**: Hide invisible UI elements to prevent unnecessary calculations in rendering. Separate your UI elements in different Canvases based on how frequently you update them. Make sure that only your Canvases that are interactable have the Graphic Raycaster component. Disable the camera rendering the 3D Scene, hide other covered UIs, disable the Canvas component, and reduce the frame rate if possible when displaying full-screen UIs.
4. **Audio Tips**: Set your audio clips option to be forced to mono to save memory space and disk space. Reduce the compression bitrate if you need additional compression. Unload any audio-related source from memory or any audio clip from memory when the game is muted.
Remember, these are general tips and might not apply to every situation. Always profile your game to see if these optimizations make a significant difference.

https://www.youtube.com/watch?v=tsGmWvf7I6c

Here's a summary of the key points from the video titled "Optimizing Mobile Games in Unity" by Alexander Zhdankin:
1. **Environment Shader**: The speaker discusses the use of a single shader for different textures and lightmaps. He mentions that the shader is optimized and takes up only 32% of the total frame time on an iPhone X. He also mentions the use of Xcode's GPU snapshot feature to analyze shader performance.
2. **Shader GUI**: The speaker talks about the use of Shader GUI for inspecting shaders. He mentions that it's useful for creating custom controllers and making the workflow easier. He also talks about the use of Shader GUI to check if textures have alpha and to compile a simpler version of the shader if they don't.
3. **Shadow Optimization**: The speaker discusses the importance of shadow optimization. He mentions that shadows typically require a lot of calculations and can be heavier than drawing environment textures. He talks about their custom solution to use a shadow mask, which is faster than the traditional approach.
4. **Character Shader**: The speaker discusses the creation of a character shader. He mentions that they started with a surface shader and then modified it to suit their needs. He also talks about the importance of optimizing shaders for different devices and adjusting device settings accordingly.
5. **Visual Effects**: The speaker discusses the importance of optimizing visual effects, particularly in relation to overdraw. He talks about the use of simple shaders that do one thing well, rather than trying to create a "mega shader" that does everything.
6. **Bloom**: The speaker talks about the importance of bloom in their game. He mentions that they use a lower resolution buffer for bloom and manage without depth testing. He also discusses a custom approach to bloom that reduces overdraw and improves performance.
7. **Custom Rain Effect**: The speaker discusses the creation of a custom rain effect using a vertex shader. He mentions that they pre-bake a large number of quads and store them in a bundle. They then use the vertex shader to give each particle different lifetimes and speeds.
8. **Android Profiling**: The speaker recommends the use of Snapdragon Profiler and GAPID for Android profiling. He mentions that these tools can provide useful information for optimization.
9. **Quality Settings**: The speaker discusses the use of different quality settings for different devices. He mentions that they try to determine the best settings automatically based on the device's capabilities.
10. **Learning from Mistakes**: The speaker acknowledges that not everything went perfectly in their optimization process. He mentions that they experienced frame drops on different devices and that some effects needed optimization. However, he also highlights their successes, including the game's good performance on most modern phones and the large amount of content they were able to include.

https://www.youtube.com/watch?v=GuODu4-cXXQ

Here's the summary of the video "Unite Copenhagen 2019 - Practical Guide to Optimization for Mobiles" by Unity:
1. **Debugging and Profiling**: Use Unity's built-in profiler to understand where your allocations are coming from and fix them. For example, excessive use of Debug.Log can cause significant memory allocations. To avoid this, you can create a custom logging class that wraps Debug.Log and use conditional compilation to strip out debug logging from the final build.
2. **Garbage Collection**: The Unity garbage collector can cause disruptions in gameplay due to its "stop the world" approach. To mitigate this, consider unloading all resources when transitioning between scenes, allocating a pool of objects for reuse during gameplay, optimizing frame time as much as possible, and enabling the incremental garbage collector. The incremental garbage collector, introduced in Unity 2019, splits the work across multiple frames, reducing the impact on gameplay.
3. **GPU Optimization**: Reduce the stress on the GPU by minimizing the number of unnecessary rendering operations, reducing the amount of data sent to the GPU, minimizing the number of state changes, and optimizing expensive shaders. Unity's Frame Debugger can provide a breakdown of all the draw calls that compose your final frame, helping you understand why certain draw calls couldn't be batched with previous ones.
4. **Level of Detail (LOD)**: For objects in the background or far from the camera, consider using lower levels of detail. This can significantly reduce the number of triangles rendered and thus the load on the GPU.
5. **Memory Footprint**: Understanding your requirements and using the correct settings can significantly reduce your memory footprint. For example, for large audio files, consider using the "streaming" load type instead of "decompress on load" to reduce memory usage.
6. **Asset Bundles**: Be aware that Unity's built-in shaders cannot be explicitly included in an asset bundle and will be implicitly included in every asset bundle that contains a material referencing the shader. To avoid this, download a copy of the built-in shaders, rename it, add it to its own asset bundle, and fix all materials to use the new renamed shader.
7. **Automation**: To avoid human errors, consider using Unity's Asset Postprocessor to automate the process of setting correct settings for your assets.
8. **General Tips**: Always profile on the target device, profile early and often, apply fixes one at a time, and follow the optimization triangle: optimize your assets, update fewer things, and draw less stuff.

[https://www.youtube.com/watch?v=YOtDVv5-0A4](https://www.youtube.com/watch?v=YOtDVv5-0A4)

Here's a summary of the key optimization and performance tips from the video titled "Alba: A Wildlife Adventure - Optimizing for Mobile" by UsTwo Games:
1. **World Generation**: The game uses procedural generation for terrain and features. However, the team found that as the project progressed, developers were hesitant to use these tools due to the disparity between the low-resolution input and the high-resolution output.
2. **Performance Challenges**: The game faced significant performance issues halfway through development. The team had to prioritize performance optimization, focusing on worst-case scenarios and catching spikes in frame rate, not just averages.
3. **Profiling Tools**: The team used a variety of profiling tools, including Unity's built-in tools (Profiler, Profile Analyzer, Frame Debugger, Memory Profiler) and platform-specific tools (Xcode Frame Debugger, Instruments Time Profiler, Instruments Game Performance). These tools helped identify areas of high expense.
4. **Foliage Rendering**: The team initially used imposter-based LODs for foliage rendering, but found them to be too expensive on mobile devices due to transparency breaking hidden surface removal on tile-based renderers. They switched to a new art direction for trees involving no transparency and pure geometry.
5. **Multifaceted LOD System**: To manage the high number of AI animals, NPCs, and interactive objects, the team created a multifaceted LOD system that controlled visibility of animators, enabled/disabled audio, and specific script behavior. This system was accelerated by Burst and Jobs.
6. **Hard-to-Profile Areas**: The team found certain areas, like the photography mechanic, hard to profile. They discovered that rendering the game twice (once for the main world and once for the camera view) was too expensive and opted for a single render on low-end devices.
7. **Miscellaneous Improvements**: The team made several other improvements, including disabling unused Cinemachine Virtual Cameras, turning off Physics.autoSyncTransforms, precompiling shaders, and optimizing asset import settings.
8. **Automated Profiling**: The team set up an automated profiling system that would regularly check performance on target devices, ensuring that performance wasn't regressing.
9. **Device-Specific Optimizations**: The team made device-specific optimizations, disabling real-time shadows and post-processing on low-end devices, while allowing these features on high-end devices.
10. **Lessons Learned**: The team emphasized the importance of having ambition informed by reality and driving optimization by profiling. They also highlighted the importance of using profiling tools to identify and address performance issues.Regenerate responseChatGPT may produce inaccurate information about people, places, or facts.

---

## From: Resonance Ascension 78f79231f2ed4ef99e667c7667b6edd0.md

# Resonance Ascension

Title: Resonance Ascension

Story:

In the beginning, the world was in harmony, and music was the language that connected everything. The people of this world built the Tower of Babel, a colossal structure representing the unification of sound, as a way to create the perfect symphony and reach God. However, the tower was struck by divine intervention, shattering it into pieces and scattering its musical essence throughout the world.

Setting:

Resonance Ascension is set in a surreal, ethereal world where music and rhythm dictate the flow of life. The environments are a blend of celestial and terrestrial elements, with levels taking players through the remnants of the Tower of Babel, now shattered across time and space.

Levels:

1. Descent - The player begins their journey at the ruins of the Tower of Babel, with the goal of collecting its scattered pieces.
2. Echoes of Eternity - Venturing through a labyrinth of time loops and musical echoes, the player must navigate a space where time and sound are interwoven.
3. Requiem of the Fallen - Set in a graveyard of fallen angels, the player must confront ancient guardians protecting the lost fragments of the tower.
4. Celestial Realm - Ascending into a realm where angelic beings reside, the player must overcome trials of musical precision and harmony.
5. Rebirth - The player rebuilds the Tower of Babel and witnesses its destruction once more. The levels are then remixed, offering new challenges and altered landscapes.

Bosses:

1. Ophanim - A multi-winged, wheel-like being with eyes covering its body. It attacks with lasers that move to the rhythm of the music.
2. Ouroboros - A serpentine creature that encircles the player, creating an ever-shrinking arena. Its attacks are wave-based and must be dodged to the beat.
3. Seraphim - A colossal, six-winged angel that uses its wings to generate destructive soundwaves, testing the player's ability to maintain the rhythm.
4. God's Architect - The enigmatic being responsible for the creation and destruction of the Tower of Babel. It attacks with a combination of all previous bosses' abilities.

Ending:

Upon defeating the final boss, the protagonist transcends their mortal form and becomes an ethereal being of sound and light. They learn that the true purpose of the Tower of Babel was not to reach God, but to inspire the world through the language of music. The protagonist weaves together the fragments of the tower, creating an everlasting symphony that spreads throughout the universe.

As the music echoes into infinity, the protagonist discovers the truth: the cyclical nature of life, death, and rebirth is intrinsically connected to the ebb and flow of sound and silence. In the end, the player's journey represents a new beginning, with the everlasting symphony serving as a testament to the resilience of art and the power of music to connect all living beings.

---

## From: Rez Analysis a9b6367f4da44d0aaeb196d14ce0e7f3.md

# Rez Analysis

**Area 4**

Enemy Types

![Untitled](Rez%20Analysis%20a9b6367f4da44d0aaeb196d14ce0e7f3/Untitled.png)

group patterns of 4 / 6 / 8

![Untitled](Rez%20Analysis%20a9b6367f4da44d0aaeb196d14ce0e7f3/Untitled%201.png)

bigger enemy, more hits, group of 2 to 4

![Untitled](Rez%20Analysis%20a9b6367f4da44d0aaeb196d14ce0e7f3/Untitled%202.png)

![Untitled](Rez%20Analysis%20a9b6367f4da44d0aaeb196d14ce0e7f3/Untitled%203.png)

All in first 1 min 30 seconds!!!

![Untitled](Rez%20Analysis%20a9b6367f4da44d0aaeb196d14ce0e7f3/Untitled%204.png)

Many of these emerge from larger guy above

![Untitled](Rez%20Analysis%20a9b6367f4da44d0aaeb196d14ce0e7f3/Untitled%205.png)

These come in two parts from left and right of screen

Join together

Take out the lock and destoryed

Variations of this throughout

Level before boss encounter takes 5 minutes

**UI**

Many cool UI FX when locked on, moving aim reticle around, look at these

![Untitled](Rez%20Analysis%20a9b6367f4da44d0aaeb196d14ce0e7f3/Untitled%206.png)

![Untitled](Rez%20Analysis%20a9b6367f4da44d0aaeb196d14ce0e7f3/Untitled%207.png)

These explosive lighting FX - what are they? Can fog / light recreate this?

![Untitled](Rez%20Analysis%20a9b6367f4da44d0aaeb196d14ce0e7f3/Untitled%208.png)

Game systems

Shoot enemies - maximum 8 lock ons

No dodge

collect power ups that evolve your form

overdrive bombs that destroy everything - are these collectable?

---

## From: Rhythm Ascension 5f47e6850a5b41848986a4bda6a95d8a.md

# Rhythm Ascension

Title: Rhythm Ascension

Setting: A surreal, futuristic world where the remnants of the Tower of Babel, an ancient structure that once attempted to use the power of music and art to reach the divine, lay scattered across the land.

Protagonist: An enigmatic, silent figure known as The Seeker, who possesses the unique abilities to manipulate time and harness the power of music.

Plot:

Level 1: The Fall of Babel
The game begins with a cinematic of the Tower of Babel being destroyed. The Seeker is mysteriously drawn to the ruins and embarks on a quest to rebuild the tower using fragments of the once-great structure.

Level 2: Echoes of the Past
The Seeker traverses a desolate landscape, collecting musical fragments and battling rhythm-based enemies, including the first boss, a seraphim-like creature called Echorythm.

Level 3: The Timekeeper's Domain
The Seeker enters a clockwork realm where the power to manipulate time is strengthened. Here, they face the second boss, Ophanim, a powerful guardian angel made of gears and machinery.

Level 4: The Infinite Loop
The Seeker navigates a level designed like a Mobius strip, symbolizing the cyclic nature of life and death. They encounter the third boss, Ouroboros, an enormous serpent that represents eternity and the never-ending cycle of creation and destruction.

Level 5: The Ascension
Having gathered enough fragments, The Seeker reassembles the Tower of Babel. This level features vertical gameplay as they ascend the tower, battling against gravity and waves of angelic adversaries.

Level 6: Requiem for Babel
As the Tower reaches completion, it is destroyed again, causing the world to be reformed with remixed levels. The Seeker must navigate these familiar yet altered landscapes, confronting the previous bosses in their evolved forms, culminating in a battle against a colossal, Evangelion-inspired angelic figure named Archisynth.

Level 7: Resonance
With the defeat of Archisynth, The Seeker is transported to an ethereal plane where the essence of the Tower of Babel resides. They must confront the final boss, Harmonia, the embodiment of music, art, and creation itself.

Ending:
Upon defeating Harmonia, The Seeker merges with the entity, transcending their mortal existence. They become a celestial being, responsible for maintaining the balance between sound and silence, life and death. The remnants of the Tower of Babel disperse throughout the universe, creating an eternal symphony that connects all living beings to the divine through the power of music.

---

## From: Sept 1 39b128da45964517a5d904608723aad7.md

# Sept 1

looking at best way of controller character movement

trying all sorts of gravity tricks and things

now attempting just using A* to have the player follow the reticle

Works pretty well! I think there’s a refined version of this that is THE solution!

Double check on ‘Constrain Inside Graph attribute - can I use it? Pretty sure it’s not meant for this 

High setting of Funnel Modifier requires recalculating normals on navmesh. Good? WOrth it?

Try gravity for bullets or other things? 

Cool design

![Untitled](Sept%201%2039b128da45964517a5d904608723aad7/Untitled.png)

Sept 4th

Looking into leaks in code

‘You should look for the `CreateForwardPlusBuffers` method in your code (or potentially in Unity's universal rendering pipeline code if you're using that) and make sure any `NativeArray<T>` objects are being properly disposed of. If you're using Unity's universal rendering pipeline and this method is part of Unity's code, you might need to update to a newer version of Unity or the universal rendering pipeline package, as this could be a bug that's been fixed in a newer version.’

‘

You should look for the `CreateForwardPlusBuffers` method in your code (or potentially in Unity's universal rendering pipeline code if you're using that) and make sure any `GraphicsBuffer` objects are being properly released. If you're using Unity's universal rendering pipeline and this method is part of Unity's code, you might need to update to a newer version of Unity or the universal rendering pipeline package, as this could be a bug that's been fixed in a newer version.

Also, consider using Unity's Memory Profiler to get a better understanding of your game's memory usage and potentially find other memory leaks [docs.unity3d.com](https://docs.unity3d.com/Packages/com.unity.memoryprofiler@0.2/manual/workflow-memory-leaks.html).

Given that the memory leak is connected to the Unity's Universal Render Pipeline (URP), it's worth noting that there have been reported issues of memory leaks with URP in the past [issuetracker.unity3d.com](https://issuetracker.unity3d.com/issues/urp-memory-leak-when-in-play-mode). If updating Unity or URP doesn't solve the issue, you might need to report this to Unity's issue tracker.’

Forward+ Renderer appears to have memory leak issues, may need to follow up on new version of Unity LTS that addresses this problem. 

Switched to Direct X 11 and the memory leaks went away - likely a Unity issue

Switch haptics to the interhaptics razer suite? Added but maybe not working properly

May be unity issue, supposedly store is down

---

## From: Sept 18 fe324058ac7c4ab2a41df85199ca7c31.md

# Sept 18

Fixing up proper BTRemake so it has improvements from jumbled one

Things needs to do 

- Fix lock on
    - This is layer or tag based issue, there are a few similar
- Turning and player flying away
    - Making adjustments to spline to help prevent fall off of main character,  issue with turning at certain places - perspective turning that is - might need to tighten how turning works so player doesn’t get stuck
    - Sphere collider restricting movement space worked! try this again
- Fix transparency issues on aim cube
- Fix positioning of UI
- Mobius 2 Variation has enemies falling off - look into this!
- May need to export dolly/waypoint placement from Beat Remake to BTRemake, had made some adjustments I think
    
    

Open ‘Beat Traveller’ from 4TB Hard drive to see if my appropriate render pipeline is still intact there

Trying to rebuild old pipeline, can use a general URP pipeline but want all my old render features and more

Not working, think i have to just rebuild render pipeline. Not the worst! Have the files within 

<aside>
💡 Should the highlight transparent queue be opaque or transparent? Check on this

</aside>

<aside>
💡 SRP batcher causing graphical errors - can i use it? can i fix it? off for now

</aside>

Added beautify 3 - play around with this for visual enhancement!

IDEA

Music - is movement - is action - is ?

movement forward / backward/ left / right 

dodging / moving resemble b boy dance moves?

Spikes in garbage collector - what is it?

- Every time you call Debug.Log, or an error or warning is triggered (that's how you get the whole path of "how did we even get to this function?" when you click on a console log), a stacktrace is performed, which in large numbers is a big performance hog and GC generator. Do you have some Debug.Log's happening that you don't need?

Added logo image - need to clean up a bit

---

## From: Sept 20 3e7877758a0d498ba6a9bd5ab352315d.md

# Sept 20

Hang Line GDC talk

[Moving Mountains: Speedy Level Creation as a Desperate Indie Dev](https://www.youtube.com/watch?v=XK-yTqYAD-c)

Potential interesting strategies to game development 

[Fake Grimlock: Win Like Stupid](https://readwrite.com/fake-grimlock-win-like-stupid/)

Simple unique aspect to mechanic - build entire game around this

Building levels quickly

- automatic mesh generation
- applying noise to these meshes

Layering game mechanics

- Made a chart of various mechanics
    - Goats can kick, objects are destructible, what if goat can kick objects? This kind of approach
    - How can various currently isolated mechanics interact (layer) to give the game greater complexity? (Meaning more things can happen)

How to make a good level

- Tutorials
- Variety
- Theming

Tutorials for Mechanics

- How to teach player organically
    - Within a grouping, first level teaches mechanic, next few levels reinforces, then next levels push that mechanic further to its limits
    - Ex. Goats kicking itnroduced, then you encoutner many goats, then you get to levels where rocks can fall and crush goat, goat can hit something and kick it into something else, etc
- Made a colour coded chart of every mechanic, and all levels, and seeing which levels use which mechanics, to see the pacing and how much any of them are used
- Introduce mechanics faster at start of the game, later in the game not as necessary to move at that pace

Variety

- No single part of the level should repeat in the same way later
- Change from large spaces to tighter spaces
- Vary difficulty of sections of the level
- Main path in level, but have several side paths
- Create challenge through optional item pickups

Theming

- At least one unique and memorable element in each level
    - Why does this level exist? Does it add anything new to the current set of levels we have?
- Could a player describe the level to their friend without using its name?

Level building process that allows rapid iteration is very helpful!

Bioshock  - How to do linear level design

Conveyance - communicating with the player

Referenced script on TargetCube missing, fixed this

Investigating camera options for better reticle look around movement

Replaced camera with Cinemachine Free Look - this might work way better!

Comparing with RDR Mix and Jam file

Need to make spherical movement work - hard lock reticle ?

Adjust AimRotationSphere location to roughly camera position - much better!

Need to restrict left/right distance or adjust properties of reticle movement to fit this new movement setup

Adjusted a bit better now - still needs refinement! Getting there!

---

## From: Sept 21 9a0efa1527024d4c8b2fc339ef45e388.md

# Sept 21

Idea - boomerang / axe throw with projectiles but you have to dodge / shield them when they come back

[Recreating God of War's Axe Throw | Mix and Jam](https://www.youtube.com/watch?v=M-3P59GtRW4)

Looking at adding a dodge mechanic

Or maybe dodge + parry?

[Recreating Batman Arkham's Freeflow Combat | Mix and Jam](https://www.youtube.com/watch?v=GFOpKcpKGKQ&t=1s)

What if the proximity of the bullet decides the action? 

Using Sensor Toolkit as a basis of this

Range Sensor

Issue - bullets not dying upon impact with player? 

Box Collider was too low, more approrpiately placed now.

Continuing Range Sensor work for bullets

- deleted projectileslowsphere
- delted ground collider

Trying to implement dodge and parry

Batman,Sekiro,and Tunic all do something like this

- Allow hold to function  longer block?
- Tap would function as parry if at right time

Pathway for Dodge Parry control partially setup

How should this work? Should parry be its own state?

- Parry meaning it flies back towards the enemy who shot it

Dodge will likely be easier to implement

- character moves away from in coming bullet
- timing based - use dotween?
- 
- 

—LIGHTING—

Watching how to setup lights in Unity

 

[Lighting tutorial: 4 techniques to light environments in Unity | Unite 2022](https://www.youtube.com/watch?v=DlxuvvYZO4Q&t=32s)

Some errors occuring when baking lighting

![Untitled](Sept%2021%209a0efa1527024d4c8b2fc339ef45e388/Untitled.png)

Bake lighting experiement taking a while - try it overnight

Sept 26th

Fixed Cinemachine camera movement - attached ot reticle movement

Looking at enemy lockon - issues bouncing between different enemies - not sure why this happens?

Adjusted lock on enemyLockInterval from 0.5 to 1

In OnLockEnemy, Adjusted RaySpawnEnemyLocking.transform.lossyScale / 2 to RaySpawnEnemyLocking.transform.lossyScale / 4

Seems better! Need to play and test

Now looking at reticle placement with rotating camera movement

Needs work, expecially when looking backwards

AimRotationSPhere parenteed to camera seems like an issue, putting it back on gameplay plane

This helps, investigating camera movement issues now. When nothing moves, camera movement seems appropriate distance from reticle and character. 

Is it a camera physics issue, that movement causes it’s to get close when aimed backwards?

Manually adjusting Z value of Shooting when platform rotates, to multiples of itself to adjust for thigns moving off a bit. Seems to work fine! May be the permanent solution. This is done in player movement where platform rotation is handled

On a better track with camera movement now it seems, but way too sensitive to movement

need to dampen and try things

Use this video for reference on what to try 

[How to use Cinemachine's Free Look Camera | 3rd Person Camera in Unity](https://www.youtube.com/watch?v=XT6mUlpO4fA&t=1s)

Looking at fixing my highlight render feature now 

Not sure what’s wrong yet - need to investigate more

Sept 27th

RenderFeature set wrong! For opaque layer when it should have been transparent

Highlight fixed

Need to dial in materials for this - for different objects like enemies vs bullets

Added UModeler X for AI texturing, but thinking this might be better in blender? May be case by case basis for these things. Need to go through tutorials

Sept 28th

Attempting to fix some camera issues. Want to keep player and reticle in frame always

Camera Target Group for Look At is working, but camera movement is a bit much

Increasing dead zone for all rigs, may help this - it didnt!

Lowered that significantly and camera is a bit better

Camera may be ‘good enough’ currently - needs more work though

Hard to play needs more adjustments!

Fixed RenderOnTop for Reticle. Need seperate ones for differnet objects, currently doesn’t looking great

Adjusted the  Reticle Spin because it wasn’t working on disable / enable. May work now but probably need to adjust this for unregister / register for events

Daniel Ilett has some good videos, maybe good for adding interesting things

Notes from build

- better camera control
    - Need to be able to look up more!
    - Adjust Clamp values in Shooter Movement to allow for this?
- better death animations for enemies
    - does unscaling the particle effect allow it to play properly? it may not be playing due to the time skipping upon enemy death?
        - No, not seeing it now? Not sure whats up
    - current effect is ok, may need more to it
- better death animations for bullets
    - unscaling time for this too
    - current effect is jank, need to look for others

Pitch, yaw, and roll damping appear to be a large part of the speed of rotation. 

Fine tune these so that it’s not too slow / not too fast

Player flies off of second stage - refine path - this will fix it

Blast of coloured light like rez for some death effects? 

VFX graph solar flare? look for ideas

Change Birth/Deaht particles on enemy to prefabs and reference them? More efficient

Unity Muse has good suggestions for efficiency and pooling for this

Need URP versions of Shapes FX packs

Also need to look up data mosh reddit stuff - recreate

Need to look at SHMUP boss and other toolkits as well - any usefulness ?

Triadic color palette for setting up game?

[Triad Color Palette Generator | Toptal®](https://www.toptal.com/designers/colourcode/triad-color-builder)

UI

[Interface In Game | Collection of video games UI | Screenshots and videos](https://interfaceingame.com/)

Designing UI - turn everything off and turn on one by one to see if you need things, and where to put them. 

Whats a game that puts information like health around your cursor?

Dont use more than 3 fonts in a game! 

Tween tool seems interesting

[Just launched PrimeTween - my high-performance tween library with an extremely simple but powerful API. Create thousands of animations, delays, and sequences with zero KB of memory allocations! Check out the performance tests in the first comment! FREE on Asset Store.](https://www.reddit.com/r/Unity3D/comments/16vdwmn/just_launched_primetween_my_highperformance_tween/)

Effects to use in Beat Remake

Level with this as mechanic?

![Untitled](Sept%2021%209a0efa1527024d4c8b2fc339ef45e388/Untitled%201.png)

Other advanced dissolve effects also look good for this

Need to consider collectables in the game - what for? where might they exist?

mirza beig - - psys loop plexus from ultimate vfx - good for enemy death?

Lots of interesting mesh effects in shape fx 1 and 2, not sure where they are most applicable

Sept 30th

Enemy Death effect 

- psys loop organica
- netOcean-4k
- plexus

Projectile Death

- Sparks Plexus
- turbulent impact

Other fun ones

- psys loop horizon - for background?
- psys loop hyperspace
- serenity
- starfall
- savezone 3
- loop gravitron
- super galaxy
- logo dissolve

Make adjustment to overall scene lighting or skybox when enemy dies? 

Tried this in Destroy script - doesnt seem to work. 

May be better to adjust Filter attribute 

Can uni bullet hell be used with my object particle system shooting? Or will i use more than one system for bullets, same bullet prefab

[Uni Bullet Hell](https://assetstore.unity.com/packages/tools/integration/uni-bullet-hell-19088)

CMF Advice

[Caldera Interactive on Twitter / X](https://twitter.com/CalderaInteract/status/1708888222980510001)

October 2nd

Fixing paths of ouroboros

- Weird camera behaviour on Mobius Tube 6 - rotating for no reason
    - Taking that out of rotation for now
- Minor issues with Mobius Tube 2
    - Keeping in rotation, just need to fix
- Multi Mobius 1 and Mobius Tube 3 disabled due to not being round enough - i think
    - Can I round these out? Do something with them?

Disabling Player Movement Extension because I don’t think it’s working. I think I need to integrate it into A* movement of player. 

Trying to adapt snake movement for new movement style of player 

- giving it a shot - untested

Bullet type idea

- Lock on, splits into multiple bullets
- Need to approach these differently

How can i break down my systems in more meaningful / deep ideas?

- Can on-rail traversal be looked at in a few different ways?
- Would a slower photo-mode / sentry mode be useful?
    - Place something down before you go full on in the level? What is the musical equivalent?

**From CDJ Music standpoint**

Cue Points and Loops:

Players can set, recall, and clear cue points.
Auto/manual looping functions for creating loops of varying lengths.
Hot cues allow for immediate playback from predetermined points.

Allow player to setup cue points in a level? 

Ableton Features - Inspiration here?

Ableton Live is a popular digital audio workstation (DAW) known for its versatility and ease of use, especially in live performance settings. Here are some of its basic features:

1. **Session View and Arrangement View**:
    - **Session View**: A non-linear grid for recording and playing back audio and MIDI clips in real-time.
    - **Arrangement View**: A traditional, timeline-based interface for arranging, recording, and editing music.
2. **Clip Launching**:
    - Ability to launch audio and MIDI clips independently or in sync, allowing for real-time performance and composition.
3. **Warping**:
    - Real-time time-stretching and pitch-shifting of audio, enabling tempo and pitch changes without affecting the other.
4. **MIDI Sequencing and Virtual Instruments**:
    - Extensive MIDI sequencing capabilities for both hardware and software instruments.
    - Comes with a variety of built-in virtual instruments.
5. **Audio and MIDI Effects**:
    - A wide range of audio effects (e.g., EQ, reverb, delay) and MIDI effects (e.g., arpeggiator) for sound processing and creative manipulation.
6. **Audio Recording and Editing**:
    - Multi-track audio recording and comprehensive audio editing features.
7. **Max for Live**:
    - An integrated platform for building custom instruments, effects, and tools within Ableton Live.
8. **Instrument and Effect Racks**:
    - Combining multiple instruments and effects into a single rack for easier control and more complex sound design.
9. **Automation and Modulation**:
    - Real-time recording of parameter changes, automation curves, and modulation for dynamic sound changes.
10. **Browser and Collections**:
    - Easy browsing, previewing, and importing of sounds, samples, and effects.
    - Collections for organizing and quickly accessing favorite or frequently used items.
11. **Built-in Audio and MIDI Setup**:
    - Comprehensive audio and MIDI setup with support for a wide range of hardware.
12. **Export and Sharing**:
    - Export options for audio, video, and stems, with integrated sharing features for collaborating with other musicians or sharing your work online.

These features make Ableton Live a powerful tool for musicians, producers, and live performers, allowing them to create, produce, and perform music in a fluid and intuitive manner.

Using prefab generator to try and make a basic structure for player to walk across on the snake. Most of the way along, hope to figure this out tomorrow. 

If this doesn’t work - could disable the movement maybe when on snake? Re-enable for other structures? Not worth spending too much time on the movement maybe

May need to create a U like structure to box player in on the snake, so they dont fall off

Not a bad idea! Can function as prefab!

Oct. 3rd

Thinking about this today - issues due to movement of bones in all dimensions, affects path length

if i lock Y and Z values of the animation, does that help? 

- path length still changing - but is it more managable?

What if I just locked the x? 

NOT WORKING

What is working? Position and Move mode set to Relative- this works!

Works for movement - Camera struggling with this a bit

Likely need new camera mode for this or to lock some constraints

Rough snake mode working - issues primarily with camera

Need tow versions of enemies - navmesh and on rails (for snake)

Could use behaviour designed on snake but just stick it to set path? 

Magic Light Probes for placing light probes? Or bakery?

Trying MLP first - could be good!

Look into using Bakery+RTPreview

Snake roughly working, enemy that follows a curvy spline setup but just need appropriate Behavior Designer for AI - look at forums and discord for this!

Oct. 4th

Adding Curvy integration package to Behavior Designer

Not really what I wanted

Trying more basic - Behavior Desginer and A* - Patrol implementation

Not really woring either with grid graph. 

Thiking curvy integration for movement would still be better than this

Need to write up proper BD scripts for that

Can just have curvy spline do movement - working!

Seperate attack into BD 

Very basic AI - can get way more advanced

Need to find a way to change forward / backward in spline controller. How is this not obvious? Search forums and discord. Doesnt appear to be an exposed property. 

New method of changing direction that seems to properly compile! 

Test and see

Oct. 5th

Upgraded Buto to 2022 version

Changed light to Bakery setup

Need to figure out why snake teleport script is no longer working

Want to improve outline / detection of bullets

try pro radar builder for this

Cannot get UI target Tracker working properly, finnicky as hell

Pro Radar seems fine for minimap though, may just need to use seperate system? 

Trying HUD Navigation System

HUD Navigation is much better! Feature rich, works great. Stick with the new system. 

Light Cookies fixed in Buto

Need to setup the wave spawn points for moving snakes - figure out how whole snake thing will work

Maybe when you reach the end of the snake wave is over? Requires proper tightening…. refinement on what completing that properly looks like. Feels possible. 

This can be an interlude to several more ouroboros

Can i have a snake look like it’s unwinding from an ouroboros? What does that camera shot look like? You fly off and land on a snake moving forward? 

Flag these distoriton effects

[Distortion Shader Pack 2](https://assetstore.unity.com/packages/vfx/shaders/distortion-shader-pack-2-62426)

Advanced Dissolve really good - new to use this for something!

Level where everyting is dark and bullets are the only visible things?

![Untitled](Sept%2021%209a0efa1527024d4c8b2fc339ef45e388/Untitled%202.png)

What is my game’s vision?

Really simplify what that is

Particle effect not appearing on projectiles - fix this!

Refine projectile look - disable circle mesh or make transparent more? Need a cooler look

Reference other games 

birth/death effects for projectiles - looking for stuff

Some interesting ones!

- Effect_04_chargeshot
- Efect_12_CosmicHorror
- Effect_37_PlanetStone
- Effect_47_PreciseShot

Basically any planet one may be good to use and tweak

Need better way to handle death particles - instantiate in place probably

Do I need P Move in Crosshair script? Remove if unecessary

Reticle lock on not working - fixing this

Position mode needs to be Relative - Relative for snake it seems, but this likely causes problems on the static ouroboros forms. May need to switch between setups ofr these 

Got that working!

Trying to fix camera on snake now - thing it’s weights in camera target group

Maybe a bit of damping?

Adjust weights to equal and damping from 1,1,1, on each access but still bad

Change back? Look into what else needs to be done here

Maybe slower or more static snake for now?

Need to fix camera!

Back to old one - testing…. better in some ways

Need to add line generation back to shooting of bullets

Trailer renderer or something!

Need to make sure camera works for all sections now as well  

Oct. 11th

[Three Steps to Becoming a Better Artist Immediately](https://www.youtube.com/watch?v=amlwcI8dh_g&list=TLPQMTExMDIwMjN_lCXzutHS6A&index=1)

Idea - What is it? Intellectual read

Design - style choices, shape language, proportions

Technique - method of rendering

All three in service of tone - mood, vibe, enforce themes - lighting, colors, cinematography. Feel more than see

Idea, Design, Technique, Tone, Execution

Fuck style - Expression is the ultimate goal!

Checklist for better art

- Stop and think - what are you doing / saying?
- Use reference

A creature is a feature!

- how does move, where does it live, all the things surrounding it

why ami doing this?

what do i want to say?

who am i speaking to?

how can i be most expressive, to reach them best?

REMEMBER - anything can have character - a barrel is not just a barrel!

Think set design

Fill in your design spec - what why who where how etc

Creating art is like arranging flowers

Expressive reference can say what you want to say for you! Use an expressive reference

Fixed movement Player Restriciton sphere! Working now!

Oct. 18th

FPS Encounter design video

DPS (Damage Per Second) Race designs - boring approach

Getting reacquainted with project

Successfully created bounds for player model

so I can edit the rail path for player to more closely match level

<aside>
💡 Also need to look over ground adjustment setup for player height

</aside>

May not be working properly 

Oct. 24

![Untitled](Sept%2021%209a0efa1527024d4c8b2fc339ef45e388/Untitled%203.png)

trying reflective shaders and materials for metatron scene

ghost one

Good talk on FMOD audio!

[The 'TUNIC' Audio Talk](https://www.youtube.com/watch?v=sCIK78OHrIY)

**Oct. 30th**

As i built out other levels, need to properly define prefab for player

Break this out into most basic parts / blocks

---

## From: Sept 6 edf532ce46554c1ca600871fcf015464.md

# Sept 6

Tried using target tracker and improving radar. Frustrating! Maybe off screen targetting is good, unsure, need a good way to test. 

Radar square and radar positioning not working out great 😟

On transition - need to freeze all player x / y / x positions then unfreeze when next wave starts

Currently changed to just disable and re-enable, which seems to work ok!

Need to get further levels setup in unity

Visually build them out a bit

Consider technical infrastructure, such as Metatron’s cube and others

trees / vines ? nav mesh too complicated - simplify? Or try to fully scan the vines?

---

## From: Sept 8 5342a6319d1a4488a0462b44fb219220.md

# Sept 8

Need to fix lock on 

Then look through previous notes

- Fixed, lock on effect working

Making adjustments to spline to help prevent fall off of main character

Largely an issue with turning at certain places - perspective turning that is

Might need to tighten how turning works so player doesnt get stuck 

Attempting to increase enemy and projectile visibility while balancing fog effect 

Attempting to fix player fall off issues. Sphere collider restricting movement space

Seems to work!

Transparency on enemy cube lock on nedds to be fixed 

Mobius 2 Variation has enemies falling off - look into this!

may hve ruined today’s project - go to backup overnight if so!

BTRemake is old version

Beat Remake is new / partially broken one

Check this stuff out and revise if needed!

---

## From: Shmup Principles f8fd6182542e41b2aeb09314fe226b11.md

# Shmup Principles

[Movement, Shooting & Hitboxes [SHMUP WORKSHOP 01]](https://www.youtube.com/watch?v=yAF2FkgyiYM)

No acceleration / decelertation

Add a turning animation - make the speed match

Use follow through animations

Fast bullets - feel more dagerous and powerful

- make sure they’re adequately faster than the player

Fast bullets should be tall, simulate motion blur

Think detailed bullets and splash aniamtions - make them high contrast

Add a shot limit?

- adds risk vs reward dynamics to shooting

Use animations to make some projectile seem more powerful while only increasing effectiveness by a minimal amount

Hurt boxes vs Hit boxes - easier to aim but harder to collide and get hurt

[Visibility [SHMUP WORKSHOP 02]](https://www.youtube.com/watch?v=jYuqPKa0yPE)

Attack types

[Bullet Pattern Design [SHMUP WORKSHOP 03]](https://www.youtube.com/watch?v=ArZRZSYICLo)

Aimed / Static / Randomized 

Dodging types

Micro and Macro 

Think about telegraphing attacks 

How can attack sequences flow into each other?

Think in lanes - what are the lanes of my game?

---

## From: Small List of Suggestions from Cursor 10e89b71ec3848adba5f267fa888cd0e.md

# Small List of Suggestions from Cursor

Here are some suggestions for improving the code and sharing data between the scripts:

1. **Use Singleton or Service Locator Pattern**: If there are some services or data that need to be accessed by multiple scripts, consider using the Singleton or Service Locator pattern. For example, you could create a GameManager Singleton that holds references to common data or services.
2. **Use Events or Delegates**: If there are actions in one script that need to trigger behavior in another script, consider using events or delegates. This can help to reduce coupling between scripts.
3. **Avoid FindObjectOfType and GetComponent in Update or FixedUpdate**: These methods are expensive and can slow down your game if used frequently. Try to cache references to components in Start or Awake instead.
4. **Use Object Pooling**: If you're instantiating and destroying a lot of objects (like projectiles), consider using an object pooling system. This can help to reduce the overhead of constantly creating and destroying objects.
5. **Use ScriptableObjects for Shared Data**: If there are pieces of data that need to be shared between multiple scripts, consider using ScriptableObjects. These can be created in the Unity editor and referenced in your scripts.
6. **Avoid Public Variables**: Public variables can be modified from anywhere, which can make debugging difficult. Consider using [SerializeField] for variables that need to be set in the Unity editor, and properties or methods for variables that need to be accessed by other scripts.
7. **Use LayerMasks for Physics Checks**: If you're doing a lot of physics checks (like raycasts or boxcasts), consider using LayerMasks to only check against certain layers. This can help to reduce the number of checks that need to be performed.
8. **Avoid Duplicate Code**: There seems to be some duplicate code in the OnTriggerEnter methods of the ProjectileState subclasses. Consider moving this common code to the base class or a utility method.
9. **Use Coroutines Wisely**: Coroutines can be a powerful tool, but they can also make code difficult to follow if used excessively. Consider whether there are simpler ways to achieve the same result.
10. **Use Unity's New Input System**: It seems like you're already using Unity's new Input System in some scripts, but not in others. Consider updating all your scripts to use the new Input System for consistency.

---

## From: Symphony of Celestial Rebirth fdb02267e34a4468a47d0db0637f9212.md

# Symphony of Celestial Rebirth

Title: Symphony of Celestial Rebirth

Story and Setting:
In a distant future, the universe is a beautiful symphony of celestial bodies and digital elements. Within this cosmic realm, various species have collaborated to create the Tower of Babel, a monumental structure designed to represent the harmony of the cosmos and the quest for the perfect music/art. **The tower serves as a bridge between the physical and digital realms**, aiming to unite all beings in their pursuit of enlightenment and understanding of the universe.

The story begins with the sudden and inexplicable destruction of the Tower of Babel, scattering its fragments throughout the galaxies. Our silent protagonist, a mysterious figure known as the Maestro, embarks on a journey to collect these fragments and rebuild the tower. Equipped with a unique weapon that harnesses the power of music and rhythm, the Maestro faces various angelic bosses, reminiscent of creatures from Neon Genesis Evangelion, each representing a part of the destroyed tower.

As the Maestro progresses through each level, they learn to manipulate time like a DJ, slowing down and rewinding time to navigate the increasingly challenging levels.

Levels and Bosses:

1. Ophanim's Rings:
The Maestro navigates through a mesmerizing galaxy, following the trail of the Ophanim, celestial beings with rings of divine fire. The on-rail shooter aspect is justified as the Maestro moves along a pre-determined path through the rings, shooting musical projectiles to match the rhythm of the music and avoid obstacles.
2. Nebula Serpent:
In this level, the Maestro encounters a massive, digital serpent-like creature weaving through a vibrant nebula. The on-rail mechanic requires the Maestro to move along the body of the serpent, defeating smaller enemies and avoiding obstacles as they make their way to the serpent's head for the final confrontation.
3. Celestial Choir:
The Maestro enters a cosmic cathedral filled with angelic beings, representing the shattered pieces of the Tower's musical essence. The on-rail shooter element is justified as the Maestro must follow a set path, synchronizing attacks with the music to defeat each choir member and absorb their power.
4. Ouroboros:
In this level, the Maestro faces a massive, cosmic Ouroboros, the serpent that eternally consumes its own tail. The on-rail aspect involves the Maestro running along the ever-looping body of the Ouroboros while battling smaller enemies and overcoming obstacles in time with the music. The boss fight culminates in the Maestro using their time manipulation abilities to break the eternal cycle of the Ouroboros.
5. Tower of Babel Reconstructed:
With all fragments collected, the Maestro rebuilds the Tower of Babel. However, the tower's reconstruction unleashes a cataclysmic force, destroying it once again and scattering the fragments throughout the universe.
6. Remixed Levels:
The Maestro must revisit the previous levels in a remixed, more challenging form, representing the duality of life, death, and rebirth. The on-rail mechanics remain consistent, but the levels feature new enemies, obstacles, and rhythmic patterns.

Conclusion:
Upon overcoming all challenges and defeating the final boss, a celestial being representing the force that destroyed the Tower of Babel, the Maestro discovers the true meaning behind the tower's existence: the balance between sound and silence, life and death, and creation and destruction. The Maestro accepts the impermanence of the Tower of Babel and embraces the eternal cycle of the universe. With this newfound understanding, they release a cosmic symphony that resonates throughout the galaxies, uniting all beings in the harmony of life, death, and rebirth.

---

## From: Symphony of the Cosmos dbdf83baaa8a46598881f05040f9d5e1.md

# Symphony of the Cosmos

Title: Symphony of the Cosmos

Story and Setting:
In a distant future, the remnants of humanity have traveled across the cosmos, seeking to harness the power of divine creativity. They construct the Tower of Babel, an immense structure that houses the ultimate art and music, which they believe will enable them to ascend and communicate with the divine.

As the Tower nears completion, its creators fail to realize the hubris of their actions. The heavens unleash cosmic forces that shatter the Tower, sending its fragments throughout the galaxies. These fragments, each containing pieces of the perfect music and art, corrupt the celestial beings they encounter, morphing them into monstrous guardians.

The protagonist, a mute and enigmatic figure known as the Composer, embarks on a quest to gather the fragments and restore the Tower of Babel. Using their unique spacecraft, the Composer navigates the celestial realms in an on-rail journey through solar systems and galaxies, all while contending with cosmic horrors.

Level 1: Ophanim's Cosmic Rings
The Composer encounters Ophanim, a celestial being composed of numerous interlocking rings. As the Composer's spacecraft traverses through the rings, they must dodge and shoot to the rhythm of the music. Each ring contains a fragment of the Tower. Upon defeating Ophanim, the Composer collects the first fragment and moves on to the next level.

Level 2: Seraphim's Nebula
The Composer ventures into a colorful, digital-looking nebula guarded by Seraphim, a multi-winged, fiery guardian. The Composer's spacecraft is pulled along the trails of fire left by Seraphim, forcing them to dodge and shoot rhythmically. Upon defeating Seraphim, the Composer collects another fragment and continues the journey.

Level 3: Ouroboros' Space-Time Loop
In this level, the Composer encounters Ouroboros, a serpent-like creature consuming its own tail. The spacecraft is caught in the creature's eternal loop. The Composer must navigate the loop and dodge the serpent's attacks, using the time manipulation abilities to their advantage. After defeating Ouroboros, the Composer retrieves another fragment.

Level 4: Rebirth of the Tower
The Composer rebuilds the Tower of Babel, unwittingly causing it to resonate with the cosmic rhythm. The heavens respond, shattering the Tower once more. The levels are remixed, and the Composer must revisit the previous levels, facing more powerful versions of the bosses while the celestial environment has become more hostile.

Final Battle: Celestial Symphony
As the Composer gathers the final fragments, they confront the ultimate guardian, the Celestial Symphony, a divine entity embodying the perfect balance of sound and silence, life and death. In a climactic battle, the Composer's spacecraft navigates the intricate patterns of the Celestial Symphony's attacks, shooting in harmony with the music.

Ending:
Upon defeating the Celestial Symphony, the Composer absorbs the essence of the perfect music and art. The heavens reveal the truth: the divine forces sought to teach humanity humility and the importance of balance. The Composer, now enlightened, transcends their physical form and ascends to a higher plane, becoming a guardian of the celestial realms. The game ends with the reconstructed Tower of Babel standing as a testament to the power of creative expression, a symbol of humanity's capacity for growth, and the delicate balance between life and death, sound and silence.

---
