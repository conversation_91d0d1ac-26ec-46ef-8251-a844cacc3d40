---
title: Hooking Custom Systems into Unity's Player Loop
tags: [Unity, Performance, PlayerLoop, CustomSystems]
date: 2025-01-20
---

# [[Hooking Custom Systems into Unity's Player Loop]]

## [[Overview]]
Unity's Player Loop is a powerful but often overlooked feature that allows developers to inject custom systems directly into the game engine's update cycle. This technique opens up possibilities for creating more efficient and flexible game systems.

## [[Implementation]]

### [[Understanding the Player Loop]]
The Unity Player Loop is a graph-like structure that represents the order of execution for various engine systems. It consists of nodes called Player Loop Systems, each potentially containing subsystems.

```csharp
// Key Player Loop Methods
PlayerLoop.GetDefaultPlayerLoop()  // Returns default order of engine systems
PlayerLoop.GetCurrentPlayerLoop()  // Retrieves current state of Player Loop  
PlayerLoop.SetPlayerLoop()         // Sets modified Player Loop as active
```

### [[Creating a Custom System]]
We'll create an improved timer system that updates every frame without relying on MonoBehaviour.

```csharp
public static class TimerManager
{
    private static List<Timer> timers = new List<Timer>();
    
    public static void RegisterTimer(Timer timer) { /* ... */ }
    public static void DeregisterTimer(Timer timer) { /* ... */ }
    
    public static void UpdateTimers()
    {
        foreach (var timer in timers)
        {
            timer.Tick();
        }
    }
    
    public static void Clear() { /* ... */ }
}
```

### [[Inserting the System]]
To insert our TimerManager into the Player Loop:

```csharp
PlayerLoopSystem timerSystem = new PlayerLoopSystem
{
    type = typeof(TimerManager),
    updateDelegate = TimerManager.UpdateTimers
};

PlayerLoopSystem playerLoop = PlayerLoop.GetCurrentPlayerLoop();
InsertSystem<PlayerLoops.Update>(ref playerLoop, timerSystem, 0);
PlayerLoop.SetPlayerLoop(playerLoop);
```

## [[Best Practices]]
- **[[Domain Reloading]]**: Be cautious when domain reloading is disabled
- **[[Cleanup]]**: Implement cleanup method when exiting play mode
- **[[Static Clearing]]**: Manually clear static data as Unity doesn't guarantee it

## [[Timer Implementation]]
```csharp
public abstract class Timer : IDisposable
{
    protected float currentTime;
    protected float initialTime;
    public bool IsRunning { get; private set; }
    
    public virtual void Start() { /* ... */ }
    public virtual void Stop() { /* ... */ }
    public abstract void Tick();
    
    // Implement IDisposable pattern
}
```

## [[Applications]]
This technique can be extended to various systems:
- [[Data Binding Systems]]
- [[Input Rebinding Systems]]  
- [[Custom Update Cycles]]

## [[Additional Resources]]
- [[Unity Documentation: Player Loop API]]
- [[Understanding Unity's Execution Order]]
- [[Custom Update Systems Best Practices]]
- [Unity Player Loop Deep Dive](https://docs.unity3d.com/ScriptReference/LowLevel.PlayerLoop.html)