# Task 🔨

Created: January 12, 2022 6:42 PM

# Overview

## Problem statement

Describe the problem you're trying to solve by doing this work.

## Proposed work

High-level overview of what we're building and why we think it will solve the problem.

# Success criteria

The criteria that must be met in order to consider this project a success. 

- 

# User stories

How the product should work for various user types.

## **User type 1**

- 

## **User type 2**

- 

# Scope

## Requirements

Current project requirements.

- 

## Future work

Future requirements.

- 

## Non-requirements

List anything that is out of scope.

- 

# Designs

Include designs here or add directly to the Requirements or User Stories sections. 

# Alternatives considered

List any alternatives you considered to this approach. Explain why they weren't used.

# Related documents

Include links to other pages as necessary (e.g. technical design doc, project proposal, etc.)