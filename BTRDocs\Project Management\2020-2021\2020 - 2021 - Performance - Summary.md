# Summary of 2020 - 2021 - Performance.md

This file tracks performance optimization efforts for the project during 2020-2021. Key activities include:

- **Unity Upgrades:** Upgrading to newer LTS versions of Unity (2019 LTS and 2020 LTS) to address bugs and potentially improve performance.
- **Profiling:** Utilizing Unity Profiler and Deep Profiling to analyze performance and identify bottlenecks. Initially, profiling suggested no major performance concerns.
- **Level Optimization:** Addressing performance issues in early levels (Greybox 12) by:
    - Improving building pipelines (exploring Polyfew and TessGen).
    - Using more performant and batchable shaders, including mobile transparency shaders.
    - Optimizing building models in Blender to reduce draw calls.
    - Experimenting with Mesh Combine and Me<PERSON> Baker for scene optimization.
    - Considering Occlusion culling for finalized scenes.
    - Reducing model complexity (vertices and triangles).
    - Implementing LOD (Level of Detail) systems (AutoLOD, Ultimate LOD System).
    - Testing GPU Instancer for prefab instancing.
- **Mesh Optimization:** Investigating mesh memory usage and reduction techniques in Blender.
- **Play Mode Load Time:** Addressing slow play mode loading times.