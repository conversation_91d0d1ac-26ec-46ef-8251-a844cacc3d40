**Summary of Design & Mechanics Related Entries in 2024**

**2022 - Initial Design Explorations:**
*   **Enemy Movement & AI:** Explored spawner systems, dynamic waypoints, and Behavior Designer for advanced AI patterns like "Scoot and Shoot" and "Flank".
*   **Aiming & Lock-On:** Experimented with player aiming and lock-on mechanics, focusing on timing and feedback.
*   **Gameplay Mechanics:** Implemented and integrated Tetris-like mechanics and explored Jelly Cube puzzles.
*   **UI/UX:** Added wave and enemy hints to improve player understanding.
*   **Level & Enemy Design:** Continued experimenting with level design, enemy AI using Ultimate Spawner and Waypoint systems, and geometric/deconstructed boss designs using geometry nodes.

**2023 - Camera & Projectile Refinements:**
*   **Camera Movement:** Improved camera movement and rotation when the reticle is at screen edges.
*   **Projectile Effects:** Experimented with color-based projectiles, opting for material swaps over global material changes.
*   **Shooting Movement:** Adjusted shooting movement to reduce camera clipping through the ground.
*   **Mechanics:** Developed a trap-laying mechanic instead of a ricochet/shield.
*   **Balancing:** Focused on balancing player and bullet speeds for a near-miss feel.

**2024 - Boss Design & Enemy Variety:**
*   **Boss Stage Planning:** Emphasized better planning for boss stages, including gameplay and sequences.
*   **Boss Concepts:** Brainstormed boss ideas, including power-ups inspired by Rez and visual synchronization with music (X-Men Sentinel fight).
*   **Twin Snake Boss:** Developed twin snake boss mechanics, polyrhythmic shooting, dodge/shoot-back mechanics on beat, and considered a parry option.
*   **Ouroboros Boss:** Conceptualized Ouroboros as a serpentine, arena-shrinking boss with wave-based attacks dodged to the beat.
*   **Enemy Types:** Defined enemy types (Snakes, Orbitals) and brainstormed new enemy concepts: Fractured, Tessellated Wall, Elastic, Mines, Phased movement, Large guardians, Reflective shields, Cloaked Stalkers.
*   **Infinite Track Snake:** Added snake head to infinite track for a chasing ouroboros concept.

**Mechanics & Features:**
*   **Speed Dash:** Questioned implementing a speed dash affecting tempo/music.
*   **Scriptable Objects:** Planned to use scriptable objects for defining attributes for projectiles and enemies.
*   **Rhythm Elements:** Considered adding more rhythm elements to gameplay.
*   **QTE Implementation:** Implemented QTE (Quick Time Event) with bug testing needed.
*   **Score/Time Boost:** Added score/time boost for wave/section changes.
*   **Time Features:** Shifted time features to Game Manager.
*   **Projectile Aiming:** Considered having projectiles aim in player's facing direction.
*   **Reticle System:** Changed reticle system to Shapes asset pack.
*   **Cursor Feedback:** Implemented cursor shrink/grow to indicate target locking.
*   **Enemy Structure:** Refactored enemies into EnemyBase and EnemyBehavior structure.
*   **Projectile System:** Implemented structured and optimized enemy shooting system with configurable parameters and cooldowns.
*   **Visual Feedback:** Added time-freeze frame, danger zone visualization, and warning period for projectile spawning.

**Issues & Fixes:**
*   **Player Movement:** Addressed player character bopping/rotating issues, jittery movement, and smoothed jitter with lerping on ground detection.
*   **Lock-and-Shoot:** Identified and aimed to avoid "lockandshoot" situations.
*   **Koreographer Sets:** Assessed deleting "wait for it koreo sets".
*   **A\* Issues:** Investigated A\* issues related to rotation.
*   **Infinite Track:** Started infinite track idea on a spline for randomized boss battles in a constrained area.
*   **Camera Adjustments:** Adjusted camera in first Ouroboros level and generally.
*   **Projectile Logic:** Updated projectile logic.
*   **Rewind Time:** Addressed getting stuck in rewind time state.