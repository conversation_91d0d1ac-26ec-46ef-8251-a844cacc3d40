# March 15

Using <PERSON> to improve Crosshair.cs

Didnt work! broke stuff i think! or maybe it was good, hard to tell because other stuff broke.

<PERSON><PERSON> will give this a second try once I better understand issue with locking proejctiles

Projectile locking working once again, but not sure if it’s fully fixed.

Can define boxcast in crosshair inspector now - makes it easier to see range etc

Figured out A Star Debug messages being A LOT - change logging level in A* object