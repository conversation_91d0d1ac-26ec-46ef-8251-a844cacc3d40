# Summary of 2023 - Tech Issues.md

This file is a chronological log of technical issues encountered and addressed during the game development in 2023. The issues span various areas of the project, reflecting the iterative and problem-solving nature of game development. Key recurring themes and issues include:

*   **Unity Errors & Crashes:** Frequent Unity crashes, often with stack traces that are not fully informative. Issues related to specific Unity versions (upgrades causing errors), rendering pipelines, and graphics settings (D12 vs D11).
*   **Shader & Visual Problems:** Shader compilation issues, washed-out colors in builds, volumetric fog rendering problems, alpha clipping glitches, transparency issues, and datamosh effect implementation difficulties.
*   **Object Pooling & Spawning:**  Recurring errors with Object Particle Spawner (OPS), assertion failures, and issues with enemy and bullet pooling mechanisms. Inefficient recycling of game objects and memory concerns.
*   **AI & Navmesh Issues:** Enemies falling off navmeshes, AI not sticking to surfaces, pathfinding problems, and issues related to A* Pathfinding updates.
*   **Projectile Behavior:** Projectiles not hitting enemies, erratic projectile movement, incorrect trajectories, and time rewind affecting projectile behavior.
*   **Camera & Movement Bugs:** Camera glitches, flickering, upside-down images (FSR plugin), camera blending problems, spinning dolly/wheel issues, and general movement script errors (shooter movement, snake movement).
*   **UI & Input Problems:** Controller input issues in UI menus, inability to click UI elements with a controller, and pause menu triggering unexpectedly.
*   **Build & Platform Issues:**  Problems with builds, differences in behavior between editor and builds, and platform-specific issues (Steam Deck performance).
*   **Asset & Package Issues:** Errors related to specific assets (Buto, GPU Instancer, Magic Arsenal, MeshBaker), and package integration problems (Rokoko, Curvy, Interhaptics Razer).
*   **Debugging & Tooling:** Challenges with debugging custom setups, SRDebugger issues, video gameplay recording not working, and finding efficient debugging methods.

In summary, the "Tech Issues" log is a detailed record of the technical challenges faced throughout 2023. It highlights the iterative process of identifying, diagnosing, and resolving bugs and performance problems across different areas of the game, from rendering and shaders to AI, physics, and UI. The notes emphasize the ongoing nature of technical problem-solving in game development and the importance of systematic debugging and testing.