
*   **January 10th**:
    * Beautify does not seem to be working properly according to demos
    * Voulmetric fog doesn’t look that great - may be better assets to try for this look
    * Valid shader files are not extracted using AssetStudio Gui
    *   BOXOPHOBIC fog
    *   BUTO fog asset
*   **January 12**:
    *   Footsparks do not follow rotation
    *   tying ANimation Events to Playing on VFX graph, but it Won’t play more than once.
*   **July 18**:
    * FSR plugin cause camera to flicker with upside down image occarionally
    * Snake speeds have problems with adjustments
*   **February 14**:
    *   Spinning issues with dolly/wheels.
    *   Events not syncing due to moving elements.
    *   Enemies not moving, try snapping A* scan to scene bounds. Likely scanning in the wrong area!
    *   Dreamtek ‘Rotate’ script needs editing for more features.
    *   Need to control dolly's during transitions.
    *   Namespace accessibility issues.
    *   No matter how set up, <PERSON> triggers pause menu on and off.
    *   Prefab vs in-scene Rewired Manager issue.
    *   Fix compiling shader varaints.
    *   Washed out colors in Unity builds. Changing color space to Linear, Anti-Aliasing issues.
    *   Object Spawner issues causing problems.
    *   Assertion failure with ObjectParticleSpawner script.
    *   Issues with bullet pool.
    *   Errors with the bullet pool .
    *   Pooling not working correctly for enemies.
    *   OPS errors with the particle system.
*   **February 17**:
    *   Enemies are -20 lower and not seeing graph properly.
    *   Enemies slowly move up.
    *   Enemies wont shoot.
    *   Fix Rotate RIng issues
    *  Errors with the height Fog - could be contributing to the color issues
*   **March 1 2023**:
    *   Map not constrained properly, bullets flying off.
    *   Bullets circling enemies, not hitting them.
    *   Not transitioning between Ophanim rings properly.
    *  GPU bound of performance - optimization needed.
    *   Unity Crashing “Stack trace could not be displayed. See the latest crash report generated in Editor Log by clicking the "Go to crash logs" button”
*   **March 6**:
    *   Controller as curson implemented for Pause menu.
    *   Cant seem to click anything. Mouse works fine though
*   **March 7**:
    *   Hot Reload for unity issues, need to try again.
    *   Issue with Z value of Shooting Game Object rotating.
*   **March 8**:
    *  Easy Performant Outlines - what is it?
    *   Deleting Magic Arsenal and MeshBaker from project to remove a possible error
*   **March 9**:
    *   Menu quality settings issue with resolution, need options to apply and revert.
    *  Quality Settings - Resolution - need to add options for apply and revert for resolution
    *   Graphics issues with performance from
    *   Gfx.WaitForPresent: When the main thread is ready to start rendering the next frame, but the render thread has not finished waiting on the GPU to Present the frame. This might indicate that your game is GPU bound.
*   **March 10**:
    *   Stack trace could not be displayed
    *   Fix functionality of tracking on reticle
    *   Fix bullet trajectory calculations
    *   Find a way to turn dev console on/off
    *   SRDebugger issues
*   **March 13**:
    *   Issues with custom debugging setup.
*   **March 14**:
    *   Can’t figure out rotation issues with Shooting/Reticle.
    *  HDR screws up space time graphics assets, disabling HDR to see what I can do
    *  Video gameplay recorder for builds not working
    *   Bullet movement affected by time changes
*   **March 15**:
    * Object Particle Spawner delays occuring
*   **March 16**:
    *  Datamosh not working in proper fashion
*   **March 19**:
    * Error trying to use New Boss4Attacks4Positions Behavior Designer, issues with 4 attack types and extending the tactical agent class.
*   **March 21**:
    *   Some errors after updating to Unity 2022.2.11.
    *   Removed A* and GPU Instancer due to errors.
    *   Buto - not working yet, pink.
    *   Trying to fix these errors
*   **March 22**:
    *   Buto is not working 100%
    *   Alpha clipping issues on projectiles.
    *   Check if HDR on and off makes a big difference.
*   **March 23**:
    *   Buto not working, adjusting to manageable state.
    *   Unity crashing, changed from D12 to D11.
    *   A* Graph issues.
    *   Enemy AI bugging out.
    *   Controller use in UI menus.
    *   Settings UI needs camera for cursor selection, post-processing getting in the way.
*   **March 25**:
    * Wleminated screen similar to rollerdrome - need to make this better for the visuals
    *   Dynamic mesh cutting looks interesting
    *   Tried New outline Shader - full screen effect in Unity 2022
*   **March 27**:
    *   Rokoko animation importing issues.
    *   AUto Rig in Blender and not liked by Mixamo issues
*   **March 28**:
    * Need to keep in mind, Roller Drome objectives list
    * Need to also assess any Google Keep notes, ideas for moving forward
    * Also working on Creatues in Blender.
    * Need to better look at Random FLow
*   **March 29**:
    * Black Hole VFX issues
*   **May 1**:
    *   Navmesh scanning work again with beta 61 of A star pathfinding.
    *   Enemies appear to be working on mesh! Kinematic was enabled and this was the problem.
    *  NavMesh testing for movement
    *   Looked at the torodial problem
*   **May 2**:
    *   Enemies are shooting but not moving
    *   Player running anim direction gets wonky - why?
    *   Crosshair Reticle sideways and wonky - why?
    * Clamp them to the navmesh using e.g. AstarPath.active.GetNearest. See AIPath.ClampToNavmesh for an example.
    * Cannot get Torodial working, not sure why! Something to do with the game object? What am I missing?
*   **May 3**:
    *  Enemies moving - but fly off easily. Need to clamp them to navmesh properly.
    *   Crosshair reticle issue.
*  **May 3**:
    * Updated Buto to fix any issues
    * Camera Blend issues
*   **May 5**:
    *  Fixed error
    * Navmesh Test Scene - Ouroboros 3 - More complex Behaviors
    *   Character not rotating properly or animation not changing properly - why?
    *   Ground check doesn’t appear to be altering the height of the player game object - look into this
    *   Projectiles are rarely coming into contact with enemies when fired at them. Enemies are not really firing projectiles at me either. Need to adjust values, get balanced.
*   **May 8**:
    *   Integrated Aron Granberg’s feedback on how to get AI to stick to Navmesh
*   **May 9**:
    *   Spawner was not working properply yesterday
    * Birth/Death Particles aren’t working correctly, have issues
    *Trying a Gimbal-like approach to movement, due to the jerky nature of the Navmesh movement.
* **May 10**:
    *   New camera is glitchy
    *   Enemies fall of the edges of levels
    *   Enemies movies around on the whole navmesh, but not sticking to it.
    * Get an idea of mechanics design / technical limitations
*   **May 11**:
    * Enemeies seem to be falling off
*   **May 16**:
    * Enemeis now stick to mesh , kinematic
*   **May 17**:
    *  Could anything from Projectile Toolkit?
*   **May 18**:
    * Successfully fixed errors to allow a build.
    * Attempted fix with errors for lock on when ground is in the way, seems to be working.
    * Need to adjust projectile behavior - not hitting enemies most of the time, behave too erratically. Would improve gameplay loop drastically if fixed.
*   **May 19**:
    *   X ray effect not finsihed, will get it working tomorrow
    * Time rewind seems to screw up enemies know their position (getnearest planting them firmly on mesh)
*   **May 22**:
    * Re-lock on needed for taking out most enemies
    * Attempt to fix projectile movement
    * Implementing the X Ray URP feature as a good start to the day
    * Checking GroundCheck on Player to see if it’s causing player to fly off screen when transition happens
    *  Ground check needs improvement, disabled for now.
*   **May 23**:
    * Enemies bug out - bug found the other day.
    * projectile bug
    * Make adjustment to a script for Snake Head movement in Ouroboros level, could be useful to general timed movement.
*   **May 24**:
    * Seems I may need to have a check on time rewind on death, seems if two happen too close together it causes an infinite loop.
*   **May 25**:
    * Projectile movement - issues with hitting enemies
    *  Fix the rewind of player on enemy death 
*   **May 26**:
    *   Enemies dying when i dont even shoot some of the bullets.
    *   Error at 303 from OPS, need to address and fix the bug
    * May need an alternative to Rewind to account for enemies
    * Time Rewind issues

* **May 29**:
    *  Projectiles not moving- A* causing problem on the model
* **May 30**:
 *  Fixing paths of ouroboros
*   **May 31**:
* * alternate color when shot is not working, issue with alpha channel of flatkit shader
  * Need to fix errors of the scene being set- need to go over it
*   **June 19**:
 *  Problems working in Butto- can only run on high settings
*  **July 18**:
    * FSR plugin cause camera to flicker with upside down image occarionally
    * Removing second global clock from Timekeeper because it seemed it is not needed
* **July 20**:
    * Snake speeds have problems with adjustments
*   **July 24**:
    *   Update on mesh disappearing at times in game
    *   Broke something in Shooter movement with refactor - need to look at old code to fix
*   **July 25**:
    *   Trying to fix weird issues with shooting reticle movement.
    * Weird issue with things not working in build with this movement script, versus things working somewhat in editor. This may be a factor?
    * Change UI menu to be in place
*   **July 26**:
    * Error with this - can’t find it. Not sure why
    * On 60hz display i dont get the flashing and issues i do on my laptop screen. Need to add support for alternate refresh rates.
    * Testing issue with Shoot and Scoot stopping.
*   **Sept 1**:
    *  'You should look for the `CreateForwardPlusBuffers` method in your code (or potentially in Unity's universal rendering pipeline code if you're using that) and make sure any `GraphicsBuffer` objects are being properly released" (memory leaks)
* **Sept 4**
*   New code to Unity's issue - may not be solveable yet
*   Problems adding inter haptics (Razer addition)
*   Need to work on the render pipline to have the apporpaite features added
*   **Sept 6**
    *Tried using target tracker and improving radar.
    *On transition - need to freeze all player x / y / x positions then unfreeze when next wave starts 
*   **Sept 8**
   *Trying to rebuild old pipeline, can use a general URP pipeline but want all my old render features and more
        -Open ‘Beat Traveller’ from 4TB Hard drive to see if my appropriate render pipeline is still intact there
     *Should the highlight transparent queue be opaque or transparent? Check on this
     *SRP batcher causing graphical errors - can i use it? can i fix it? off for now

---


