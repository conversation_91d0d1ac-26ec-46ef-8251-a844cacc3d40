# 2023

January 2023:

- Continued work on the ground check script, trying to fix issues with the player character randomly spinning the wrong way.
- Explored ways to improve the camera movement and rotation when the reticle moves towards the edges of the screen.
- Investigated methods to reduce build times, such as using Assembly Definitions.
- Identified and worked on fixing various small problems with gameplay, audio, and other aspects that were thought to be previously resolved.

February 2023:

- Implemented a "hacky" LookAt solution to stop the player game object from rotating.
- Figured out an issue with the orientation anchor in the Curvy Spline component, which helped fix orientation problems.
- Experimented with color-based projectile effects, but decided against a global material change approach in favor of a material swap.
- Recognized the need for a proper issue tracker or Kanban board to manage game bugs and development tasks.

March 2023:

- Adjusted the shooting movement to prevent the camera from going directly through the ground as much.
- Fixed issues with the audio filter no longer being applied when hitting the start button.
- Continued making small adjustments and fixes to improve the overall gameplay experience.

April 2023:

- Worked on integrating the project with Perforce Helix for source control and build automation.
- Investigated and troubleshot issues with the A *pathfinding system, particularly related to the Ouroboros level.*
- Explored the use of new URP features and visual effects, such as the Blender Tree Generator asset for creating a Yggdrasil-inspired environment.
- Discussed ideas for a boss encounter design, incorporating elements like time manipulation and Fresnel-based effects.

May 2023:

- Made adjustments to the Snake Head movement script in the Ouroboros level, which could be useful for other timed movement scenarios.
- Added ground collision detection on the main camera to improve the player's perspective and experience.
- Experimented with the idea of a "ricochet/shield" element, but ended up creating a trap-laying mechanic instead.
- Focused on balancing the speed of player movement and bullet speed to create a sense of the player being moments away from being hit.

June 2023 - December 2023:

- The available documentation does not contain any specific details about the project's progress during these months. It's possible that development continued, but the information provided does not include a clear summary for this time period.

Overall, the project's development in 2023 seems to have focused on resolving technical issues, improving the core gameplay mechanics, and exploring new visual and design ideas. The team continued to iterate on the player controls, camera, enemy AI, and level design, while also investigating ways to optimize the project's performance and build process.