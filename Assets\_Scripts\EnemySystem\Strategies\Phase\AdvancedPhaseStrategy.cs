using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using BTR.EnemySystem;

namespace BTR.EnemySystem
{
    /// <summary>
    /// Advanced phase strategy that supports complex boss-like behavior transitions.
    /// Handles strategy switching, invulnerability periods, and conditional phase triggers.
    /// </summary>
    public class AdvancedPhaseStrategy : MonoBehaviour
    {
        [System.Serializable]
        public class PhaseConfiguration
        {
            [Header("Phase Settings")]
            public string phaseName = "Phase";
            public float healthThreshold = 0.5f; // 0-1 normalized
            public float transitionDuration = 1f;
            public bool invulnerableDuringTransition = true;

            [Header("Strategy Overrides")]
            public CombatStrategy combatStrategy;
            public MovementStrategy movementStrategy;
            public float attackSpeedMultiplier = 1f;
            public float movementSpeedMultiplier = 1f;

            [Header("Conditional Triggers")]
            public bool requireAllPartsDestroyed = false;
            public string[] requiredDestroyedParts = new string[0];
            public float timeBasedTrigger = -1f; // -1 = disabled

            [Header("Effects")]
            public string transitionEffectName = "";
            public string phaseStartEffectName = "";
            public Color phaseColor = Color.white;

            [Header("Audio")]
            public string phaseTransitionSound = "";
            public string phaseActiveSound = "";
        }

        [Header("Phase Configuration")]
        [SerializeField] private bool enableDebugLogs = false;
        [SerializeField] private PhaseConfiguration[] phases = new PhaseConfiguration[0];
        [SerializeField] private bool resetHealthOnPhaseChange = false;
        [SerializeField] private bool allowPhaseSkipping = false;

        // Component references
        private IPhaseableEntity phaseableEntity;
        private ICombatEntity combatEntity;
        private StrategicEnemyEntity strategicEntity;
        private DamageablePartsComponent damageableParts;

        // Phase state
        private int currentPhaseIndex = 0;
        private bool isTransitioning = false;
        private float phaseStartTime = 0f;
        private bool isInitialized = false;

        // Original strategies (for restoration)
        private CombatStrategy originalCombatStrategy;
        private MovementStrategy originalMovementStrategy;

        // Events
        public System.Action<int, int> OnPhaseChanged; // oldPhase, newPhase
        public System.Action<int> OnPhaseTransitionStarted;
        public System.Action<int> OnPhaseTransitionCompleted;

        private void Awake()
        {
            // Get required components
            phaseableEntity = GetComponent<IPhaseableEntity>();
            combatEntity = GetComponent<ICombatEntity>();
            strategicEntity = GetComponent<StrategicEnemyEntity>();
            damageableParts = GetComponent<DamageablePartsComponent>();

            if (combatEntity == null)
            {
                Debug.LogError($"[AdvancedPhaseStrategy] No ICombatEntity found on {gameObject.name}");
                enabled = false;
                return;
            }
        }

        private void Start()
        {
            Initialize();
        }

        public void Initialize()
        {
            if (isInitialized) return;

            // Store original strategies
            if (strategicEntity != null)
            {
                originalCombatStrategy = strategicEntity.CurrentCombatStrategy;
                originalMovementStrategy = strategicEntity.CurrentMovementStrategy;
            }

            // Subscribe to events
            combatEntity.OnHealthChanged += HandleHealthChanged;

            if (damageableParts != null)
            {
                damageableParts.OnPartDestroyed += HandlePartDestroyed;
                damageableParts.OnAllPartsDestroyed += HandleAllPartsDestroyed;
            }

            // Initialize first phase
            phaseStartTime = Time.time;
            ApplyPhaseConfiguration(0);

            isInitialized = true;

            if (enableDebugLogs)
            {
                Debug.Log($"[AdvancedPhaseStrategy] Initialized with {phases.Length} phases");
            }
        }

        private void Update()
        {
            if (!isInitialized || isTransitioning) return;

            // Check for time-based phase transitions
            CheckTimeBasedTransitions();

            // Check for health-based phase transitions
            CheckHealthBasedTransitions();
        }

        private void CheckTimeBasedTransitions()
        {
            if (currentPhaseIndex >= phases.Length) return;

            var currentPhase = phases[currentPhaseIndex];
            if (currentPhase.timeBasedTrigger > 0f)
            {
                float timeInPhase = Time.time - phaseStartTime;
                if (timeInPhase >= currentPhase.timeBasedTrigger)
                {
                    TriggerNextPhase();
                }
            }
        }

        private void CheckHealthBasedTransitions()
        {
            if (combatEntity == null || currentPhaseIndex >= phases.Length - 1) return;

            float healthPercentage = combatEntity.Health / combatEntity.MaxHealth;

            // Check if we should transition to next phase
            for (int i = currentPhaseIndex + 1; i < phases.Length; i++)
            {
                var phase = phases[i];

                if (healthPercentage <= phase.healthThreshold)
                {
                    if (allowPhaseSkipping || i == currentPhaseIndex + 1)
                    {
                        if (CanTransitionToPhase(i))
                        {
                            TransitionToPhase(i);
                            break;
                        }
                    }
                }
            }
        }

        private bool CanTransitionToPhase(int phaseIndex)
        {
            if (phaseIndex < 0 || phaseIndex >= phases.Length) return false;

            var phase = phases[phaseIndex];

            // Check if all required parts are destroyed
            if (phase.requireAllPartsDestroyed && damageableParts != null)
            {
                if (damageableParts.GetDestructionPercentage() < 1f)
                {
                    return false;
                }
            }

            // Check specific required destroyed parts
            if (phase.requiredDestroyedParts.Length > 0 && damageableParts != null)
            {
                foreach (var partName in phase.requiredDestroyedParts)
                {
                    if (!damageableParts.IsPartDestroyed(partName))
                    {
                        return false;
                    }
                }
            }

            return true;
        }

        public void TransitionToPhase(int phaseIndex)
        {
            if (isTransitioning || phaseIndex < 0 || phaseIndex >= phases.Length || phaseIndex == currentPhaseIndex)
                return;

            StartCoroutine(PhaseTransitionCoroutine(phaseIndex));
        }

        public void TriggerNextPhase()
        {
            if (currentPhaseIndex < phases.Length - 1)
            {
                TransitionToPhase(currentPhaseIndex + 1);
            }
        }

        private IEnumerator PhaseTransitionCoroutine(int newPhaseIndex)
        {
            isTransitioning = true;
            int oldPhaseIndex = currentPhaseIndex;
            var newPhase = phases[newPhaseIndex];

            OnPhaseTransitionStarted?.Invoke(newPhaseIndex);

            if (enableDebugLogs)
            {
                Debug.Log($"[AdvancedPhaseStrategy] Transitioning from phase {oldPhaseIndex} to {newPhaseIndex}: {newPhase.phaseName}");
            }

            // Set invulnerability during transition
            if (newPhase.invulnerableDuringTransition && combatEntity != null)
            {
                combatEntity.SetVulnerability(false);
            }

            // Play transition effects
            PlayTransitionEffects(newPhase);

            // Wait for transition duration
            if (newPhase.transitionDuration > 0f)
            {
                yield return new WaitForSeconds(newPhase.transitionDuration);
            }

            // Apply new phase configuration
            currentPhaseIndex = newPhaseIndex;
            phaseStartTime = Time.time;
            ApplyPhaseConfiguration(newPhaseIndex);

            // Restore vulnerability
            if (newPhase.invulnerableDuringTransition && combatEntity != null)
            {
                combatEntity.SetVulnerability(true);
            }

            // Reset health if configured
            if (resetHealthOnPhaseChange && combatEntity != null)
            {
                combatEntity.ResetHealth();
            }

            isTransitioning = false;

            OnPhaseChanged?.Invoke(oldPhaseIndex, newPhaseIndex);
            OnPhaseTransitionCompleted?.Invoke(newPhaseIndex);

            if (enableDebugLogs)
            {
                Debug.Log($"[AdvancedPhaseStrategy] Phase transition completed: {newPhase.phaseName}");
            }
        }

        private void ApplyPhaseConfiguration(int phaseIndex)
        {
            if (phaseIndex < 0 || phaseIndex >= phases.Length) return;

            var phase = phases[phaseIndex];

            // Switch combat strategy
            if (phase.combatStrategy != null && strategicEntity != null)
            {
                strategicEntity.SetCombatStrategy(phase.combatStrategy);
            }

            // Switch movement strategy
            if (phase.movementStrategy != null && strategicEntity != null)
            {
                strategicEntity.SetMovementStrategy(phase.movementStrategy);
            }

            // Apply speed multipliers (this would need to be implemented in the strategies)
            ApplySpeedMultipliers(phase);

            // Play phase start effects
            PlayPhaseStartEffects(phase);

            if (enableDebugLogs)
            {
                Debug.Log($"[AdvancedPhaseStrategy] Applied configuration for phase: {phase.phaseName}");
            }
        }

        private void ApplySpeedMultipliers(PhaseConfiguration phase)
        {
            // This would need to be implemented based on your strategy system
            // For now, this is a placeholder
            if (enableDebugLogs)
            {
                Debug.Log($"[AdvancedPhaseStrategy] Applying speed multipliers - Attack: {phase.attackSpeedMultiplier}, Movement: {phase.movementSpeedMultiplier}");
            }
        }

        private void PlayTransitionEffects(PhaseConfiguration phase)
        {
            // Play transition effect
            if (!string.IsNullOrEmpty(phase.transitionEffectName))
            {
                SpawnEffect(phase.transitionEffectName, transform.position, transform.rotation);
            }

            // Play transition sound
            if (!string.IsNullOrEmpty(phase.phaseTransitionSound))
            {
                PlaySound(phase.phaseTransitionSound);
            }
        }

        private void PlayPhaseStartEffects(PhaseConfiguration phase)
        {
            // Play phase start effect
            if (!string.IsNullOrEmpty(phase.phaseStartEffectName))
            {
                SpawnEffect(phase.phaseStartEffectName, transform.position, transform.rotation);
            }

            // Play phase active sound
            if (!string.IsNullOrEmpty(phase.phaseActiveSound))
            {
                PlaySound(phase.phaseActiveSound);
            }
        }

        private void SpawnEffect(string effectName, Vector3 position, Quaternion rotation)
        {
            // Placeholder - replace with your effect system
            if (enableDebugLogs)
            {
                Debug.Log($"[AdvancedPhaseStrategy] Spawning effect: {effectName}");
            }
        }

        private void PlaySound(string soundName)
        {
            // Placeholder - replace with your audio system
            if (enableDebugLogs)
            {
                Debug.Log($"[AdvancedPhaseStrategy] Playing sound: {soundName}");
            }
        }

        private void HandleHealthChanged(float oldHealth, float newHealth)
        {
            // Health-based transitions are handled in Update()
        }

        private void HandlePartDestroyed(string partName)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[AdvancedPhaseStrategy] Part destroyed: {partName}");
            }

            // Check if this enables any phase transitions
            CheckHealthBasedTransitions();
        }

        private void HandleAllPartsDestroyed()
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[AdvancedPhaseStrategy] All parts destroyed");
            }

            // Trigger next phase if available
            TriggerNextPhase();
        }

        private void OnDestroy()
        {
            // Unsubscribe from events
            if (combatEntity != null)
            {
                combatEntity.OnHealthChanged -= HandleHealthChanged;
            }

            if (damageableParts != null)
            {
                damageableParts.OnPartDestroyed -= HandlePartDestroyed;
                damageableParts.OnAllPartsDestroyed -= HandleAllPartsDestroyed;
            }
        }

        // Public API
        public int CurrentPhase => currentPhaseIndex;
        public bool IsTransitioning => isTransitioning;
        public string CurrentPhaseName => currentPhaseIndex < phases.Length ? phases[currentPhaseIndex].phaseName : "Unknown";
        public float TimeInCurrentPhase => Time.time - phaseStartTime;

        public PhaseConfiguration GetPhaseConfiguration(int phaseIndex)
        {
            return phaseIndex >= 0 && phaseIndex < phases.Length ? phases[phaseIndex] : null;
        }

        public void ForceTransitionToPhase(int phaseIndex)
        {
            if (!isTransitioning)
            {
                TransitionToPhase(phaseIndex);
            }
        }
    }
}
