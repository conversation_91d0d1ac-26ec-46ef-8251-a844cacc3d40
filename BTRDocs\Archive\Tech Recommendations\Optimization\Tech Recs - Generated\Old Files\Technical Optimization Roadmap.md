# Technical Optimization Roadmap

## Core Architectural Priorities

### 1. Event System Implementation (Phase 1 - Critical) [IN PROGRESS]

#### Completed Tasks
1. Created dedicated Events folder structure:
```
Assets/_Scripts/Events/
├── BTR.Events.asmdef
├── GameEvents.cs
├── TimeEvents.cs
└── EnemyEvents.cs
```

2. Implemented core event classes:
```csharp
// GameEvents.cs - Core game flow and state management
public static class GameEvents {
    // Game State Events
    public static event GameStateEventHandler OnGameStarted;
    public static event GameStateEventHandler OnGameOver;
    public static event GameStateEventHandler OnGameRestart;
    public static event GameStateEventHandler OnGameRestarted;

    // Score Events
    public static event ScoreEventHandler OnScoreUpdated;
    public static event ScoreEventHandler OnHighScoreUpdated;

    // Scene Events
    public static event Action<Scene, LoadSceneMode> OnSceneInitializationRequested;
    public static event Action OnSceneTransitionStarted;
    public static event Action OnSceneTransitionCompleted;
}

// TimeEvents.cs - Time management and control
public static class TimeEvents {
    public static event TimeStateEventHandler OnTimePaused;
    public static event TimeStateEventHandler OnTimeResumed;
    public static event TimeStateEventHandler OnTimeScaleChanged;
    public static event TimeScaleEventHandler OnTimeScaleSet;
}

// EnemyEvents.cs - Enemy state and management
public static class EnemyEvents {
    // State Events
    public static event Action<EnemyCore> OnEnemyStateChanged;
    public static event Action<Transform> OnEnemyRegistered;
    public static event Action<Transform> OnEnemyLockStateChanged;
    
    // Combat Events
    public static event Action<Transform> OnEnemyEngaged;
    public static event Action<Transform> OnEnemyDied;
}
```



#### Remaining Tasks


3. **Event System Enhancements** [CRITICAL]
- Implement proper observer pattern for:
  - Combat events (damage/status changes)
  - Projectile lifecycle (fire/impact/despawn)
  - UI state updates
- Add event validation and error handling
- Implement event logging system
- Add performance monitoring for event triggers

4. **Documentation** [HIGH]
- Create event system usage guide with anti-pattern examples:
  - "Direct Component References" vs "Event Subscription"
  - "God Object Communication" vs "Decoupled Events"
- Document event flow diagrams for core systems
- Add XML documentation to all event classes
- Create EventSystemCheatSheet.md with common patterns

### 2. Compute Shader Optimization (Phase 1)
**DigitalLayerEffect.cs** needs:
- Throttled updates (0.1s intervals)
- Buffer validation
- LOD based on camera distance

```hlsl
// DigitalLayerCompute.compute
// Add distance-based culling
if (distanceToCamera > MAX_RENDER_DISTANCE) {
    spriteData.color.a = 0;
}
```

### 3. UI Performance Patterns (Phase 2)
**PlayerUI.cs** requirements:
- Event-driven updates instead of polling
- Canvas pooling
- Batch material updates

```csharp
// Bad pattern (update every frame):
void Update() { scoreText.text = currentScore.ToString(); }

// Good pattern:
void OnEnable() {
    GameEvents.OnScoreUpdated += HandleScoreUpdate;
}
```

## Performance Hotspots

### 4. Projectile Systems
| Script | Issue | Solution |
|--------|-------|----------|
| ProjectileManager.cs | Instantiation spikes | Prewarm pool (+20% capacity) |
| ProjectileDetection.cs | Unfiltered collisions | Layer mask filtering |
| ProjectileStateBased.cs | GC Allocations | Struct refactoring |

### 5. Debug System Overhaul 
**ConditionalDebug.cs** improvements implemented:
- Integrated with DebugSettings for configurable log levels using flags system
- Enhanced logging features:
  - Timestamps and thread IDs for better tracing
  - Category-based filtering
  - Automatic source file attribution
- Smart file logging system:
  - Configurable file paths
  - Automatic log rotation
  - Size limit management
- Zero overhead in production builds through conditional compilation
- Stack trace support for error logging

Example Usage:
```csharp
// Development/Editor builds only:
ConditionalDebug.LogInfo("Player spawned", "PlayerSystem");
ConditionalDebug.LogError("Connection failed", "NetworkSystem");

// Automatically omitted in production builds
```

## Memory Management

### 6. GPU Resource Handling
**DigitalLayerEffect.cs** critical path:
1. Validate buffers on disable
2. Implement LOD groups
3. Add texture streaming

```csharp
void OnDisable() {
    if(positionBuffer != null && positionBuffer.IsValid()) {
        positionBuffer.Release();
    }
}
```

## Implementation Phases

| Phase | Timeline | Key Deliverables | Status |
|-------|----------|------------------|---------|
| 1a - Event System | Sprint 21 | Core event classes, Assembly setup | 75% Complete |
| 1b - Event Integration | Sprint 21 | Component updates, Error resolution | 25% Complete |
| 2 - GPU Optimization | Sprint 22 | Compute LOD, Buffer lifecycle | Not Started |
| 3 - Memory & Physics | Sprint 23 | Pooling system, Layer masks | Not Started |
| 4 - Final Polish | Sprint 24 | Addressables, Profile reports | Not Started |

## Metrics & Validation

**Performance Benchmarks**:
- Target 60fps on i5-8600K/RTX 2060
- Max GC Alloc: 2KB/frame
- VRAM Usage: <3.5GB

**Validation Tools**:
1. Unity Memory Profiler
2. RenderDoc GPU captures
3. Custom allocation tracker