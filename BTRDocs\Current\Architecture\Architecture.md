# BTR Core Architecture

```mermaid
flowchart TB
    %% Core Management
    GameManager[GameManager]
    EventSystem[Event System]
    SceneManager[Scene Management]
    TimeManager[Time Manager]
    AudioManager[Audio Manager]
    ScoreManager[Score Manager]
    
    %% Core Systems
    PlayerSystem[Player System]
    EnemySystem[Enemy System]
    ProjectileSystem[Projectile System]
    UISystem[UI System]
    
    %% Interfaces & Core
    IEventCompatible[IEventCompatible]
    IDamageable[IDamageable]
    
    %% Player Subsystems
    PlayerMovement[Player Movement]
    PlayerHealth[Player Health]
    PlayerShooting[Player Shooting]
    PlayerLocking[Player Locking]
    
    %% Enemy Subsystems
    EnemyManager[Enemy Manager]
    EnemyCore[Enemy Core]
    DamageablePart[Damageable Part]
    EnemyAudio[Enemy Audio Manager]
    
    %% UI Components
    PlayerUI[Player UI]
    LoadingScreen[Loading Screen]
    WaveHUD[Wave HUD]
    
    %% Event Channels
    GameEvents[Game Events]
    EnemyEvents[Enemy Events]
    SceneEvents[Scene Events]
    TimeEvents[Time Events]
    
    %% Core Management Relationships
    GameManager --> EventSystem
    GameManager --> SceneManager
    GameManager --> TimeManager
    GameManager --> AudioManager
    GameManager --> ScoreManager
    
    %% System Implementation of IEventCompatible
    IEventCompatible --> |implements| GameManager
    IEventCompatible --> |implements| PlayerSystem
    IEventCompatible --> |implements| EnemySystem
    
    %% IDamageable Implementation
    IDamageable --> |implements| PlayerHealth
    IDamageable --> |implements| EnemyCore
    IDamageable --> |implements| DamageablePart
    
    %% Player System Hierarchy
    PlayerSystem --> PlayerMovement
    PlayerSystem --> PlayerHealth
    PlayerSystem --> PlayerShooting
    PlayerSystem --> PlayerLocking
    
    %% Enemy System Hierarchy
    EnemySystem --> EnemyManager
    EnemySystem --> EnemyCore
    EnemySystem --> EnemyAudio
    EnemyCore --> DamageablePart
    
    %% UI System Hierarchy
    UISystem --> PlayerUI
    UISystem --> LoadingScreen
    UISystem --> WaveHUD
    
    %% UI System Connections
    PlayerUI --> PlayerHealth
    PlayerUI --> PlayerLocking
    PlayerUI --> ScoreManager
    
    %% Event System Relationships
    EventSystem --> GameEvents
    EventSystem --> EnemyEvents
    EventSystem --> SceneEvents
    EventSystem --> TimeEvents
    
    %% System Interactions
    GameManager --> PlayerSystem
    GameManager --> EnemySystem
    GameManager --> ProjectileSystem
    GameManager --> UISystem

    %% Styling
    classDef manager fill:#f9f,stroke:#333,stroke-width:2px
    classDef system fill:#bbf,stroke:#333,stroke-width:2px
    classDef interface fill:#bfb,stroke:#333,stroke-width:2px
    classDef events fill:#fbb,stroke:#333,stroke-width:2px
    classDef ui fill:#ffb,stroke:#333,stroke-width:2px
    
    class GameManager,TimeManager,AudioManager,SceneManager,ScoreManager manager
    class PlayerSystem,EnemySystem,ProjectileSystem system
    class IEventCompatible,IDamageable interface
    class GameEvents,EnemyEvents,SceneEvents,TimeEvents events
    class PlayerUI,LoadingScreen,WaveHUD,UISystem ui
```

## Color Legend
- 🟪 Managers (Purple): Core management classes
- 🟦 Systems (Blue): Major gameplay systems
- 🟩 Interfaces (Green): Core interfaces
- 🟥 Events (Red): Event channels
- 🟨 UI (Yellow): UI components and systems

## System Description
This diagram shows the core architecture of the BTR project, focusing on:

1. **Core Management Systems**
   - GameManager as the central controller
   - Various manager systems (Time, Audio, Scene, Score)
   - Event system coordination

2. **Major Systems**
   - Player System with components
   - Enemy System with damage handling
   - Projectile System
   - UI System integration

3. **Interface Architecture**
   - IEventCompatible for event handling
   - IDamageable for unified damage system
   - System-specific interfaces

4. **Event Architecture**
   - Various event channels for system communication
   - Event-driven state management
   - Scene transition coordination

5. **UI Integration**
   - Player state visualization
   - Game state management
   - Score and health tracking
   - Scene transitions

[[Architecture/architecture_overview|Back to Overview]]