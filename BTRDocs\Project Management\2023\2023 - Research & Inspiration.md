
**Research & Inspiration Log**

**February 19th:**

*   colour palette - develop one!
    *   shadows never black - blue?
    *   look at color theory
    *   adobe colour
*   [https://www.youtube.com/watch?v=nKMTU6TbUF0](https://www.youtube.com/watch?v=nKMTU6TbUF0)  - Depth Testing in this shader - do this effect?

**February 22:**

*   Recursion - Serpinski Triangle!
*   Recursion in Music - [https://www.youtube.com/watch?v=4ydZrKcvejU](https://www.youtube.com/watch?v=4ydZrKcvejU)
*   [Why Is It So Hard To Make Games On Rails?](https://www.youtube.com/watch?v=mUjZUPPrz-o) - What to learn from Starfox 64?
*   [https://en.wikipedia.org/wiki/Geocentric_model](https://en.wikipedia.org/wiki/Geocentric_model)
*   [https://en.wikipedia.org/wiki/Celestial_spheres](https://en.wikipedia.org/wiki/Celestial_spheres)
*   Watch Level Design video
*   [Shmup Principles](Shmup%20Principles%20f8fd6182542e41b2aeb09314fe226b11.md)

**March 2:**

*   tower of babylon that is an allegory for how music works
*   [https://www.youtube.com/watch?v=kt3O9AgH8WY](https://www.youtube.com/watch?v=kt3O9AgH8WY) - Look into using Flexalon
*   Tower of Babel as rings (ophanim) idea

**March 5:**

*   Call and Response

**March 6:**

*   [What Makes Good Rhythm Game UX?](https://www.youtube.com/watch?v=nRJ4iVjIMAc) - Rhythm games require great UX

**March 7:**

*   [Accurate Aiming Options Discussed & Implemented | Gun Series 7 | Unity Tutorial](https://www.youtube.com/watch?v=x8ECpNWMmag) - Looking at the following video for ideas on fixing aiming
*   [How to Walk Around a Sphere (Walk on Planet) in Unity 3D](https://www.youtube.com/watch?v=cH4oBoXkE00) -Managed to get rotation around sphere working through fake gravity unity video

**March 8:**

*   [QUICK START GUIDE - TO EVERY SHMUP EVER](https://www.youtube.com/watch?v=c_vUM1V_s1A) - Overview of movement and shot types!

**March 14:**

*   [https://twitter.com/stevesaylor/status/1618344326312099841](https://twitter.com/stevesaylor/status/1618344326312099841)  Accessibility
*   [Time Manipulation in DestroyIt with Chronos](https://www.youtube.com/watch?v=B6hk1dNeY50) - Interesting resource that may help me solidify my game!
*   [Practical Tools for Empowering the Relationship between Theme & Mechanics](https://polarisgamedesign.com/2022/practical-tools-for-empowering-the-relationship-between-theme-mechanics/)
*   Mechanics → Tone → Themes

**March 19:**

*   Look at what Rez does for this
*   Different orders construct the tower differently, give you a different ‘ending’
*   jungian Ouroboros

**May 17:**

*   [Game Design Tools; Cognitive, Psychological, and Practical Approaches Summary](Game%20Design%20Tools;%20Cognitive,%20Psychological,%20and%20P%2094fd60e9282d48c2a73941138709fbb1.md)
*   [Game Design Tools; Cognitive, Psychological, and Practical Approaches Exercises](Game%20Design%20Tools;%20Cognitive,%20Psychological,%20and%20P%20399eee644999454b9c1e74e6c3423d25.md)
*   Is there anything in Projectile Toolkit I could use?

**May 22:**

*   [X-ray Stealth Vision in Unity Shader Graph and Universal Render Pipeline](https://www.youtube.com/watch?v=eLIe95csKWE&t=1s)

**Sept 1:**
*   What am I missing?
*   WHAT AM I MISSING?
*   Navmesh
*   [Untitled](Sept%201%2039b128da45964517a5d904608723aad7/Untitled.png)

**Sept 18:**

*   [Moving Mountains: Speedy Level Creation as a Desperate Indie Dev](https://www.youtube.com/watch?v=XK-yTqYAD-c)  Hang Line GDC talk

**Sept 20:**
*   [Fake Grimlock: Win Like Stupid](https://readwrite.com/fake-grimlock-win-like-stupid/)
*   simple unique aspect to mechanic - build entire game around this
*   BIOSCHOCK

**Sept 21:**

*   [Recreating God of War's Axe Throw | Mix and Jam](https://www.youtube.com/watch?v=M-3P59GtRW4)
*   [Recreating Batman Arkham's Freeflow Combat | Mix and Jam](https://www.youtube.com/watch?v=GFOpKcpKGKQ&t=1s)
*   [Lighting tutorial: 4 techniques to light environments in Unity | Unite 2022](https://www.youtube.com/watch?v=DlxuvvYZO4Q&t=32s)
*   [How to use Cinemachine's Free Look Camera | 3rd Person Camera in Unity](https://www.youtube.com/watch?v=XT6mUlpO4fA&t=1s)

**Sept 27:**

*   Daniel Ilett has some good videos, maybe good for adding interesting things

**Sept 30:**

*   Shader information :[https://www.mayerowitz.io/blog/a-journey-into-shaders](https://www.mayerowitz.io/blog/a-journey-into-shaders)
*   Loadong scenes advice:
    *   [I beg you: Dont use the buildindex for loading Scenes](https://www.reddit.com/r/Unity3D/comments/1888oax/i_beg_you_dont_use_the_buildindex_for_loading/)

**Oct 11:**

*   [Three Steps to Becoming a Better Artist Immediately](https://www.youtube.com/watch?v=amlwcI8dh_g&list=TLPQMTExMDIwMjN_lCXzutHS6A&index=1)

**Oct 11:**

*   What was my games vision and what does this mean?

**Oct 18:**

*   FPS Encounter design video
*   DPS (Damage Per Second) Race designs - boring approach

**Oct 24**
*   [The 'TUNIC' Audio Talk](https://www.youtube.com/watch?v=sCIK78OHrIY)Good talk on FMOD audio!

**Nov**
Nov. 5th

*   [https://jakehicksphotography.com/quick-tips](https://jakehicksphotography.com/quick-tips)
*   Lighting theory

**November**

Nov. 7th

*   [Deleuze - Control Societies & Cybernetic Posthumanism](https://www.youtube.com/watch?v=Hu4Cq_-bLlY)

Nov. 8th 

*   [Remaking the Black Hole Shader from Outer Wilds! | Unity Shader](https://www.youtube.com/watch?v=hNkPHPhzXVA)
*   Shader info :[https://www.mayerowitz.io/blog/a-journey-into-shaders](https://www.mayerowitz.io/blog/a-journey-into-shaders)
*   Solid Principles:
    *   [https://www.youtube.com/watch?v=eIf3-aDTOOA](https://www.youtube.com/watch?v=eIf3-aDTOOA)
*   Loading Scenes - best ways to do so?:[I beg you: Dont use the buildindex for loading Scenes](https://www.reddit.com/r/Unity3D/comments/1888oax/i_beg_you_dont_use_the_buildindex_for_loading/)

**November:**

Nov. 30th
*   [Dreamscaper: Killer Combat on an Indie Budget](https://www.youtube.com/watch?v=3Omb5exWpd4)
    *   Key Areas to focus on
    *   Establish Strong Pillars - define feeling of player experience and turn that into actionable things
