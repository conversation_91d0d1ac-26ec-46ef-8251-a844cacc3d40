---
title: Static Shooter Enemy
date: 2024-01-17
tags: [enemy, static, shooter, configuration]
aliases: [StaticShooter]
---

# Static Shooter Enemy

## Overview
The Static Shooter is a simple, stationary enemy type that fires projectiles at predetermined intervals, controlled by the EnemyManager's group shooting system. It is designed to be lightweight and focused solely on shooting mechanics.

## Configuration
```yaml
# Projectile Settings
shootSpeed: 25
projectileLifetime: 3
projectileScale: 1
projectileDamage: 10

# Safety Settings
minShotInterval: 0.1
```

## Required Components
1. [[StaticShooter]] - Main component handling shooting mechanics
2. StaticShooterConfiguration - ScriptableObject for settings

## Key Features
- Simple, focused shooting mechanics
- Group-based shooting coordination via EnemyManager
- Configurable projectile properties
- Safety cooldown system
- No movement, health, or damage systems
- Lightweight implementation

## Integration Points
1. **EnemyManager**
   - Coordinates group shooting through Koreographer events
   - Manages multiple static shooters simultaneously
   - Handles audio feedback for group shots

2. **ProjectileSystem**
   - Uses standard projectile spawning
   - Configurable projectile properties
   - Automatic state management

## Usage Guidelines
1. Place static shooters strategically in levels
2. Configure group shooting patterns in EnemyManager
3. Ensure proper orientation for shooting direction
4. Consider player approach paths when placing

## Group Management
StaticShooter enemies can be managed in groups using the ChildActivator component:

### ChildActivator Setup
1. Create an empty GameObject as the group parent
2. Add ChildActivator component to parent
3. Place StaticShooter objects as children
4. Use SetChildrenActive(bool) to control the group

### Benefits
- Batch activation/deactivation
- Proper initialization timing
- Coordinated shooting control
- Simplified level management

### Example Usage
```csharp
// Get reference to ChildActivator
var activator = GetComponent<ChildActivator>();

// Enable all static shooters in group
activator.SetChildrenActive(true);

// Disable all static shooters in group
activator.SetChildrenActive(false);
```

## Best Practices
1. Use in groups for effective patterns
2. Place at varied heights and angles
3. Consider sight lines and cover
4. Balance projectile properties for difficulty
5. Keep configuration focused on shooting mechanics 