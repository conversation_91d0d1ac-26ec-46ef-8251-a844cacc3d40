# March 9, 2025 - Projectile Radar System Update

## Summary
Fixed major radar visibility issues and identified improvements needed for non-homing projectiles.

## Details

### Issue Resolution
- Finally tracked down the main radar visibility issue - it was related to the `MAX_ENTITIES` limit in the job system
- The system was silently failing when trying to track more projectiles than the allocated buffer size
- This took longer than expected to diagnose due to the silent failure mode

### Current State
- Radar system is now functioning correctly for homing projectiles
- Job system properly synchronizes state between `ProjectileStateBased` and `ProjectileJobSystem`
- Visibility flags (`ActiveFlags`, `HomingFlags`, `HasTargetFlags`) are now properly managed

### Needed Improvements
#### Non-Homing Projectile Visualization
- Need to implement subtle radar indicators for non-homing projectiles
- Suggested visual treatments:
  - Use smaller, more transparent icons
  - Consider using a different shape (dots vs arrows)
  - Possibly fade based on distance/threat level
  - Could use a muted color palette to differentiate from homing threats

#### Technical Considerations
- Ensure non-homing projectiles don't consume homing projectile slots
- May want to implement a separate tracking system for non-homing projectiles to avoid MAX_ENTITIES limitations
- Consider adding a configuration option to toggle non-homing projectile visibility

### Future Optimizations
- Consider implementing a dynamic buffer size system
- Add warning systems when approaching MAX_ENTITIES limit
- Implement proper cleanup of inactive projectiles to free up slots faster

### Notes for Future Reference
- When debugging radar visibility issues, check MAX_ENTITIES limit first
- Monitor job system buffer utilization during heavy combat scenarios
- Consider adding debug visualization tools for tracking entity slot usage

## Files Modified
- `ProjectileJobSystem.cs`
- `ProjectileStateBased.cs`
- `RadarManager.cs` 