
## April 2022

#### April 5

*   Tunic VFX - Scrolling textures
    *   Chromatic aberration built into the texture it seems
    *   ShaderForge used for the texture
    *   Should be able to do this all in Shader Graph

        ![Tunic VFX 1](April%205%20a010455306bb40c0b1be2dcf1a6ccff4/Untitled.png)
*   Applying similar Texture/Shader FX to a different meshes as well

    ![Tunic VFX 2](April%205%20a010455306bb40c0b1be2dcf1a6ccff4/Untitled%201.png)
*   Scrolling textures along tubes here, could maybe do the same with bezier curves easier?
*   Looks great though especially with Post Processing
*   These are UV’d shapes with scrolling textures
*   Background is a scrolling texture as well - Using minimum to blend two things
*   Colour wheel, if you’re gonna go hard on one chroma, try pulling back a little and giving some in from a colour on the opposite side of the wheel
*   Turns on Light Mapping, shadow catches of geometric complexity - nice touch - try thinking this way?

    ![Tunic VFX 3](April%205%20a010455306bb40c0b1be2dcf1a6ccff4/Untitled%202.png)
*   Mentions turning on SSAO first but this scene doesnt have it - can I add to my game?
*   Turns on bloom next

    ![Tunic VFX 4](April%205%20a010455306bb40c0b1be2dcf1a6ccff4/Untitled%203.png)
*   Using Amplify Color with custom lookup tables next
*   Tonemapping / Colorgrading brings a lot to this
*   Amplify Color can let you import a photoshop file
*   You can screenshot your scene, bring into photoshop, make changes, then import to amplify color

    ![Tunic VFX 5](April%205%20a010455306bb40c0b1be2dcf1a6ccff4/Untitled%204.png)
*   Saturating shadows is a key trick to do
*   Applied a gradient to the whole image through one of the cameras - using BLIT camera
*   Applied upper right and lower left
*   Adds a glowing off in the distance
*   Different scenes will use different colors / types of this

    ![Tunic VFX 6](April%205%20a010455306bb40c0b1be2dcf1a6ccff4/Untitled%205.png)
*   Showing old version of the world without textures - looks too flat in some ways
*   Using some textures to add a bit of detail really brings out more
*   Maybe simplest way of bringing grit to the objects
*   Good technique to steal!
*   SSAO leaned on pretty hard for lighting
*   Random Flow for Blender - thought occurs, all these metallic textures and materials i see pop up for sci fi stuff. Play around with them! See what kind of things I can create. Could possibly bring a bit more of a textured look to characters and things like Tunic does

---

#### April 13

*   Set shape properties of particle system to not rely on single material OR use mesh colors - no error!
*   Bullet trails not in correct direction
*   ALSO Glitch effect - destroy all current bombs with this and gain back some stamina? Could balance out time reverse stamina usage
*   Go over June 13 as well

#### April 14

*   Quibli Shaders - Cannot get alpha of outline to work
*   Textures on low poly models
    *   [https://github.com/eldskald/unity-support-textures-generators](https://github.com/eldskald/unity-support-textures-generators)
    *   This reminds me of the tunic talk
    *   Adding grit to low poly
    *   Quibli Shader has an option i think?
    *   For reference - removed this from Torus

        ![Torus FX](April%2014%201633eaf26d58423e8d180043fcca492a/Untitled%201.png)
*   Need to learn what these maps mean

    ![Texture Maps](April%2014%201633eaf26d58423e8d180043fcca492a/Untitled%202.png)

---

## Feb 1

*   Adding Feel to some elements - Lock and Shoot camera shake
*   Adding transparency for projectiles so screen is less obstructed
    *   Used DOTween!
    *   Only do this within certain range? or always?
*   Added diamond enemy as shape
*   Looked at ShaderGraph Dissolve Dissolve metallic for enemies but decided differently
*   Added Movement Particle gObj to Projectiles

---

## Feb 2

*   Worked on Death Particle effect, refine process / look
*   Improving bullet movement particle effect
*   Added Warp Effect into game

## Feb 3

*   Adding Unity video recorder plug
*   Moving texture map is here! May want this as well sometime
*   Trying with Koreographer implemention - looks cool!
*   Imported a projectiles package to make proj look better
*   Added muzzle effects to reticle? or just launch or particles?
*   Need impact effect as well
*   Idea - Living Particles for zipping through walls with a break away effect

## Feb 4

*   Flying Animations Asset pack - Icuzo is the dev
*   Doesnt seem to work properly, and see multiple complaints about it not being setup for Humanoid
*   May have to look up animation controller more and look into how to adopt this
*   Comment in reviews suggested it could be converted to Humanoid with some settings
*   Adding Magic Arsenal kit, some interesting effects to work with here

## Feb 8
*   Object Particle Spawner working!
*   Trying to do new music made me think - i really need to do

##May 7

""Add UI target Tracker
""#
*   Graphical Style*
Add animations for the game
### May 8
## June 13
*   Decreased Repeat Attack Delay on Diamonds
*   Reticle now looking at Aim Wall to free up connection with Camera
*   Changed Reticle - Not sure what I think! SOmething like this could good butnot totally there yet
 *  [https://asstst.unity.m/ckageo/vtx/khedeps/retrowove-kmes-dynsm-synwve-kyx-asst-pk-197258](tps://ssetste.uity.com/ptktgcs/vfx/shakers/retrodave-skies-dyeami -sy thwavgtskybrx-assep- ack-197258)

## May 16
### What would I do to get my point across to others in the best manner
*   Japan loves wires and lightpoles and such - very aesthetic!!
*   [https://www.youtube.com/watch?v=VsGG5JYlqIU](https://www.youtube.com/watch?v=VsGG5JYlqIU)
## May 17
*   changing Cubes that trigger sound to gates
SkyPortal Cubemap is awesome - do a level with this!
*   Using Marble Flow Currently - anyway to get this moving and trippy?

## May 22
*   What did the game need?
*   How the player or others can help?
SkyPortal Cubemap is awesome
*   ShaderGraph Skybox and VFX Graph ideas - look into whats possible and what i can do here
    *   Can Iuse my desctructed meshes for shader graph / vfx graph stuff?
    *   [Skinned Mesh Sampling in Unity VFX Graph](https://www.youtube.com/watch?v=bIMyCKr0bFs)

## November 20-22

*   Should have an AutoLOD
    *   Takes just over 1 minute

###May 23

Camera Dutch in level as effect would be cool!
*   Removed Camera Follow script from CM vcam1 becasue Cinemachine seems to work great instead
*   Ignore Time Scale on CinemachineBrain is now enabled
What are themes for this shooting theme""

### May 24
*   Trying out new level designs with GPUInstancer in scene as well
*   Importing modern Sci Fi building back to Blender file for Level Design

## May 25

*   Shooting is always looking at camera, but maybe best to have it look at a wall instead? That way I can switch camera angles and have it looking in different directions based on wall position. f
*   Dynamic Crosshair

### June 1
[https://www.reddit.com/r/Unity3D/comments/v1ombe/ive_always_used_planar_quads_for_decals_but_with/

*   This may be a good thing to follow up on
*   DOFade works when surface type is transparent / blending mode is alpha
  *   Can use code in script  FMOD Studio Parameter Trigger as basis for swithcing sections
    *     https://www.youtube.com/watch?v=agr-QEsYwD0&t=4s/
What do I do that causes Breaking sense of wonder?

### June 2

Used C Scape for Procedural city? or CiDy2?
*   Quick puzzle sections with a puzzle asset?
*   SpriteLights for point lighting effect?
*Advanced Disolve - cool effect?
*   Sci Fi Corridor set - any utility here?

#June6

*   Distortion shader pack!! Looks cool
*   Mirror asset - cool effect! Many Mirrors!
*   What can be learned from Every Extend Extra?
*   https://unityassetcollection.com/sci-fi-forcefield-hologram-free-download/
*   https://unityassetcollection.com/sci-fi-environment-free-download/
*   https://unityassetcollection.com/sci-fi-vehicle-constructor-free-download/
*   https://unityassetcollection.com/hololens-shader-pack-free-download/

### June 17

*   https://www.youtube.com/watch?v=ij_y6rBawl0
*   Dolly relative to player perspective - figure this out!
*   Level Progression
    *   Datastream → City → Weird Void
*   Adding the scanner script on my rewind fx - not working due to no geometry i think?
*   Adding Eye Basics script
*   Does it make sense to just use Magnet regions? Can move forwards and backwards with them

### June 23
Favelas abandoned places - private business and government gave up on them

* Think Reboot Internet ideas, more wild and pirate like.

## Sept 22
*   How do I setup some vibe / lore without going too deep?

## October 13
*   [https://www.youtube.com/watch?v=ixEk6EWrL6Y 12:16 PM]
*   destroyed / destructured asset idea for most enviroment
   * maybe for enemies as well
What did the game need"