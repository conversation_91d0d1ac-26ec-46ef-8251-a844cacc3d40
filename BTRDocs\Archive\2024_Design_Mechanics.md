

- January 2022: Explored using a spawner system and dynamic waypoints for enemy movement.
- January 2022: Experimented with different approaches for player aiming and locking onto enemies.
- January 2022: Investigated using Behavior Designer for more advanced enemy AI patterns like "Scoot and Shoot" and "Flank".
- February 2022: Worked on improving the lock-on and shooting mechanics, including adjusting the timing and visual feedback.
- March 2022: Implemented a Tetris-like gameplay mechanic, including piece movement and rotation.
- March 2022: Worked on integrating the Tetris gameplay with the existing enemy and bullet mechanics.
- March 2022: Explored using Jelly Cube and other puzzle-based mechanics.
- April 2022: Implemented additional UI elements, such as a wave hint and enemy hint, to improve the player's understanding of the game state.
- April 2022: Continued experimenting with level design and enemy AI, including the use of the Ultimate Spawner and Waypoint systems.
- May 2022: Continued experimenting with different enemy and boss designs, exploring the use of geometry nodes and deconstructed shapes.
- June 2022: Explored integrating Tetris-like gameplay mechanics more deeply into the core experience.
- June 2022: Continued work on enemy and boss designs, experimenting with different geometric shapes and deconstructed visual styles.

## 2023.md

- January 2023: Explored ways to improve the camera movement and rotation when the reticle moves towards the edges of the screen.
- February 2023: Experimented with color-based projectile effects, but decided against a global material change approach in favor of a material swap.
- March 2023: Adjusted the shooting movement to prevent the camera from going directly through the ground as much.
- May 2023: Experimented with the idea of a "ricochet/shield" element, but ended up creating a trap-laying mechanic instead.
- May 2023: Focused on balancing the speed of player movement and bullet speed to create a sense of the player being moments away from being hit.

## April 1.md

- April 1: Looking at issues with player character bopping and rotating
- April 1: rotating aimlessly is a A* issue?
- April 1: jittery / bopping is a ground detection issue
- April 1: Seemed to have smoothed out the jittery isssue with lerping on ground detection

## April 10.md

- April 10: Need better planning for the boss stage - gameplay and sequence
- April 10: power ups like rez - upgrade character?
- April 10: Inspired by x men sentinel fight, flashes of color and light in time with the music

## April 12 13 14.md

- April 12 / 13 / 14: Twin snake - on each side
- April 12 / 13 / 14: Polyrhythm shooting tied with music!
- April 12 / 13 / 14: when landing on the beat - dodge! otherwise can shoot back
- April 12 / 13 / 14: how can i do a parry option that fires in direction instead of lock on?
    - could be fun!
- April 12 / 13 / 14: Adding Movement sensor script to twin snakes to make sure they move a little bit if they’re about to animate / hit into the player track. 
- April 12 / 13 / 14: could use get hit left / right for firign things wheh nchasing on left / right
- April 12 / 13 / 14: Setting target to ‘Player Aim Target’ for my precise target for projectiles
- April 12 / 13 / 14: For background
    - Put tail animator snake on curvy spline, have it wiggle around in places
- April 12 / 13 / 14: Need to properly plan out Ouroboros! AND other levels + tech needed

## April 15.md

- April 15: Thinking about what i wrote here, in Mid march, again
> Think ouroboros - surround an orb - shoot any bullets at that orb to take out the health of the current orb and when you are successful a kill all enemies event occurs , per wave
- April 15: Ouroboros - A serpentine creature that encircles the player, creating an ever-shrinking arena. Its attacks are wave-based and must be dodged to the beat.
- April 15: Should i have a speed dash that affects tempo / music?

## April 19.md

- April 19: Im sick it sucks
- April 19: Use scriptable objets to define attributes
    - Projectiles
    - Enemies
    - register projectiles? Register enemies?

## April 22.md

- April 22: Plan out game and levels
- April 22: Include more rhythm elements??
- April 22: Find the fun!!

## April 24.md

- April 24: lockandshoot
- April 24: Need to avoid those situations?

## April 30.md

- April 30: Need to assess if deleting wait for it koreo sets breaks anything in current iteration of game

## April 4.md

- April 4: Started on Idea of Infinite track but following a spline
- April 4: This is potentially how we can have some randomization in the boss battle but restrain to a specific area

## April 5 6.md

- April 5 / 6: Attempting a roughly set / randomized path for the boss fighting. A variation on OuroborosInfinite
- April 5 / 6: Rough things laid down for the twin snakes
- April 5 / 6: Working on the rough forward / reverse time feature of the twin snakes

## April 7.md

- April 7: Working on boss ideas of Twin Snakes / Time

## April 8 9.md

- April 8 / 9: Possible idea - use A* for snakes on  a platform if moving around to follow player? Move slow

## August 2.md

- August 2: Feel like i need to inject a bit more speed in the game
- August 2: Bounce sound could use more note choices, more of a sense of harmony and surprise? Drive some melody? Or refer to unfold Gabor Lazar for change ideas
-

## August 5.md

- August 5: SNake gets hit sound, as well as Death sound needed
- August 5: Section 3 need the enemies and bullet speeds adjusted for faster movement.
- August 5: Consider second snake encounter - maybe not the crazy graphics? WHat else ot make it different

## August 8.md

- August 8: Types
    - Snakes
    - Orbitals

## August 14.md
- August 14: Shifted time features to Game Manager - not fully working but needed to be done!

## August 21.md
- August 21: Adding a score / time boost when cahnging waves / sections.

## August 26.md

- August 26:Been updating this and figuring out whats up
- August 26: QTE Implemented, but errors, need to bug test but it might be interesting!

## August 30.md
- August 30: Between different levels, highlight difference - think of how it plays with your mechanics

## August 2,md
- August 2, Made things faster in first ouroboros level, aslo adjusting camera

## August 14,md
- August 14: SlowToBeat so it effect player movement on spline time as well

## December 4.md

- December 4: Add a brief time-freeze frame when projectiles first spawn
- December 4: Add a "danger zone" visualization that shows where projectiles will be in the next few frames
- December 4: Include a brief warning period before projectiles start moving
-December 4: turning Enemies into more managable structure of EnemyBase and EnemyBehavior

## December 11.md
- December 11: Should i just have all projectiles aim for the direction the player is currently facing? will this mean less mvoement? is this worth playnig with?
-December 11: Change Reticle over to a system based on Shapes asset pack
- December 11: Need proper effect for ricochet - blast no longer works?
- December 11: Shrikn and grow cursor to indicate target locking

## December 26.md

*   PlayerMovement.cs Changes:

    *   Added new fields for rotation cooldown and duration (lines 48-51).
    *   Modified dodge mechanics with cooldowns and effects (lines 55-61).
    *   Added new direction reversal method (lines 618-630).
*   Impact: These changes affect player movement mechanics and control responsiveness.
*   StaticEnemyShooting.cs Changes:
    *   Implemented a structured shooting system with:
        *   Configurable shooting parameters (speed, lifetime, scale, damage).
        *   Shooting request structure for position/direction/speed.
        *   Cooldown system with a minimum shot interval (0.1s).
        *   Integration with EnemyManager for registration/unregistration.
        *   Performance optimizations for projectile spawning.
*   Impact: More controlled and optimized enemy shooting behavior.

## January 10th, 2024.md

*   Trying to do a build of a scene, upgrading things and moving to laptop seem to bring up old issues
*   Use gravity rush enemies as inspiration for my enemies - look up the various types

## January 18th.md

*   Need different radar icons for enemies vs bullets - this is perspective radar not minimap

## January 26th.md

*   Need to give them obstacle avoidance, at least for ground?
*       apply this for both enemy shooting and player shooting?
*   Place static enemies along the snake to also take out? Gives more to do

## January 30th.md

*   Snake Eyeball Prefab is made, should be good to place around multiple areas of the Snake.

## February 2nd.md

*   Implemented hacky LookAt solution so player game object stops rotating

## February 5th.md

*   IMPORTANT
*   to make things brighter / more emissive / stand out, HDR intensity needs to be adjusted along with color. Makes a big difference!

## February 6th.md

*   Should be disabled, need to reassess how this is disabled so nothing gets stuck
*   the enemies should not need to apply force to the bullets - just instantiate them
*   the bullets should move on their own (i think!)
*   Adding animancer pro for better animation control in unity

## February 8th.md

*   Want chained enemy type in Section 3
    *   Makeing them move in a snake like fasion…. unsure this can happen. Will require further deep experiments. Right now it might make the most sense to try something in line with what works right now. have the other parts spn around? use PrimeTween?
*   setting them up as orbitals! testing

    *   Snakes
    *   Orbitals
*   More ideas

    *   Fractured - destroying it breaks into multiple smaller enemies - or projectiles! That are flynig towards the player to hit them.
    *   Tesselated Wall - A large enemy composed of many interlocking polyhedrons that form a wall or barrier. As the player attacks, the polyhedrons detach and become independent attackers.
    *   Elastic - think vertices - A shape that stretches and morphs its shape, making it difficult to predict its movement. It can extend its sides to form spikes or turn into a flat sheet to dodge attacks. Also maybe these vertices are the only lock on poitns?
    *   Mines - these need to be taken out before the player hits them and gets hurt
    *   Phased movement - disappear and reappear in different spots
    *   Large guardians - maybe static in their positon and slow to move, but deal big damage
    *   Reflective shields - attacks boucne off them at differnet times. this could be the time ability in action or just a timed shield based on the music
    *   Cloaked Stalkers: move between invisible and visible! radar comes in handy for this especially

## February 11th.md

*   Fixed SNakes teleport so they use a dissolve effect, may want to adjust shader for galaxy texture, combine with other shaders, etc
*   Deep dive on PSB again. locking things into states, trying to fix bugs.

## February 13th.md

*   Updated A* to 4.95 and trying to fix version control first

## February 25th.md

*   Added Snake head to infinite track, so it’s like you’re being chased by ouroboros
*   Cool idea! Needs refinement, liekly doesnt need to be on its own spline
*   Need a better snake mouth with some aniamtion to pull it off though
*   Take time to assess multiple snakes, try one with animation

## February 27.md

*   Looking at creatures
*   Found a snake that seems good, try applying new animator controls on it, open jaw, see what we can do for chasing player
*   Play / watch some sin & punishment for idea of attach phases here
    *   enemies move up the snake’s back?
    *   snake’s eyes do something?
    *

## February 28.md

*   Snake infinite track - charging attack that you have to hit enough times before it's dismissed

## February 29.md

*   Tried this, but i think the problem is larger. scale of the object changes massively when play mode vs not. unsure why exactly
*   Made some updates to infinite track, working better now. but need a solution to gameplay style that is compelling.

    *   Maybe use player plane as a base and do more traditional rail shooter waves etc?

    *Why look back at snake? to shoot?*

    *   What is that system?
    *   Same people who make animancer have a tail / physics animation system that seems quite good
    *   May be useful for snake models

## March 4.md

*   Working on Infinite track / snake chasing
*   snake eveballs shooting exist so far
*   Create basic class for chasing snake - considered MidBoss
*   They call back to MidBoss with their names
*   Also written into Crosshair so it sees it properly

## March 6.md

*   THinking how each gameplay component combines with others
*   Can you write interesting quests?
*   Is the setting evocative? How does it draw people in?

## March 7.md

*   Lock on working in Infinite Track if the object is on the Enemy Layer and has the Enemy tag.
*   Adding this into cycle of ProjectileStateBased - ProjecitleManager - Crosshair
*   Now considering - i want lock on success to be dependent on how closs your crosshair is to the target when you fire. if you’re relatively close then it will be a success - i think air combat games have a version of this at times
*   Bullet speed and Rotation speed appear to be the limiting factors in hitting the target
*   Havce basic animations for getting hit happening in the Infinite Snake, butthe inherent problem with the scale and positioing seems messed up, and the animation behaves weird. Need to fix at the root of it!

## March 9.md

*DO NOT upgrade the unity version for this creature testing project if you dont wanna break everything!!*

## March 10.md

*   Enemies need to shoot a different colour bullet, rectify this
*   Infinite Snake - figure out gameplay structure here

## March 11.md

*   Enemies need to shoot a different colour bullet,
    *   Material swap for this?
*   Inspo
    *   Hyperpassive as a rubric for the music and interactivity in Beat Remake

*   Also I want to lose the lock on after an enemy gets hit. I think this occurs at two levels
    *   1 - Crosshair loses it’s lock on a moment after shooting at it
    *   2 - once the enemy is hit with a lock on bullet, the lock on anim goes away

    *These should happen in conjunction with each other - i think?*
* Thinking they are shot off and caught by hanging items. Lat think shooting them off… not sure what yet. But having them caught means you can see where they’re going. Need to reposition them to have it work a bit better

## March 12.md

*   Adjusted Rotations on static shooters of mobius 2 (first one you play on)
*   Maybe a longer trail / any trail on the static shot ones would be good?
*   Something to showcase their directionality

## March 15.md

*   Using Claude to improve Crosshair.cs
*   Didnt work! broke stuff i think! or maybe it was good, hard to tell because other stuff broke.
*   Likely will give this a second try once I better understand issue with locking proejctiles
*   *Projectile locking working once again, but not sure if it’s fully fixed.*

*Fix what a star can’t do.  Remove AI from A*
*Not using A* in Infinite Track example - adjust rotation / ground detection accordingly
*Using Claude to improve crosshair again.

## March 18.md

*   Should add a visual to highlight QTE

## March 21.md

*   Use Object Particle Spawner or adapt Uni Bullet Hell into 3D?
*   Privating event calls in Adjust Song Paramters because if the object is persistent across scenes, this is easier way to handle it.
*       There are a lot of events on the wave spawner which need a new setup
*   Should I adjust midboss death so it doesnt call scene switching so directly? Unsure
*   SnakeMidBoss
* Started adding attack - setup basics of animation

## March 22.md

- Started adding attack - setup basics of animation
- Make sure movement is accurate when hit, not always on beat, add visual indication and maybe a counter.

## March 26.md

* Thinking about how I avoid making music for the game… It's more than just procrastination
* And though music generating is easy now, what gives it meaning?

## July 5.md
  * Refine level progression

  * Consider Cube or other shape with eyes on all sides for the interior eye level
 * Make a bit of a rhythm game in that you’re floating in the middle and different eyes are shooting at you - eyelid opens and closes - you need to lock on and shoot an open eye. if it’s closed it doesnt damage them. cube is rotating and you only need to take out 4 of 6 eyes.
   * Ophanim Boss fight idea -similar mechanic to level, but every few you take out, you go deeper on the ring.
   * Get to base of tree - go to reflection of tree
   *  Metatron’s Cube - Player moves to the curvy spline that they’re currently facing while also trying to take out all the enemies
  -Use colour to delineate this
  * Try deform of colliders and see how enemies respond to that. Check performance of altering these things.

##  July 22.md

* Need to opmize gameplay based on the profiler, and check various features
   * Snake Mid boss
   * Level strucutre 
   * Bullet hell implementation and spawning
   * 
##  July 24 25.md

* Going into reverse time with the player is difficult
  * Can we prevent the player from reversing direction and the camera not flipping?

##  July 29 30.md

* Testing game - make adjustment notes
* Transprent material in snake shooting section - why do bullets do this, i forget, not sure i like it
* Locked state material defined on projectile - problem with this level is the scale of the projecitle makes it too big when locked, looks odd - Sclaed down from 10 to 3 currently
* Verify Infinite Snake View Target - is this necessary anymore?
  * Adding deformables to the tail end of the infinit ouroboros generation

## November 4,md
  * Use VFX graph to help improve efficieny
  * Look at better performance to make things happen correctly for time events
  * Create efficient enemy behaviours

## November 11.md

* Need to focus on visualy identifying core mechanics and engagement to make it all work

## Dec 18-19.md

* fix homing for enemy shot projectiles - not homing properly

April 22
- find the themes
Colors can do this as well
 colors can do this as well
 Still picking through more

Other notes

Plan out game and levels

Include more rhythm elements??

Find the fun!!