# Sept 22

Theme / Game / Lore buidling

Start in a test tutorial level 

- Introduce / Immerse you into the way things work in ‘the net’

SUPERHOT

Computer console intro

[SUPERHOT - Full Game Walkthrough 【No Deaths】](https://www.youtube.com/watch?v=n5lu4Gyb1KM)

Boring description of being an anti virus / IT guy, 

X person has a virus on their computer, you have to go in and remove it

interspersed with silly conversations about the virus / what’s wrong with the pc

can bring this back to my own experience dealing with people about computer issues

What are the games I see in a similar camp?

Thumper, Rez, Rollerdrome, Superhot, Neon White, Boomerang X

Hyper Demon?

How do I setup some vibe / lore without going too deep?

Watching Rollerdrome stream on Unity Twitch

Node Canvas vs Behaviour Designer

Seems interesting! Node Canvas may be better?? Used in Rollerdrome

Refine elements

- aiming
    - enemy
    - projectile
- absorb bullet aiming needs fixing
- fix projectiles locking up out of nowhere? whats happening?