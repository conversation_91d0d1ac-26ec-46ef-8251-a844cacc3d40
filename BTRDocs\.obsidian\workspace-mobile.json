{"main": {"id": "e547f557c4c2e654", "type": "split", "children": [{"id": "f0bcd6f4871bb4b1", "type": "tabs", "children": [{"id": "5296013ab790a3f5", "type": "leaf", "state": {"type": "markdown", "state": {"file": "BogHog Design Considerations.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "BogHog Design Considerations"}}]}], "direction": "vertical"}, "left": {"id": "0d24378e8f713f76", "type": "mobile-drawer", "children": [{"id": "a8192362162c2845", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical"}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "81d2c8dc2ddfe174", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "fdebf5474de5edd5", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "2a387a81987ac927", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}], "currentTab": 0}, "right": {"id": "df9d0452ff55bf94", "type": "mobile-drawer", "children": [{"id": "968c5beeafbe6b05", "type": "leaf", "state": {"type": "backlink", "state": {"file": "BogHog Design Considerations.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks"}}, {"id": "667d062b0bf26b74", "type": "leaf", "state": {"type": "outgoing-link", "state": {"linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links"}}, {"id": "5ea84faa0d77508a", "type": "leaf", "state": {"type": "outline", "state": {}, "icon": "lucide-list", "title": "Outline"}}], "currentTab": 0}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false, "syncthing-integration:Open Syncthing conflict manager modal": false}}, "active": "5296013ab790a3f5", "lastOpenFiles": ["Resolutions.md", "KnownIssues.md", "Technical Optimization Roadmap.md", "BogHog Design Considerations.md", "Tech Recs/Performance tips & tricks from a Unity consultant.txt", "Tech Recs/Performance tips & tricks from a Unity consultant.md", "Tech Recs/Insanely FAST Spatial Hashing in Unity with Jobs & Burst.txt", "Tech Recs/Serialize Types in the Unity Editor using Reflection.txt", "Tech Recs/Making Ubisoft’s Prince of Persia_ The Lost Crown with Unity _ Unite 2024 - English (United States).md", "Tech Recs/Advanced Coding Tips For Big Unity Projects.md", "Tech Recs/Advanced Coding Tips For Big Unity Projects.txt", "Tech Recs/<PERSON> Pratical Unity Optimizations.txt", "Tech Recs/Easy and Powerful Extension Methods  Unity C#.md", "Tech Recs/RAYCASTING Made Insanely Fast for Collision Detection.md", "Tech Recs/Unity Stats and Modifiers.txt", "Tech Recs/Better Save Load using Data Binding in Unity.md", "Tech Recs/RAYCASTING Made Insanely Fast for Collision Detection.txt", "Tech Recs/Learn Unit Testing for MVP MVC Architecture in Unity.txt", "Tech Recs/Making Ubisoft’s Prince of Persia_ The Lost Crown with Unity _ Unite 2024 - English (United States).txt", "Tech Recs/Better Save Load using Data Binding in Unity.txt", "Tech Recs/Code Monkey Burst ECS Jobs.md", "Tech Recs/Unity Misconceptions.md", "Tech Recs/Working on a Big Game in Unity.md", "Tech Recs/Learn to Build an Advanced Event Bus Unity Architecture.md", "Tech Recs/Streamline Your Game - Without Being a Memory EXPERT.md", "Tech Recs/Learn Unit Testing for MVP MVC Architecture in Unity.md", "Tech Recs/Unity Optimizations Tips.md", "Tech Recs/Unity Performance Tips - Draw calls.md", "Tech Recs/Insanely FAST Spatial Hashing in Unity with Jobs & Burst.md", "Tech Recs/Serialize Types in the Unity Editor using Reflection.md", "Tech Recs/Code Monkey - DOTS.md", "Tech Recs/<PERSON> Pratical Unity Optimizations.md", "Tech Recs/Hooking Custom Systems into Unity's Player Loop.md", "Tech Recs/The Unity HACK that the PROS know.md", "Tech Recs/Unity Tips.md", "Tech Recs/Unity Stats and Modifiers.md"]}