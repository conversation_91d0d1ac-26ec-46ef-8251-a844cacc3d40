# Feb 29

Series of animations for <PERSON> head decided

Need UMotion editing

Can I apply layer on multiple animations?

![Untitled](Feb%2029%200b8ae405e1f74759a29f7edc7c23f434/Untitled.png)

Did many, seems rotation is off on Idle Open Mouth compared to the others

fixing… 90 in y?

Redid idle position to fix this, but need more work on animations cause it totally throws off positioning - should be able to fix in umotion with root or something like that i think?

maybe just needs to be set?

Tried this, but i think the problem is larger. scale of the object changes massively when play mode vs not. unsure why exactly

Seems like its issues with ROOT_ or above, deleting some of those attributes in the animation seems to have made a difference

Delete top level stuff, seems to have fixed issue. Also deleted tag frames at end, seemed unessecarry due to many animations being shorter than the 2 second place these were placed

They seem to be an error or something i did wrong

Made some updates to infinite track, working better now. but need a solution to gameplay style that is compelling. 

Maybe tail animator can have snakes that follows but move with the physics?

Maybe use player plane as a base and do more traditional rail shooter waves etc?

Why look back at snake? to shoot?

What is that system?

Same people who make animancer have a tail / physics animation system that seems quite good

May be useful for snake models