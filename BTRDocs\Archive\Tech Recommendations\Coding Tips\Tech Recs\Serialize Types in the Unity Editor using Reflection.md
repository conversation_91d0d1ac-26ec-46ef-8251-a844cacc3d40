---
title: Serialize Types in the Unity Editor using Reflection
tags: [Unity, Reflection, Serialization, EditorExtensions]
date: 2025-01-20
---

# [[Serialize Types in the Unity Editor using Reflection]]

## [[Overview]]
This implementation provides a powerful way to work with types in the Unity Editor, enabling more dynamic and flexible systems. The techniques shown can be adapted for various use cases, particularly in systems that require runtime type selection and instantiation.

## [[Implementation]]

### [[Type Extension Method]]
```csharp
public static class TypeExtensions {
    public static bool InheritsOrImplements(this Type type, Type baseType) {
        type = ResolveGenericType(type);
        baseType = ResolveGenericType(baseType);

        while (type != typeof(object)) {
            if (baseType == type || HasAnyInterfaces(type, baseType))
                return true;

            type = type.BaseType;
            if (type == null) break;
        }
        return false;
    }

    private static Type ResolveGenericType(Type type) {
        if (!type.IsGenericType)
            return type;
        return type.GetGenericTypeDefinition();
    }

    private static bool HasAnyInterfaces(Type type, Type baseType) {
        return type.GetInterfaces()
            .Any(i => ResolveGenericType(i) == baseType);
    }
}
```

### [[Type Filter Attribute]]
```csharp
public class TypeFilterAttribute : PropertyAttribute {
    public Func<Type, bool> Filter { get; private set; }

    public TypeFilterAttribute(Type baseType) {
        Filter = type => type.InheritsOrImplements(baseType) &&
                        !type.IsAbstract &&
                        !type.IsInterface &&
                        !type.IsGenericType;
    }
}
```

### [[Serializable Type Class]]
```csharp
[Serializable]
public class SerializableType : ISerializationCallbackReceiver {
    [SerializeField] private string assemblyQualifiedName = string.Empty;
    private Type type;

    void ISerializationCallbackReceiver.OnBeforeSerialize() {
        if (type != null)
            assemblyQualifiedName = type.AssemblyQualifiedName;
    }

    void ISerializationCallbackReceiver.OnAfterDeserialize() {
        if (TryGetType(assemblyQualifiedName, out Type result))
            type = result;
        else
            Debug.LogError($"Failed to deserialize type: {assemblyQualifiedName}");
    }

    private bool TryGetType(string name, out Type result) {
        result = Type.GetType(name);
        return result != null && !string.IsNullOrEmpty(name);
    }
}
```

### [[Property Drawer]]
```csharp
[CustomPropertyDrawer(typeof(SerializableType))]
public class SerializableTypeDrawer : PropertyDrawer {
    private string[] typeNames;
    private string[] typeFullNames;

    public override void OnGUI(Rect position, SerializedProperty property, GUIContent label) {
        Initialize();

        var aqnProperty = property.FindPropertyRelative("assemblyQualifiedName");
        string currentName = aqnProperty.stringValue;

        if (string.IsNullOrEmpty(currentName)) {
            aqnProperty.stringValue = typeFullNames[0];
            property.serializedObject.ApplyModifiedProperties();
        }

        int currentIndex = Array.IndexOf(typeFullNames, currentName);
        int newIndex = EditorGUI.Popup(position, label.text, currentIndex, typeNames);

        if (newIndex >= 0 && newIndex != currentIndex) {
            aqnProperty.stringValue = typeFullNames[newIndex];
            property.serializedObject.ApplyModifiedProperties();
        }
    }

    private void Initialize() {
        if (typeNames != null) return;

        var filter = (attribute as TypeFilterAttribute)?.Filter ?? (t => true);
        
        var types = AppDomain.CurrentDomain.GetAssemblies()
            .SelectMany(a => a.GetTypes())
            .Where(filter)
            .ToArray();

        typeNames = types.Select(t => t.ReflectedType != null ? 
            $"{t.ReflectedType.Name}.{t.Name}" : t.Name)
            .ToArray();

        typeFullNames = types.Select(t => t.AssemblyQualifiedName)
            .ToArray();
    }
}
```

## [[Usage Example]]
```csharp
public class Hero : MonoBehaviour {
    [Header("State Machine")]
    [TypeFilter(typeof(IState))]
    public SerializableType initialState;

    [Header("Event Bus")]
    [TypeFilter(typeof(IEvent))]
    public SerializableType gameOverEvent;
    public SerializableType levelCompleteEvent;

    void Start() {
        Debug.Log($"Initial State: {initialState}");
        Debug.Log($"Game Over Event: {gameOverEvent}");
    }
}
```

## [[Best Practices]]
1. Use [[Type Filtering]] to restrict selection to relevant types
2. Cache [[Type Information]] to improve performance
3. Handle [[Type Resolution]] failures gracefully
4. Consider [[Namespace Organization]] for friendly type names
5. Be mindful of [[Performance]] when scanning assemblies

## [[Additional Resources]]
- [[Unity Documentation: Property Drawers]]
- [[Reflection Best Practices]]
- [[Type Serialization Patterns]]
- [Unity Editor Scripting Guide](https://docs.unity3d.com/Manual/EditorScripting.html)
- [Advanced Reflection Techniques](https://example.com/advanced-reflection)