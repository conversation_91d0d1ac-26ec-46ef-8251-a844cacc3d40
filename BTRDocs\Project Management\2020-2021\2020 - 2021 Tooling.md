Tooling & Workflow
April 7th

MAKE A BACKUP SYSTEM WORK!!!!

April 8th

Added Odin Inspector

April 7th

Backup working with Github - maybe use SourceContol as a second backup?

May 22

Imported EasySave

June 17

Pipeline errors - spent the morning fixing that.

Need to get in Pro Builder and Pro Grids, etc

July 1

Also - reconnect to Source Tree!

August 14

Cleaning up office!

Setup Bugs sections for Notion

Looking over some technical documentation on assets used -

Koreogrpaher and Object Particle Spawner

Jan 13

Develop targetting system more!!! Need a proper bug and dev categories / log to keep track of these things and mark them as DONE