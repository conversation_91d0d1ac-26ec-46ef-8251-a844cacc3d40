# Jan 30

Working through entire Crosshair class

SIDENOTE: Figure out how to hide elements in debug panel versus general inspector

<PERSON> Emerald AI Projectile class issues - I THINK - deleted EmeraldAI to try and rectify this

This was due to some paritcle prefabs I think! I didnt totally dissociate them from EmeraldAI

Believe I can safely re-add if needed

[Crosshair / Projectile bug Fixing](Jan%2030%20c3d856e971c741c29901f69bcc0c3dd3/Crosshair%20Projectile%20bug%20Fixing%2036853ac3d16c4df08caaf2ae442ac77b.md)

Analyzing system - need to continue with how gameobjects move through states - what might be going wrong

Also need to get Wave Spawner properly working

Bullet homing issues - can this be solved easily?

Pause button not working??

Spawner points properly working with Spawn Master

Need to get Wave Graphs working - now it’s working!!

Need to look over Koreographer files to figure out if my implementation still makes sense

Updates for Slack channel ! January roundup 

- A star pathfinding - moving platform - moving enemies
- Wave spawning - inspired by Boomerang X
- Code revisions!
- Music tools - Output Arcade, Slate + Ash libraries, HARD sampling

Things to look investigate

- Update Radar
- Investigate FMOD integration
    - - [https://www.youtube.com/watch?v=Dg32zkZt5e0&](https://www.youtube.com/watch?v=Dg32zkZt5e0&)