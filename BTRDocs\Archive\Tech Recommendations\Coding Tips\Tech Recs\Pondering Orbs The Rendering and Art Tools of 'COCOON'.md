
# Pondering Orbs: The Rendering and Art Tools of 'COCOON'
![](https://i.ytimg.com/vi/_xbGK_5wlfs/maxresdefault.jpg)



## Initial Development and Lighting Techniques (2017-2020)
- The Cocoon game development began in 2017 with <PERSON><PERSON>, and later, <PERSON> and <PERSON>, the [Art director](https://en.wikipedia.org/wiki/Art_director), joined the project to solve visual mysteries of the game's cosmic journey [(00:00:57)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=57s).
- In late 2018, <PERSON> joined the team to find solutions to the technical puzzles of the production, and after years of hard work, the game was finally developed [(00:01:13)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=73s).
- The game was shipped on Unity 2020 LTs using Euro P 10, and the team replaced the global illumination techniques found within the engine with their custom indirect lighting solution [(00:01:57)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=117s).
- The custom light baker was implemented to replace the original indirect lighting, which used two directional lights stored in a uniform spherical harmonic, to achieve higher fidelity and support for transparent particles, fog, and animated or skin meshes [(00:02:05)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=125s).
- The team used I radiance, a term for incoming light or light that can be seen from a given point, which is then turned into radiance by materials, and their light probes are first order, meaning they have two bands, one of an ambient component and another band of three directional components [(00:02:31)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=151s).
- Each of the four components is full sign HTR color, stored as half precision floating point during baking, adding up to 192 bits per vel, but the team used BC six H, a hardware texture compression for unsigned HDR color textures, to reduce the storage size [(00:02:57)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=177s).
- The team also used a technique to remove the sign from the directional part by adding the ambient part to the directional part and using BC six H on the rest, reducing the storage size to 32 bits per axel [(00:03:29)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=209s).
- Another technique, BC1, a hardware texture compression technique used for normalized ranges, could help if the directional component was fixed range instead of floating range, by dividing the directional part by the ambient part and multiplying again while reading it [(00:03:42)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=222s).
- The use of Voxels in the rendering process helps to reduce noise and achieve a tolerable level of quality, even at 24 bits per voxel [(00:03:58)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=238s).
- Voxels also allow for the avoidance of parameterizing measures, eliminating the need for light map UVs, although light maps, charts, and atlases are still utilized [(00:04:06)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=246s).
- Charts are used to create bounding boxes around sub-scenes, and multiple charts are combined to form an atlas, which is used to organize and render the scene [(00:04:20)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=260s).
- To create charts, ray tracing is employed, but instead of implementing a dedicated ray tracing API, the existing restorer is used, which is essentially a ray tracer that traces a grid of rays [(00:04:39)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=279s).
- The restorer is modified to render a camera from an isometric point of view with a texture array, and the view and projection matrices are defined by the chart's transform and [Bounding volume](https://en.wikipedia.org/wiki/Bounding_volume) [(00:05:03)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=303s).
- The [Shader](https://en.wikipedia.org/wiki/Shader) is used to set the slice ID to the instance ID and offset the position by the slice ID, allowing each slice to start at a particular point [(00:05:23)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=323s).
- A ray matrix is used to control the ray direction, offset, and near and far planes, allowing for random directions and jitter to be applied [(00:05:41)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=341s).
- The ray matrix is used to avoid self-shadowing and other issues, and is multiplied in the vertex shader to make it compatible with the baker [(00:06:10)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=370s).
- The use of a single shader for everything helps to avoid copy-paste errors and makes the process more efficient [(00:06:31)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=391s).
- Additional details, such as vertex exclusion, can be added using regular physics rays and a sper harmonic triple product, allowing for the composition of multiple feral data together [(00:06:49)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=409s).
- The use of vertex exclusion and other techniques allows for the reduction of voxel resolution and manual painting of details, giving a nice way to darken the ambient if needed [(00:07:15)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=435s).

## Orb Effects and Rendering
- The orb in the game has a point light that has been removed to build up the effect again, starting with the sun directional light to cast shadows [(00:08:18)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=498s).
- The orb's glass material only casts shadows with its back face to prevent self-shadowing the interior [(00:08:37)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=517s).
- The dynamic objects in the scene can also cast shadows, and the point light has shadows from the diorama made using a baked shadow texture [(00:08:47)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=527s).
- The character still casts shadows on the ground even without point light shadows, achieved through a capsule shadow technique [(00:09:10)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=550s).
- The capsule shadow technique involves projecting lines from the light source down to the ground, calculating the distance, and adding a feather blur [(00:09:39)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=579s).
- The mist in the fog is created using a noisy height and depth fog that gives the interior of the orb some volume [(00:09:58)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=598s).
- The height fog is used to blend the diorama into the fog, while the depth fog makes the orb opaque for performance reasons [(00:10:12)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=612s).
- The depth fog is combined with a noise texture to create the final effect, which is achieved through ray marching in a fixed number of steps [(00:10:27)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=627s).
- The reflective glass material is built up through a series of effects, starting with a specular highlight that works with any light type [(00:11:04)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=664s).
- The orb's contribution is composed of several components, including a fake [Reflection (computer graphics)](https://en.wikipedia.org/wiki/Reflection_(computer_graphics)) that wraps the environment texture over the orb, making it fit into any environment despite its glowing and emissive properties [(00:11:26)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=686s).
- The fake screen space reflection was chosen over real screen space reflections because it looked better [(00:11:37)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=697s).
- The orb's effect has several steps, including rendering a world within the world, which is achieved by using two cameras and composing them together [(00:12:26)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=746s).
- To optimize performance, the cameras are set up to only render what is necessary, using techniques such as cropping the color matrix of the camera to the orb and using a scissor rectangle to eliminate unnecessary pixels [(00:13:10)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=790s).
- The orb is rendered early in the depth preparation to prevent unnecessary fragments from being shaded [(00:13:45)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=825s).
- The two cameras are composed together using a single resolve or post effect, which saves on post effects [(00:13:56)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=836s).
- To get the player to render in both cameras, the player is rendered in the main camera and then teleported to the other camera using a relative transform [(00:14:31)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=871s).
- A static shadow map is used to reduce the number of shadow draw calls and textures in foreign environments [(00:14:50)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=890s).
- The static shadow map is a low-resolution shadow map that is used specifically for this purpose [(00:14:50)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=890s).
- The game uses a shadow map for the entire world, stored as BC6 PC4, which remains unchanged throughout the game and is used for features like the orbs [(00:14:55)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=895s).

## Fish Eye Effect and Fog Implementation
- A fish eye effect is used to reduce the number of fragments that need to be rendered, allowing for a lower GPU load by rendering only a part of the screen and stretching it to fill the rest [(00:15:08)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=908s).
- The game uses 16-bit depth during the depth pre-pass, which can cause artifacts but is mitigated by moving the near plane far away when warping [(00:15:44)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=944s).
- The fog in the game is a voxel solution, approximately 128x64x256 in size, and uses single scattering in its shading, holding the color of the light, material, and alpha at each voxel [(00:16:20)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=980s).
- The fog is pre-integrated, allowing for efficient rendering of fog contributions between the eye and any given point, and is compatible with features like [Multisample anti-aliasing](https://en.wikipedia.org/wiki/Multisample_anti-aliasing) [(00:16:45)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1005s).
- The fog receives Voxel lighting, which helps to integrate it with the environment, and can be seen when comparing the fog with and without Voxel lighting [(00:17:19)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1039s).
- The game uses spatial filtering to filter the fog, specifically mid-maps, which allows for quick effects on the fog for gameplay reasons [(00:18:16)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1096s).
- The mid-maps are generated using a method similar to the classic or [Killzone 2](https://en.wikipedia.org/wiki/Killzone_2) style stable cascades, but with stability across the board at each resolution [(00:18:38)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1118s).
- The fog in the game is sampled with ddxddy and DD set, which isn't a hardware thing but rather an analytical process [(00:18:49)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1129s).
- The fog has a localized solution, requiring the creation of fog bounding volumes, which are defined by a regular box transform [(00:20:05)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1205s).
- The box transform is a bounding box that can be set up, and as the camera zooms in, the density of the fog increases [(00:20:10)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1210s).
- The fog's color and density can be modified, and turbulent noise can be added to create a more realistic effect [(00:20:21)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1221s).
- The fog is sampled with dvxddydd set for manual MP selection, which prevents texture aliasing [(00:20:28)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1228s).
- The rasterizer is used to render the fog bounding volumes, drawing them as quads instead of boxes [(00:21:02)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1262s).
- The quads are rendered as cross-sections of a box, using the slice ID to assign the instance ID [(00:21:11)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1271s).
- A line trace is used to find the intersection of the slice plane and the box, allowing the fog to be drawn within the bounds of the box [(00:21:24)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1284s).
- The fragment position is set to the T value of the line trace, using the rasterizer's built-in plane clipping to ensure that only the four sides of the box are colored [(00:21:50)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1310s).
- This method allows for the generation of fragment threads only where needed, utilizing the GPU's capabilities and built-in hardware blending [(00:22:08)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1328s).
- The game features multiple fog volumes combined together to create a unique visual effect [(00:22:20)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1340s).

## Crystalline Bridge and Water Effects
- After gaining a new ability from each world orb, an asteroid belt appears, revealing a crystalline bridge with structures made up of cellular crystals [(00:22:45)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1365s).
- The crystalline bridge is created using a volumetric Voronoi diagram made with a regular point grid, orbited around with a pseudorandom value [(00:23:04)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1384s).
- The interior and exterior of the bridge are shaded using a regular lighting function, with refraction and blending effects to achieve the interior look [(00:23:21)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1401s).
- To add color to the bridge, a scrolling texture is used along with Lambert and [Fresnel lens](https://en.wikipedia.org/wiki/Fresnel_lens) products, multiplied on top of the specular and diffuse color [(00:23:37)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1417s).
- The ability to reveal the bridges is achieved using an alpha-dissolved circle driven by the asteroid belt, quantized to be per crystal [(00:24:08)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1448s).
- The game uses a technique of having interior rendering as an effect on geometry, which is also used in various emissive effects throughout the game [(00:24:38)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1478s).
- In another world, a baggish water effect is added, which includes refraction and reflection effects on the whole world [(00:25:04)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1504s).
- The water also features fog, which is created using a regular fog volume with a bright fog on top of the water and a dark fog below [(00:25:22)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1522s).
- The water surface uses blurry refraction, which is achieved using a standard grab or opaque pass texture, working naively with the fog [(00:25:31)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1531s).
- The fog is applied during regular opaque or transparent passes, allowing for nice shadows and indirect shadows [(00:25:56)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1556s).
- The normals of the refraction in the water use a technique called a shore map, which is a [Signed distance function](https://en.wikipedia.org/wiki/Signed_distance_function) (SDF) to the nearest shore as a vector, to create edge ripples and help ground the water to the environment [(00:26:17)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1577s).
- The shore map is used to animate the distance with a sign and fade it with the distance of the SDF, and then multiply the nearest direction onto it [(00:26:26)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1586s).
- The texture used for the shore map is an RGB texture with red and green for direction, and blue for the fall off or distance [(00:26:42)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1602s).
- The texture is generated offline using a tool that compresses it, and is rendered with a giant camera above the entire world using a replacement [Shader](https://en.wikipedia.org/wiki/Shader) and geometry shader [(00:26:48)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1608s).
- The normals on top of the shore map use a technique called isotropic stretching, which stretches out the normals using the shore vector to create a slowing effect along the shoreline [(00:27:20)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1640s).
- The combination of procedural ripples and textured normals creates a nice mix, especially with the isotropic stretching along the side [(00:27:44)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1664s).
- The game uses planar reflections, which are rendered with a low-res camera and use [Multisample anti-aliasing](https://en.wikipedia.org/wiki/Multisample_anti-aliasing) to hide the coarseness of the reflection [(00:27:52)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1672s).
- The planar reflections also use static shadows to avoid shadow rendering in the reflections [(00:28:06)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1686s).

## Diorama and Rain Effects
- The game features handmade diorama geometry for each warp point, which is rendered with a green skybox, green light, and fog to flatten the look [(00:28:26)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1706s).
- The reflections in the diorama use the same tricks as the planar reflections, including pruning in the color matrix and using a depth mask [(00:28:54)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1734s).
- The game uses an analytic specular light instead of a skybox sun, which creates a shimmery shiny effect [(00:29:53)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1793s).
- The technique used to achieve correct shadows for the gypsies involves sampling the depth buffer of the reflection and using the sample comparison state on the far plane [(00:30:15)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1815s).
- The rain effect is created using a Verona diagram texture that animates a distance with a sign and multiplies direction on top of it, with randomization achieved through an index that scatters the animation [(00:30:44)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1844s).
- The Verona diagram texture uses the red and green channels for direction, the blue channel for distance, and the alpha channel for the randomization index [(00:31:07)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1867s).
- The crystalline bridges and the [Signed distance function](https://en.wikipedia.org/wiki/Signed_distance_function) of the shore come together to create the rain effect [(00:31:18)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1878s).

## Vanishing Trick and Membrane Effects
- The second orb ability is a vanishing trick that turns solid volumes into a transparent fog effect, allowing the player to ascend through columns [(00:31:39)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1899s).
- The shading for the columns is done using a ray march defect, similar to the orbs, but with a bounding box and distortion [(00:32:11)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1931s).
- The shading model for the columns uses non-directional indirect lighting and samples sunlight and orb light [(00:32:29)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1949s).
- The transition between the solid and transparent states of the columns is achieved through a multi-layered process, involving fading, clipping planes, and a glow effect [(00:33:09)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=1989s).
- The fading of the column is done using a smooth step function based on the height of the effect [(00:33:22)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2002s).
- The column itself is clipped using an SV clip distance, and a glow effect is faded in on top to hide the seam [(00:33:29)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2009s).
- The rendering of orbs involves using a simplified glowy [Shader](https://en.wikipedia.org/wiki/Shader) and rendering the same mesh again with the opposite clip plane to create a transitional effect between solid and transparent versions [(00:33:42)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2022s).
- The clip plane is used to achieve transitions between transparent and opaque objects without affecting hardware performance, as long as it's not used with features like SV coverage or discard [(00:33:59)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2039s).
- In the alien world, the translucent skin membranes use refraction to display the organic interior, with the exterior supporting a normal map and oscillating emissive textures for bioluminescence [(00:34:32)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2072s).
- The interior of the membranes uses an offscreen texture to render interior parts, which is then sampled with refractive normals and directional blur to create a refraction effect [(00:35:03)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2103s).
- The offscreen texture is rendered at a lower resolution to conserve resources, and the graph pass is downsampled to give it an initial blur, with directional blur added to hide bilinear artifacts [(00:35:29)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2129s).
- In the game's underground area, the organic interior membranes are lit up, and the exteriors cast shadows with their back faces, while the interiors are not shadowed by the exterior [(00:36:10)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2170s).
- The membranes use an offscreen texture to avoid occlusion artifacts, especially when the player can walk on top of them [(00:36:33)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2193s).

## Refraction, Depth Masks, and Amber Effects
- The game features a refraction effect with fog, achieved by using a depth range with color on top, and sampling it with bilinear filtering or a sample comparison state to get a nicely anti-aliased and filtered mask [(00:37:44)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2264s).
- To avoid edge artifacts around discontinuous objects when using a range or smooth step operation, a trick is to use gather red and then interpolate, similar to smooth PCF (Percentage Closer Filtering), which has proven to be a useful tool in various situations [(00:38:27)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2307s).
- Depth masks are used extensively throughout the game, including for grounding reflections, and applying a smooth step operation before interpolation helps to eliminate stair-step artifacts [(00:38:49)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2329s).
- The same technique is applied to refraction to remove fringes around objects, which is particularly important when MSAA ([Multisample anti-aliasing](https://en.wikipedia.org/wiki/Multisample_anti-aliasing)) is the only anti-aliasing method used [(00:39:07)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2347s).
- In the game, there are creatures called "work drivers" or "borbo" that can steal the player's orb, which is then encased in amber and visible through a refraction effect [(00:39:51)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2391s).
- The refraction effect uses an offscreen texture and a volume light to fill the interior of the amber, creating a spilling effect into the surroundings [(00:40:29)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2429s).
- The effect is achieved through ray marching inside a sphere, using a cookie texture and an inverse square fall-off, with angular marching invented by Simon Brown to reduce noise [(00:40:47)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2447s).
- Optimizations were made to the texture and arithmetic operations, including moving the heterogeneous part outside the loop, using bilinear interpolation to avoid cookie noise, and avoiding trigonometry in the loop by using linear rotation [(00:41:16)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2476s).
- The game uses volume lights for orbs, portals, and other objects to achieve sharp volume lighting, which is different from the Foxal fog used in the game [(00:41:58)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2518s).
- The game features a world where amber is being fracked, processed, and stored in pools, which is essentially just water with refraction and no planar reflections [(00:42:10)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2530s).
- The amber pools have a lot of fog and act as an emissive during light baking, which contributes to their appearance [(00:42:13)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2533s).
- The game's world has amber pods that use the same [Shader](https://en.wikipedia.org/wiki/Shader) as the amber crystals, which have a volume light inside them [(00:42:31)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2551s).
- The volume light inside the amber crystals and pods lights up the fog in a particular shot, creating a cauldron of fog effect [(00:42:42)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2562s).

## Ancestor Effects and Sun World
- The game features multiple worlds, each with its own unique characteristics, and the player has traveled to three worlds so far [(00:42:50)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2570s).
- The game's ancestors have a statue appearance before awakening, which is achieved using a shaded trick similar to the cloak effect, but with a dark blue LBI color [(00:43:48)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2628s).
- When the ancestors prepare to jump, they dissolve into a cloud of particles, which was originally done using two shaders: a geometry [Shader](https://en.wikipedia.org/wiki/Shader) and a tessellation shader [(00:44:05)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2645s).
- The tessellation shader ensured equal particle size, but it didn't ship, and a new approach was used to achieve the same effect [(00:44:17)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2657s).
- The new approach uses a preprocessor to write a vector to the midpoint of the mesh, which is then transformed correctly into world space [(00:44:43)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2683s).
- The effect scales down each triangle into a smaller point, gives them a fuzzy random spin, and orbits them in a turbulent cloud-like motion [(00:45:10)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2710s).
- The game's sun world has a faint warm glow and a cold atmosphere, and it plays heavily with light and fog [(00:45:30)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2730s).
- The sun world features orbs that emanate energy, either light or ripples, and each orb has a secret inside, which uses the same shader as the amber [(00:45:52)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2752s).
- The volume light effect in the game is achieved by transforming the light into an orb, which is a creative reuse of an existing effect [(00:46:01)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2761s).
- The ripples on the ground are created using a [Signed distance function](https://en.wikipedia.org/wiki/Signed_distance_function) (SDF) technique, similar to the water shore effect, but applied to spheres instead of the shore [(00:46:13)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2773s).
- The ripples are not texture-based, but rather use a uniform array in the [Shader](https://en.wikipedia.org/wiki/Shader), similar to forward lights [(00:46:24)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2784s).
- The waves emanating from the orb on the portal are also created using a uniform array [(00:46:32)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2792s).
- The footsteps on the ground use a similar technique, which is a nice alternative to offscreen ripple or normal texture methods [(00:46:39)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2799s).

## World Lighting and Puzzle Design
- The world's lighting is composed of multiple elements, including fog, bespoke lighting, and a big emit for the hot underglow effect [(00:47:04)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2824s).
- The fog and lighting are designed to work together to separate the playground and foreground, with the fog doing some of the heavy lifting [(00:46:57)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2817s).
- The lighting uses a combination of cold and hot colors to create depth and separation [(00:47:17)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2837s).
- Baked emissive lighting is used extensively in the game to light up scenes, especially in areas without sunlight [(00:47:26)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2846s).
- The game's world-within-worlds puzzle features a structure that uses a camera-like effect to create a duplicated environment [(00:48:45)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2925s).
- The structure uses a combination of techniques, including a rectangle, mass edge, and depth pass, to create a seamless transition between the two environments [(00:49:10)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2950s).
- The game's rendering engine is optimized to work on the [Nintendo Switch](https://en.wikipedia.org/wiki/Nintendo_Switch), with techniques such as not rendering unnecessary fragments [(00:49:20)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2960s).
- The game's shadowed fog effect is created by combining shadowed fog with light that passes through the door, creating a seamless transition between the two environments [(00:49:33)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2973s).
- The light is duplicated on either side of the door to create a consistent lighting effect [(00:49:58)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=2998s).
- The door shadow is created using two planes that stick to the door frame, which are then used to create the shadow effect [(00:50:05)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=3005s).
- The duplicated light should not shadow around the sides, and the transition through the portal plane involves teleportation, but the exact frame is not visible [(00:50:11)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=3011s).

## Gray World, Purple World, and Desert Scene
- In the gray world, there's another bus that was skipped ahead, and it's a complex issue [(00:50:25)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=3025s).
- The scene with the purple world, water, and a door introduces another recursive camera, but the coloring, cropping, and masking help simplify it [(00:51:10)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=3070s).
- The scene with refractive orbs, ripples on the ground, and a crystalline bridge was a major profiling benchmark for the game [(00:51:26)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=3086s).
- The scene features expensive elements like Misty Columns, making it a challenging but well-designed scene [(00:51:34)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=3094s).
- The game's final part is not spoiled, but it's promised to be weird and exciting [(00:51:42)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=3102s).
- In the desert scene, the fog density is concentrated around shadows to exaggerate them, creating a unique visual effect [(00:52:40)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=3160s).
- The shot from the review trailer features a world that wasn't present initially, and the sun's atmosphere is shadowed by its architecture [(00:53:58)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=3238s).
- The shadowing effect is achieved using a regular cookie texture, rather than a bespoke Raymark defect, and the fog is used to create a spotlight effect [(00:54:27)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=3267s).
- The spotlight is not a point light, but rather a spotlight that allows for self-shadows, which would be difficult to achieve with a point light at a distance [(00:54:55)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=3295s).
- The planet's atmosphere is represented by a round fog volume that uses a custom blob shadow function to create a shadow effect [(00:55:23)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=3323s).
- The fog can be animated without temporal sampling issues, and a shallow wall of higher density fog is added to exaggerate the space fog [(00:55:38)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=3338s).
- The intersection of the planet and the fog is used to create a 100% shadowed area, which is achieved by adding a higher density fog at the intersection [(00:56:08)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=3368s).

## Conclusion and Acknowledgements
- The presentation is coming to a close, with the speaker concluding their journey through the topic of rendering and art tools in 'COCOON' [(00:56:32)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=3392s).
- The speaker expresses gratitude to their colleagues for supporting them in creating an elaborate talk [(00:57:31)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=3451s).
- Appreciation is also extended to individuals who encouraged the speaker to give the talk, including AJ G and ye [(00:57:36)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=3456s).
- The speaker thanks Kisa and the advisory board for trusting them with content, as well as Tuki for lending them a MacBook [(00:57:45)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=3465s).
- The speaker invites the audience to rate their talk and give it five stars [(00:58:33)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=3513s).
- With two minutes and 30 seconds remaining, the speaker invites the audience to ask one final question before the presentation concludes [(00:58:16)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=3496s).
- The speaker and the rest of the team will be available to talk outside after the presentation [(00:58:26)](https://www.youtube.com/watch?v=_xbGK_5wlfs&t=3506s).
