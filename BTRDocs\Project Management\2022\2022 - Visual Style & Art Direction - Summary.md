# 2022 - Visual Style & Art Direction - Summary

This document summarizes the visual style and art direction notes for 2022, focusing on inspirations, shader explorations, and visual effects.

## Key Summary Points:

*   **Tunic-Inspired VFX (April):**  Extensive analysis of visual effects in Tunic, particularly scrolling textures, chromatic aberration, and post-processing techniques. Aimed to replicate and adapt Tunic's shader and texture effects using Shader Graph in Unity.
*   **Shader Exploration (April, Feb, June):** Experimented with various shaders, including Quibli Shaders, Shader Graph dissolve effects, distortion shaders, and advanced dissolve techniques. Investigated transparency for projectiles and surface shaders for decals.
*   **Post-Processing Techniques (April):** Deep dive into post-processing, inspired by Tunic, including SSAO, bloom, Amplify Color, and custom lookup tables. Explored color grading, gradient application, and shadow saturation to enhance visual depth and style.
*   **Particle Effects (Feb, May):** Focused on refining particle effects for various game elements like death, bullet movement, muzzle flashes, and impacts. Explored object particle spawners and "living particles" for unique visual trails.
*   **Reticle Design (May, June):** Iterated on reticle design, experimenting with different styles and considering dynamic crosshairs for better player feedback.
*   **Environment and Level Visuals (May, June):** Explored sky portal cubemaps, marble flow textures, and procedural city generation (C Scape, CiDy2) for level backgrounds and environments. Considered themes of digital corruption and Japanese-inspired aesthetics with wires and light poles.
*   **Camera Effects (Feb, May, June):** Implemented camera shake for lock and shoot actions, warp effects, and experimented with camera dutch angles for dynamic level presentation. Investigated dolly zoom effects relative to player perspective.
*   **Asset Store for Visual Resources (Feb, June):** Leveraged the Unity Asset Store for visual assets, including projectile packages, magic arsenal kits, flying animations, distortion shader packs, mirror assets, and sci-fi environment/vehicle assets.
*   **Animation Integration (May):** Planned to add animations to the game to enhance visual feedback and character/enemy 표현.

This summary reflects a strong emphasis on visual polish and experimentation throughout 2022. The focus on shaders, post-processing, and particle effects, combined with inspirations from games like Tunic and a broad exploration of Unity Asset Store resources, indicates a drive to create a visually compelling and stylistically distinct game.