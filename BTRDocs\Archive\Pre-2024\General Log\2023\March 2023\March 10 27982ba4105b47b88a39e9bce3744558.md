# March 10

Mario day

Looking into GPU bound of performance - optimization!

Unity Crashing “Stack trace could not be displayed. See the latest crash report generated in Editor Log by clicking the "Go to crash logs" button”

Optimization - Additional Camera existed that was doing nothing! Taking up resources 😟

Important to check everything

Currently working very well! 60 FPS on steam deck with current settings. BUTO takes a good chunk of rendering resources but not an issues at this point. 

Things to tackle

- Fix functionality of tracking on reticle
- Fix bullet trajectory calculations
- Work on Menu UI more
    - Add HUD Scaling
    - Add MAP scaling
- ~~Find a way to turn dev console on/off~~
- Refine HUD UI
- Try alternative control sceme

Fixed projectile UI being the wrong number

Need to fix Enemy UI - update when enemy dies?

Maybe represent differently? Is it important? 

Bullet absorb mode - change music to something more chill when this happens? 

Instead of a glitch

Absorbing is a bit more relaxed, adds health / time to the clock 

make bullets reduce score / health and try this out

How can i make using the absorb feature more engaging? Is it enough? 

Is there a way to make it somewhat timed? 

**Shaders! Things to do**

Need to learn

- Texture Maps
- Normal Maps
- Emission Maps

How should  I set Alpha Clipping?

Do I need different Materials for different colors of same shader? Just use GPU Instancing?

Playing with texturing and effects on <PERSON>hani<PERSON> 6

Looking at bringing more detail and interest to things - experiments!

Added alternative control schemes

Not sold any on any yet but I think they’re moving in the right direction

Consider FPS / Third Person ideas for best practices here

Concept thoughts

Ophanim Rings turn into tower pieces many rings into structure?

Rings split off into many pieces? 

To rebuild the tower

What if the rings ARE the tower? 

The rings expand outward until you reach the divine… god…. 

What if the rings exist around planets? You’re attempting to reach the center of each 

Section 1 / ring infiltration

Section 2 / Different worlds

GAMMA SPACE

- Think about these categories from the perspective of someone who doesn’t know these things