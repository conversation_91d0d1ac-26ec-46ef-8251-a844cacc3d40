---
title: Basic Enemy
date: 2024-01-15
tags: [enemy, basic, configuration]
aliases: [BasicEnemy]
---

# Basic Enemy

## Overview
The Basic Enemy is a standard enemy type that implements core combat and movement behaviors. It serves as a foundation for more complex enemy types.

## Configuration
```yaml
# Core Settings
maxHealth: 10
moveSpeed: 20
isVulnerable: true
clockKey: "Test"

# Combat Settings
minPlayerDistance: 5
maxPlayerDistance: 10
hitsToKillPart: 3
damageAmount: 10
attackCooldown: 1
attackRange: 10

# Projectile Settings
projectileSpeed: 30
projectileLifetime: 10
projectileScale: 1
projectileDamage: 10
enableHoming: true
projectileAccuracy: 0.8
```

## Required Components
1. [[EnemyCore]]
2. [[PathfindingMovementBehavior]]
3. [[ProjectileCombatBehavior]]
4. [[DamageVisualizationBehavior]]
5. [[TargetIndicatorBehavior]]

## Behavior Implementation
The Basic Enemy implements:
- Standard pathfinding movement
- Basic projectile combat
- Health and damage management
- Player targeting and line of sight
- Effect spawning system

## Related Documents
- [[EnemySystemGuide|Enemy System Documentation]]
- [[ProjectileSystem|Projectile System Documentation]]
- [[SystemsDesignAnalysis#Core Systems|Systems Design Analysis]] 