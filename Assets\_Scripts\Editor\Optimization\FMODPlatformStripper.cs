using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;

namespace BTR.EditorOptimization
{
    /// <summary>
    /// Helper that disables all unused FMOD platform scripts (everything except the Windows platform)
    /// by renaming their .cs file to .disabled. This prevents Unity from compiling them and removes
    /// their [InitializeOnLoad] cost while keeping them easily restorable.
    /// </summary>
    public static class FMODPlatformStripper
    {
        private const string MenuPath = "Tools/Optimization/Disable Unused FMOD Platforms";
        private const string FMODPlatformsRoot = "Assets/Plugins/FMOD/platforms";
        private static readonly string[] KeepFolders = { "win" }; // the platforms we want to keep

        [MenuItem(MenuPath, priority = 2000)]
        private static void DisableUnusedPlatforms()
        {
            if (!Directory.Exists(FMODPlatformsRoot))
            {
                Debug.LogWarning($"FMOD platforms folder not found at '{FMODPlatformsRoot}'. Nothing to strip.");
                return;
            }

            var platformDirs = Directory.GetDirectories(FMODPlatformsRoot, "*", SearchOption.TopDirectoryOnly)
                                         .Where(dir => KeepFolders.All(k => !dir.EndsWith(k)));

            int processed = 0;
            foreach (var dir in platformDirs)
            {
                var csFiles = Directory.GetFiles(dir, "*.cs", SearchOption.AllDirectories);
                foreach (var file in csFiles)
                {
                    var newPath = file + ".disabled";
                    if (File.Exists(newPath)) continue; // already disabled
                    AssetDatabase.MoveAsset(file, newPath);
                    processed++;
                }
            }

            AssetDatabase.Refresh();
            EditorUtility.DisplayDialog("FMOD Platform Stripper", $"Disabled {processed} unused FMOD platform scripts.\nUnity will recompile without them now.", "Ok");
        }

        [MenuItem(MenuPath, true)]
        private static bool DisableUnusedPlatformsValidate() => true;
    }
}
