# June 14

Select all vertices and Delete Loose was helpful, though it might delete too much

Mesh → Cleanup → Delete Loose

In Unity

<aside>
💡 AutoLOD needs to have Save Meshes enabled and they need to be reassigned hwen dragging a prefab into it. Otherwise we get the GPU Instacer errors cause there are actually no meshes assocaited with other lod groups.

</aside>

Dolly relative to player perspective - figure this out!

Quick way in scene / editor mode to positon player at dolly points

- Just swap paths file! Doesnt break anything

Is it differnet if I transfer into new dolly than start at it?

Not an issue if I got from city to datastream

Pidi Planar reflections - look into it! 

Level Progression

Datastream → City → Weird Void

Adjust Scale script on void / pipe to create cool visual effects

Need to either start game on DATASTREAM or need to have gameplay plane set to proper starting position - one of these seems to make the enemy AI work

Unity Profiler Webinar

Disk IO is extremly expensive ! Watch out for performance

WHen youre in frame budget - stop profiling! Choose per device

Mobile - 30 FPS is solid

Console - 60 FPS

Shader tool profiler FOR ARM - Mali Offline Compiler

Something like this for pc / console / desktop?

Top tip form ebook unity profiling!

Profiler Analyzer - Before and After an optimization

But you can aggreegate a bunch of frames at once for one capture and it can 

Play with this!

Now learning more about the profiler

Shader Variants

Taking 20 minutes to build! Way too long. Shader issue I believe

![Untitled](June%2014%20d4befec92fa24d3a98c8f0d20881abef/Untitled.png)

TIPS

Graphics API settings

- Reduce this, we dont necessarily need both

If things dont need shadows on certain objects, fog, gpu instancing, then disable it. Will lower shader variants.

Use mono for compiling, IL2CPP is not necessary during development

Can use for final build but it takes WAY longer according to people online

![Untitled](June%2014%20d4befec92fa24d3a98c8f0d20881abef/Untitled%201.png)

You can do into shaders and see how many variants they have

Make a Shader variant collection or use Shader Control to disable un needed shaders?

Made an Eye Basics script for the eyes that follow player in the Ophanim loop

Putting the scanner script on my rewind fx  - not working due to no geometry i think?

Look at other wave emission effects to use?

ALSO Glitch effect - destroy all current bombs with this and gain back some stamina? Could balance out time reverse stamina usage 

Go over June 13 as well

ANGELS

Ophanim

- group of angels that are famous for their knowledge and wisdom
- Thrones / Wheels
- who are never asleep and are always guarding the throne of the god

Astrolabe?

Maybe the Ophanim are lost but their rings maintained 

Wiki - According to this conception, the heavenly Seraphim and Cherubim as well as the Ophanim continue to **aid humans in spiritual evolution**
; as do the heavenly Archangels and Angels.