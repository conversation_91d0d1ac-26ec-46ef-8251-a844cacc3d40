# Issue Tracking Index

This file provides an index of all issue tracking documents in this directory.

- [Adjust visual design of locked enemies](issues/Adjust%20visual%20design%20of%20locked%20enemies.md)
- [BackgroundFX / Lock On FX](issues/BackgroundFX%20Lock%20On%20FX.md)
- [Bullets spinning in circles around player](issues/Bullets%20spinning%20in%20circles%20around%20player.md)
- [Clean Assets / Export needed files](issues/Clean%20Assets%20Export%20needed%20files.md)
- [Destory all bullets on transition](issues/Destory%20all%20bullets%20on%20transition.md)
- [Enemy bullet shooting weird directions](issues/Enemy%20bullet%20shooting%20weird%20directions.md)
- [Enemy Lock On issues when <1 possible](issues/Enemy%20Lock%20On%20issues%20when%201%20possible.md)
- [Enemy Movement - Behavior Designer Setups](issues/Enemy%20Movement%20-%20Behavior%20Designer%20Setups.md)
- [Enemy Movement Issues - Jittery](issues/Enemy%20Movement%20Issues%20-%20Jittery.md)
- [General Log](General%20Log%20040a8cf9157b46b990120c836af93fbd.md)
- [Get controller to pull back to center](issues/Get%20controller%20to%20pull%20back%20to%20center.md)
- [Learn Profiler](issues/Learn%20Profiler.md)
- [Make build times faster](issues/Make%20build%20times%20faster.md)
- [Player Facing Direction needs adjustment - should not be start of wave](issues/Player%20Facing%20Direction%20needs%20adjustment%20-%20should%20not%20be%20start%20of%20wave.md)
- [Player movement issues - when plat moving](issues/Player%20movement%20issues%20-%20when%20plat%20moving.md)
- [Pool of enemies throwing errors when an enemy is reused](issues/Pool%20of%20enemies%20throwing%20errors%20when%20an%20enemy%20is%20reused.md)
- [Shaderbox Skybox adjustable properties](issues/Shaderbox%20Skybox%20adjustable%20properties.md)
- [Smooth Scene Change / Load](issues/Smooth%20Scene%20Change%20Load.md)
- [Switch Dolly on Transition](issues/Switch%20Dolly%20on%20Transition.md)
- [Task Template](issues/Task%20Template.md)
- [UI Target Tracker not working](issues/UI%20Target%20Tracker%20not%20working.md)
- [General Log Subdirectory](General%20Log/)