# February 14

Compilation of notes over last couple weeks below

Go over notes again - What approach do I want? What do I want in a demo?

Break down design of Hi Fi Rush - how can I have multiple moves? Is this achievable? 

Assets that need VFX

- Bullet being shot

Float down - laser - floating animation - level loading and 

START

moving forward through barren landscape

come upon city

move forward through city

come upon data center / guardian / ophanim

move along each ring  - several layers

zap into center!

different gameplay mode - not moving forward as much - things coming at you from all sides

Walls closing in? attack with bullets?

Puzzle element here? 

ESCAPE TIME

moving through city faster???

Look at destruction in blender for rings

look at blender tool for making robotic looking rings and such

Sayonara Wild Hearts - think about different modes provided here - do gameplay analysis

Use Steam Asset Ripper on this game - figure out if it works

Jan 30th

Ophanim rings

Test this as on rails shooter - how will enemy waves work? 

What is the variation in gameplay?

Combat types? 

Can do square and triangle rings as well?

Talk to Eileen !!!

Spinning issues

![Untitled](February%2014%2059e0f8fb56844d94bc6fc295e968d7fd/Untitled.png)

Cause current wheel dolly to spin forward? and only rotate all other wheels when not in use? 

Can keep player in exact same position - level moves around them

Then I don’t have to deal with Camera flipping issues?  

Am I delaying issues that might come up in the future anwyay? 

Is there a way to have everything rotate and the play can exist on it without issue

SCRIPTS - Rotate Around, other rotate basics

Positive game loop - dont punish for being off beat, but reward for being on beat 

Do I need moving A* for the ophanim rings? 

Looks like some events are not syncing up due to moving elements around!!!

If enemies not moving, try snapping A* scan to scene bounds. Likely scanning in the wrong area!

When player is located on a ring

- Ring only rotates for fake forward motion
- Other rings rotate in many directions

Using Dreamtek ‘Rotate’ script. Will have to edit this for more features eventual!!

Successfully moving between dolly points now - but need to control dolly’s during transitions. Working on this!

Remember - if something exists in a seperate namespace is may be inaccessible! Check for these things

Test out transition between rings when i’ve returned! Add more dollys, look at spinning in scene view 

Alternative gameplay modes for center of Ophanim

- Static in center - surrounded by walls to shoot things

Rez bosses inspo

- moving through hallways of enemy

Working on functional menu system with controller

may need to update shooting script/player movement to disable input processing when paused!!!!

No matter how I set it up…. UP on the UI seems to trigger pause menu on and off, just like escape

Why?? Unsure

Switch back to D Pad and see what happens with more troubleshooting   

Fixed this! Weird listings in Rewired due to prefab vs in scene version of the rewired manager

Need to keep an eye on this issue

Compiling shader varaints → An approach to solving this issue!

here is a fix to this. Go to Edit->Project Settings->Graphics. Go to the very bottom. It will state the number of shaders and shader variants. Make sure that seems about right to you. There is a button called 'Save to asset...'. Use that to save a compiled shader variants file. In the graphics tab you're still in, drag that saved file from your Project window into the 'Preloaded Shaders' list. That will decrease the built time dramatically.

Washed out colors in Unity builds, but not in editor play view. Attempting to fix this

Changing from Gamma to Linear color space - may fix this 

Anti-Aliasing part of the issue? Trying FXAA and Linear color space

Disabled Optimize mesh data to speed up build times

[UNITY SHADER VARIANTS - TIPS to Speed Up Your Build Time!](https://www.youtube.com/watch?v=3i2V8Q7SsOM)

Height fog global is possibly causing the coloring issues

[Atmospheric Height Fog | Optimized Fog for Consoles, Mobile and VR | Fullscreen & Camera Effects | Unity Asset Store](https://assetstore.unity.com/packages/vfx/shaders/fullscreen-camera-effects/atmospheric-height-fog-optimized-fog-for-consoles-mobile-and-vr-143825)

Look into settings or find another fog solution

Trigger FLoating animation when transition step is happening!

- Need to reset player viewpoint and animation once transition is over
- Need to fix all menu controller issuesson

Object Spawner issues seem to be tanking things once level moves along - figure out what the issue is here

```jsx
AssertionException: Assertion failure. Value was False
Expected: True
UnityEngine.Assertions.Assert.Fail (System.String message, System.String userMessage) (at <1332c534a8a44623b2e5cc943a0b790e>:0)
UnityEngine.Assertions.Assert.IsTrue (System.Boolean condition, System.String message) (at <1332c534a8a44623b2e5cc943a0b790e>:0)
UnityEngine.Assertions.Assert.IsTrue (System.Boolean condition) (at <1332c534a8a44623b2e5cc943a0b790e>:0)
HohStudios.Tools.ObjectParticleSpawner.ObjectParticleSpawner.LateUpdate () (at Assets/HoH Studios/Packages/Tools/Runtime/Object Particle Spawner/Spawner/ObjectParticleSpawner.cs:303)
```

Matrix Notes - • [Seraph](https://en.wikipedia.org/wiki/List_of_Matrix_series_characters#Seraph) is a supporting character in the second and third films of The Matrix Trilogy. Seraph is an exile program who is seen acting as a "guardian angel" of the Oracle, and is described as the personification of a sophisticated challenge-handshake authentication protocol which guards the Oracle.

The Bible later makes mention of a third type of angel found in the merkabah called *"[seraphim](https://en.wikipedia.org/wiki/Seraph)"*
 (lit. "burning") angels. These angels appear like flashes of fire continuously ascending and descending. These *seraphim* angels power the movement of the chariot. In the hierarchy of these angels, *hayyoth* are the highest, that is, closest to God, followed by the *ophanim*, which are followed by the *seraphim* The chariot is in a constant state of motion, and the energy behind this movement runs according to this hierarchy. The movement of the *ophanim* is controlled by the "Living creatures", or *Hayyot*, while the movement of the *hayyot* is controlled by the *seraphim*. The movement of all the angels of the chariot is controlled by the "Likeness of a Man" on the Throne.

[Angels in Judaism - Wikipedia](https://en.wikipedia.org/wiki/Angels_in_Judaism#Angelic_hierarchy)

Shadow of the colossus story style…show up in a land. 

How does it all relate to music? 

World without sound?

rumbling….

Solar Ash

- voidrunner jumps into a black hole

Shadow of the colossus

- world establishing shot following a bird - nighttime
    - showcases the vast land
    - shows nature - a living world
- we see a man on horse, on mountain, headed somewhere - has something with him
- moves through forest

IDEA - start with enticing scope of world shot - the 4 ophanim / bosses - looks huge!

Zoom down in scale towards our protagonist - start level

Enemy Shooting Particles appears to have the error 

DEBUG of OPS error

OPS line 303

```jsx
// Error catching assertions.. not included in build
            if (_numberOfAliveParticles < _aliveParticles.Length)
                UnityEngine.Assertions.Assert.IsTrue(_aliveParticles[_numberOfAliveParticles].remainingLifetime < 0.001f);
            if (_numberOfAliveObjects < _aliveObjects.Length)
                UnityEngine.Assertions.Assert.IsTrue(_aliveObjects[_numberOfAliveObjects] == null || !_aliveObjects[_numberOfAliveObjects].IsInitialized);
        }
```

OPS line 158

```jsx
// Error catching assertions.. not included in build
            if (_numberOfAliveObjects < _aliveObjects.Length)
                UnityEngine.Assertions.Assert.IsTrue(_aliveObjects[_numberOfAliveObjects] == null || !_aliveObjects[_numberOfAliveObjects].IsInitialized);
```

Assertion Failure! This are showing up false 

Issues with the bullet pool 

NOTE - Seems to happen when ENEMIES are what needs to be reused, not bullets ???

Verify this finding. Still issues with bullet pool i think but…. needs more testing

Trying with enemy preload amount of 40 instead of 20

Appears to be true! Made it to Wave 7 until error occured

As soon as 1st two enemies are reused the Assertion failure occurs. Pooling is not working correctly for enemies. 

Doesn’t seem to be a big deal for bullets - Though may be worth cleaning out inactive game objects? Also how might I combine all enemies pools to work together? Doesn’t seem to do functioning 

Need to change redirection of player to front so that it’s END OF TRANSITION not START OF WAVE

Do I need to use ALINE ? Am I using it? Potential performance gaing from getting rid of it

Moving from mono ot il2cpp back end made significant differnece in frame rate!!! stick with this

IL2CPP takes a long time to build but works twice as fast in the build! Even more! Some issues though

Aspect ratio wrong - need to set this? Add resolution as well?

Using Asset Hunter to slim down project and improve build times

Enemies evolve as iterations on a theme

Geometric

Move through rocky landscape

approach ophanim / city

More variation among sections??

What about the center?

Glitched out area? 

Ophanim rings… shoot or collect something along the interior? 

Need to use enemy bullets to do this!

Get to center - circle around it

DO SOMETHING

Circle / Square / Triangle 

Add a FOV slider 

Look into Volumetric Lights and Fog using Shader Graph!

[https://www.youtube.com/watch?v=rihJzWq7sE4](https://www.youtube.com/watch?v=rihJzWq7sE4)

Dynamic Portals Asset?

[Dynamic Portals](https://assetstore.unity.com/packages/tools/physics/dynamic-portals-235818#reviews)

Beautify not working with QUibli scene - Looking at rendering pipeline for QUibli again - may be setup wrong!

Looking at URP overview - SRP information
Reflection Probe Basics
[https://www.youtube.com/watch?v=NIAG2QsLqsA](https://www.youtube.com/watch?v=NIAG2QsLqsA)

Chroma - Pro Shaders Gradient! 

Bought this - use it! Test it out!

Playing with VFX graph effects and Chrome today!

Chrome looks great but need to find right instances for it

Likely characters and enemies and the like

Error with Enemy Spawn pool - this may be the pooling issue i think is occuring to crash the game?

```jsx
SpawnPool SpawnPool1 (Enemy Diamond): You turned ON Culling and entered a 'Cull Above' threshold greater than the 'Preload Amount'! This will cause the culling feature to trigger immediatly, which is wrong conceptually. Only use culling for extreme situations. See the docs.
UnityEngine.Debug:LogWarning (object)
PathologicalGames.PrefabPool:PreloadInstances () (at Assets/Plugins/PathologicalGames/PoolManager/SpawnPool.cs:1823)
PathologicalGames.SpawnPool:CreatePrefabPool (PathologicalGames.PrefabPool) (at Assets/Plugins/PathologicalGames/PoolManager/SpawnPool.cs:374)
PathologicalGames.SpawnPool:Awake () (at Assets/Plugins/PathologicalGames/PoolManager/SpawnPool.cs:196)
```

Fixed this - no improvement

Brought project into another instance - less fat!

Big thing to fix - enemy aiming! Collecting bullets is ok but enemy aiming is quite bad 

What's the problem?

SPent a couple hours trying to get a good fog going with Fog Volume 3

Maybe less time

Not going to work, seems it interferes with render layers???

Trying Aura 2

Doesn’t Work

Installed Buto - seems to work a bit. Some Errors, need to investigate proper use in demo scene

Read up on documetnation 

Feb 17th

enemies are -20 lowere and not seeing graph properly - dont move graph, seems to be a good place?

Enemies slowly move up. Freezing Position in Y on rigid body to counter this. Need to find out the actual error?

Enemies wont shoot. Bringing in music and files from Resources folder to see if this will fix it!!!

RezV6 backed up on NVME - D, trying to scale down Rez V6 and them might reduce assets

Fix Rotate RIng issues!!!
May need to setup this script all over again

Adjust Radar to match new scale

Can I switch basck to Visual Studio 2022??