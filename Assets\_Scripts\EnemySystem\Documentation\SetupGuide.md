# Enemy Anti-Grouping System Setup Guide

## Quick Setup (Automatic Integration)

The system is designed to work **automatically** with minimal setup required. Here's what happens:

### ✅ Automatic Setup (No Manual Work Required)

1. **Services Auto-Initialize**: The `EnemyManager` automatically creates and initializes both separation services when the scene starts
2. **Enemies Auto-Register**: All enemies using `BasicChaseMovementStrategy` automatically register with the separation services
3. **Integration Active**: Separation behavior is automatically applied to enemy movement decisions

### 🔧 Manual Configuration (Optional)

You can optionally add configuration components for fine-tuning:

## Setup Steps

### Step 1: Verify EnemyManager Exists

- Ensure you have an `EnemyManager` in your scene (this should already exist)
- The EnemyManager will automatically initialize the separation services

### Step 2: Configure Separation Settings (Optional)

Add the `EnemySeparationTester` component to any GameObject for real-time configuration:

```csharp
// Add to any GameObject in scene for testing and configuration
[AddComponentMenu("Enemy System/Enemy Separation Tester")]
public class EnemySeparationTester : MonoBehaviour
```

**How to Add:**

1. Select any GameObject in your scene
2. Add Component → Enemy System → Enemy Separation Tester
3. Configure settings in the Inspector
4. Use context menu options for testing

### Step 3: Enemy Prefab Configuration (Automatic)

Your enemy prefabs should already work if they use:

- `BasicChaseMovementStrategy` (enhanced with separation)
- `EnemyCore` or implement `ICombatEntity` interface

**No manual changes needed** - the integration is automatic.

## Configuration Options

### EnemySeparationTester Inspector Settings

```csharp
[Header("Separation Settings")]
public float separationRadius = 3f;           // How far enemies detect each other
public float separationForce = 10f;           // Strength of repulsion force
public float maxSeparationDistance = 6f;      // Maximum separation influence distance

[Header("Mandatory Spacing")]
public float mandatoryMinimumDistance = 2f;   // Hard minimum distance constraint
public bool enforceHardSpacing = true;       // Enable hard spacing enforcement

[Header("Performance")]
public bool enableDistanceLOD = true;         // Performance optimization for distant enemies
public int maxSeparationChecksPerFrame = 20;  // Limit CPU usage per frame

[Header("Debug")]
public bool showDebugInfo = true;             // Show real-time statistics
public bool drawDebugGizmos = true;           // Visual debugging in Scene view
```

### Context Menu Options (Right-click on EnemySeparationTester)

- **"Run Immediate Test"** - Test separation system immediately
- **"Reset Stats"** - Clear performance statistics
- **"Toggle Separation System"** - Enable/disable soft separation
- **"Toggle Mandatory Spacing"** - Enable/disable hard spacing

## What Each Script Does

### Service Classes (No Manual Setup)

- **`EnemySeparationService`** - Handles soft separation forces (automatic)
- **`MandatorySpacingEnforcer`** - Handles hard distance constraints (automatic)

### Enhanced Existing Scripts (No Manual Setup)

- **`EnemyManager`** - Now auto-initializes separation services
- **`BasicChaseMovementStrategy`** - Now includes separation behavior

### Testing Component (Optional Manual Setup)

- **`EnemySeparationTester`** - Add to any GameObject for configuration and testing

## Recommended Setup Workflow

### For Testing/Development:

1. Add `EnemySeparationTester` to an empty GameObject
2. Enable "Show Debug Info" and "Draw Debug Gizmos"
3. Play the scene and observe enemy behavior
4. Adjust settings in real-time through the Inspector
5. Use context menu options to test different scenarios

### For Production:

1. Remove or disable `EnemySeparationTester` components
2. The separation system continues working automatically
3. Performance is optimized through built-in LOD systems

## Verification Checklist

✅ **EnemyManager exists in scene** - Should already be present
✅ **Enemies use BasicChaseMovementStrategy** - Should already be configured
✅ **No compilation errors** - All scripts should compile cleanly
✅ **Enemies maintain distance** - Test by spawning multiple enemies near player

## Troubleshooting

### "Enemies still grouping together"

- Add `EnemySeparationTester` to verify system is active
- Increase `separationForce` value
- Enable `enforceHardSpacing`
- Check that enemies are using `BasicChaseMovementStrategy`

### "Performance issues"

- Enable `enableDistanceLOD`
- Reduce `maxSeparationChecksPerFrame`
- Increase separation update intervals

### "System not working"

- Verify `EnemyManager` exists in scene
- Check Console for initialization messages
- Add `EnemySeparationTester` and check "Show Debug Info"
- Ensure enemies implement `ICombatEntity` or use `EnemyCore`

## Summary

**Most users need NO manual setup** - the system works automatically with existing enemy prefabs that use `BasicChaseMovementStrategy`.

**Optional**: Add `EnemySeparationTester` component for configuration and testing.

The separation system integrates seamlessly with your existing A\* pathfinding and enemy behavior without requiring changes to enemy prefabs or GameObjects.
