

**2023:**

*   **January 2023 - April 2023:** *No entries found.*
*   **May 2023:**
    *   Discussed ideas for a boss encounter design, incorporating elements like time manipulation and Fresnel-based effects *(Implicit reference to time manipulation seen in other games/media and Fresnel shaders as a visual effect)*.
*   **July 2023:**
    *   Mentioned the need to assess if fixing new project version is better, then building on top of old version until something breaks *(References an iterative approach to software development and version control)*.
*   **August 2023:**
    *   Thoughts on sound design ([https://www.youtube.com/watch?v=oxeD8kuCT_g](https://www.youtube.com/watch?v=oxeD8kuCT_g)):
        *   Bookends and Highway method
        *   Use FilterFreak and Speakerphone for processing your SFX
        *   Plan creatures as Blue / Red - Soft / Angry
        *   Pre-attack sounds - design by making the first bookend (front) Copy it and it can be your pre attack
        *   Granulate - seems like a cool Granular synth
*   **August 30, 2023:**
    *   Some recommendations on Game Design

        *   GDC talks
        *   Designer Notes Podcast
        *   Google Scholar (games and culture, gamestudies.org)
        *   Abstract - The Art of Design
    *   Between different levels, highlight difference - think of how it plays with your mechanics *Implies studying design principles on level variation*.
*   **April 22, 2023:**
    *   GDC Talks and inspirations for the game

        *   "POP" audio and color usage (voice only for hidden folks, child perspective for among the sleep)
        *   Finding themes, essences, patterns, colors with specific saturation for expression
*   **March 2023:** *No specific entries found, general code optimizations etc.*
*   **February 2023:** *No specific entries found, focused on fixing bugs, also refers to a need for issue tracker, which could implicitly reference kanban methodology etc.*

**2024:**

*   **December 11, 2024:**
    *   References a design article "[Featured Blog | How to create a 'fair' auto-aiming system in a robot shooter](https://www.gamedeveloper.com/design/how-to-create-a-fair-auto-aiming-system-in-a-robot-shooter-)" for ideas to influence game design.
*   **January 5, 2024:**
    *   Need to see if Importing with 3DS Max, then exporting to Blender, may work *(This can be refrenced as looking up standard art processes)*.

*   **January 10, 2024:**
    *   Use gravity rush enemies as inspiration for my enemies - look up the various types.
    *   Want to apply this to text and ui in Unity - https://twitter.com/ashlee3dee/status/1745524506872176970.

        *   Maybe look at Ace Combat, emulates this a bit - search for this.

*   **January 15, 2024:**
    *   Game Dev Links

        *   [https://github.com/QuentinKing/OuterWildsBlackHole](https://github.com/QuentinKing/OuterWildsBlackHole)
        *   [https://github.com/QuentinKing/OuterWildsBlackHole/issues/1](https://github.com/QuentinKing/OuterWildsBlackHole/issues/1)
        *   [https://github.com/JonasDeM/QuickSave](https://github.com/JonasDeM/QuickSave)
        *   [https://blog.unity.com/engine-platform/updated-2022-lts-best-practice-guides](https://blog.unity.com/engine-platform/updated-2022-lts-best-practice-guides)
        *   [https://www.occasoftware.com/blog/65-tips-how-to-improve-a-unity-games-performance](https://www.occasoftware.com/blog/65-tips-how-to-improve-a-unity-games-performance)
        *   [https://www.reddit.com/r/SoloDevelopment/comments/19740pt/6_years_of_solo_dev_later_still_not_finished_but/](https://www.reddit.com/r/SoloDevelopment/comments/19740pt/6_years_of_solo_dev_later_still_not_finished_but/)
        *   [https://www.youtube.com/watch?v=3CfpJlGI2wc](https://www.youtube.com/watch?v=3CfpJlGI2wc)
        *   [https://aftermath.site/new-book-reminds-us-that-wipeout-remains-the-coolest-video-game-ever-released](https://aftermath.site/new-book-reminds-us-that-wipeout-remains-the-coolest-video-game-ever-released)
        *   [https://twitter.com/OhPoorPup/status/1727814207398408439](https://twitter.com/OhPoorPup/status/1727814207398408439)
        *   [One Wheel Studio](https://www.youtube.com/@OneWheelStudio/videos)
        *   [https://twitter.com/folmerkelly/status/1729145782337716492](https://twitter.com/folmerkelly/status/1729145782337716492)
        *   [https://www.youtube.com/watch?v=1N_4NzwprJI](https://www.youtube.com/watch?v=1N_4NzwprJI)
        *   [https://vol.co/products/wipeout-futurism](https://vol.co/products/wipeout-futurism)
        *   [https://www.youtube.com/watch?v=hJcFgZqT0ow](https://www.youtube.com/watch?v=hJcFgZqT0ow)
        *   [Scott Game Sounds](https://www.youtube.com/@ScottGameSounds/videos)
        *   [https://twitter.com/DominicTarason/status/1731662776840372406](https://twitter.com/DominicTarason/status/1731662776840372406)
        *   [Portal effect in URP](https://www.reddit.com/r/Unity3D/comments/18aeidd/portal_effect_in_urp/)
        *   [https://github.com/Unity-Technologies/ProjectAuditor](https://github.com/Unity-Technologies/ProjectAuditor)
        *   [https://twitter.com/andytouch/status/1733190142548992437](https://twitter.com/andytouch/status/1733190142548992437)
        *   [Easy and Powerful Extension Methods | Unity C#](https://www.youtube.com/watch?v=Nk49EUf7yyU)
        *   [Shader Graph: Learn to use nodes with Node Reference Samples | Tutorial](https://www.youtube.com/watch?app=desktop&v=vrEi6M3GjC4)
        *   [https://github.com/DeadlyRedCube/Cathode-Retro](https://github.com/DeadlyRedCube/Cathode-Retro)
        *   [https://www.32-33.co/post/the-art-of-game-marketing-speak-to-your-audience-s-desires-the-intangible-value-of-events](https://www.32-33.co/post/the-art-of-game-marketing-speak-to-your-audience-s-desires-the-intangible-value-of-events)
        *   [https://github.com/pointcache/BasicEventBus](https://github.com/pointcache/BasicEventBus)
        *   [https://softsaz.ir/blender-market-citybuilder-3d/](https://softsaz.ir/blender-market-citybuilder-3d/)
        *   [https://www.reddit.com/r/gamedev/comments/17hmlao/i_underestimated_the_power_of_just_do_a_little/](https://www.reddit.com/r/gamedev/comments/17hmlao/i_underestimated_the_power_of_just_do_a_little/)
        *   [RGD Workshop : Rational Game and Level Design](https://www.youtube.com/watch?v=xqHAjwrNp70)
        *   [https://www.reddit.com/r/gamedev/comments/17d38i5/what_are_the_major_systems_you_need_to_think_of/](https://www.reddit.com/r/gamedev/comments/17d38i5/what_are_the_major_systems_you_need_to_think_of/)
        *   [Mirza Beig on Twitter / X](https://twitter.com/TheMirzaBeig/status/1715936953407815983)
        *   [https://docs.google.com/spreadsheets/d/1QhFyPfYSjHv7PjibGrslF3mNW_CIDXWv9o-iQgLbu1o/edit#gid=1404087630](https://docs.google.com/spreadsheets/d/1QhFyPfYSjHv7PjibGrslF3mNW_CIDXWv9o-iQgLbu1o/edit#gid=1404087630)
        *   [Harry Clarke’s Illustrations](https://kottke.org/23/12/harry-clarkes-illustrations)
        *   [Shmup Visual Design, Colour Theory and Bullet Visibility (Subtítulos en Español)](https://www.youtube.com/watch?v=NxVG7caNmO0)
        *   [Twisted Corridor Effect in Unity Shader Graph](https://www.youtube.com/watch?v=CGI0SyiElfY)
        *   [The Cinematography of Evangelion](https://www.youtube.com/watch?v=fsmbpEZ_lI8)
        *   [How Platinum Design a Combat System](https://www.youtube.com/watch?v=D689ZBdOAuw)
        *   [What's new in Unity's Universal Render Pipeline?](https://www.youtube.com/watch?v=Z-XCk8mXJtI)
        *   [[TUTORIAL] Stencil buffer in Unity URP (cutting holes, impossible geometry, magic card)](https://www.youtube.com/watch?v=y-SEiDTbszk)
        *   [Rhythm game architecture](https://www.reddit.com/r/Unity3D/comments/196l47e/rhythm_game_architecture/)
        *   [10 Unity Tips You (Probably) Didn't Know About](https://www.youtube.com/watch?v=JgAhIX8YLBw)
        *   [https://twitter.com/ashlee3dee/status/1746606648330686730](https://twitter.com/ashlee3dee/status/1746606648330686730)
        *   [Better Singletons in Unity C#](https://www.youtube.com/watch?v=LFOXge7Ak3E)
        *   [hazylevels](https://www.youtube.com/@hazylevels/videos)
        *   [Service Locator: Inversion of Control in Unity C#](https://www.youtube.com/watch?v=D4r5EyYQvwY)
        *   [https://github.com/electronicarts/IRIS](https://github.com/electronicarts/IRIS)
        *   [https://github.com/sudotman/BetterUnity](https://github.com/sudotman/BetterUnity)
        *   [Easy Optimization! Flyweight design pattern in unity](https://www.youtube.com/watch?v=zrUhpjwBpPI)
        *   [I Asked 17 Game Developers For Their Biggest Tips](https://www.youtube.com/watch?v=ESrw7BerWgM)
        *   [Write Clean Code to Reduce Cognitive Load](https://www.reddit.com/r/programming/comments/17to0h1/write_clean_code_to_reduce_cognitive_load/)
        *   [Warped Imagination](https://www.youtube.com/@WarpedImagination/videos)
        *   [Eric Wang_VFX Artist](https://www.youtube.com/@EricWang0110)
        *   [Shockwave Shader Graph - How to make a shock wave shader in Unity URP/HDRP](https://www.youtube.com/watch?v=dFDAwT5iozo)
        *   [Don't Use Deep Profiler! - Profiler Marker Unity](https://www.youtube.com/watch?app=desktop&v=DsrFkPP0Qe8)
        *   ALternate radar?

            *   [How to make RPG Radar Chart (Unity Tutorial)](https://www.youtube.com/watch?v=twjMW7CxIKk)
        *   Make bullets glow - look into this

            *   [How to make Unity GLOW! (Post Processing Bloom Unity Tutorial)](https://www.youtube.com/watch?v=bkPe1hxOmbI)

        *   Apply bloom to specific objects?

            *   [How to Add GLOW to Certain Objects | Unity 2022](https://www.youtube.com/watch?v=9v6yyRIoWnE)

            *   THis work for adding selective bloom, just need to adjust intensity of objects emission map
*   **February 1st:**
    *Useful for Ophanim*
        *   *Ring World Using Random Flow*
    *Model for how I implemented SDF particle effects*
        * Unity VFX Graph：Use SDF to make model particle effects
    *Game Design Resources full spreadsheet*
        * [https://docs.google.com/spreadsheets/d/1QhFyPfYSjHv7PjibGrslF3mNW_CIDXWv9o-iQgLbu1o/edit#gid=1404087630](https://docs.google.com/spreadsheets/d/1QhFyPfYSjHv7PjibGrslF3mNW_CIDXWv9o-iQgLbu1o/edit#gid=1404087630)
    *Unity Optimization and Programming*
        * SwiftRoll 🐦 – Medium
    *Cool enemy character inspo*
        * A multitude of Empty Slots (for Comissions) on Twitter / X
    *Convert projectile system to DOTS?*
        *   [https://www.youtube.com/watch?v=4ZYn9sR3btg](https://www.youtube.com/watch?v=4ZYn9sR3btg)

*   **Feb 13th**
    *Referenced external sources for game design inspiration, architecture, VFX and Blender.*
        *   [Hyperpassive as a rubric for the music and interactivity in Beat Remake]  (https://www.youtube.com/watch?v=S4pHJN8YclE)
        *   [https://www.youtube.com/watch?v=woLgWVZPMk4](https://www.youtube.com/watch?v=woLgWVZPMk4)
        *   [https://www.youtube.com/watch?v=osGJGqDDby8](https://www.youtube.com/watch?v=osGJGqDDby8)
        *   [https://www.youtube.com/watch?v=ZytOQ4NSciU](https://www.youtube.com/watch?v=ZytOQ4NSciU)
        *   [Spatial Communication in Level Design](https://www.youtube.com/watch?v=AKeUZVikPV8)
        *   [Unity VFX Graph：Ribbons and Balls (Event control)](https://www.youtube.com/watch?v=h9ApA9tHiqk&list=WL&index=12)
        *   [SIMPLE Tip For Better Unity Game Architecture](https://www.youtube.com/watch?v=mAfpfUYhpAs)
        *   [Practical generators for blender](https://www.youtube.com/watch?v=wBT0GGui75I)
        *   [3 Game Programming Patterns WE ACTUALLY NEED.](https://www.youtube.com/watch?v=BwA36em_DnA&list=WL)

*   **February 27**

    *   Play / watch some sin & punishment for idea of attach phases here
---


