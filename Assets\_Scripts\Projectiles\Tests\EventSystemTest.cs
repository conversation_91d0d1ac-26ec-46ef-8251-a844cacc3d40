using UnityEngine;
using BTR.Events;
using BTR.Projectiles;

namespace BTR.Testing
{
    /// <summary>
    /// Test script for the projectile event system.
    /// Demonstrates event subscription, dispatching, and system integration.
    /// </summary>
    public class EventSystemTest : MonoBehaviour
    {
        [Header("Test Configuration")]
        [SerializeField] private bool runTestOnStart = true;
        [SerializeField] private bool enableDetailedLogging = true;
        [SerializeField] private ProjectileEntity testProjectile;

        [Header("Event Testing")]
        [SerializeField] private bool testLifecycleEvents = true;
        [SerializeField] private bool testCollisionEvents = true;
        [SerializeField] private bool testStateEvents = true;
        [SerializeField] private bool testCombatEvents = true;

        [Header("Performance Testing")]
        [SerializeField] private int performanceTestEventCount = 1000;
        [SerializeField] private bool runPerformanceTest = false;

        // Event counters for testing
        private int lifecycleEventsReceived = 0;
        private int collisionEventsReceived = 0;
        private int stateEventsReceived = 0;
        private int combatEventsReceived = 0;

        private void Start()
        {
            if (runTestOnStart)
            {
                StartEventSystemTest();
            }
        }

        [ContextMenu("Start Event System Test")]
        public void StartEventSystemTest()
        {
            Debug.Log("=== Event System Test Started ===");

            if (!ProjectileEvents.IsEventSystemReady())
            {
                Debug.LogError("❌ Event system not ready! Make sure ProjectileEventDispatcher is in the scene.");
                return;
            }

            Debug.Log("✅ Event system is ready");

            // Subscribe to events
            SubscribeToEvents();

            // Run individual tests
            if (testLifecycleEvents)
            {
                TestLifecycleEvents();
            }

            if (testCollisionEvents)
            {
                TestCollisionEvents();
            }

            if (testStateEvents)
            {
                TestStateEvents();
            }

            if (testCombatEvents)
            {
                TestCombatEvents();
            }

            if (runPerformanceTest)
            {
                TestEventPerformance();
            }

            // Log final results
            LogTestResults();

            Debug.Log("=== Event System Test Completed ===");
        }

        #region Event Subscription

        private void SubscribeToEvents()
        {
            Debug.Log("--- Subscribing to Events ---");

            // Subscribe to lifecycle events
            ProjectileEvents.OnLifecycle(HandleTestLifecycleEvent);
            Debug.Log($"✅ Subscribed to lifecycle events. Subscribers: {ProjectileEvents.GetSubscriberCount<ProjectileLifecycleEventData>()}");

            // Subscribe to collision events
            ProjectileEvents.OnCollision(HandleTestCollisionEvent);
            Debug.Log($"✅ Subscribed to collision events. Subscribers: {ProjectileEvents.GetSubscriberCount<ProjectileCollisionEventData>()}");

            // Subscribe to state events
            ProjectileEvents.OnStateChange(HandleTestStateEvent);
            Debug.Log($"✅ Subscribed to state events. Subscribers: {ProjectileEvents.GetSubscriberCount<ProjectileStateEventData>()}");

            // Subscribe to combat events
            ProjectileEvents.OnCombat(HandleTestCombatEvent);
            Debug.Log($"✅ Subscribed to combat events. Subscribers: {ProjectileEvents.GetSubscriberCount<ProjectileCombatEventData>()}");
        }

        private void UnsubscribeFromEvents()
        {
            Debug.Log("--- Unsubscribing from Events ---");

            ProjectileEvents.OffLifecycle(HandleTestLifecycleEvent);
            ProjectileEvents.OffCollision(HandleTestCollisionEvent);
            ProjectileEvents.OffStateChange(HandleTestStateEvent);
            ProjectileEvents.OffCombat(HandleTestCombatEvent);

            Debug.Log("✅ Unsubscribed from all events");
        }

        #endregion

        #region Event Handlers

        private void HandleTestLifecycleEvent(ProjectileLifecycleEventData eventData)
        {
            lifecycleEventsReceived++;

            if (enableDetailedLogging)
            {
                Debug.Log($"[EventTest] Lifecycle Event: {eventData.lifecycleType} - {eventData.reason} from {eventData.baseData.eventSource}");
            }
        }

        private void HandleTestCollisionEvent(ProjectileCollisionEventData eventData)
        {
            collisionEventsReceived++;

            if (enableDetailedLogging)
            {
                Debug.Log($"[EventTest] Collision Event: Hit {eventData.hitTag} at {eventData.hitPoint} with force {eventData.impactForce}");
            }
        }

        private void HandleTestStateEvent(ProjectileStateEventData eventData)
        {
            stateEventsReceived++;

            if (enableDetailedLogging)
            {
                Debug.Log($"[EventTest] State Event: {eventData.stateType} - {eventData.propertyName} changed from {eventData.oldValue} to {eventData.newValue}");
            }
        }

        private void HandleTestCombatEvent(ProjectileCombatEventData eventData)
        {
            combatEventsReceived++;

            if (enableDetailedLogging)
            {
                Debug.Log($"[EventTest] Combat Event: {eventData.damageAmount} damage to {eventData.target?.name} at {eventData.damagePoint}");
            }
        }

        #endregion

        #region Individual Tests

        [ContextMenu("Test Lifecycle Events")]
        public void TestLifecycleEvents()
        {
            Debug.Log("--- Testing Lifecycle Events ---");

            if (testProjectile == null)
            {
                Debug.LogWarning("⚠️ No test projectile assigned, creating mock events");

                // Create mock events
                ProjectileEvents.DispatchLifecycle(null, ProjectileLifecycleType.Spawned, "Test spawn", "EventSystemTest");
                ProjectileEvents.DispatchLifecycle(null, ProjectileLifecycleType.Initialized, "Test init", "EventSystemTest");
                ProjectileEvents.DispatchLifecycle(null, ProjectileLifecycleType.Destroyed, "Test destroy", "EventSystemTest");
            }
            else
            {
                // Use real projectile
                ProjectileEvents.ProjectileSpawned(testProjectile, "EventSystemTest");
                ProjectileEvents.ProjectileDestroyed(testProjectile, "Test destruction", "EventSystemTest");
            }

            Debug.Log("✅ Lifecycle events test completed");
        }

        [ContextMenu("Test Collision Events")]
        public void TestCollisionEvents()
        {
            Debug.Log("--- Testing Collision Events ---");

            // Create mock collision data
            var mockCollision = new Collision();
            ProjectileEvents.DispatchCollision(testProjectile, mockCollision, "EventSystemTest");

            Debug.Log("✅ Collision events test completed");
        }

        [ContextMenu("Test State Events")]
        public void TestStateEvents()
        {
            Debug.Log("--- Testing State Events ---");

            // Test various state changes
            ProjectileEvents.DispatchStateChange(testProjectile, ProjectileStateType.SpeedChanged, 10f, 20f, "Speed", "EventSystemTest");
            ProjectileEvents.DispatchStateChange(testProjectile, ProjectileStateType.DamageChanged, 5f, 15f, "Damage", "EventSystemTest");
            ProjectileEvents.HomingEnabled(testProjectile, "EventSystemTest");
            ProjectileEvents.HomingDisabled(testProjectile, "EventSystemTest");

            Debug.Log("✅ State events test completed");
        }

        [ContextMenu("Test Combat Events")]
        public void TestCombatEvents()
        {
            Debug.Log("--- Testing Combat Events ---");

            // Create mock target
            var mockTarget = new GameObject("MockTarget");
            ProjectileEvents.DispatchCombat(testProjectile, mockTarget, 25f, Vector3.zero, "EventSystemTest");

            // Clean up
            DestroyImmediate(mockTarget);

            Debug.Log("✅ Combat events test completed");
        }

        #endregion

        #region Performance Testing

        [ContextMenu("Test Event Performance")]
        public void TestEventPerformance()
        {
            Debug.Log("--- Testing Event Performance ---");

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Dispatch many events
            for (int i = 0; i < performanceTestEventCount; i++)
            {
                ProjectileEvents.DispatchLifecycle(testProjectile, ProjectileLifecycleType.Spawned, $"Performance test {i}", "EventSystemTest");
            }

            stopwatch.Stop();

            Debug.Log($"✅ Dispatched {performanceTestEventCount} events in {stopwatch.ElapsedMilliseconds}ms");
            Debug.Log($"   Average: {(float)stopwatch.ElapsedMilliseconds / performanceTestEventCount:F3}ms per event");
        }

        #endregion

        #region Results and Cleanup

        [ContextMenu("Log Test Results")]
        public void LogTestResults()
        {
            Debug.Log("--- Event System Test Results ---");
            Debug.Log($"Lifecycle Events Received: {lifecycleEventsReceived}");
            Debug.Log($"Collision Events Received: {collisionEventsReceived}");
            Debug.Log($"State Events Received: {stateEventsReceived}");
            Debug.Log($"Combat Events Received: {combatEventsReceived}");
            Debug.Log($"Total Events Received: {lifecycleEventsReceived + collisionEventsReceived + stateEventsReceived + combatEventsReceived}");

            // Log event system status
            Debug.Log("--- Event System Status ---");
            Debug.Log(ProjectileEvents.GetEventSystemStatus());
        }

        [ContextMenu("Reset Test Counters")]
        public void ResetTestCounters()
        {
            lifecycleEventsReceived = 0;
            collisionEventsReceived = 0;
            stateEventsReceived = 0;
            combatEventsReceived = 0;

            Debug.Log("✅ Test counters reset");
        }

        private void OnDestroy()
        {
            // Clean up event subscriptions
            UnsubscribeFromEvents();
        }

        #endregion

        #region GUI Testing

        private void OnGUI()
        {
            if (!enableDetailedLogging) return;

            GUILayout.BeginArea(new Rect(10, 220, 300, 250));
            GUILayout.Label("Event System Test", GUI.skin.box);

            if (GUILayout.Button("Test Lifecycle Events"))
            {
                TestLifecycleEvents();
            }

            if (GUILayout.Button("Test State Events"))
            {
                TestStateEvents();
            }

            if (GUILayout.Button("Test Performance"))
            {
                TestEventPerformance();
            }

            if (GUILayout.Button("Log Results"))
            {
                LogTestResults();
            }

            if (GUILayout.Button("Reset Counters"))
            {
                ResetTestCounters();
            }

            GUILayout.Label($"Events Received: {lifecycleEventsReceived + collisionEventsReceived + stateEventsReceived + combatEventsReceived}");

            GUILayout.EndArea();
        }

        #endregion
    }
}
