# Known Issues & Bugs

## Audio System
### Music VCA Slider Not Working
- **Status**: Open
- **Priority**: Medium
- **Component**: UIManagerAudio/AudioCore
- **Description**: Music VCA slider in settings menu not affecting music volume despite logs showing proper initialization and valid VCA status.
- **Technical Details**:
  - VCA path: "vca:/Music VCA"
  - Initialization logs show successful VCA setup
  - Volume changes are being sent to the VCA
  - VCA controls work in FMOD Studio but not in Unity build
- **Investigation Notes**:
  - Logs confirm proper initialization and valid VCA status
  - SetMusicVolume calls are reaching the VCA
  - May be related to FMOD routing or event setup
- **Potential Solutions to Investigate**:
  1. Verify music events routing through Music VCA in FMOD Studio
  2. Check if music is playing through correct FMOD events
  3. Validate bus routing in FMOD Studio project

## Template for New Issues
### Issue Title
- **Status**: Open/In Progress/Resolved
- **Priority**: Low/Medium/High/Critical
- **Component**: Affected system/component
- **Description**: Brief description of the issue
- **Technical Details**: Relevant technical information
- **Investigation Notes**: Current findings
- **Potential Solutions**: Ideas for resolution

## Performance
### Steam Deck Memory Usage
- **Status**: Open
- **Priority**: Low
- **Component**: Memory Management/Steam Deck
- **Description**: Discrepancy between Steam Deck OS reported memory usage and Unity Profiler measurements
- **Technical Details**:
  - Steam Deck OS reports ~4.6GB RAM and ~1.4GB VRAM usage
  - Unity Profiler shows only ~1.61GB total allocated memory
  - Steam Deck readings include:
    - Unity managed memory (~1.61GB)
    - Native allocations
    - System libraries
    - Steam Runtime environment
    - OS overhead
  - Unity Profiler only shows managed memory allocations
  - VRAM is tracked separately and includes textures, render targets, and graphics buffers
- **Investigation Notes**:
  - Disparity is expected and normal
  - Multiple monitoring tools needed for complete picture
- **Potential Solutions**:
  1. Use Unity Memory Profiler package for detailed native memory analysis
  2. Monitor trends over time rather than absolute values
  3. Maintain both Steam Deck system monitor and Unity Profiler observations 

## Projectile System
### ProjectileZoneManager Directional Targeting Not Working
- **Status**: Open
- **Priority**: Medium
- **Component**: ProjectileZoneManager/Projectile System
- **Description**: ProjectileZoneManager fails to properly redirect projectiles to attack the player from specified directional zones (e.g., frontal attacks)
- **Technical Details**:
  - ProjectileZoneManager's directional targeting system not affecting projectile trajectories
  - Intended behavior: Projectiles should approach player from designated zones/angles
  - Current behavior: Projectiles not respecting zone constraints
- **Investigation Notes**:
  - May be related to projectile trajectory calculation
  - Could be an issue with zone boundary definitions
  - Possible conflict with existing projectile movement systems
- **Potential Solutions to Investigate**:
  1. Debug zone boundary calculations and visualization
  2. Verify trajectory modification logic in ProjectileZoneManager
  3. Check interaction between zone system and projectile movement states
  4. Review coordinate space transformations for zone calculations 

## Enemy System
### Enemy Health Not Resetting After Pool Respawn
- **Status**: Open
- **Priority**: High
- **Component**: EnemyCore/Object Pooling
- **Description**: Enemies spawned from the pool sometimes appear with 0 health and in a disabled state, despite multiple health reset attempts in the spawn/despawn cycle
- **Technical Details**:
  - EnemyCore attempts to reset health in multiple places:
    - OnSpawned()
    - OnDespawned()
    - OnEnable()
    - OnFinalCleanup()
  - Health value (currentHealth) not persisting through pool cycle
  - Affects both main enemy health and damageable parts
  - Debug logs show health being set to MaxHealth but not maintaining value
- **Investigation Notes**:
  - Health reset logic exists but not effectively maintaining state
  - Multiple systems involved in cleanup/reset cycle:
    - PoolManager (PathologicalGames)
    - EnemyCleanupCoordinator
    - DamageablePartBehavior
  - Timing/order of operations may be causing state inconsistency
- **Potential Solutions to Investigate**:
  1. Review entire death -> cleanup -> pool -> respawn lifecycle
  2. Add state validation checks throughout pooling cycle
  3. Consider implementing IPoolable interface for more direct pool lifecycle control
  4. Investigate if cleanup coordinator is interfering with pool reset
  5. Add persistent state tracking to debug value changes 

## Scene Management
### Scene Transition Timing Inconsistencies
- **Status**: Open
- **Priority**: Medium
- **Component**: SceneManagerBTR/Scene Transitions
- **Description**: Scene transitions taking longer than expected despite 1-second cooldown setting
- **Technical Details**:
  - SCENE_CHANGE_COOLDOWN set to 1 second
  - Multiple operations contributing to transition time:
    - Scene unloading/loading sequence
    - Loading screen fade in/out
    - Audio manager initialization
    - Multiple event triggers in sequence
  - Cascading transitions from wave completion -> section change -> spline increment -> scene change
- **Investigation Notes**:
  - Scene loading operations add inherent delay
  - Multiple event triggers may cause stacked transitions
  - Loading screen operations add additional time
  - Async operations may not be optimally sequenced
- **Potential Solutions to Investigate**:
  1. Optimize scene loading sequence
  2. Review and potentially reduce loading screen fade durations
  3. Consolidate transition events to prevent cascading
  4. Consider preloading next scene
  5. Profile transition sequence for optimization opportunities 

## Core Systems
### Issue Title
- **Status**: Open/In Progress/Resolved
- **Priority**: Low/Medium/High/Critical
- **Component**: Affected system/component
- **Description**: Brief description of the issue
- **Technical Details**: Relevant technical information
- **Investigation Notes**: Current findings
- **Potential Solutions**: Ideas for resolution

## UI System
### Issue Title
- **Status**: Open/In Progress/Resolved
- **Priority**: Low/Medium/High/Critical
- **Component**: Affected system/component
- **Description**: Brief description of the issue
- **Technical Details**: Relevant technical information
- **Investigation Notes**: Current findings
- **Potential Solutions**: Ideas for resolution 