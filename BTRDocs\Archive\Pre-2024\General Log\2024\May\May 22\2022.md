# 2022

January 2022:

- Explored using a spawner system and dynamic waypoints for enemy movement.
- Experimented with different approaches for player aiming and locking onto enemies.
- Encountered issues with enemy AI and bullet behavior, such as enemies getting stuck and bullets colliding with each other.
- Investigated using Behavior Designer for more advanced enemy AI patterns like "Scoot and Shoot" and "Flank".
- Worked on implementing a functional menu system with controller support.
- Optimized the project by compiling shader variants and using GPU Instancer.
- Explored using Ethereal URP for visual effects.

February 2022:

- Fixed issues with the rewind system and input processing when the game is paused.
- Continued work on the menu system and controller integration.
- Experimented with different approaches to handle the rotation and movement of the player and the level.
- Investigated using <PERSON><PERSON><PERSON> for better audio and music integration.
- Worked on improving the lock-on and shooting mechanics, including adjusting the timing and visual feedback.
- Explored using Tessera Pro and other tools for level design and optimization.
- Investigated using Polarith AI Pro for more advanced enemy AI.

March 2022:

- Implemented a Tetris-like gameplay mechanic, including piece movement and rotation.
- Worked on integrating the Tetris gameplay with the existing enemy and bullet mechanics.
- Explored using Jelly <PERSON>ube and other puzzle-based mechanics.
- Continued optimizing the project, including investigating the use of Mesh Baker and Automatic LOD.
- Experimented with different visual styles and effects, such as the use of Chroma Pro Shaders.
- Encountered issues with the enemy spawning and pooling system, leading to further investigation and troubleshooting.

April 2022:

- Focused on resolving issues with the wave spawning system, working with the Trivial Interactive team to find solutions.
- Implemented additional UI elements, such as a wave hint and enemy hint, to improve the player's understanding of the game state.
- Continued experimenting with level design and enemy AI, including the use of the Ultimate Spawner and Waypoint systems.
- Explored ways to better integrate the music and audio with the gameplay, such as using Koreographer events.
- Investigated potential issues with the camera and player movement, particularly related to the rotation and perspective.

May 2022:

- Worked on improving the visual quality and effects of the game, including the use of Beautify 3 and other post-processing tools.
- Continued experimenting with different enemy and boss designs, exploring the use of geometry nodes and deconstructed shapes.
- Encountered issues with importing rigged characters into Blender, leading to investigations into using 3DS Max and Maya as alternative tools.
- Focused on solidifying the overall game design and creative direction, including decisions around boss and level structure.

June 2022:

- Explored integrating Tetris-like gameplay mechanics more deeply into the core experience.
- Continued work on enemy and boss designs, experimenting with different geometric shapes and deconstructed visual styles.
- Investigated issues with character rigging and importing into Blender, trying various workflows with 3DS Max and Maya.
- Worked on improving the camera and player movement, particularly related to rotation and perspective.
- Explored the use of Radar and other UI elements to enhance the player's spatial awareness and orientation.

July 2022 - December 2022:

- The documentation does not contain any specific details about the project's progress during these months. It's possible that development continued, but the available information does not provide a clear summary for this time period.