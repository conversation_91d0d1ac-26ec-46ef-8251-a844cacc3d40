

This file is a comprehensive development log for "Rez V3" spanning from March 2020 to January 2022. It details the project's evolution, covering rebuilt foundations, feature implementations, bug fixes, and ongoing experimentation across various game aspects.

**Key Development Periods & Themes:**

**2020:**

*   **March 2020 - April 2020: Rebuilding and Core Mechanics**
    *   Project rebuilt on a new foundation using Starfox Mix, Jam, Red Dead Dead Eye, and Rez V2 remnants.
    *   Focus on refining lock-on, importing enemies, implementing bullet pooling, and fixing level loading.
    *   Early audio integration with Koreographer for shoot/lock-on timing.
    *   Initial visual experiments with Joost shaders and particle effects.
    *   Greyboxing levels and learning Cinemachine Dolly.
    *   Implementing basic game elements like vibration, particle effects for death, and different bullet/enemy types.
    *   Addressing early bugs and refining core mechanics like lock-on and shooting timing.
*   **May 2020 - June 2020: Puzzle Integration and Time Mechanics**
    *   Exploring puzzle mechanics, particularly shape-based puzzles and Tetris integration.
    *   Experimentation with time-rewind mechanics using Chronos, integrated with Koreographer.
    *   Refinement of aiming and reticle controls, including Panzer Dragoon camera angle changes.
    *   Visual experiments with mirror shaders and outlines.
    *   Implementation of a pause menu and EasySave integration.
    *   Addressing bullet behavior issues, including bullets getting stuck and recycling problems.
*   **July 2020 - August 2020: Performance Optimization and Enemy AI**
    *   Significant focus on performance optimization, including level optimization, shader adjustments, mesh combining, and LOD implementation.
    *   Exploration of enemy AI, initially basic and later considering Behavior Designer and A*.
    *   Experimentation with Sensor Toolkit for enemy line of sight.
    *   Visual enhancements with fog and ghost shaders.
    *   Continued debugging and refinement of core gameplay mechanics.
    *   Level design iterations and experimentation with different level structures.

**2021:**

*   **September 2021 - December 2021: Glitch Effects, Enemy AI, and Object Pooling**
    *   Developing glitch animation scripts using Joost modifiers and DOTween.
    *   Further development of enemy AI, integrating A* pathfinding and Behavior Designer for tactical behaviors.
    *   Implementing object pooling to address performance issues with enemy spawning and A* navmesh.
    *   Experimentation with Cinemachine camera blending and Timeline for cinematic sections.
    *   Addressing DOTween integration issues and SWS errors.
    *   Continued bug fixing and refinement of enemy behavior and bullet mechanics.
*   **January 2022: Enemy AI and Bug Fixing Focus**
    *   Deep dive into enemy AI implementation, focusing on tactical behaviors and integration with A* pathfinding and Behavior Designer.
    *   Addressing persistent bugs related to bullet behavior, lock-on issues, and A* graph movement.
    *   Experimentation with different bullet types and homing mechanics.
    *   Continued performance optimization efforts, including Mesh Baker and LOD systems.
    *   Preparation for transitioning from prototyping to a more structured development phase.

**Overall Summary:**

The log reveals a highly iterative development process characterized by continuous experimentation, problem-solving, and feature expansion.  The project progressed from establishing core mechanics and visual style to tackling more complex challenges in AI, performance optimization, and puzzle integration.  A significant portion of the development was dedicated to debugging and refining existing systems, indicating an ongoing effort to solidify the game's foundation while exploring new gameplay possibilities. The project demonstrates a strong focus on rhythm-based gameplay, visual style, and enemy AI, with consistent reference to external resources, tutorials, and assets to achieve the desired game experience.