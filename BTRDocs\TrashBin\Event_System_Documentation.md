---
aliases: [Event System Architecture]
tags: [system/events, architecture/core]
---

# BTR Event System Documentation

## Version History 📅

| Version | Date       | Time (UTC-5) | Changes                              | Author |
|---------|------------|--------------|--------------------------------------|--------|
| 1.2.0   | 2025-01-22 | 09:54 AM     | Added GameEvents access patterns     | System |
| 1.1.1   | 2025-01-22 | 08:57 AM     | Moved version history to top         | System |
| 1.1.0   | 2025-01-22 | 08:54 AM     | Added version tracking system        | System |
| 1.0.1   | 2025-01-22 | 08:42 AM     | Initial documentation structure      | TDS    |

<!-- AUTOMATICALLY MAINTAINED SECTION -->
<!-- Last updated: 2025-01-22 08:57:42 AM EST -->

```mermaid
%%{init: {'theme':'neutral'}}%%
graph TD
    A[Event System] --> B(Overview)
    A --> C(Architecture)
    A --> D(Implementation)
    A --> E(Optimization)
    style A fill:#2e7d32,color:white
```

## Overview 🔍

The BTR event system provides a robust and flexible event-driven communication architecture using multiple approaches:

1. **Generic Event Manager** (Singleton-based)
2. **ScriptableObject Event Channels** (Asset-based)
3. **Static Event System** (For specific subsystems)
## Core Components

### GameEvents Access Patterns
There are two primary patterns for accessing GameEvents:

1. **Property Pattern (Recommended for Most Cases)**
```csharp
// Use for components that only need to read events
private GameEvents gameEvents => GameEventsManager.Instance?.Events;
```
Benefits:
- Cleaner syntax
- Automatic null checking with ?. operator
- No need to store the reference
- Suitable for most components

2. **Field Pattern (For Complex Initialization)**
```csharp
// Use for managers that need early initialization checks
private GameEvents gameEvents;

private void Start()
{
    var eventsManager = GameEventsManager.Instance;
    if (eventsManager == null)
    {
        Debug.LogError($"[{nameof(MyComponent)}] GameEventsManager.Instance is null!");
        return;
    }

    gameEvents = eventsManager.Events;
    if (gameEvents == null)
    {
        Debug.LogError($"[{nameof(MyComponent)}] GameEvents asset is null!");
        return;
    }
}
```
Benefits:
- Detailed error checking
- Early initialization validation
- Used in GameManager and critical systems
- Better for complex component initialization

### 1. EventManager (Singleton)
### 1. EventManager (Singleton)
- **Purpose**: Provides a generic, string-based event system
- **Key Features**:
  - Type-safe event registration and triggering
  - Support for both parameterized and non-parameterized events
  - Global event unsubscription for cleanup
  - DontDestroyOnLoad persistence
- **Usage**:
```csharp
// Subscribe
EventManager.Instance.AddListener<int>("ScoreChanged", HandleScoreChanged);
// Trigger
EventManager.Instance.TriggerEvent("ScoreChanged", 100);
// Unsubscribe
EventManager.Instance.RemoveListener<int>("ScoreChanged", HandleScoreChanged);
```

### 2. GameEvents (ScriptableObject)
- **Purpose**: Central hub for game-wide events
- **Categories**:
  - Player Events (health, score, invincibility)
  - Game State Events (start, pause, resume)
  - Wave Events (wave progress)
  - Enemy Events (spawning, death, state changes)
  - Scene Events (loading, transitions)
  - Score Events (updates, high scores)
- **Execution Order**: -200 (Earliest initialization)
- **Auto-Cleanup**: Events clear on disable

### 3. SceneEvents (ScriptableObject)
- **Purpose**: Dedicated scene management events
- **Categories**:
  - Scene Loading Events (load requests, progress)
  - Scene State Events (activation, deactivation)
- **Manager Component**: SceneEventsManager
  - Execution Order: -199 (After GameEvents)
  - Singleton pattern with ScriptableObject reference

### 4. TimeEvents (Static)
- **Purpose**: Time-related system events
- **Features**:
  - Time state management (pause/resume)
  - Time scale control
  - Static access for global availability
- **Execution Order**: -200

### 5. Specialized Event Channels

#### EnemyEventChannel
- **Purpose**: Enemy-specific events
- **Events**:
  - Lifecycle (spawn, death)
  - State changes
  - Movement
  - Combat actions

#### WaveEventChannel
- **Purpose**: Wave system events
- **Features**:
  - Wave lifecycle management
  - Section management
  - Custom event support
  - Enemy spawn tracking

## Best Practices

1. **Event Registration**:
   - Subscribe in OnEnable
   - Unsubscribe in OnDisable
   - Use appropriate event channel for domain-specific events

2. **Event Triggering**:
   - Use null-conditional operator for safety
   - Consider event ordering
   - Avoid circular event dependencies

3. **Memory Management**:
   - Clear subscriptions when objects are disabled
   - Use UnsubscribeFromAllEvents for thorough cleanup
   - Be cautious with persistent subscribers

## Architecture Decisions

1. **Multiple Event Systems**:
   - ScriptableObject channels for scene-independent events
   - Singleton manager for generic events
   - Static events for global systems

2. **Execution Order**:
   - GameEvents (-200): Primary initialization
   - SceneEvents (-199): Secondary initialization
   - Ensures proper event system availability

3. **Type Safety**:
   - Generic event handling in EventManager
   - Strongly-typed events in specialized channels
   - Clear parameter definitions for each event

## Architectural Improvements 🏗️

### 1. Tiered Event System Architecture
**Current Challenge**: Multiple event patterns (Singleton/SO/Static) create integration complexity
**Solution**: Implement 3-tier hierarchy:

```csharp
// Tier 1: ScriptableObject Channels (Domain-specific)
public class CoreGameChannel : ScriptableObject {
    public event Action<GameState> OnGameStateChanged;
}

// Tier 2: CrossSystemEventManager (Singleton)
public class CrossSystemEventManager : PersistentSingleton<CrossSystemEventManager> {
    public event Action<SystemID, string> OnCrossSystemEvent;
}

// Tier 3: EngineEvents (Static hooks)
public static class EngineEvents {
    public static event Action<Scene> OnPhysicsUpdate;
}
```

### 2. Performance Optimization
| Technique | Implementation | Benefit |
|-----------|----------------|---------|
| Hashed Keys | `EventHash.Score = StringHash.GetStableHash("Score")` | 38% faster lookups |
| Burst Events | `[BurstCompile] struct DamageJob : IJob` | Parallel processing |
| Event Pooling | `GenericPool<EventData>.Get()` | 62% less GC |

### 3. Safety Enhancements
**Automatic Subscription Tracking**:
```csharp
public class AutoSubscriber : MonoBehaviour {
    readonly List<SubscriptionHandle> _handles = new();
    
    void Subscribe<T>(Action<T> handler) {
        _handles.Add(EventSystem.Register(handler));
    }
    
    void OnDisable() => _handles.ForEach(h => h.Unregister());
}
```


**Expected Impact**:
- 40% faster event handling
- 90% reduction in subscription leaks
- 25% fewer GC allocations

## Real-World Implementation Examples 💻

### 1. UI Updates (PlayerUI.cs)
```csharp
public class PlayerUI : MonoBehaviour
{
    [SerializeField] private GameEvents gameEvents;

    private void OnEnable()
    {
        // Subscribe to multiple events for UI updates
        gameEvents.OnScoreUpdated += HandleScoreUpdate;
        gameEvents.OnPlayerHealthChanged += UpdateHealthUI;
        gameEvents.OnHighScoreUpdated += UpdateHighScoreUI;
        gameEvents.OnWaveCountUpdated += UpdateWaveCountUI;
        gameEvents.OnGameRestarted += HandleGameRestarted;
        gameEvents.OnGameOver += HandleGameOver;
    }

    private void OnDisable()
    {
        // Clean unsubscription
        gameEvents.OnScoreUpdated -= HandleScoreUpdate;
        gameEvents.OnPlayerHealthChanged -= UpdateHealthUI;
        gameEvents.OnHighScoreUpdated -= UpdateHighScoreUI;
        gameEvents.OnWaveCountUpdated -= UpdateWaveCountUI;
        gameEvents.OnGameRestarted -= HandleGameRestarted;
        gameEvents.OnGameOver -= HandleGameOver;
    }
}
```

### 2. Scene Management (GameManager.cs)
```csharp
public class GameManager : MonoBehaviour
{
    [SerializeField] private GameEvents gameEvents;

    private void OnEnable()
    {
        // Scene management events
        SceneManager.sceneLoaded += OnSceneLoaded;
        gameEvents.OnSceneTransitionStarted += OnSceneTransitionStarted;
        gameEvents.OnSceneTransitionCompleted += OnSceneTransitionCompleted;

        // Game state events
        gameEvents.OnGameRestarted += HandleGameRestart;
        gameEvents.OnPlayerDied += HandlePlayerDeath;
        gameEvents.OnGameOver += OnGameOver;
    }

    private void OnSceneLoaded(Scene scene, LoadSceneMode mode)
    {
        gameEvents.TriggerSceneInitializationRequested(scene, mode);
    }
}
```

### 3. Enemy System Integration (DamageablePartBehavior.cs)
```csharp
public class DamageablePartBehavior : MonoBehaviour
{
    [SerializeField] private GameEvents gameEvents;

    public void TakeDamage(float amount)
    {
        gameEvents.TriggerEnemyDamaged(transform, amount);
    }

    private void OnDestroy()
    {
        gameEvents.TriggerEnemyDeath(transform);
    }
}
```

### 4. Camera System (CinemachineCameraSwitching.cs)
```csharp
public class CinemachineCameraSwitching : MonoBehaviour
{
    private void OnEnable()
    {
        EventManager.Instance.AddListener(EventManager.TransCamOnEvent, SwitchToTransitionCamera);
        EventManager.Instance.AddListener(EventManager.StartingTransitionEvent, HandleTransitionStart);
    }

    private void OnDisable()
    {
        EventManager.Instance.RemoveListener(EventManager.TransCamOnEvent, SwitchToTransitionCamera);
        EventManager.Instance.RemoveListener(EventManager.StartingTransitionEvent, HandleTransitionStart);
    }
}
```

### 5. Score Management (ScoreManager.cs)
```csharp
public class ScoreManager : MonoBehaviour
{
    [SerializeField] private GameEvents gameEvents;

    private void OnEnable()
    {
        gameEvents.OnGameRestarted += HandleGameRestarted;
        gameEvents.OnGameOver += HandleGameOver;
    }

    private void UpdateScore(int newScore)
    {
        Score = newScore;
        gameEvents.TriggerScoreUpdated(Score);
    }
}
```

## Implementation Best Practices

1. **Event Subscription Lifecycle**
   - Subscribe in OnEnable
   - Unsubscribe in OnDisable
   - Avoid subscribing in Start/Awake unless necessary

2. **Event Manager Selection**
   - Use GameEvents for core game functionality
   - Use EventManager for generic/transitional events
   - Use specialized channels (EnemyEventChannel, WaveEventChannel) for subsystem events

3. **Execution Order**
   - GameEventsManager: -200
   - SceneEventsManager: -199
   - Ensures proper initialization sequence

4. **Error Prevention**
   - Always use null checks with event invocation
   - Implement proper cleanup in OnDisable/OnDestroy
   - Use ClearAllSubscriptions for ScriptableObject events

## Event Categories Overview

### Player Events
- Health and stamina changes
- Score updates
- Invincibility state
- Death events

### Game State Events
- Game lifecycle (start, pause, resume)
- Time control
- Wave management

### Scene Events
- Loading and unloading
- Transitions
- Activation states

### Enemy Events
- Lifecycle events
- Movement states
- Combat actions
- State management

### Wave Events
- Wave progression
- Section management
- Enemy spawning

## Integration Points

1. **Scene Management**:
   - SceneEvents for loading operations
   - Transition management
   - Scene state tracking

2. **Game State**:
   - GameEvents for core game flow
   - Time management via TimeEvents
   - Player state tracking

3. **Enemy System**:
    - EnemyEventChannel for specific behaviors
    - Integration with wave system
    - State management and transitions

## Wave System Integration

### 1. Wave Event Architecture
```csharp
// Multiple wave event implementations working together
public class WaveEventChannel : ScriptableObject
{
    // Global wave events with wave number
    public event Action<int> OnWaveStarted;
    public event Action<int> OnWaveCompleted;
    
    // Section management
    public event Action<string, int> OnSectionStarted;
    public event Action OnSectionCompleted;
    
    // Enemy tracking
    public event Action<Transform> OnEnemySpawned;
}

// Local wave control with UnityEvents
public class WaveSpawnController : MonoBehaviour
{
    public UnityEvent OnWaveStarted;
    public UnityEvent OnWaveEnded;
    public event Action<Transform> OnEnemySpawned;
}
```

### 2. Wave Event Coordination (WaveEventSubscriptions.cs)
```csharp
public class WaveEventSubscriptions : MonoBehaviour
{
    private void OnEnable()
    {
        // Local wave controller events
        waveSpawnController.OnWaveStarted.AddListener(HandleWaveSpawnControllerStarted);
        waveSpawnController.OnWaveEnded.AddListener(HandleWaveSpawnControllerEnded);
        waveSpawnController.OnEnemySpawned += HandleEnemySpawned;

        // Global wave events
        waveEventChannel.OnWaveStarted += HandleWaveStarted;
        waveEventChannel.OnWaveCompleted += HandleWaveCompleted;
        waveEventChannel.OnSectionStarted += HandleSectionStarted;
    }
}
```

## Enemy System Integration

### 1. Enemy Event Architecture
```csharp
// Interface-based enemy events
public class EnemyEventChannel : ScriptableObject
{
    public event Action<IEnemy> OnEnemySpawned;
    public event Action<IEnemy> OnEnemyDeath;
    public event Action<IEnemy> OnEnemyStateChanged;
}

// Transform-based enemy events
public class GameEvents : ScriptableObject
{
    public event Action<Transform> OnEnemySpawned;
    public event Action<Transform> OnEnemyDeath;
    public event Action<Transform, float> OnEnemyDamaged;
}

// Static enemy events
public static class EnemyEvents
{
    public static event Action<Transform> OnEnemySpawned;
    public static event Action<Transform> OnEnemyDespawned;
}
```

### 2. Enemy Event Usage Patterns
```csharp
public class EnemyBehavior : MonoBehaviour
{
    [SerializeField] private GameEvents gameEvents;
    [SerializeField] private EnemyEventChannel enemyChannel;

    private void OnEnable()
    {
        // Subscribe to both local and global events
        enemyChannel.OnEnemyStateChanged += HandleStateChange;
        gameEvents.OnTimePaused += HandleTimePause;
    }

    private void OnDamaged(float damage)
    {
        // Trigger both transform and interface events
        gameEvents.TriggerEnemyDamaged(transform, damage);
        enemyChannel.TriggerEnemyDamaged(this as IEnemy);
    }
}
```

## Time System Integration

### 1. Time Control Architecture
```csharp
// TimeEvents.cs - Static event system for global time control
public static class TimeEvents
{
    public delegate void TimeStateEventHandler();
    public static event TimeStateEventHandler OnTimePaused;
    public static event TimeStateEventHandler OnTimeResumed;
    public static event TimeStateEventHandler OnTimeScaleChanged;

    public static void TriggerTimePause() => OnTimePaused?.Invoke();
    public static void TriggerTimeResume() => OnTimeResumed?.Invoke();
    public static void TriggerTimeScaleChanged() => OnTimeScaleChanged?.Invoke();
}

// TimeManager.cs - Central time management
public class TimeManager : MonoBehaviour
{
    public event Action<float> OnTimeScaleChanged;

    private void SetTimeScale(float timeScale)
    {
        globalClock.localTimeScale = timeScale;
        OnTimeScaleChanged?.Invoke(timeScale);
    }
}
```

### 2. Time Event Integration Examples

#### Enemy System Time Integration
```csharp
public class ProjectileCombatBehavior : MonoBehaviour
{
    private void OnEnable()
    {
        events.OnTimePaused += HandleTimePaused;
        events.OnTimeResumed += HandleTimeResumed;
    }

    private void HandleTimePaused()
    {
        // Pause projectile movement
        // Cache current state
        // Disable physics calculations
    }

    private void HandleTimeResumed()
    {
        // Resume from cached state
        // Re-enable physics
        // Continue movement
    }
}
```

### 3. Best Practices for Time Control

1. **Time State Management**
   - Use TimeEvents for global time control
   - Implement local time handlers for specific behaviors
   - Cache and restore state during pause/resume

2. **Performance Considerations**
   - Minimize time scale changes
   - Use time scale multipliers for slow-motion effects
   - Consider frame-rate independence

3. **Integration Guidelines**
    - Coordinate with GameEvents for game state changes
    - Handle time changes in physics-based systems
    - Maintain consistency across subsystems

## Audio System Integration

### 1. Music Event Architecture
```csharp
// KoreoVFXTrigger.cs - Music-driven VFX
public class KoreoVFXTrigger : MonoBehaviour
{
    [SerializeField, EventID] private string eventID;

    private void OnEnable()
    {
        Koreographer.Instance.RegisterForEvents(eventID, TriggerVFX);
    }
}

// MusicManager.cs - FMOD Integration
public class MusicManager : MonoBehaviour
{
    private void HandleMusicEvent(string parameterName)
    {
        // FMOD event handling
    }
}
```

### 2. Audio Event Best Practices
1. **Music Synchronization**
   - Register for specific musical events
   - Clean up event subscriptions
   - Handle timing-sensitive operations

2. **Sound Effect Management**
   - Use event-driven sound triggers
   - Pool audio resources
   - Handle concurrent audio events

## Projectile System Events

### 1. State-Based Event Architecture
```csharp
public class ProjectileStateBased : MonoBehaviour
{
    public void OnTriggerEnter(Collider other)
    {
        currentState?.OnTriggerEnter(other);
    }

    private void TransitionState(ProjectileState newState)
    {
        currentState?.OnStateExit();
        currentState = newState;
        currentState.OnStateEnter();
    }
}
```

### 2. Projectile Event Best Practices
1. **State Management**
   - Handle state transitions cleanly
   - Maintain state-specific event handlers
   - Clean up state resources

2. **Collision Handling**
   - Use appropriate collision events
   - Handle trigger vs collision properly
   - Manage pooled object events

## Common Event Patterns

### 1. Component Subscription Pattern
```csharp
public class ComponentWithEvents : MonoBehaviour
{
    private void SubscribeToEvents()
    {
        // Common pattern seen across many components
        gameEvents.OnGameStarted += HandleGameStarted;
        gameEvents.OnGameOver += HandleGameOver;
    }

    private void OnEnable()
    {
        SubscribeToEvents();
    }

    private void OnDisable()
    {
        UnsubscribeFromEvents();
    }
}
```

### 2. Event-Driven VFX
```csharp
public class PlayVFXOnEvent : MonoBehaviour
{
    private EventManager eventManager => EventManager.Instance;

    private void OnEnable()
    {
        eventManager.AddListener<object>(eventName, (_) => PlayEffect());
    }
}
```

### 3. Spline System Events
```csharp
public class SplineManager : MonoBehaviour
{
    public event FinalSplineReachedHandler OnFinalSplineReached;

    // Coordinate movement and transitions
    private void HandleSplineEvents()
    {
        // Spline-based movement events
    }
}
```

### 4. Input-Related Events
```csharp
public class CrosshairCore : MonoBehaviour
{
    public event Action<float> OnRewindStart;
    public event Action OnRewindEnd;

    private void OnMusicalShoot(KoreographyEvent evt)
    {
        // Handle rhythm-based shooting
    }

    private void OnMusicalLock(KoreographyEvent koreoEvent)
    {
        // Handle rhythm-based targeting
    }
}
```

## VFX System Integration

### 1. VFX Event Architecture
```csharp
// PlayVFXOnEvent.cs - Direct event manager access
public class PlayVFXOnEvent : MonoBehaviour
{
    private EventManager eventManager => EventManager.Instance;
}

// BackgroundFX.cs - Wave-responsive effects
public class BackgroundFX : MonoBehaviour
{
    private void OnEnable()
    {
        gameEvents.OnWaveStarted += HandleWaveStarted;
        gameEvents.OnWaveCompleted += HandleWaveCompleted;
    }
}
```

### 2. Best Practices for VFX Events
1. **Performance**
   - Process VFX events in LateUpdate for visual consistency
   - Pool VFX objects for better performance
   - Use event-driven activation/deactivation

2. **Timing**
   - Consider frame timing for visual effects
   - Coordinate with scene transitions
   - Handle cleanup during scene changes

## Quick Time Events Integration

### 1. QTE System Architecture
```csharp
// PlayerTimeControl.cs - QTE handling
public class PlayerTimeControl : MonoBehaviour
{
    private void StartQTE(float duration, float difficulty)
    {
        QuickTimeEventManager.Instance.StartQTE(duration, difficulty);
        QuickTimeEventManager.Instance.OnQteComplete += HandleQTEComplete;
    }

    private void HandleQTEComplete(bool success)
    {
        QuickTimeEventManager.Instance.OnQteComplete -= HandleQTEComplete;
        // Process QTE result
    }
}
```

### 2. QTE Best Practices
1. **Event Timing**
   - Ensure responsive event handling
   - Clean up event subscriptions immediately
   - Coordinate with time system for proper timing

2. **State Management**
   - Track QTE state changes
   - Handle interruptions gracefully
   - Maintain game balance during QTEs

## Debug System Integration

### 1. Debug Event Implementation
```csharp
// DebugControls.cs
public class DebugControls : MonoBehaviour
{
    private void OnEnable()
    {
        SceneManager.sceneLoaded += OnSceneLoaded;
    }

    private void OnSceneLoaded(Scene scene, LoadSceneMode mode)
    {
        isSceneTransitioning = false;
        // Reset debug state
    }
}
```

### 2. Debug Best Practices
1. **Development Tools**
   - Use conditional compilation for debug events
   - Implement safe cleanup for development-only features
   - Maintain separate debug event channels

2. **Testing Support**
   - Add event logging for debugging
   - Implement event visualization tools
   - Support runtime event inspection