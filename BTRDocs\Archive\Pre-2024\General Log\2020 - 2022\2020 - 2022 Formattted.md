You are absolutely right! I apologize for the incomplete response. I missed the end of August and the entirety of September, October, November, December, and January. Here's a complete, corrected version of the log:

# Rez V3 Log (2020-2022)

## 2020

**March 28th 2020**

Rebuilt using Starfox Mix and Jam + Red Dead Dead Eye and remnants of Rez V2

Looking good! Much better basis for control

Things to do:

*   Refine Lock on
*   Import enemies and get bullet polling working
*   Enemy Pooling?
*   Fix Level Loader
*   Koreographer shoot / lock on mechanisms

**April 3**

Working on timing

Targets List Gathers all targets

Locked list is all targets in time

Release pulls from Locked list

Do I need true/false to prevent locking/firing from interferring with each other?

**IMP -** Need to run a check on Targets for anything that should be on LockedList

Having Lock and Shoot working, need to refine sounds and clean up

Look at Joost or other shaders effecting environment

Add vibration

Add particle effects for Death

Think of different bullet/enemy types

*   Procedural Boss ex - [https://www.youtube.com/watch?v=LVSmp0zW8pY&t=124s](https://www.youtube.com/watch?v=LVSmp0zW8pY&t=124s)

Enemy ideas - Krang-like

**IDEA** - At the end of odd grouping of locked on targets, play a tag to even it out!

**April 4th**

Vibration added

Reviewing Post-Processing [https://www.youtube.com/watch?v=9tjYz6Ab0oc](https://www.youtube.com/watch?v=9tjYz6Ab0oc)

Global for now, but Local Volume for Takahasi video effects?

Upgraded to URP with limited PP added

Trying out different timings for music

Added Kick vibration Script to Koreo Object

**April 5th**

Doing some of Particles tutorial - [https://www.youtube.com/watch?v=hyBbcFCvDR8&](https://www.youtube.com/watch?v=hyBbcFCvDR8&t=482s)

Need to tighten lock on - how to achieve this?

Basic color changing environment method added to Crosshairs class

Playing with Joost on bullets - need script to alter parameters. THinking of use cases

**April 6th**

**Goals:**

*   Grey box levels! - Tested, need to make one with ProBuilder - Check!
*   Learn how Cinemachine Dolly works - Check!
*   Make a level transition! - Next level works, but timing is off - could easily setup a trigger as well

Added a character instead oh ship - adjust rotation and movement

Tried new shaders!

Looking for particles for bullet destruction

Looking for better lock on graphic

MAKE A BACKUP SYSTEM WORK!!!!

**April 7th**

Backup working with Github - maybe use SourceContol as a second backup?

Learning Living Particles - affectors

Living Particles - Tiling and Offset create interesting effects - manipulate these with Koreogrpaher?

Scene Alt Audio 2-2 Trans - Procedural snake creature moving away shooting these???

Look at procedural youtube vid!

**April 8th**

IDEA: Voiceover with lip sync? evangelion face in horizon - looming head - type of thing

Added Odin Inspector

Added Cube Snake - [https://learn.unity.com/tutorial/using-animation-rigging-damped-transform?projectId=5f9350ffedbc2a0020193331#5faddb95edbc2a0fd05a3512](https://learn.unity.com/tutorial/using-animation-rigging-damped-transform?projectId=5f9350ffedbc2a0020193331#5faddb95edbc2a0fd05a3512)

IDEA: Fargo dePalma multiple camera angles - could use splitting the screen effectively like this?

**April 9**

Figure out issues with Particle Clip Channel Texture- What is it doing? Effects look drastically

Need Read/Write on the image used - easy fix

Using DOTween for basic movement

Building out Cube Snake Procedural aniamtion

Think about Koreographer / DOTween intergrations?

Added Iceberg Rotation

IDEA: Steve Reich movement - sync/out of sync stuff

**April 10th**

*   Greybox a level
*   Optimize?
*   Shoot time already based on Koreographer Tempo

Created GlobalControls.SceneBPM for referencing Tempo

Experimented with Snake procedural graphics

Experimenting with particle shapes for bullets - can emit patterns and lots of things

Testing mixamo floating animation and Local Volume on player character

**April 11**

Matched Player HOLO Shader to Global SceneBPM

Greyboxing level concepts

Created **Object Rotations** script for setting up trigger object movements

Greyboxed level with these tools, likely interlude to song sections

Introduced Score and Lock counters on UI

Player Death now possible

Greybox a more traditional level with enemies next?

**April 12**

Introduced Audio Mixing Group

Ducking Noise track when Locking/Shooting

Introduced Combo multiplier for Shooting - should this be an exponent?

Fixed Locks Number - shows number of locks counting down as enemies shot (didnt before)

Issues with Line Renderer - Not drawing lines between objects

IDEA Change particle speed when locked?

Did this! Particles slowly reverse - maybe just have them hold?

IDEA Particle Spawner releases different patterns that can be drawn?

**April 13**

Sorted out an Ideas page for logging inspired thoughts

Greybox 1-2 - Melodic ideas and new skybox/Global Volume on

**April 14**

Looking into satisfying lock on rhythm adjustments

Looking into offset rhythm experiment with Rival Consoles track as example

*   Koreogrpaher Pro Ideal for this?

Use F mod instead of Unity Tools? Need to see Koreographer integration

IDEA: Draw shapes with target selections - could this be a shield to protect against an enemy?

**April 17th**

Koreogrpaher Pro - Midi implementation

Ableton2Midi - MidiEditor for tempo - Import

DoTween Movement script - Koreo Square Movement

Object moves in square pattern in time with track

Working on Greybox 7 - use a span to dictate when an enemy is shooting?

**April 18th**

Particle shooting is working! Also found Emit on Span script for particles

Greybox 8 - Arp spastic tempo moves on enemies?

**April 19th**

Joined the Gamma Space casual chat! Talked for a couple hours about making games / playing games

**April 20th**

Need to fix shooting in Greybox 7 - see what shooting bug is

Fixed this - missing shooting track - also replaced audio for shooting for Greybox 7

Issues with MMFeedbacks giving up part way through scene

Upgrading to newest Unity 2019 LTS to fix TextMeshPro error - see if it helps anything else

MMFeedbacks Impulse Error - `Impulses exist in 3D space`

**April 21**

Change to Boxcast for aiming, I think it's working better

Need to check sizing

Question Ray cast choices.... UI better? Unsure

Extended song a little bit

**April 22**

G7 song extended, put into level

Working!

Want to time particle releases to beat more

Need to identify how I want mixer to work - proper submixes etc

Need naming scheme on Koreographers - G7 - etc - similar to submixes

Set particle rate lower - makes it obvious ray cast not catching everything - FIX AIMING

Aiming Idea - Just use Square recticle as RaySpawn?

*   How to always make it face forward? or where i want?

Look at Rez Ref tracks and think level design - FITNESSS ?

**April 24**

**ds**dd Object Pooling not working as before? Maybe never working?

Particles are not dying, gathering up into big ball behind player

Added CowCatcher to Recycle bullet objects!

I THINK some locked on targets are getting recycled and causing issues - need to investigate this!

Playing with Player Moment script as well

Camera Follow + Player Movement determine screen space

Need to find a nice balance here

Writing a mehtod to find the lost enenmies to try aND FIX THE PROBLEM

**April 25**

**f**- Bug testing Lock on/particle bullets

Added color switching as well! some level design

**April 26**

Some bullets hanging in mid air - not being added to musical lock list. Still on targets list

Approaches to solving this problem?

Tried moving Lock=true from OnLock to OnMusicalLock, didnt work. Game feel was off and ended up with list of Targets that were not being added to MusicalLockList

Another approach, If something is on Target list for X amount of time but not MusicalLockList then DO THING?

**April 28**

Playing with shapes - lock on shapes? - particle emitting shapes?

Where does this bring in an interesting second action for gameplay?

And how does this fit into/effect the structure of the music?

**April 29**

Change bullet movement from particle system to script based movement (translate based on speed)

This allows me to rewind movement with Chronos feature

Also allowed me to add a homing feature, with a switch can make bullet look at player and move towards them

Looking at Chronos/Koreographer integration now. Can hit rewind button on beat, but cannot return to normal time on beat of koreographer timeline

Also seems a bit buggy - cant switch modes quickly

Trying it out with a set amount of rewind, then launching back into normal scale with left bumper

Some issues may be due to record interval - needs to match tempo?

Error - when time resume to original position

*   Dolly issue? Fixed with slight offset in time

Need Shooting on Gameplay dolly - but this rewinds it's movement as well. Way to exclude certain axis from timeline? Want it to move with Gameplay Plane but not X and Y of screen

EXPLORE - central Koreo and multiple KOreo timelines effected by chronos

Need to blow up all bullets when entering reverse time?

Bullets being released from particle system issues - improved cow catcher to remove lock locked enemy and also DEATH everything it catches

Disabled rewind on shooting element

**May 1**

Found CowCatcher Bug - needs to remove LOCKS number (not currently doing this)

Still problems with Burst and released Bullet objects? Not sure whats happening - colliding with each other?

Moving Time Control to Shooting class?

Need a OnRewindShoot method because Koreographer timeline not available during rewind I THINK

Introduced shooting continuing through rewind loops

Maybe set a different sound for this? or reverse the shooting sound? not sure if thats happening now

Removed TimeControl - now integrated into Crosshairs script

At some point rewinding stops working.... Not sure why

Bullet freeze no longer working

**May 2**

Fixed Bullet freeze! Pretty sure this fixed cow catcher issues as well (locked items never hit cow catcher now)

Seems like time glitch isn't breaking anymore too - maybe a result of this?

**May 5**

Winding Rez Halls for a new level?

**May 6th**

Trying enemy snake with multi selects

CUBESNAKE

Some errors - probably need Regular Enemy script and Enemy Script for particle object system

*   this is for releases from system on death, etc. maybe just a check if it's part of a particle system or not

GLobalControls like Scene BPM in ascriptable object seems like a good idea! look into this again

Need to setup a proper archetype for enemies and movement!

Also look at object particle spanner videos of shapes and styles! Lots of bullet styles here

IDEA: Move whole dolly and system in lock on pattern?

**May 7th**

If at full combo, slow down time? testing different combo numbers / effects

Glitching during musical firing

Make a log of current ongoing systems

IDEA - Shoot out objects in a particular shape, lock on to make shape, then particle shooter switches to new shape

Construct a volume at those points - color of objects looks different through that

shooting applies that color to the rest of the scene

This allows you to prepare for the change - but what is this change? and it's value?

Gntk movement videoHow to create constant flow in movement

**Mayy 11th**

Shape as notation - different combos / different rhythms? notation and puzzles?

Release bullets as triangle that you connect together

Try doing a snare or kick release pattern for this - link together generate portal? or generate thing?

Triangles!!!

Things to do!

*   Find out how to make a proper triangle
*   Rotate to the beat
*   3d audio / sounds of synths rising up as bullets come in?

Make a series of these type of levels / puzzles and chain them together

Look up Hindsight game trailer for effect

**May 12**

Replace Rayspawn in Cross hair script with Reticle - does the aim work better????

THIS BROKE THINGS

Had to go through a lot of trouble to fix

Replace G9 with G8 and build up trianlge movement

Figure out with G8 bullets move backwards when not homing

**May 14th**

Fixed G9

**May 15**

G9 - Spinning triangle working with DOTween (Full 360)

Attempting Fix of boxcast - need better aiming

FIXED!

Objects effected by time vs those that aren't - G9 Triangles

Commented out RewindShoot due to error tracing - cant find why objects are still being destoryed when i have alreayd commented out some ocode

Pretty sure trying to destory child of bullet actualyl destoryos entire bullet

FIX THIS

Launchab;e bullets not working

FIX THIS

**May 16**

*   am not recycling on release from particle system
*   am not calling Death

Seemed to fix this, uncertian how exactly. Recognized Lauchable in the Update method, that seems to be it

Enabled isTrigger on Homing Bullet

G9-2 has this lock object / fire back at target loop working

Need to learn Unity Events for trigger different stages of this boss / puzzle

[https://www.youtube.com/watch?v=TWxXD-UpvSg](https://www.youtube.com/watch?v=TWxXD-UpvSg)

**May 17th**

Gamma Space Meeting

**May 18th**

Unity Events  - [https://www.youtube.com/watch?v=OuZrhykVytg&t=12s](https://www.youtube.com/watch?v=OuZrhykVytg&t=12s)

Maybe I should use Actions instead of events?

Switch - look into this more

Learned Unity Events and have working system for bring up new game objects! All in G9

**May 19th**

Extending puzzle system - added more layers of objects to hit

Added player invincible option

Fixed collision problems by slowing launchback bullets

Also changed OnCollisionEnter to OnTrigger - homingbullets are triggers now

TODO

MeshManipulation pLugin - use this on objects??? Performant? NEED TO BUY $5

Change MMFeedbacks to Feel package? maybe already easy?

**May 22**

Changed out MMFeedbacks to Feel - may be some missed connections I will find in time

Linear speed areas VS Puzzle areas

Pause Menu integrated! Rough but working

Imported EasySave

Not using yet

**May 23**

Added Mesh Tracer to project

Some interesting effects for enemies / objects - flag for later use

**May 24**

Mechanically inspired by helix jumper

[https://assetstore.unity.com/packages/templates/packs/helix-smash-with-live-auto-generator-147944](https://assetstore.unity.com/packages/templates/packs/helix-smash-with-live-auto-generator-147944)

might be cool to have section where you move through faster/slow to get past targets

New greybox with mov forward through puzzle mechanism

bullets now attach to reticle before launch, and if using time skip mechanic can reaim a cluster of bullets to a new position

**May 25**

Added Easy Performant Outline - Try this with lock on for visual clarity

Made a CowCatcherForward for catching launched bullets

*   decent solution so they dont go to infinity but likely need something better

Tried limiting number of rewinds possible

Still buggy!

May need to rethink this whole structure. Do I need Coroutines for all of this? Could probably just use methods / events? Look into this deeply!

**May 26**

Learned a bit about Unity Profiler and Deep Profiling

Suggests to me that I don't have much to worry about currently! Not seeing any big performance problems there

Try changing location of loop

**May 27**

Location of loop changed, limtied number of right trigger rewinds appears to be working

Feels like a fire shoot be initiated when you run out - adding this

Figured it out!

Mirror rotating idea, dont forget!

[https://assetstore.unity.com/packages/vfx/shaders/urp-lwrp-mirror-shaders-135215](https://assetstore.unity.com/packages/vfx/shaders/urp-lwrp-mirror-shaders-135215)

Try Easy Outliner and get it working

JUST REALIZED i should set the puzzle as waypoints, not just forward speed. Look at how to set this up with Cinemachine Dolly, and how to start/stop at waypoints

IDEA: counterpoint like occurring here

**May 29**

LightProbeGroup/ReflectionProbe exists in original StarFox file - why not mine?

Changed Player tag to Player gameobject instead of Player model - does this cause problems?

Added lots! Start / Stop waypoints

Aiming laser for locked bullets

Playing with Reticle position when locked on / aiming a grouping of targets - needs work

Messing with Targets / Enenmy shooting relationship

Bug fixing!

Should I change the look at of bullets / laser so it's CowCatcherForward?

Will this fix my aiming issues?

**June 1**

**-** Sophie is away, i am also going a bit crazy, so progress has been slow at best maybe. I need to review all previous notes for May, see what i'm planning for June. Also working on a Gamma Space project concurrently

Viewing some of the Code Money Unity Functions tutorial tonight, to just get more familiar with the basics

**June 9**

Centering not working on lock on - center aim reticle to targets - reposition aim reticle

Seems to be a problem in the computer of center with X and Y

Is this due to negative/positive numbers? COME BACK TO THIS

Need to make better level design for lock on / shooting mechanic

Learn from Ynglet?

**June 10**

Adding the Panzer Dragoon camera angle changing feature

Not working properly. Needs adjustment

Need to be additive with angle on player movement

How to rotate the aiming reticle?

**June 11**

Need to thoroughly look into how aim reticle is bounded

Errors happening when rotated

Camera Properly rotates! Mostly! Weird 360 spins sometimes?

Reticle needs adjusting

**June 15**

Several days downtime due to second covid shot

Looking to Panzer Dragoon for character control

Should I just chain player character to reticle?

Need to understand reticle movement better!

Spin everything on a rotation game object??? Seems way simpler. Trying this. It works!!!

Fix jittery movement - refer to old project on desktop

**June 16**

Trying rotation with Time Glitching

Need more thorough implementation to keep in time

Design choice: Time Glitch and rotate or just allow rotation?? Maybe just allow rotation with sound effect?

Download Pro Radar to integrate radar on game for orientation

**June 17**

Pipeline errors - spent the morning fixing that.

Made small adjustments to radar. Need to get in Pro Builder and Pro Grids, etc

**June 18**

[https://www.youtube.com/watch?v=GioRYdZbGGk](https://www.youtube.com/watch?v=GioRYdZbGGk)

Can make a Material with flat "base map" of level, apply it to a plane, and build from there

Lots of good greyboxing advice in for 20 minutes

Error - if rotating while moving downward, screws up perspective, need to fix this!!!

How to handle rotation platform rotating in any direction?

Have a function recognizing Y movement that changes rotation axis - not totally working, may need to adjust for oritentation - if 1 of 4 positions, rotate accordingly to next position on X or Z/Y axis

**June 19**

Figuring out rotations for dolly travelling in different directions

Spent several hours on this, still didnt get anywhere

**June 22**

Add Tetris gameplay - very rough and needs work.

Try to make pieces lockable and transferable between perspectives?

Integrate EnemyBasics Script with game pieces script? Maybe need an edited version of this for these

**June 23**

Thinking out Tetris integration problems

Movement class constantly spawning new and object is not moving down - need to fix!

commented out spawning new

**June 24**

Fixed problems with Tessera Pro conflict and Tetris Class "Rotation" - now Tetris Rotation

**June 28**

Upgraded to HDRP over past couple days and also played with Tetris implementation

Looking at Jelly Cube puzzle implementation as well

6 Games Match Puzzle and Jigsaw Puzzle and Toybox Construction Kit too!

ALSO Polarith AI Pro for some enemies? Might be cool! Worth Testing

Some forward / Backward level like this?

[https://assetstore.unity.com/packages/templates/packs/000-endless-maze-complete-project-template-175485](https://assetstore.unity.com/packages/templates/packs/000-endless-maze-complete-project-template-175485)

Assessed several - looks like

Really need to assess what I'm trying to make here. Maybe scale back, keep it simpler

**June 29**

Checking out Tesera - can generate flat landscapes this way

**July 1**

Level Building - Sci Fi GreyBox 12

Implementing Ethereal Urp - Unsure on proper Forward Rendering to choose for camera

Need Fog to cover up LOD pop in issues?

Need to try Mesh Bakery for optimization as well

Need to construct moving enemies - possibly with Emerald AI? or overkill?

*   Line of Sight attack, move along waypoint path, ranged attack options available
*   Also option to have them become ally - interesting!

Also - reconnect to Source Tree!

**July 9**

Adding keyboard controls - spinning and mouse work

Fire not working

Synchronic film - music - drug is the needle on the record idea

**July 19**

Early level is not performant - Greybox 12

Need a better building → Polyfew → TessGen pipeline

Need to use more performant shaders that can be batched

Look at mobile transparency shaders?

Build a new level

Greybox 13

THING TO TRY - Polyfew Combine Meshes for level layouts - only when finalized?

Also maybe figure out Mesh Baker? Seems to be most popular

**July 20**

Ghost Shader - Studio CS Ghost UPR Advanced Always Visible

Make buildings one Mesh, some sevenral?

**July 21**

Optimized buildings in Blender - much better!

Game running much faster - no crazy draw calls

Need to try using ghost shader now too

construct more of level, see what I can do

Can build a few TessGen tiles before performance tanks

Can BatchFew the TessGen tiles - bit of placement required after

HUGE decrease in batch calls but performance isn't better?

MESH takes up too much memory it seems

How to reduce? Look into this. Can blender do this?

TIP 1

"If enabled, Mesh data is kept in memory so that a custom script can read and change it. Disabling this option saves memory, because Unity can unload a copy of Mesh data in the game. However, in certain cases when the Mesh is used with a Mesh Collider, this option must be enabled. These cases include:

*   Negative scaling (for example, (–1, 1, 1)).
*   Shear transform (for example, when a rotated Mesh has a scaled parent transform."

**July 23**

Keep seeing that number of verts and triangles could be the issue for batching. Trying severly reduced models to see how that fairs

SRP Batcher problems / benchmarking

[https://forum.unity.com/threads/gpu-instancing-not-working-properly-with-hdrp.809109/](https://forum.unity.com/threads/gpu-instancing-not-working-properly-with-hdrp.809109/)

Using a different shader for buildings cut batching by ALMOST half!

Using LOD's also cut batching down, by about half

**July 24**

Try Greybox 8 - Moving shapes movement

with Greybox 13 electrical platforms? or buidlings?

Movement layers - refer to VJ videos

Koreo Movement Script - use this for some movement?

Looking deeper into RealEffects4 - How to have random movement in shaders etc

Used Lighting Shot Material on platform

SineVFX may be better for this?

Reduced Far Clip Plane works for populated dense building area - likely not for less populated areas

**July 26**

Script animations for interesting movement of enivorment objects

Mesh Combine for scene optimazation

Can use Occlusion culling when full scene is decided upon

**July 27**

Debugging gameplay mostly

Rotating needs to adjust for rotation of Dolly path

If dolly turns causes issues for TriggerL/R rotations

Removed DOTween for rotation, hard coded, rotation working better now

Changed bullet lock on to Horizontal Plane for rotation of lock on bullets

Fixed aiming for 4 rotation positions - raycast works in all of them now

**July 28**

Moved rotate view to Player Movement from Shooter - doing some cleanup! Added back some animation to it (no DOTWEEN)

**NEEDS WORK - Rotate HARD to one of four positions**

[https://answers.unity.com/questions/1250587/how-can-i-rotate-an-object-to-a-specific-angle-ove.html](https://answers.unity.com/questions/1250587/how-can-i-rotate-an-object-to-a-specific-angle-ove.html)

Went back to old basic rotate- issue flagged for consultation!

Adding radar to bullet enemies - working!

**August 1**

Played with shaders and possible new unity assets to use

Looking at clamping Z movement of Reticle/Shooter

Reference original Starfox project for Reticle movement

Locked player right now to figure out reticle movement

Issues with clamping Reticle position

Removed clamping - still some jittery on left/right positions. Front/Back are fine

Find way to move bullets in opposite trajectory of their travelling direction

*   may need to change based on viewpoint?

Clamping breaks at high speeds!!

Need a different way to clamp- see what's wrong here

FIXEDUPDATE RECTIFIED ALL PROBLEMS??? OK!

Seems to fix all physics based problems wow

**August 3**

TODO:

Adjust bullets so when switching perspectives, they fly opposite of the aim reticle

IDEA:

Allow reticle to move bullets around?

Aim Assist for firing bullets? This could be way too complicated imo

*   Maybe depends on enemy?

Did an hour of Freya's Shader course

Projectiles firing in correct direction now!

Create enemies that get hit by projectiles Squares with holes in them?

Draw out scenarios

Sensor toolkit added - watch tutorial videos

[https://www.youtube.com/watch?v=hVBpwwjqM00](https://www.youtube.com/watch?v=hVBpwwjqM00)

Refer to action blocks Figma file

Try this asset for exploration? [https://assetstore.unity.com/packages/templates/packs/space-journey-42259](https://assetstore.unity.com/packages/templates/packs/space-journey-42259) - downloaded

Refer to Reboot for world wide web ideas?

Think of Lanthimos and false realities - [https://www.youtube.com/watch?v=2pPGeVkBYrU](https://www.youtube.com/watch?v=2pPGeVkBYrU)

**August 4**

Thinking Cow Catcher will no longer work, actually causes errors

Maybe if bullets leave a proximity they destroy themselves? LOOK INTO THIS

Trying Sensor toolkit on shooting enemies

Would trigger enter etc with wide range be better? Thinking line of site features may make Sensor Toolkit the better option

Sensor Toolkit working! Line of sight working!

Created Blockable Layer for things that can block line of sight of enemies

Line of sight may be iffy, be aware!

IDEA- CowCatcher causes bullets to reduce in speed if approaching from behind? collision causes slow down

Greybox Enemy Placement showing promise! Experiment with designs

**August 5**

Adding hittable targets - destroyed on impact

Looking at radar - adding enemies and hittable targets - need to think of design philosophy here

THings need to happen for a reason

Some combination of xall response and more traditional gameplay as seen in orta or zwei - think how to meld thumper and these - fast paced but free enemies to grab or hit

**August 6**

Hard long day at work. No work done really.

**August 7**

Lots of experimenting with fog - not much luck! Ethereal URP is difficult

Think I will stick with it though. Like it more then not having it

**August 9**

Found this - [https://www.resurgamstudios.com/cgf-manual](https://www.resurgamstudios.com/cgf-manual)

Seems like a cool way to have aim assist for bullets

**August 10**

Bullet speeds and level design

Trying to improve bullet orbit issues - maybe it worked?

Looking at level design structures - lots of trial and error

Something broken in rotating bullets now

When they parent horizontal platform - is something going wrong?

Player model need to be tagger Player Aim Target now or fix this

Enable Player Visibility when not use Sensor for control of enemies

Rotating seems to be an issue with speed? Working in Player Movement Testing scene

May need to reduce overall speeds on levels for some functionality?

Use combo of L/R and Glitching to position things. Likely need to refine glithcing

Objects still moving when locked on... why? Shouldnt it stop? Look into this

Player Movement Testing

**August 11**

Looking into objects not locking on and staying still

*   rb.constraints = RigidbodyConstraints.FreezeAll;
*   rb.constraints = RigidbodyConstraints.None;

IDEA: Move # of Locks into aiming reticle

IDEA: after locks, release of projectiles aims wherever cursor is pointing

*   pass reticle/rayspawn aiming to launchback function?

Need to shoot in general direction of rayspawn, hitting target isn't necessary

Seems to be working! Need to find a way to aim them at general direction of where Reticle is looking as well, rather then just forward

Maybe add Layer to things that can be hit? Maybe end of range OR target option

IDEA: Change shape of reticle depending on mode - lock vs shoot

**August 12**

Trying to pass a target (RaycastTarget) to Launchback command in order to aim the bullets at a specific point (the direction of the reticle)

setup tempV target to test homing of bullets

need to go over homing again

Launchback is creating sphere at target location - use these as lock on points for homing bullets?

Successfully creating sphere at target location and projectiles shoot towards them!

Switch this to a prefab that's destroyed on impact, maybe has a collision radius as well?

**August 13**

Using targetCube prefabs for targets of bullets now

Using Target Solo for collision detection script on these. Mostly working

Likely need a self destruct on these aiming cubes incase no collision

IDEA: only one cube as target instead of multiple, scaled according to number of bullets selected? EXPLORE

OPS Fixes

Removed destroy from TargetSolo - bullet should recycle itself

Fixing bullet's target - reassigning from targetcube to player at death

Fixing homing(true/false) of bullet on recycle

Fixed launching to false in Death phase, seems to fix issues with bullets not recycled and fired properly

**August 14**

Cleaning up office!

Setup Bugs sections for Notion

Doing some bug fixing as well

Need to Look over some technical documentation on assets used -

Koreogrpaher and Object Particle Spawner

*   barely did this, but need to return to it!

**August 16**

[https://www.youtube.com/watch?v=CTBor4rhnQs&t=1s](https://www.youtube.com/watch?v=CTBor4rhnQs&t=1s)

Thinking about Level Design - Radically Nonlinear Level Design

Discuss Doom 2016's levels. Combo of linear areas with combat arenas

Goals were constant movement and improvisation of navigation

Look up this Doom 2016 talk - push forward combat?

MDA framework for game analysis mentioned

What about Linear level Design?

Watched this on visual novels as well

[https://www.youtube.com/watch?v=vrxz3s0L8F8](https://www.youtube.com/watch?v=vrxz3s0L8F8)

**August 18**

I am sick, have been for a couple days probably. Not fun!

**August 21**

First day feeling mostly better

Boomerang X Unity Dev Spotlight - [https://www.youtube.com/watch?v=L7lf2VnTC74](https://www.youtube.com/watch?v=L7lf2VnTC74)

Cool shared effect shown, and ideas on tutorials

Look at Shield Effect again. Also Vertex Animations! 0 animations moved from cpu to gpu - vertex

good for porting to consoles and such apparently - how to do this?

Collider view shown in video? What is this?

Need to learn how modelling / shaders work. What are UV Maps?

They use a Mesh Combiner - then triangle eraser - custom tool for reduction for switch

They have waves and setup the waves per level - worth revisiting!

Sliders for changing object color for accessiblity

How is color applied? slider controls global shader variable

Discussion on community management - Discord channel

Early thing - every day - discord acronyms channel - D&D-like prompt posted with acronym ### random generated - funny and interesting community things listed

Different themes every month as well

Weekly silly channel as well

Looking up decimation tools

Zbrush - installed, need to try

Trying to make a Boss Test level - Boss Test 1

Spin in circle, being shot at

Found error with bullet direction when not homing, doesn't just go relative forward

Partially fixed, but unsure why in some direction z value is flipped. rotating bullet shooter / origin point to compensate

Homing bullets are weird! Not sure what's going on! Lots of weirdness

Need to standardize on bullet types

**August 22**

Editing Target Solo so that it doesn't kill itself

Integrating Ultimate Spawner + Wave component in Boss Test 1

Thinking about Boomerang X here

Fixed weird z behaviour on Standard Bullets by flipping x and y

Removed box collider on Target Cube

Converted Bullet Death to Coroutine, to see if it can be properly detected by TargetSolo - Does Not help

Issues around destroying enemies still present

Maybe something like (if bullet stops moving, recycle it?)

TargetSolo is disabled yet im still able to destroy prisms - why???

**August 24**

Prisms still jiggling when destroyed

Realized that turning off a script DOES NOT disable the behaviour here (Target Solo caused animation death)

IDEA: TargetCubes are sticky, need X number on hitting/attached an enemy to kill them

Enemy has a counter for how many are currently touching

Certain can accept X numebr of noise, large amount overwhelms them

IDEA: One enemies type does not affect itself - need to shoot it at another enemy type

Enemies can recognize their own type of data, this makes sense. So we need to mix in ways it does not expect

Conceptually - do i really want

You're right, my apologies again. I'm having trouble processing the entire file at once. I'll give you the rest of the log now:

**August 25**

Minor class cleanup (Crosshair)

Adding ReWired - Finish video! Integrate Movement

**August 26**

Morning - Finished ReWire integration for player

More touch ups to do like proper mouse integration - map to touch controls, etc

**August 27**

**B**Building glitch animation scripts with unity

Joost Modifier - can do music timed or rnadom glitching on x,y,z like Joost Eggermnot

Building this out to have lerping - need to look into how to properply do this

DOTween??

**Weekend Break**

It was nice!

**August 30**

Trying to time the animations, difficulty defining the loop and timing.

Keep failing, this is hard to do! Crashing system with current scripts. Need to look up animation scipts

[https://www.reddit.com/r/Unity3D/comments/feceog/dotween_float/](https://www.reddit.com/r/Unity3D/comments/feceog/dotween_float/)

Tween a float to get the motion needed?

or smooth stepping

[https://www.youtube.com/watch?v=iWc7hKvWA5U](https://www.youtube.com/watch?v=iWc7hKvWA5U)

**August 31**

New implementation that seems to work

Able to adjust a wait duration

Extending to RandomGlitchSmoothAnim

This would smoothly transition between wait durations (glitch duration)

Look over this and run

Still hitting issues! Not the right math

Hard coded a constant increase!

Cool effects sort of! Lots to play with

## 2021

**September 1st**

Experimenting with this on objects

Problems: Mesh that requires multiple materials - how to fix????

Problems: StackOverflow errors when glitch duration is too low - sub 0.2 I think

What is the issue? Does rrestructing this help?

IDEA - Playing with Shapes + Tessera manipulation!!

Place them alll around randomly

Really just —- make a level! abandoned places - failing archetecture - do this! move through

Use Mesh Combiner and then apply joost on top of that? Might be best solution

Reduce in Blender first though!

**September 6th**

Modify Offset Matrix in Joost scripts

Want to have this done to children objects

Made #script Joost Mod Group

Allows random offset vectors for child objects with set timing

Also made #script Joost Music Mod Group

Also Joost Music Mod Grow Shrink Group

*   need to implement scale factor in a better place here. not effecting counter
*   int / float issue!

**September 7th**

[http://codebetter.com/patricksmacchia/2008/11/19/an-easy-and-efficient-way-to-improve-net-code-performances/](http://codebetter.com/patricksmacchia/2008/11/19/an-easy-and-efficient-way-to-improve-net-code-performances/)

For instead of for each when possible?

#script Joost Music Exp Deep Child

Attempting to get Renders and adjust mats of all children of children as well!

Also simplifying some code - it works!!

Change scaleFactor to incrementValue

Changed beginning / end of loop so it doesnt look like it stutters

**Sept 13th**

Mixed post processing objects with non-post!

[https://www.youtube.com/watch?v=LAopDQDCwak](https://www.youtube.com/watch?v=LAopDQDCwak)

Use this for glitching techniques

**October 30**

**U**Under controllers in any scene - check if Rewired is there! Source of most problems when loading right now.

Update ReWired for best Mouse/Keyboard options

**November 6**

Upgraded to newest 2020 LTS

Had to remove Mesh Baker / Mesh Baker LOD

**Nov. 10**

Didn't I add a pause button? What is it / why isn't it working??

Would like to change rotation - make it more elegant!

Went back to DOTWEEN Method, but using adjustments on currentY not to exceed 360

Seems to work? But final rotation is never precise 0, 180, 90, etc.

Need to fix that in long run!

Working on Lock on with rotation as well - checking it works properly

Need to write up a breakdown of Enemy Setup

Need to do so for music and other pipelines as well

Using Greybox - Player Movement Testing to try out lock on and rotation

Looked through some Joost Building scripts for building movements

**Nov. 11**

Worked on some enemies and arrangements mostly

Upload probably started to occur as well

**Nov. 12**

Implement a rough version of character into Dreamteck - Forever - Collider Run

Looks promising

**Nov. 13**

Looking for into Forever implementation

Can use .NET seed to have consistent randomization - could I use this?

Public Follow method in Collider Run can be used to start movement

**Nov. 14**

Played with some of the Collider Run, using bullets doesnt seem to go anywhere

Making some enemy types, need to try integrating them into a level format

Thinking about best way to construct their movement

Wave system instantiates them

They move toward the player

They then move in a particular pattern form ?

Happens X times before they fly away

Try Behaviour Designer + other integrations

**Nov. 15**

Behaviour Designer - Tactical - Attack for basic instantiate / attack

**Nov. 19**

Trying to bring A* into the game - some success. Need a proper full tutorial on integration

Moving example prefabs brought in can work but Im not sure WHY its working lol

seems like it can move in 3d to some degree?

get this working then get Behaviour Designer involved as well

**Nov. 20**

Got the A* working - looking to integrate with Behaviour Designer

Working for some integrations! Need to plan out this whole thing

Not moving with scene like it should

Need to write out full enemy tech stack

UltimateWaveSpawner putting out enemies

Behavior Designer + A* Pathfinder handle movement

Behavior Designer / Sensor Toolkit for attacking?

**Nov. 21**

Behavior Designer tutorials and different integrations

It's coming along! Also posted to Slack!

SWS throwing lots of errors in project - DoTWEEN, and others

Need to find the fix for that

**Nov. 28th**

Figured out SWS / DOTween / BD errors thrown

Working on integration path

Brought prefab ships into project

Need Ultiamte SPawner to spawn these as children on A* nav mesh

Add script to object that make them child of proper object?

Trying this

MakeChildOfEnemyPlane script on enemies

Maybe need to add local space after it becomes child?

Not sure why local space is having issues besides the obvious (looks like its not on the mesh)

*   ask how to spawn things on a moving mesh from A* folks?

ERROR when NOT Moving as well, maybe moving isnt the issue? **look up A* things for spawn points**

**Nov. 29**

An enemy spawned as child of nothing works fine on nav mesh - previous thoughts are not the issue?

Error

NullReferenceException: Object reference not set to an instance of an object
Pathfinding.Examples.LocalSpaceRichAI.RefreshTransform () (at Assets/AstarPathfindingProject/ExampleScenes/Example13_Moving/LocalSpaceRichAI.cs:39)
Pathfinding.Examples.LocalSpaceRichAI.Start () (at Assets/AstarPathfindingProject/ExampleScenes/Example13_Moving/LocalSpaceRichAI.cs:45)

Might need pooling system for this to work? LOOKS RIGHT!

Use a wave spawner with pooling

New Pooling system in Unity - worth trying? Need to switch versions.....

[https://thegamedev.guru/unity-cpu-performance/object-pooling/#how-to-use-the-new-object-pooling-api-in-unity-2021](https://thegamedev.guru/unity-cpu-performance/object-pooling/#how-to-use-the-new-object-pooling-api-in-unity-2021)

or do basic pooling tutorial

[https://learn.unity.com/tutorial/introduction-to-object-pooling#5ff8d015edbc2a002063971c](https://learn.unity.com/tutorial/introduction-to-object-pooling#5ff8d015edbc2a002063971c)

Then tie the ultimate spawner instantiate to my system

Also see support email response

**December 7**

Cinemachine tutorials

Blend between cameras - important aspect!

Ignore Timescale property may be important for me

**Q** Use Timeline for certain cinematic sections? Or better solution?

Integrating Object Pooling into current scene as part of A* / Behavior Designer Pipeline

Object pooling fixes issues with A* navmesh

Made an ovveride script for UltimateSpawner's Instantiate method

*   will this be needed on every instance?

How do I get this working for multiple types of enemies being pulled from the pool?

EnemyObjectPool.cs is working! Have a hacky way to make it work

But how do I get it to work with multiple enemy type being called by Ultimate Spawner?

How to compare if prefab and instance of object match?

[https://docs.unity3d.com/ScriptReference/PrefabUtility.GetPrefabObject.html](https://docs.unity3d.com/ScriptReference/PrefabUtility.GetPrefabObject.html)

THIS IS OUTDATED

THink about how Object Particle Spawner works as a comparison

Spawns objects by weight - maybe not so useful

I want Ultimate Spawner's call to Instantiate a specific Objec tto pull from the necessary pool

Q? - Do I need to use custom spawnable item providers? Look into this tomorrow

Downloaded Pool Manager Asset incase it's useful for this at all

**December 9**

Thinking on the solution - match the pool tag to the prefab tag? Every enemy type needs a unique tag then

Maybe Layers is better? Can assign more then one. Limited number of Layers! Best not to use for this

Currently use LaunchableBullet tag for projectiles

Some of these tags are not necessary - need to audit

Relevant? [https://forum.unity.com/threads/test-if-prefab-is-an-instance.562900/](https://forum.unity.com/threads/test-if-prefab-is-an-instance.562900/)

**NEW APPROACH**

Try using Pool Manager

It's working! Renamed class to "Spawn From Pool"

Need to get spawn from pool to pull name from SpawnPool class - NOT HARDCODE

**December 13**

Previous solution broken. Possibly due to renaming? May have been last step after working

NO not the issue. Spawn Pool Per Prefab Pool Options had not saved. The prefab object was not selected and number to preload not given. This caused A* problems. For moving nav mesh you need to preload these elements or it does not work properly.

changed poolname to pull from component in same gameObject

**December 14**

Did some research on behaviour designer

need to download some more examples and dig into some good AI setups

**December 15**

Bringing in example scenes from tie in assets for BD

*   Pool Manager, Sensor Toolkit,

Tried adding a Player Radius to the Player, so that enemies using Seek stop at this radius - never too close to player

Not sure how target object is being assigned to Behavior Designer Prefabs. Look into this for appropriate behaviour

Also need to look into proper structure for Seek → Wander - Repeat type BD loop

Not currently working after Wander

**December 16**

Fixing the long nagging DOTween issue. Seems like it's the \*.meta file associated with the folder that kept bringing it back? We'll see!

WRONG it seems to be after a SourceTree upload

Seems to be when Git LFS file kicks in, it redownloads this stuff

Found error on defining enemy transform in Spaw Pool. Referenced an instance within scene NOT the one that's a prefab in assets

Problem with setting things by prefab. Seems Seek in Behavior Designer does NOT successfully act if it's set to find the prefab, needs to be set to the thing in the SCENE

This could be an issue with setting up different levels. Can I automate some type of Find Target In Scene?

[https://opsive.com/support/documentation/behavior-designer/referencing-scene-objects/](https://opsive.com/support/documentation/behavior-designer/referencing-scene-objects/)

Still not fully working and not sure why

Local Space AI issues - look through notes on how to resolve

**December 19**

Realized error in enemy AI is likely that due to player radius game object not existing to the NavMesh - when AI tries to seek it doesnt see that object and cause no movement.

Player Radius purpose is to get enemy to stop at a specific radius - other means of doing that?

Changed Layer of Player Radius to Default instead of Enemy Plane so it is not ignored -

Actually not sure if this made a difference lol. Changing to directsly the playing radius object in scene for testing and no result there either

Many issues here. Dont think they’re actually find their targets, just defaulting to the coordinates that can be set (default is 0,0,0)

Targetting of game objects is working, 1 was player, other was player radius, retargetted approrpiately

PROBLEM is that it heads for center of object. Does just approach next to object depending on size at all. When player radius and player in same position and both targetted, both AI will end in same spot tho Player radius MUCH bigger then player

Conditional Aborts - [https://www.youtube.com/watch?v=GFsK5x6ZW7k](https://www.youtube.com/watch?v=GFsK5x6ZW7k)

**December 20**

Followed advice in this video

Conditional Aborts - [https://www.youtube.com/watch?v=GFsK5x6ZW7k](https://www.youtube.com/watch?v=GFsK5x6ZW7k)

Solved problem of distance and a good idea on game logic

Done on ship04 COPY enemy. More testing and experimenting to be done!

**December 21**

Behaviour Designer Testing and learning

Can inherit a reference from another Behavior Tree - may be useful for multiple enemies to inherit target

Found in RTS Sample Project

**December 27**

Trying wandering AI - previously not working

Working now but pretty dumb

May just need points to wander between? May be a way to fix Wander function

Changing Field of view due to 4 quadrant turning not capturing complete persepctive

Previously 40

Now 55

CHRONOS: Do enemies move backward in time or no??

Wander gets stuck while moving forward - cannot calculate position properly? Stuck on walls / corners. Seems the point chosen is not contexual to a moving navmesh - work arounds to this?

Might need reference points to navigate between - patrol but move out the way of incoming objects / scenery??

How to stay center on moving object unity scene view????

**December 29**

Building enemies in a scene

Working on getting 1 enemy functioning properly on battlefield

Need to evaluate Wander ability / try out way points instead for when moving player

Trying out waypoints now

NOTE: Need a way for BD to auto pick up all waypoints in scene for Patrol function, by tag / layer / name / whatever

Works but bullets inheirit rotation - meaning, when enemy moves around bullets move with them as well - mainly the rotating part is bad

Can stop rotating but bullets all move in wrong direction and sometimes towards the sky?

WEIRD

Also might want to face Player at all times? Or maybe some enemy types would be good for that

**December 30**

Weapon Demo Scene from Obj Particle Spawner may be helpful for bullet issues

Problem appears to be something in Enemy Basic Script - is disabled nothing even happens

Change in direction of bullet appears to be bullet released from system.

Disable Shape in particle effects to shoot straight ahead

Homing features dont on/off dont seem to effect bullet trajectory issues

Tried using Standard Bullet - Boss Test 1 and still happening! Still trajectory issues! Something odd happening

When assigned a different pool, enemyshootingbullets started exhibiting same issues with trajectory - always shooting to one thing. this appears to be the cause!

Orientation always seems to be an issue. Need the local space of the bullet not to be enemy as parent. However this causes shooting direction issues.

**December 31**

Looking at best way to implement bullet movement - current translate implementation seems to be problem with current trajectory issues

Fixed Trajectory issues! Much simpler implenetation in Enemy Basics class. Seems some of the Chronos stuff is not necessary for me?

Need to make enemy basics into a bullet script

Then make a seperate script for enemies health, etc- different pooling system needed etc.

Working on BD finding all objects tagged enemy waypoints and adding them to global variable list, which then it use for patrol

May help - [https://www.opsive.com/forum/index.php?threads/patrol-waypoints-disappear-after-changing-scene.4727/](https://www.opsive.com/forum/index.php?threads/patrol-waypoints-disappear-after-changing-scene.4727/)

## 2022

**January 2**

Spawner + Dyna mic Waypoint testing

Current working scene is “Dynamic Waypoint Testing and Spawning”

Need to create a proper Enemy Class and use old one for just bullet now

Bullets can be everywhere - so how does aiming and combat work?

Need to think this through a bit. Should I be able to lock on to enemies?

Prototype this idea out - locked bullets fire at current locked enemy

What are the possible scenarios for optimal shooting at enemies?

*   gather up bullets on aiming device, fire back in a line
*   lock on to target enemy, all bullets aim at them

**January 3**

Exploring case 2

if raycast seeing tag is enemy

if locked bullets > 0

lock on to target - only lock on to one at a time

this attaches game object of LOCKED ON like bullets do

set bullets to target that enemy

Running into many errors, need to put this aspect aside to properly separate Enemy and Bullet classes

Doing a backup first!

Renaming EnemyBasics to Projectile

Enemy Basic Setup is new Enemy class

working on enemy lock on now

see if enemy class locked becomes true - if bullets can target it

using homing and playerTarget in Projectile to get Projectile to target the enemies

may need to seperate this - same homing for homing bullets hitting player may not be the best approach for bullet aimed at an enemy through LaunchAtEnemy

May need to implement a different Minimap / Radar option. Not updating colors and some functions at run time!

Running into koreographer issues - cross hair issues

Crosshair.OnMusicalShoot (SonicBloom.Koreo.KoreographyEvent evt) (at Assets/Scripts/Crosshair.cs:216)

Choke point of project?

Looks like homing is off possibly?

Projectiles are hitting enemy and health is decreasing now - fixed by turning off homing closer to enemy

Homing does seem somewhat off... not sure why

IDEA: Mixing between running animation and flying segments?

Issues with waypoint verticality might be more to do with external behaviours then anything else? Tag name in BD - white vs greyed out difference??

Dyanmic waypoint testing and spawning 1 WORKS but not 2!

Figure out if any meaningful difference and move on from here

**January 4**

Made a scene based on 1, not 2, could not fix 2. now operating in 3

Disabled Player Radius on the Player model - believe it is unecessary

Playing with camera angle and perspective

How do I get the best angle to see what’s happening while also maintaing a close to the action, thrilling feel??

LOCK ON BUG

Locks list shows 1 but Targets shows no objects

*   how does this issue occur? Put in a check for discrepancy?

Bullets getting stuck on grid as well - why is this happening? They die eventually due to lifetime, but unsure why this is happening. Will need addressing

Tactical AI pack for my enemies!

*   Shoot and Scoot
*   Flank
*   Ambush
*   Surround

Bringing in A* Versions to scene to test

Trying to intergrate Behavior Designer A* Tactical Assets with A* Star Moving example and it’s not working. Getting Local Space Rich AI Graph error. Have had this error before, means it doesnt see the graph.

NullReferenceException: Object reference not set to an instance of an object
Pathfinding.Examples.LocalSpaceRichAI.RefreshTransform () (at Assets/AstarPathfindingProject/ExampleScenes/Example13_Moving/LocalSpaceRichAI.cs:39)
Pathfinding.Examples.LocalSpaceRichAI.Update () (at Assets/AstarPathfindingProject/ExampleScenes/Example13_Moving/LocalSpaceRichAI.cs:57)

Anything in the Behavior Designer A* Movement Pack works fine. Remove AI Path (2D,3D) and replaced with Local Space Rich AI. Have RVO Controller that recognizes Local Space Rich AI

Full error

NullReferenceException: Object reference not set to an instance of an object
Pathfinding.Examples.LocalSpaceRichAI.RefreshTransform () (at Assets/AstarPathfindingProject/ExampleScenes/Example13_Moving/LocalSpaceRichAI.cs:39)
Pathfinding.Examples.LocalSpaceRichAI.CalculatePathRequestEndpoints (UnityEngine.Vector3& start, UnityEngine.Vector3& end) (at Assets/AstarPathfindingProject/ExampleScenes/Example13_Moving/LocalSpaceRichAI.cs:50)
Pathfinding.AIBase.SearchPath () (at Assets/AstarPathfindingProject/Core/AI/AIBase.cs:480)
Pathfinding.RichAI.SearchPath () (at Assets/AstarPathfindingProject/Core/AI/RichAI.cs:252)
BehaviorDesigner.Runtime.Tactical.AstarPathfindingProject.Tasks.IAstarAITacticalGroup+NavMeshTacticalAgent.SetDestination (UnityEngine.Vector3 destination) (at Assets/Behavior Designer Tactical/Integrations/Astar Pathfinding Project/Tasks/IAstarAITacticalGroup.cs:57)
BehaviorDesigner.Runtime.Tactical.AstarPathfindingProject.Tasks.ShootAndScoot.OnUpdate () (at Assets/Behavior Designer Tactical/Integrations/Astar Pathfinding Project/Tasks/ShootAndScoot.cs:129)
BehaviorDesigner.Runtime.BehaviorManager.RunTask (BehaviorDesigner.Runtime.BehaviorManager+BehaviorTree behaviorTree, System.Int32 taskIndex, System.Int32 stackIndex, BehaviorDesigner.Runtime.Tasks.TaskStatus previousStatus) (at <58623c9461324266a5b7839460bb9d26>:0)
BehaviorDesigner.Runtime.BehaviorManager.Tick (BehaviorDesigner.Runtime.BehaviorManager+BehaviorTree behaviorTree) (at <58623c9461324266a5b7839460bb9d26>:0)
BehaviorDesigner.Runtime.BehaviorManager.Tick () (at <58623c9461324266a5b7839460bb9d26>:0)
BehaviorDesigner.Runtime.BehaviorManager.Update () (at <58623c9461324266a5b7839460bb9d26>:0)

Trying a scene combining the moving example and the BD Tactical but no luck.

Add Ultimate Character Controller to try out Deathmatch AI - need to look into implementation as it shoots errors now

IDEA: A a dodge button for attacks? general dodge option within a certain closeness, or just attach dodging to the guiding of the reticle?

**January 12**

Think previous bullet error stem from crashing into each other. What to do in this case?

Program for this scenario

**January 13**

Enabled this feature

[https://www.youtube.com/watch?v=P7cYVg5fAvY](https://www.youtube.com/watch?v=P7cYVg5fAvY)

Change this playmode options if weird bug occurs! May need to troubleshoot

Static variables may not be reset when reload domain is OFF

Reload Scene needs to be on for proper use of Scriptiable Objects - like A* !!!!

If all enemies defeated, need to clear out target

Need to develop targetting system more!!! Need a proper bug and dev categories / log to keep track of these things and mark them as DONE

These videos teach me that I am still in the prototyping phase IMO - focus on this

Contact Robbie - Level Curve? - about FMOD and other things- talk to Henry about this!

Judgement Silversword - SHIELD - should I do this? Or a dodge?

**Jan 18**

Things to figure out

*   Bullets not moving properly?
*   Enemies not going to way points?

Issue when area is moving, static seems fine! Double check this though

Reparent to EP1 once awake? WORKED WOO

Tried slow down on lock on - not sure how I feel about this! It’s pretty intense effect

IDEA: Use Mirror / Portal like effect for a BOX level / section?

[https://www.youtube.com/watch?v=3AIsleRlx5Q](https://www.youtube.com/watch?v=3AIsleRlx5Q)

**Jan 19**

If make power lines, need to be concious of sag of wire. will sag in wrong direction if on side

Utility pole / Power Line prefab made, need to work on a better scene for it

Learn more about Mesh Combine and Mesh Simplify - settings shoudl help you reduce objects

Figure out what is taking so long to get to play mode

**Jan 20**

**Installed and using Mesh Baker instead of Mesh Combine**

*   Insight note - Baking the whole mesh may be harder on the system. Baking quadrants looks like it might be better, because then some of them will be culled. Sound like culling will not occur if all baked into one mesh

This is true! One big mesh is BAD

Texture Baker Video

[https://www.youtube.com/watch?v=XSdwuLvChRg](https://www.youtube.com/watch?v=XSdwuLvChRg)

Mesh Baker Grouper Video

[https://www.youtube.com/watch?v=y5kmWixP0tg&](https://www.youtube.com/watch?v=y5kmWixP0tg&)

Trying out Ultimate LOD System while downloading Automatic LOD

Will see if this helps / will do or more popular tool is better

**Jan 21**

Trying many different optimization tools

AutoLOD for all the buildings

Prefab Brush+ is working!!!! Paint Prefabs on a plane

Using GPU Instancer with Prefabs seems to help performance - not as much as I thought!

Moving Navmesh doesnt seem to work anymore UGH

Consider alternatives for this - could i do smaller sections and flip between scenes? Or is that even necesary?

**Jan 22**

Maybe should use general A* Pathfinding

Research adjust material color at runtime. DOTWeen can probably lerp this?

[https://oxmond.com/changing-color-of-gameobject-over-a-period-of-time/](https://oxmond.com/changing-color-of-gameobject-over-a-period-of-time/)

Tried Ethereal URP again for FOG and such, no great luck it seems! Maybe try again later

**Jan 24**

Graph problems are fixed! Aron has found the problem

Need to fix spawn point location issues - not properly updating?

ALso fix my little guy disappearing. bring him back!

**Jan 25**

Spawn Points Bug - Why is it not moving properly? Position not updating?

Implement IDamagable interface into Enemies and Player for Behavior Designer

Implemented these things in Player and enemy. Broke the Enemy Rhythm shooting - more investigation needed to fix this

Cannot seem to get interesting Behavior Designer patterns working for enemy AI

Attempting import of Deathmatch AI kits to rectify this.

Bring over that enemy AI into my scene, see how it behaves?

Figured out import errors but need to make a Deathmatch AI Test scene to see if anything from this is feasible

Chart out how Deathmatch AI works

**Jan 26**

Successful AI scenario with BD

Shoot and Scoot - working!

Flank - working! No leader on main enemy, others must follow that one

One good enemy type - scoot and shoot - can i get spawner properply spawning it?

Not able to shoot when spawned - unsure why this is happening!

When fired the original enemy show itself as parent spawner

This is not happening on clones

Pool is also not being generated, while that is not an issue for enemy already in scene

Enemy IN SCENE will generate pool once made active - but does not shoot!

Needs to be active at scene start - why? How did I get around this?

In Old scene, Particle System and OBS was on child game object- trying this

FIXED IT - Needs to have particle system / Object Particle Spawner on a child object

Enemy Test 3-2 works with Shoot and Scoot in the Spawner now

Enemies are getting stuck though when trying to move around though

*   adjusting settings on them to affect this

**Believe it may be recalculating graph issues - when not moving it never gets stuck**

*   read up on graph properties

Fluxy for fluid visual effects!!!!

Magio visual effects?

Go through all recent examples

**Jan 27th**

Key Problems Today

*   A* graph while moving (noted yesterday)
*   Can’t lock on to enemies anymore (no blue outline or what?)
*   Bullets getting stuck - maybe have them destruct when collide with each other?
*   Add Koreo to Shooting Mechanism

Bullet fix - seems to work! Will go with this for now - need to do more to account for all bullet states though

Trying this solution as well

> Make a new layer called "Bullets"
>
> Go to Edit -> Project Settings -> Physics and under the Layer Collision Matrix, untick the box that has the row and column for Bullets (Should be on the anti-diagonal)
>
> Now your bullets will pass through each others

Seem to only get stuck when fired at enemies now

Will also get stuck around player character

Ex. Two bullets, both stuck - only enabled states are Homing and Homing Enabled on Start

turns out this next bit is related!

Enemy Lock On - Did not have Enemy Basic Setup class on Enemy Test 3-2

*   Need to inheriet IDamagable for BD use? Look into this

Stuck bullets look like this

None appear to have hit the enemy

Had any issue with bullets colliding with the enemy shooting them

Projectiles set to ignore any gameobject tagged Enemy for Triggers

Currently using Trigger instead of OnCollision but probably doesn’t matter

Enemies weren’t taking damage but was just a dumb oversight - good reason to clean up the code!!!

Lock on bullets to hit enemies are ALWAYS getting stuck

**Enemies are now taking damage but this error not fixed - investigate what is happening here**

Also happens to the Player - bullets get stuck around here

Checking if the bullets that get stuck had previously been recycled or are fresh

Look like it’s been recycled - not all properties refreshed on recycle?

Disabled homingEnabledOnStart - looks like a source of some error

**CAN get stuck with recycle count = 0**

Something else is the issue here

Bullet stuck next to play - homing is only thing enabled

disabling homing variable on death - need to look at legitimate homing methods for different bullet type

SOme stuck bullets in enviroment - maybe prev stuck around enemy?

**KEY SCENARIO? -** else if (launching == true && locked == true)

Is the projectile circling the player very quickly? constantly looking at it and then rehoming?

I increased the distance needed for the projectile to disable homing - now many are missing the intended target

Local rotation and position is just CONSTANTLY flipping back and forth on this, so i think this diagnosis of the issue is on the right track?

This is happening here as well, with all 4 parameter enabled on these stuck bullets. One recycle count ZERO and hasHitPlayer is NOT checked.

Another stuck one

If both of these are true- what should be done? This is turning up more now - bullets i’ve locked onto to shoot as well

Reassessing structure while referencing [https://www.youtube.com/watch?v=Z6qBeuN-H1M&t=12s](https://www.youtube.com/watch?v=Z6qBeuN-H1M&t=12s)

Moving bullets to rigid bodies for movement and trying something new with slower speeds

IMPORTANT: Need projectile to check that target still exists or it will just get stuck when it’s already destroyed. Quick fix is now in for this

Audio Cue for every time particle emits

[https://www.youtube.com/watch?v=jKSz8JJnL4E](https://www.youtube.com/watch?v=jKSz8JJnL4E)

Look at ObjectParticleSpawner to see if that might be more performant with sound effects

Maybe just have all bullets play sound on awake?

DISABLED TargetCube to reduce visual clutter - need to come back to this idea of using target cube

Important difference between using BehaviorDesigner.Runtime.Tactical and declaring within namespace BehaviorDesigner.Runtime.Tactical

Learn more about this!

~ Spawner Issues - spawner location changing ~ do not seems to be happening anymore! Unsure why ~

Trying bullet as drone - interesting!

Koreogrpaher stack or Shooting stack seems to break things - look into this deeper

Reference Area X for visual clarity when lots of far away bullets / items are on screen moving around

Look through twitter sends and look through recent assets

**Jan. 28**

Early thoughts - Outline on main character?

Errors in DOTWEEN Support so it’s been removed

Fluxy package - looked at a little bit - may be neat - look at in future

Need to fix this

Added projectiletarget=nulll if statement

MAKE SURE this doesnt mess up other aspects of projectile states - NOT SURE IF IT DOES

Seems to be Koreographer related issue. Is it a timing thing?

Investigate this, adapt Wave Spawner waves and inventory other issues

**Jan 29**

Bullets caught in circling loops when missing enemy target

Add bullet particle effect with kick track?

