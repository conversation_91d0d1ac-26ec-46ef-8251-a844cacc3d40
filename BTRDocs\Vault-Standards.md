

### Core Systems Taxonomy

#### Game Entities
- #game/entity/player
- #game/entity/enemy
- #game/entity/projectile

#### Audio Events
- #audio/event/explosion
- #audio/event/impact
- #audio/event/alert

#### Visual Systems
- #visual/vfx/particle
- #visual/vfx/screen-shake
- #visual/ui/hud

#### Bidirectional Relationships
- #link/game-audio {{Enemy destroyed → Explosion sound}}
- #link/game-visual {{Projectile impact → Screen shake}}
- #link/audio-visual {{Music intensity → VFX intensity}}

# Obsidian Vault Structure 2.0

## Core Directories (Preserved)
```text
├── Project Management/
│   └── [YYYY]/          # Year-based organization
├── Tech Recommendations/
│   ├── Coding Tips/     
│   └── Optimization/    
├── AssetLibrary/        # Existing asset structure
└── Design/
    ├── Systems/         # Combined architecture/implementation
    └── References/      # External research/inspiration
```

## Enhanced Conventions
1. **File Types**:
   - Preserve existing naming where practical
   - Add standardized frontmatter to existing docs
   - `[system]-guide.md` (e.g., `enemy-system-guide.md`)
   - `[year]-[category].md` (e.g., `2024-design.md`)
   - Daily journals: `YYYY-MM-DD.md` with [[Daily Progress Journal Template]]


2. **Tagging System**:

3. **Frontmatter Template**:
```yaml
---
systems: [enemy, audio]
components: [spawning, pathfinding]
priority: p0
last_touched: 2025-04-09
links:
  - [[2024-design-mechanics]]
  - [[tech-recs-pathfinding]]
---
```

## Linking Conventions
```obsidian
<!-- Precise block references -->
![[core_architecture.md#^event-system]]

<!-- Aliased links for clarity -->
[[2022 Design & Mechanics.md#Quarter3|Q3 Progress Report]]

<!-- Transcluded content -->
![[Tech Recommendations/DOTS Notes.md#optimization-tips]]

<!-- Relationship arrows -->
[[Enemy AI System]] → [[Projectile System]]
[[Audio Manager]] → [[VFX Controller]]
```

## Implementation Plan
1. Migrate DevelopmentStates/ content to relevant year folders
2. Convert architecture docs to system-overview format
3. Add priority tags to existing Project Management files