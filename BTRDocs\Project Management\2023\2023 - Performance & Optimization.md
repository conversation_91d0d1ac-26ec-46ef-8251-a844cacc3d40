

**February 14 59e0f8fb56844d94bc6fc295e968d7fd.md**

*   Compilation of shader variants → An approach to solving this issue! here is a fix to this. Go to Edit->Project Settings->Graphics. Go to the very bottom. It will state the number of shaders and shader variants. Make sure that seems about right to you. There is a button called 'Save to asset...'. Use that to save a compiled shader variants file. In the graphics tab you're still in, drag that saved file from your Project window into the 'Preloaded Shaders' list. That will decrease the built time dramatically.
*   Washed out colors in Unity builds, but not in editor play view. Attempting to fix this
    *   Changing from Gamma to Linear color space - may fix this
    *   Anti-Aliasing part of the issue? Trying FXAA and Linear color space
    *   Disabled Optimize mesh data to speed up build times
*   [UNITY SHADER VARIANTS - TIPS to Speed Up Your Build Time!](https://www.youtube.com/watch?v=3i2V8Q7SsOM)
*   Height fog global is possibly causing the coloring issues
*   Positive game loop - dont punish for being off beat, but reward for being on beat 
*   DEBUG of OPS error

```jsx
    AssertionException: Assertion failure. Value was False
    Expected: True
    UnityEngine.Assertions.Assert.Fail (System.String message, System.String userMessage) (at <1332c534a8a44623b2e5cc943a0b790e>:0)
    UnityEngine.Assertions.Assert.IsTrue (System.Boolean condition, System.String message) (at <1332c534a8a44623b2e5cc943a0b790e>:0)
    UnityEngine.Assertions.Assert.IsTrue (System.Boolean condition) (at <1332c534a8a44623b2e5cc943a0b790e>:0)
    HohStudios.Tools.ObjectParticleSpawner.ObjectParticleSpawner.LateUpdate () (at Assets/HoH Studios/Packages/Tools/Runtime/Object Particle Spawner/Spawner/ObjectParticleSpawner.cs:303)
```

*   OPS line 303

```jsx
    // Error catching assertions.. not included in build
                if (_numberOfAliveParticles < _aliveParticles.Length)
                    UnityEngine.Assertions.Assert.IsTrue(_aliveParticles[_numberOfAliveParticles].remainingLifetime < 0.001f);
                if (_numberOfAliveObjects < _aliveObjects.Length)
                    UnityEngine.Assertions.Assert.IsTrue(_aliveObjects[_numberOfAliveObjects] == null || !_aliveObjects[_numberOfAliveObjects].IsInitialized);
            }
```

*   OPS line 158

```jsx
    // Error catching assertions.. not included in build
                if (_numberOfAliveObjects < _aliveObjects.Length)
                    UnityEngine.Assertions.Assert.IsTrue(_aliveObjects[_numberOfAliveObjects] == null || !_aliveObjects[_numberOfAliveObjects].IsInitialized);
```

*   Assertion Failure! This are showing up false
*   Issues with the bullet pool
*   NOTE - Seems to happen when ENEMIES are what needs to be reused, not bullets ???
*   Verify this finding. Still issues with bullet pool i think but…. needs more testing
*   Appears to be true! Made it to Wave 7 until error occured
*   As soon as 1st two enemies are reused the Assertion failure occurs. Pooling is not working correctly for enemies.
*   Doesn’t seem to be a big deal for bullets - Though may be worth cleaning out inactive game objects? Also how might I combine all enemies pools to work together? Doesn’t seem to do functioning
*   Moving from mono ot il2cpp back end made significant differnece in frame rate!!! stick with this
*   IL2CPP takes a long time to build but works twice as fast in the build! Even more! Some issues though
*   Using Asset Hunter to slim down project and improve build times

**February 19th bfdc80c324114c19b31dc1378c46a9b2.md**

*   How to remove unnecessary assets? Export approach caused many issues
*   Delete most scenes, than use something that finds uneeded assets

**March 1 2023 c59550993dfc45ddae0d4c958ca523da.md**

*   - Map not constrained properly - bullets flying off
        - Mostly Fixed - scaling issue with screen aspect ratio now, i think. check black square / map size as well

**March 10 27982ba4105b47b88a39e9bce3744558.md**

*   Mario day
*   Looking into GPU bound of performance - optimization!
*   Unity Crashing “Stack trace could not be displayed. See the latest crash report generated in Editor Log by clicking the "Go to crash logs" button”
*   Optimization - Additional Camera existed that was doing nothing! Taking up resources 😟
*   Important to check everything
*   Currently working very well! 60 FPS on steam deck with current settings. BUTO takes a good chunk of rendering resources but not an issues at this point.
*   Find a way to turn dev console on/off
*   SHould I set Alpha Clipping?
*   Do I need different Materials for different colors of same shader? Just use GPU Instancing?

**March 14 af9011757db14bf98b892cce1d86e2ff.md**

*   Cant seem to figure out rotation issues with Shooting/Reticle.
*   Adding video gameplay recorder to builds for bug tracking?
*   Implemented this - works oK!

**March 15 39007b90903d486f9f10aaa397cd7d05.md**

*   Object Particle Spawner delays occuring - pooling issues?

**March 21 4151ffa3a0754b838d270fbcd26a2fe3.md**

*   Upgraded out of LTS to Unity 2022.2.11
*   Some errors! Trying to resolve these
*   Removed A* and GPU Instancer due to errors. Trying to fix this. A* should get a fix soon, Gpu Instancer, not sure exactly the problem.
*   Disabled Burst Compilation and Optimizations

**March 22 e158faf2a6ae4e25aa16e1c8ea82ca2c.md**

*   Need to offload this on another drive and try it? Look into this
*   Alpha clipping on projectiles - will this solve my issues with them looking better / fading out when they get farther away? Use a better outline shader - 2022 options?
*   Check if HDR on and off makes a big differnece to steam deck performance / pc performance.

**March 23 b6631bb283e0406ebd76b2e857bde7be.md**

*   Buto not working 100% so adjusted to work in a manageable state
*   Unity crashing, but changed from D12 to D11 and seems fine now
*   A* Graph issues, pulled in graph from previous scene and now it works
*   Experimenting with Settings UI - needs camera for cursor selection to work. BUT post processing gets in the way. Dont want to make a new camera if I don’t have to? Maybe not so bad if setting camera active / disabled

**March 25 588192b57727461cb30b4351b2b2ab22.md**

*   Dynamic Mesh Cutting
*   Could be an interesting addition - allowing the connection between several bullets to slice objects around the area
*   Tried a new outline shader - full screen effect in Unity 2022, pretty cool!

**May 1 c961aec9c03746a78be78173fa67c457.md**

*   Navmesh scanning work again with beta 61 of A star pathfinding.
*   Error found! Make sure nothing is at Scale 0, parents or childs, when something ownt move around. Scale 0 can cause this issue !

**May 10 d86fe672aad94d98994f6b30d6e5a1e8.md**

*   Enemy AI and related navmesh calculations
*   Some details on using connections
*   Using relative position may yield better results - inconclusive right now
*   Enemies sticking to mesh, need to fix projectile pooling

**May 11 aa66d9071c9946cf9c33d060c0ed3e0a.md**

*   Integrating Dreamtek Forever - Collider Run with recent game components for test of travelling level
*   Looking into some optimization methods for A* and enemies falling off

**May 16 3cb913db9061453f97a1d249ccfd9b61.md**

*   Enemies appear to be working on mesh! Kinematic was enabled and this was the problem.
*   Partially fixed thsi pooling, but still seem to build up a lot of inactive projectile game objects.
*   Need to look closely at when it’s released from the system, how it’s recycled
*   Can gain performance here i think, or at least memory?
*   [Optimization Tips](May%2016%203cb913db9061453f97a1d249ccfd9b61/Optimization%20Tips%2095ef9e3b01c1490d869a2aa3567924b8.md)

**May 17 451c7269371e4c369c230a21303c34dd.md**

*   Is there anything in Projectile Toolkit I could use?

**May 18 c7f4ad55ba864f52b102679ab1bf16f1.md**

*   Wondering if this will flag editor scripts?
*   Build works!
*   Tried to solve all problems to get a working build

**May 19 c9ee55fc1b6246ff81cc032e9f774040.md**

*   Time rewind seems to screw up enemies know their position (getnearest planting them firmly on mesh)

**May 20 2001d2490a2e41bcbacb8ed2ac572edf.md**

*   ProjectileStateBased is partially working
*   Can lock on and they do initial PlayerShotState behaviour, but not changing target / shooting at enemies

**May 22 f1e6a7e6c4024735a87ab77a2ce26e3c.md**

*   Rewinding time messes with Enemy Dodeca script that places movement directly on the navmesh (stop from flying off)
*   Physics.Boxcast taking a lot of resources - is this just a health collecatable mode issue or issue in geenral?
*   Groundcheck fixed up a bit. Seems better
*   Recent bug report, this seems to be the solution
*   Recent bug report, this seems to be the solution
*   Projectiles are not set to kinematic, so unsure why this is being flagged
*   Lighting setup and debugging
*   Fix Death / FX of Enemies - Parts are busted

**May 23 8b930eadf610457da08ccb7dea8897f8.md**

*   Funnel modifier on enemies - may have perofrmance impact - keep in mind!
*   Also setting a hard 60 fps limit in game. This is on manager - DemoGameInit
*   Disabling ground check on Shooter Reticle movement because it’s more problems caused than solved
*   Fix Death / FX of Enemies - Parts are busted
*   Ground Check

**May 26 67c42136b420403eab563ad6d9584817.md**

*   Project particle system

**May 29 22d654f8ad8f4947b476a35b631d7990.md**

*   Making Crosshair script more efficient, can see some issues in the profiler
*   Also looking into job leak issue, full stack trace leaking enabled
*   Updating to latest A* Pathfinding for potential perforamnce improvements -
*   High setting of Funnel Modifier requires recalculating normals on navmesh. Good? WOrth it?
*   Fixed falling off navmesh issues again - seems fixed now. Remember - all was rescan and save graphs when updating A* Pathfinding. WIll have issue otherwise
*   Improved Ground Check script, appears to working much better now.

**May 30 8c27e9786ce54d88ba686147033d5de5.md**

*   May need to make look a bit better

**May 31**

*   Setting dissolve material, shaders and and more to make projectiles look nicer

**May 3 dbf950e284274b2594e0fec4db2d77e5.md**

*   Buto 64 not working - wait for Buto 65 for this to be fixed in several days

**May 4 405a5d8bbf5f4b6fb7f1954d4e8def21.md**

*   Updated Buto to fix any issues

**May 5 26faf16e807441e1bd851d6cf30975da.md**

*   Ground check

**May 8 ca93e96214ca4270878c429b9b88a944.md**

*   StickToNavmesh script on enemies
*   Sort of working? They seem to stick but behavior can be odd, stuck in places, model appears to be flashing at times. Monitoring….
*   Disable StickToNavmesh script to see if it was the issue - didnt appear to be!
*   "Constrain inside graph" problem

**May 9 e0e5fbd0b07c4a62865ec3bf2ed18888.md**

*   Too intensive!
*   Culling groups?

**July 18 fd2ba3e27c6344389aceda37c614f51a.md**

*   FSR plugin cause camera to flicker with upside down image occarionally

**July 24 3edf6d65e48c47369df28ed1303c1f81.md**

*   "Most likely your skinned positions are going outside of the calculated skinned mesh renderer's bounds and therefore getting frustum culled unexpectedly.
*   You can also turn on "Update When Offscreen" on your skinned mesh renderers. It's inefficient, but simple fix. Also, if it fixes your problem, then it confirms that your bounds is the issue.

**July 25 c152e6832ef94d83acffcf50db17305e.md**

*   Changed game to IL2CPP builds. Seems better? faster builds at least.

**Sept 1 39b128da45964517a5d904608723aad7.md**

*   Forward+ Renderer appears to have memory leak issues, may need to follow up on new version of Unity LTS that addresses this problem.
*   Switched to Direct X 11 and the memory leaks went away - likely a Unity issue

**Sept 6 edf532ce46554c1ca600871fcf015464.md**

*   Trees and vines on scene, are trees / vines and such nav mesh too complicated - simplify? Or try to fully scan the vines?

**Sept 18 fe324058ac7c4ab2a41df85199ca7c31.md**

*   Transparency issues on aim cube

**Sept 20 3e7877758a0d498ba6a9bd5ab352315d.md**

*   Looking into hang line - what should a good line entail
*   Lighting

**Sept 27 9a0efa1527024d4c8b2fc339ef45e388.md**

*   Baking lighting
*   Lightmap error

**Oct 2nd 768ff9312e5b45b3b889a2d809028768.md**

*   If this doesn’t work - could disable the movement maybe when on snake? Re-enable for other structures? Not worth spending too much time on the movement maybe
*   Using magic light Probes and what it brings for performance

**Oct 5th ed261ebf0c784763aa33d274710f6747.md**

*   Upgraded Buto
*   Want to improve outlines / detection of what is in the bullets
*   Looking at adding this with a few things
*   Testing this

**Oct 18 fe6d1ebba8144b2d9e47784415759f30.md**

*   Trying and building out scene

**Nov 3 3771dc7bc53b4ede9bf2b86b23be0c37.md**

*   worked on how to create and use - and how to create in blender
*   looked up normal maps
*   looked up all maps
*   Found lighting theory for a video games video
*   Worked on datamosh
*   tried various things

**Nov 14th 1b91919493934c70a763450d7c436227.md**


