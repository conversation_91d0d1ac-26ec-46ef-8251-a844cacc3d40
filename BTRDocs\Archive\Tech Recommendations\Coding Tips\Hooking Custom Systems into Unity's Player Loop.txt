Unity's Player Loop is a powerful but often overlooked feature that allows developers to inject custom systems directly into the game engine's update cycle. This technique, while not widely documented, opens up possibilities for creating more efficient and flexible game systems.
Understanding the Player Loop
The Unity Player Loop is a graph-like structure that represents the order of execution for various engine systems. It consists of nodes called Player Loop Systems, each potentially containing subsystems. The main public methods for interacting with the Player Loop are:
GetDefaultPlayerLoop(): Returns the default order of engine systems
GetCurrentPlayerLoop(): Retrieves the current state of the Player Loop
SetPlayerLoop(): Sets a modified Player Loop as the active one
Creating a Custom System
To demonstrate how to hook into the Player Loop, we'll create an improved timer system. This system will update every frame without relying on MonoBehaviour, making it more efficient and flexible.
Timer Manager
The core of our system is the TimerManager class:
csharp
public static class TimerManager
{
    private static List<Timer> timers = new List<Timer>();
    
    public static void RegisterTimer(Timer timer) { /* ... */ }
    public static void DeregisterTimer(Timer timer) { /* ... */ }
    
    public static void UpdateTimers()
    {
        foreach (var timer in timers)
        {
            timer.Tick();
        }
    }
    
    public static void Clear() { /* ... */ }
}
Inserting the System
To insert our TimerManager into the Player Loop:
Create a PlayerLoopSystem struct representing our system
Use recursion to find the desired insertion point (e.g., Update)
Insert the system at the specified index
Set the modified Player Loop
csharp
PlayerLoopSystem timerSystem = new PlayerLoopSystem
{
    type = typeof(TimerManager),
    updateDelegate = TimerManager.UpdateTimers
};

PlayerLoopSystem playerLoop = PlayerLoop.GetCurrentPlayerLoop();
InsertSystem<PlayerLoops.Update>(ref playerLoop, timerSystem, 0);
PlayerLoop.SetPlayerLoop(playerLoop);
Gotchas and Best Practices
Domain Reloading: Be cautious when domain reloading is disabled, as it can lead to duplicate systems.
Cleanup: Implement a cleanup method to remove your system when exiting play mode.
Static Clearing: Unity doesn't guarantee clearing of statics, so manually clear any static data.
Implementing a Timer
With the TimerManager in place, we can create a Timer class:
csharp
public abstract class Timer : IDisposable
{
    protected float currentTime;
    protected float initialTime;
    public bool IsRunning { get; private set; }
    
    public virtual void Start() { /* ... */ }
    public virtual void Stop() { /* ... */ }
    public abstract void Tick();
    
    // Implement IDisposable pattern
}
Applications and Future Possibilities
This technique of hooking into the Player Loop can be extended to various systems:
Data binding
Input rebinding
Custom update cycles for specific game systems
By understanding and utilizing the Player Loop, developers can create more efficient, decoupled, and maintainable game systems in Unity.