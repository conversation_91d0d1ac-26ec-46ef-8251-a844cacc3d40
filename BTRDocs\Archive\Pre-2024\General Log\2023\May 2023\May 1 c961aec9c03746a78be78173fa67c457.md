# May 1

Navmesh scanning work again with beta 61 of A star pathfinding. 

Attemping to get enemy movement working on the Ouroboros Level

Error found! Make sure nothing is at Scale 0, parents or childs, when something ownt move around. Scale 0 can cause this issue !

Movement seems rough / restricted on the ourobors

Need to try smoother surface and see if that works better for navmesh

May stick with that for visuals as well? A/B it and see

Made a Navmesh Testing scene to nail movement on difficult objects. Needs further testing - enemies are floating away from the mesh

Start with Basic example scene - alter to get appropriate behaviour