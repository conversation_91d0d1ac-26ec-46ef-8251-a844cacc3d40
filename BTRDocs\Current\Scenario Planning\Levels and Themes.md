# Structure Attempt 1

### **Level: <PERSON><PERSON><PERSON>'s Rings - Earth Element**

- **Description:**
    - In this level, players traverse through rings maintained by a celestial being, <PERSON><PERSON><PERSON>. They must dodge and shoot to the rhythm of the music while making their way towards the center of <PERSON><PERSON><PERSON>, which is envisioned as a cube with various eyes looking inward.
    - **Areas:** Ophanim Rings, Center Cube.
- **Mechanics:**
    - DJ Scratching to rewind/fast-forward player's position.
    - Sound Waves for different ring movements and effects.
    - Pitch Shift to affect the vertical position of the player within the rings.
- **Enemies:**
    - Tetrahedron
    
    ![[Archive/Game Development/Project Beat Traveller GDD/Beat Traveller Treatment 1/Levels and Bosses/Untitled.jpeg]]
    - Metatron enemies that challenge players as they traverse through the rings.

### **Level: Ouroboros' Space-Time Loop - Ether Element**

- **Description:**
    - A Mobius strip-themed level where players dodge attacks from <PERSON>ob<PERSON><PERSON>, a serpent-like creature, while manipulating time to navigate through the level.
    - **Areas:** Reflective bubble leading to a white space universe.
- **Mechanics:**
    - Time reversal to damage bosses or avoid attacks.
    - Ring manipulation to create connections and synchronize time flow.
    - Hourglass feature for certain time-based challenges.
- **Enemies:**
    - Octahedron
    
    ![Untitled](Structure%20Attempt%201%2068f95d87c5d84301b909e6155a4aeaf0/Untitled%201.png)
    
    - Enemies that rewind time and a boss with dual heads, one taking damage in forward time and the other in reverse.

### **Level: Solar Flare Seraphim's Nebula - Fire Element**

- **Description:**
    - A digital-looking nebula guarded by Seraphim, harnessing solar power for defense. Players navigate through tendrils of light, dodging solar flares and targeting Seraphim's weak points through various stages.
    - **Areas:** Tendrils of light.
- **Mechanics:**
    - Time Stretching to slow down solar activity.
    - Sound Waves to interact with the tendrils of light.
    - Warp Modes to distort space and dodge solar flares.
- **Enemies:**
    - Dodecahedron
    
    ![Untitled](Structure%20Attempt%201%2068f95d87c5d84301b909e6155a4aeaf0/Untitled%202.png)
    
    - Metatron enemies covering the tendrils.

### **Level: Void Behemoth - Air Element**

- **Description:**
    - Players venture through a dark, enigmatic void, relying on sound and vibration to navigate, while facing a monstrous, shadowy creature.
    - **Areas:** Variety of black holes, dark void with obscure visibility.
- **Mechanics:**
    - Sound Design to illuminate surroundings or reveal hidden enemies.
    - Time Stretching to slow down speed of attacks from shadowy creatures.
    - Sound Isolation to reveal hidden paths or enemies.
- **Enemies:**
    - Icosahedron
    
    ![Untitled](Structure%20Attempt%201%2068f95d87c5d84301b909e6155a4aeaf0/Untitled%203.png)
    
    - Shadowy creature with massive claws and waves of darkness.

### **Level: World Tree Yggdrasil - Water Element**

- **Description:**
    - Players move along the vines of the World Tree, Yggdrasil, in a level symbolizing fluid dynamics and data flow.
    - **Areas:** Vines of the World Tree.
- **Mechanics:**
    - (Specific mechanics not provided).
- **Enemies:**
    - The Norns, three entities depicted as one, attached with a thread.

### **Level ???: Metatron’s Cube - All Elements?**

- **Description:**
    - Players traverse the structure of Metatron’s Cube, navigating in multiple directions.
    - **Areas:** Structure of Metatron’s Cube.
- **Mechanics:**
    - (Specific mechanics not provided).
- **Enemies:**
    - (Specific enemies not provided).

### **Additional Level: Titan's Colossus**

- **Description:**
    - Upon landing on Saturn's largest moon, players face a colossal machine resembling the Ouroboros symbol, dismantling it piece by piece.
    - **Areas:** (Specific areas not provided).
- **Mechanics:**
    - (Specific mechanics not provided).
- **Enemies:**
    - Colossal machine with multiple heads and limbs.

### **Additional Level: Rebirth of the Tower**

- **Description:**
    - The Composer rebuilds the Tower of Babel, revisiting previous levels now rendered more hostile, facing powerful versions of earlier bosses.
    - **Areas:** Remixes of earlier levels.
- **Mechanics:**
    - (Specific mechanics not provided).
- **Enemies:**
    - Enhanced versions of previous bosses.


# BT Ideas

Words and titles I like

Garden of Delete

Obelisk

Spire

Generation

Angel of 

Renihilation - meant to signify the negation of a negation, producing affirmation and positivity (liturgy)

Abstraction

**Heaven's Ascent**

Pillar of Discord

Unreachable Apex

Harmony’s Demise

Ascent

Tower

Colossus

Lexicon

Concepts to relate

Sisyphean Task

Perpetual

Quixotic

Endless

Monument

Monolith

Similar Stories

Metropolis (1927)

**"The Nine Billion Names of God" by Arthur C. Clarke**

Thinking about art as reaching for something bigger, reaching for god, reaching for legacy, 

always and end goal. but the beauty is in the process and the reaching

Composition is just improvisation in slow motion

- **Setting & Story**: The game is set in a world where the ancient Tower of Babel, once a symbol of human achievement and unity, has collapsed, leading to chaos. The remnants of the tower have become the lairs of powerful celestial beings. The protagonist, potentially an angel or demon, is tasked with reassembling the Tower of Babel to restore balance. However, celestial beings and the fragmented angel Metatron aim to protect the broken tower, believing its restoration would disrupt the cosmic order.
- **Music's Role**: Music is central to the game's theme. It represents variance, structure, and the idea that there's no perfect 'art'—just variations in the ongoing struggle of existence and creation. The game emphasizes the cyclical nature of creation and destruction and how music shapes human experience. By embracing this cycle, players can unlock new musical possibilities.
- **Enemies**: The enemies are breakdowns of Metatron’s cube, which are fragments of God’s geometry. The game delves into the meanings of the Metatron cube, associating it with the balance of energy in the universe, a map of creation, and its relationship to the classical elements.
- **Elements & Interpretations**: The game incorporates the five platonic solids, each associated with an element:
    - Tetrahedron (fire)
    - Hexahedron (earth)
    - Octahedron (air)
    - Dodecahedron (ether or spirit)
    - Icosahedron (water)
    
    These elements have both traditional and modern interpretations. For instance, water is likened to data flows in networks, while ether represents the digital realm itself, encompassing the internet, virtual reality, and abstract digital concepts.
    
- **Protagonist's Identity**: Several ideas are proposed for the protagonist's identity, including being a sentient fragment of the tower, a celestial entity created after the tower's destruction, or a paradoxical character whose existence is tied to the tower's fate.

Relating this to a music game in the style of "Rez Infinite":

1. **Visual & Auditory Experience**: "Rez Infinite" is known for its synesthetic experience, where players traverse through different layers of a digital world, with each action producing musical notes. "Beat Traveller" can incorporate a similar style, where each level or fragment of the Tower of Babel represents a unique musical and visual landscape. As players progress, the music evolves, reflecting the ongoing struggle of existence and creation.
2. **Enemies & Bosses**: Just as "Rez Infinite" has players targeting and neutralizing threats in the digital realm, "Beat Traveller" can have players face off against celestial beings and fragments of Metatron's cube, each producing distinct musical patterns when interacted with.
3. **Narrative Depth**: While "Rez Infinite" focuses on purging a digital network of viruses, "Beat Traveller" offers a deeper narrative involving the Tower of Babel, celestial beings, and the cyclical nature of creation. This narrative can be interwoven with the music, where each level's soundtrack tells a part of the story.
4. **Evolution of the Protagonist**: As players progress in "Beat Traveller," the protagonist can evolve or change forms, similar to the player's avatar in "Rez Infinite." This evolution can be tied to the music, with each transformation introducing new musical elements.
5. **Endless Cycle**: Embracing the cycle of creation and destruction can introduce a mode where players can endlessly traverse different musical landscapes, similar to the "Area X" in "Rez Infinite," offering infinite discovery and growth.

In essence, "Beat Traveller" blends a rich narrative with a synesthetic experience, where music and visuals are deeply interconnected, offering players a unique and immersive journey.

**Ideas on functional significance**

Each enemy should have several forms per level

AI gets more advanced - maybe has more effects on the music and time as things progress?

Basic ideas for evolved enemies

Shoot more

Faster bullets

Variation on bullet behavior?

Locations / structures where they exist are different

Different movement types - can avoid your bullets - would require they keep pace with the player and continually follow so they're not obscured if they can avoid bullets?

Can I draw on tactical pack and formations to create variations? 

Can I draw on object particle spawner to create variations in enemy fire for different enemy types? 

Work backwards - maybe initially enemies are stationary and only shoot within range, so you just take out enough on range enemies and move to next wave. More advanced enemies move around. 

Then it's three types

Stationary - Movement - Groups

Think of variations that exist in Rez

Some are flying through air on a particular rail?

Some are multiple lock on pieces that need to be destroyed to take out entire enemy

- This could be one moving enemy with several stationary enemies on top of it

Think about what is effected by time and what isn't 

Some enemies might shoot bullets unaffected by time glitching/reversal/movement 

Some enemies can't be locked on - there is a central unit that you need to shoot instead and destroying that will destroy the enemies.

Think ouroboros - surround an orb - shoot any bullets at that orb to take out the health of the current orb and when you are successful a kill all enemies event occurs , per wave

**Story Telling Structures**

[Storytelling Tools to Boost Your Indie Game's Narrative and Gameplay](https://www.youtube.com/watch?v=8fXE-E1hjKk)

Flow channel - game should oscilate between easy and hard and not linearly get harder

Similar to story, need to oscilate as things develop

![Untitled](Beat%20Traveller%20Treatment%202%206559459e6de5446d91cc5abc83b19e88/Untitled.png)

Motivations

- think external and internal
    - ext. desire to change something in the world outside
    - int. a desire to change something inside the person
        - overcoming a problem, often emotional
- A story is about change! Need to have change across it

Start your story before the big events begin

- Tutorial mission can be this

Inciting Incident

- set up the challenges now that player/character knows their place in the world
- overcoming this problem is usually external motivation of your plot

Usually players wander aimlessly, look around, explore to start

They dont usually dive straight into objectives

Then - they make the choice to act with intention

Complexity increasing - is a story telling moment ! Can have narrative and mechanics both increase concurrently 

THink about the ‘grabber’ - a burst of action or fear at the beginning that promises the future of the game befor eit slows down for the ‘before the inciting incident’ scene building

Scene Structure

All scenes must have

- an objective
- conflict
- an outcome

![Untitled](Beat%20Traveller%20Treatment%202%206559459e6de5446d91cc5abc83b19e88/Untitled%201.png)

Play with this idea of player succedding - protag’s life even worse - as much as you can. if protag’s life is easy, it’s boring

idea - ‘if the ending is good, it should feel like when the actual movie starts’

![Untitled](Beat%20Traveller%20Treatment%202%206559459e6de5446d91cc5abc83b19e88/Untitled%202.png)

[Story Structure and Tips](Beat%20Traveller%20Treatment%202%206559459e6de5446d91cc5abc83b19e88/Story%20Structure%20and%20Tips%20b24ec4d21c8a49ed9f032c530d44a100.md)