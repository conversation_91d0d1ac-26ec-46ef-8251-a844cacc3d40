## Learn Unity DOTS! (FREE Tutorial Course)

**Video URL:** [https://www.youtube.com/watch?v=1gSnTlUjs-s&t=1s](https://www.youtube.com/watch?v=1gSnTlUjs-s&t=1s)

**Video language:** English (auto-generated)

---

### Introduction
-   This is a free 7-hour tutorial on learning Unity DOTS.
-   The final game is an RTS with units, buildings, zombies, resources, fog of war, ragdolls, destruction, and more.
-   Unity DOTS is a high-performance tool stack that can run code *literally* 200x faster.
-   This extreme performance has many benefits for making more complex, ambitious games that run smoothly or save battery.
-   The tutorial starts with the absolute basics: what is DOTS, an entity, component, and system?
-   It covers the job system, burst compiler, DOTS physics, authoring, runtime data, queries, and more.
-   The course is structured for a smooth learning curve through 80 lectures.
-   DOTS is an advanced topic, not for beginners. Beginners should stick with GameObjects.
-   If you're a beginner, check out the free "Catch and Chos" course or the free "Shop" course, or the "Turn-Based Strategy" course before learning DOTS.
-   If you understand the intermediate sections of those courses, then you are ready to learn DOTS.
-   If you are at an intermediate or advanced level, learning DOTS is highly recommended.
-   The free 7-hour video is a subset of the full 17-hour course.
-   The full course has no ads, split lectures, private Discord, and weekly live streams.
-   This free video has as much DOTS knowledge as possible so it is still valuable even without the full course.
-   DOTS is awesome technology and the instructor wants everyone to learn it for free.
-   If you find this video helpful, please hit the like button and subscribe.

### Unity DOTS Overview

-   DOTS (Data-Oriented Technology Stack) is Unity's high-performance tool stack.
-   Requires a different way of thinking: data-oriented programming instead of object-oriented programming (OOP).
-   Learning DOTS provides massive performance benefits.
-   The change from OOP to DOTS isn't as intimidating as it seems as long as you start with basics and increase complexity step-by-step.
-   The course is for intermediate users who already know about GameObjects, transforms, Unity, generics, events, and singletons.
-   The course assumes a basic understanding of C#
-   An RTS game is the perfect genre to learn DOTS since it involves lots of units and logic applied to many objects simultaneously.
-   The final game will have units, zombies, buildings, spawning, resource gathering, zombie hordes, flow field pathfinding, fog of war, a minimap, shooting, melee, animation, the baking system, structural changes, and combining DOTS with GameObjects.
-   All code is written on screen and there are downloadable project files for each lecture.
-   The instructor is available in the comment section.

### Final Game Overview

-   The game starts with a main menu (handling scene loading in DOTS).
-   It is a standard RTS game with:
    -   Units
    -   Resources
    -   UI
    -   Minimap
    -   Special Skills
-   Units can be selected with a click or drag, and move around.
-   Zombies appear and are attacked by units, with health bars.
-   Zombies have ragdolls when they die.
-   Fog of War is implemented, and areas explored reveal parts of the map.
-   Zombie hordes periodically attack.
-   Airstrike special skills can be triggered, which call helicopters and drop bombs.
-   Buildings can be placed, requiring resources.
-   Towers defend and are destroyed.
-   Barracks can be built to spawn more units.
    -   Harvesters can be used to collect gold and iron.
    -   Soldiers and Scouts units can be spawned.
-   Units have pathfinding logic to go around obstacles.
-   Buildings have destruction effects.
-   The goal is to explore the entire map and destroy all zombies.

### Course Overview

-   The course starts with the theory behind DOTS:
    -   Entities, components, and systems (ECS)
    -   Job system
    -   Burst compiler
-   Next, project setup:
    -   Create project
    -   Install packages
    -   Set up Unity layout
    -   Set up Visual Studio
    -   Import visual assets
    -   Set up post-processing and lighting
    -   Download the project files (to match the exact project setup)
-   Game development:
    -   DOTS sub scene baking
    -   Creating a custom component to store data
    -   Creating a system to move our units
    -   Refactor movement using DOTS physics
    -   Handling the mouse ray position (normal MonoBehaviour script)
    -   Move unit using mouse position (using job system)
    -   Unit selection, UI setup, selecting multiple units
    -   Selecting single unit (DOTS physics raycast)
    -   Generating unit move positions (formations)
    -   Implementing events in DOTS
    -   Creating zombies with factions
    -   Building the find Target system
    -   Implementing health and death
    -   Creating a visual bullet, setting up move, dealing damage
    -   Entity spawning
    -   Simple component and system for spawning zombies
    -   Making them randomly walk (using the Burst compatible random class)
    -   Creating a visual health bar using the dots renderer
    -   Implementing melee attacks and move override logic
    -   Using `RequiredForUpdate` to define required components
    -   Refactoring code from simple systems to bursted jobs
    -   Building custom animation system (using blob assets)
    -   Changing animations on the fly
    -   Implementing animation events
    -   Fixing subscene issues by creating a custom baking system
    -   Creating a scout unit (multiple unit types)
    -   Building the zombie building spawner
    -   Creating a defensive tower (reusing made components)
    -   Making a scriptable object for building types
    -   Working with dynamic buffers for unit spawning
    -   Creating a UI for barracks
    -   Building the building placement manager, UI for building
    -   Creating an HQ building (game over)
    -   Implementing camera manager
    -   Refactoring to fix memory leaks
    -   Building the Grid system with DOTS
    -   Implementing Flow field pathfinding
    -   Using debug visuals to verify the pathfinding logic
    -   Adding walls to pathfinding
    -   Implementing a flow field follower (units following a flow field)
    -   Handling multiple units going to different targets (multiple flow fields)
    -   Optimizing pathfinding
    -   Make all movement using pathfinding and updating flow fields based on obstacles
    -   Refactor pathfinding code into super-fast bursted jobs
    -   Implementing a minimap (unit and building icons)
    -   Implementing a fog of war system
    -   Harvesting resources
    -   Implementing building and unit costs
    -   Making buildings construct instead of just instantly spawning
    -   Adding a horde mechanic
    -   Implementing ragdolls
    -   Implementing a main menu
    -   Making a playable map
    -   Refactoring performance using profiler
    -   Adding tiny little details in the final polish lecture
-   The total course is about 17 hours, split across 75 lectures.
-   Take your time, understanding is what matters.

### What is DOTS?

-   DOTS (Data-Oriented Technology Stack) is a set of Unity Technologies based on very specific rules
-   It provides insane performance through its data-oriented design.
-   DOTS is a technology *stack*, composed of three main components:
    -   Entity Component System (ECS)
    -   Job System
    -   Burst Compiler
-   ECS contains three things:
    -   **Entities:** Just identifiers (numbers) with no data or logic.
    -   **Components:** Data containers (e.g. movement speed, position).
    -   **Systems:** Logic that operates on entities that have specific components.
-   **Memory management in ECS:**
    -   Memory is tightly packed (contiguous).
    -   CPU access to tightly packed data is much faster than access to scattered data in memory.
    -   In OOP, objects are scattered in memory which makes CPU access inefficient.
-   ECS uses a data-oriented design where data and logic are separate.
-   Systems do not care about what the entity *represents* but instead runs the logic on any entity that matches a defined component query.
-   **Job System:** For writing fast multi-threaded code.
    -   Runs code on multiple CPU cores at the same time.
    -   Most game code is single-threaded, not using all available CPU power.
    -   Multi-threaded code is difficult to write and debug.
    -   The Job System adds safety nets, making multi-threading easier.
    -   Unity handles complex dependencies, thread allocation.
-   **Burst Compiler:** Special compiler for performance.
    -   Can result in huge performance improvements (100x faster).
    -   Requires code to be data-oriented and has limitations related to how data needs to be defined and accessed, specially concerning Value types and Reference Types.
-   When you combine all three main components of DOTS (ECS, Job System, Burst Compiler), the performance gains are massive.
-   Example: 
    -   Normal code might take 9 milliseconds
    -   Using the Job system, the same code can run in 0.29 milliseconds
    -   Using the Job system with the Burst compiler, it can run in 0.035 milliseconds, which is 260x faster.
-   Other packages include:
    -   Unity Physics (high-performance physics)
    -   Collections (data-oriented lists and arrays)
    -   Mathematics (performant math operations)
    -   Entities Graphics (rendering entities)
    -   NetCode for Entities (networking).
-   These main DOTS parts can be used separately or combined.
-   DOTS is a tool that *can* work alongside GameObjects (mix and match).
-   You do not need to go 100% DOTS nor 100% GameObjects, you can use the right tool for the right job.
-   If you are making an action game, use GameObjects for most gameplay and DOTS for heavy performance parts.
-   DOTS can help you fix performance hotspots without re-architecting your entire game.
-   The instructor will teach you DOTS components step by step during this course, as we need to use them in the development process.
-   Two recommended resources for further learning:
    -   Unity's Official Ebook (high-level overview of the theory behind DOTS).
    -   Instructor's DOTS tutorial (condensed way of learning the technical aspects of DOTS).

### Setting up the Project and Installing Packages

-   For the project setup, watch the lectures first, then download the prepared project files. This avoids versioning issues.
-   Unity Hub > New Project > Choose Unity version 2022.3.6 or any other Unity Version >= 2022.3.6.
-   Choose the URPT template.
-   Name it something like "DOTS RTS Course"
-   Remove the URP readme.
-   Install the following packages:
    -   Entities
    -   Entities Graphics
    -   Unity Physics
    -   2D Sprite
-   DOTS requires an IDE that supports source generation (Visual Studio, Rider).
-   Unity > Edit > Project Settings > Editor > Enter Play Mode Settings: Do Not Reload Domain or Scene.

### Setting up the Unity Layout

-   Customize the layout so that you have easy access to all the things you need.
-   The instructor layout:
    -   **Inspector:** Right side
    -   **Hierarchy:** Bottom Left
    -   **Project Files and Console:** Bottom middle with Tabs
    -   **Scene View and Game view:** Middle main area.
-   Project Files:
    -   Use the one column layout
-   Console:
    -   One line log entries
    -   All message buttons should be ticked (info, warnings, errors)
    -   Enable Clear on Play
    -   Pause on Error should be enabled
-   Game View:
    -   Use a Full HD aspect ratio
    -   Enable VSync
-   Scene View:
    -   Use Pivot Center and Local Rotation
    -   Grid visibility on the Y axis
    -   Make sure all the visibility modes are as you like
-   Save the layout so you can easily come back to it.

### Setting up Visual Studio

-   Visual Studio Community 2022 is used, but any source-generation compatible IDE can be used (like Rider)
-   Make sure that the Visual Studio editor package is installed.
-   Unity > Edit > Preferences > External Tools: select Visual Studio 2022 as the editor.
-   Install the "Viasfora" extension in Visual Studio for improved code coloring.
-   The visual studio settings file and Viasfora settings can be downloaded and imported into Visual Studio to have the exact same setup as the instructor.

### Importing Visual Assets

-   Visual assets from Cinti Studios "Simple" series.
-   These assets are for non-commercial use only, for commercial use buy the packs from the Cinti store.
-   The assets include:
    -   Simple Military Pack
    -   Simple Space Pack
    -   Simple Zombies Pack
-   Double-click or drag the Unitypackage to import assets.
-   Create a ground plane (scale 40), add ground material.
-   Position camera to 0, 29, -25 and rotation to 45 on X axis, and Field of view of 40.
-   Add soldier and zombie meshes.

### Setting up Post Processing and Lighting

-   Customize post-processing effects using the global volume.
-   Remove tone mapping.
-   Bloom threshold 1, intensity 0.25.
-   Remove motion blur.
-   Vignette intensity of 0.4.
-   Set directional light intensity to 1.5.
-   Set global lighting source to color, ambient color to a grayish white.
-   Screen Space Ambient Occlusion: use interleaved gradient, with intensity 10 and radius 6, indirect lighting a bit more.
-   Set the maximum shadow distance to 150 (using the render pipeline asset).
-   The post-processing effects are saved to a profile file that can be duplicated to create multiple different looks for your project.

### Download Project Files
-   Download the project files for this lecture to have the exact same setup with all version numbers, configurations, and project settings.
-  Open the project using the Unity Hub.
-   The project will download and install all required packages when it is loaded for the first time.
-   The scene should look identical to the one used in the video, and Visual Studio should also be set up identically (including settings)
-   Remove test meshes and save the scene.

### DOTS Subscenes and Basic Concepts

-   Create a subscene. Objects inside the subscene will be converted to entities.
-   Subscene: an empty object where DOTS objects reside, whereas GameObjects outside subscenes do not convert to entities.
-   Create a simple 3D cube inside a subscene.
-   By default, the cube is treated as a normal GameObject with normal transform, mesh, and Collider.
-   But down below on the inspector, there is a special baking preview, showing that this object can be converted to an entity.
-   Authoring mode on the inspector will showcase normal GameObjects, whereas runtime mode will show the DOTS entity and its components.
-   DOTS components do not always match the GameObject components. One Gameobject can translate into multiple DOTS components.
-   The baking process converts GameObjects data into binary DOTS data.
-   Subscenes can be opened or closed for editing (data is only baked when a subscene is closed).
-   DOTS has an entity-specific hierarchy window (Window > Entities > Entities Hierarchy)
-   When you close a subscene all GameObjects are baked into binary data and can no longer be modified in the editor, but they can still be inspected by selecting their corresponding entities and modifying it when the game is running.
-   DOTS subscenes are good for world streaming: load and unload portions of the world since it is very efficient to load or unload binary data
-   Empty game objects that have no components and no children, will not get converted to an entity.
-   GameObjects with physics and graphics components will get automatically converted to DOTS physics and graphics.
-   The scene view can be set to display runtime data to accurately preview changes made to dots entities. (Edit > Preferences > Entities)

### Custom DOTS Components

-   Create a `Unit` game object (parent object), child `mesh` object for visuals, and attach a mesh filter/renderer and material for soldier mesh.
-   Create a custom component by right-clicking on the scripts folder > create > entities > IComponentData.
-   Implement the `IComponentData` interface in a struct called `MoveSpeed`.
-   Components must be a struct not a class, since structures are value types as opposed to reference types that classes are.
-   Value types store actual data, reference types store a memory location.
    -   When assigning value types you create a copy of the underlying data.
    -   When assigning reference types you simply assign a reference to the same data.
-   Classes are reference types, structs are value types.
-   Components should only store data, not functions.
-   For the `MoveSpeed` component, add a `public float value` field to store the speed.
-  Create a corresponding MonoBehavior authoring component to specify how to bake that component to an entity
-  For the `MoveSpeedAuthoring` also add a `public float value` field.
-  Add a nested class extending from `Baker<T>` and implement bake logic for the custom components
    - This provides access to the authoring component data to be baked.
    -  Use `Add Component()` method to attach a new DOTS component to the baked entity.
-  Get a baked entity by calling the function `get entity()`, and select transform flags of dynamic.
-  Add the `MoveSpeedAuthoring` component to the `Unit` game object.
- The Baker class is going to grab the value of the authoring component to set the data of the actual DOTS component.
- To keep all related code together, the DOTS component can be added directly in the authoring component code file.
- For organization, you can create an "authoring" folder to separate all authoring components.

### First System

-   Right-click on the scripts folder > create > entities > I system
-   Create a `UnitMoverSystem`, implement `ISystem`, mark as `struct`, and as `partial`.
-  The struct should be marked as `partial` because the code is going to have other parts autogenerated behind the scenes.
-   `OnCreate`, `OnUpdate`, and `OnDestroy` methods can be added (but are optional).
- The System lifecycle:
    -  Systems are created and destroyed automatically.
    -  All systems that implement `ISystem` get registered with an automatic bootstrapper.
-    The `OnUpdate` method runs every frame like a normal monbehavior's update method.
-  Use the `[BurstCompile]` attribute for better performance, although this has a limitation related to data access.
-  `SystemAPI.Query` is a type that is used to cycle through entities with components.
-  The query itself is only going to run on entities that have all the components that you specify inside the query.
-   Inside the `OnUpdate` method, create a query with `SystemAPI.Query<RefRW<LocalTransform>>` using foreach to iterate over all entities that have a local transform.
-   LocalTransform needs to be a ref (reference) or refRW (read write). If you want to modify it it should be a refRW otherwise it should be ref for read only.
-  If a simple struct component is set as a parameter to a forEach it is going to be a copy so it needs to be a reference.
-  You can not access the `localTransform` directly instead you need to access `localTransform.ValueRW` to modify it directly.
-  You use `unity.mathematics` for float3 for working with DOTS.
-   To make movement frame rate independent use `SystemAPI.Time.DeltaTime` instead of Time.deltaTime
-  You can set this to a float3 position (moving right by one unit).
-   Systems do not care about what type of entity it processes.
-   The system applies to all the entities that match the defined query, it does not care what those entities are.
-   An entity can have many types of components, the query will only use the components used by this particular system.
-   You can add multiple types of components inside the query with a comma using `RefR` or `RefRW` syntax. The component will only match if the entity has all components specified in query.
-   You can also use with other settings for the queries using the word "with" such as "with None" "with Disable" or "with Present", that can be used for specific queries.
-   You can type "VAR" in order to not be explicit about the types you use in for each it will use var to make the code less explicit, personally I prefer to define the types explicitly.
-   By adding the `MoveSpeed` component to the query, only the `unit` entities get modified.
-   Use the move speed value in the `MoveSpeed` component to scale the unit movement.

### Moving with DOTS Physics

-   Make the unit rotate towards the move direction, so the unit faces the correct way.
    -   Create a move direction vector using target - current position.
    -   Use `math.normalized` function to normalize the move direction vector.
    -   Use Quat.LookRotation to generate the proper rotation quaternion based on that move vector, always looking up using `math.up`.
    -   Set local transform rotation to that quaternion.
    - To have smoother rotation with a quaterion use `math.slerp` instead of assigning the quaterion directly, that function needs a starting, end and Time parameter to do a smooth interpolation.
    -   To avoid code from jumping around in different ways it is good to group all the variables.
-   Add a Capsule Collider and Rigidbody to the unit.
-   Disable gravity, freeze rotation, and freeze movement on the Y axis for the Rigidbody.
-   Modify the physics velocity instead of setting the transform position.
    -   Add physics velocity to the query using `RefRW<PhysicsVelocity>`.
    -   Set the `linear` velocity field on the `PhysicsVelocity` component.
-   Do not use `Time.deltaTime` when setting physics velocity. The physics system will then apply the fixed DeltaTime.
-   Set angular velocity to zero to avoid rotation through physics collisions.
-   DOTS physics and GameObject physics are completely separate, they do not interact with each other.
-   Objects with dots components can only work with other DOTS components inside of a subscene.
-  DOTS will try to bake everything into a different archetype that is why things may "teleport" in the scene view. If you are seeing this you probably have the scene view set up to show the authoring state so to show the baked state set the scene view mode to runtime. (Edit > Preferences > Entities)

### Mouse World Position

-   Create a MonoBehaviour script for getting the mouse position.
-   Create a new empty GameObject and attach the `MouseWorldPosition` script to it.
-   Create a `GetPosition` function that returns a `Vector3`.
    -   Construct a ray from the camera using `camera.ScreenPointToRay(input.MousePosition)` (using the input manager rather than input system).
    -   Construct a plane at the origin pointing up using `new Plane(Vector3.up, Vector3.zero)`.
    -   Do a raycast against the plane using the camera ray, to get the correct position.
-   Alternatively use Unity's Physics.Raycast for more complex terrains.
-   Make the MouseWorldPosition script a singleton to make it easy to access from anywhere.
-  In general: 
    - use normal MonoBehaviour scripts for things that are not performance-critical or run only on a single object
    - use DOTS for performance-critical things.
-  Modify the `UnitMoverSystem` to get the target position from the `MouseWorldPosition` script.
-  You will have a burst error because you are accessing manage data from within bursted code.
-  That will still work but it will not run using burst, it will run on the main thread so it is a good reminder to be aware of the limitations when you have the burst compile attribute.
-   Use the `Quaternion.Slerp` function to smoothly rotate the unit, define a float variable for speed to be used here.

### Unit Selection with UI

-   Refactor the `MoveSpeed` component into `UnitMover` component and add the `rotation speed`.
-   Add the rotation speed to the authoring component and bake logic.
-  The purpose of the `UnitMover` component is to store data related to the unit movement system not necessarily just a move speed so changing the name makes a little more sense.
-   Create a `UnitSelectionManager` script (MonoBehaviour) to handle unit selection.
-   Create an empty GameObject, attach the script, and reset transform.
-   Check for left mouse button down and up in `Update()`, store the start and end mouse positions.
-   Create events for start and end selections.
-  The best way to decouple the UI logic and the game logic is through events.
-   The UI should be a separate class from the logic, making the UI dependent on logic class and not the other way around that will help decouple both components.
-   Create a `UnitSelectionManagerUI` script (MonoBehaviour) to handle UI logic.
-   Create a Canvas, set render mode to "Screen Space - Overlay" and scale mode to "Scale with screen size", and reference resolution to 1080p using a "match height".
-   Create an empty gameobject inside the canvas to stretch across the entire canvas. Create an image inside that one to represent the selection area. Anchor it into the lower left corner.
-   Listen to the `UnitSelectionManager` events to show/hide and update the selection area size.
-  Make sure that the selection area image is anchored to the lower left corner.
-   The function "GetSelectionAreaRect()" will calculate the rectangle area.
-   For updating the rect position and scale, remember to use screen coordinates and then properly calculate the scale on the UI script.
-   From the `UnitSelectionManager`, query for all the units that have a `UnitMover` component and a `selected` component and set their target positions to the mouse position.
-   To interact with the DOTS world from a MonoBehaviour you need to construct a custom entity query, using `new EntityQueryBuilder`, and using an allocator to manage memory, you should use the shortest available which is `Allocator.Temp`.
-   Use a native array to work with the components and then using the manager call copy from components array.
-   You must remember to make a copy of a struct if you are going to modify it, otherwise you are just modifying the local copy and not the one associated with that component.
-  To avoid problems when using the two component data array make sure to use the correct method which has type NativeArray<T>, since the other component data array returns a regular array which will throw an exception.
-  When assigning a structure variable you are making a copy of it, if you are working inside a foreach loop then you are making a copy at each cycle so modifying it will not affect the original data. You need to work with pointers to memory locations or copy the data back.
-   If you create a brand new component and assign its data this will make the components data default to zero unless if specified manually, you need to copy previous data if you only want to change one thing.
-   To not call a function on every unit, you can modify it on a buffer and then perform that once for all.
-  `NativeArrays` are used for working with bursted code, `lists` are for GameObjects.

### Enable Component and Single Unit Selection

-  Create an `IEnableComponent` called `Selected`, used as a tag for selected entities.
-  Enable components:
    -   Components that have a flag that's enabled or disabled.
    -   Do not trigger structural changes.
    -   Query only returns entities where this component is enabled.
-   Modify the query to include the selected component
-   Modify selection logic to set a boolean property in a struct component.
-   Enable the selected component when we select an entity, and disable when deselected.
-   Use this type of component for when you need to activate or deactivate something and you do not want to trigger a structural change.
-   If you just set a component to be disabled that component still exists in that entity but it is simply disabled.
-  You can query to get components that have been disabled by including the option `withDisabled`.
-  You can query to get components if the component exists by using the option `withPresent`.
-   Add the enable component to the unit to mark it as selected.
-   Create a new system to count selected units and display a message on the console, to test out different selection queries.
-  Add a field on the selected component to store a reference to an entity.
-   Since the data in IComponents cannot be a type of Gameobject, we need to add the reference as a normal game object, so that we can select the child from the editor, and then create a transformation from this game object to an entity.
-   Use `getEntity()` to convert game object to entity, and specify `TransformUsageFlags` based on if you want the baked entity to have the transform components.
-  Make a new system to cycle all units with the selected components and show or hide a visual object.
-   Store the visual's entity in the selected component, then retrieve that entity's local transform to modify it.
-  Set to scale 0 on the y to hide it and to one or two to show the scale.
-   Make the rotation smoother by implementing Slerp in the custom transform.
-   For smooth rotation make sure all the child object transforms are set to zero.
-   When using `Slerp` for a rotation, you need the starting quaterion, the end quaterion and a time value.
-  To implement single unit selection, you can use physics to raycast and then retrieve an entity if it is clicked.
-   Use Physics.Raycast to select units by clicking on the UI.
-   Create a check for is multiple selection to see if you should select just one or multiple.
-   Use an `EntityQuery` with `SystemAPI.Query<T>` to get a single entity at the end of a physics raycast operation.
-   Set the `select` component to `true` to select that unit.

### Custom Animation System

-  DOTS does not support built-in animations, that's why you need to build your own.
-   Start by defining what do we want to implement in our system and create that data using custom components.

### Grid and Pathfinding

-  DOTS can improve performance in open world games.
-  Grid system: data-oriented
    -   Create a visual debug grid.
    -   Flowfield pathfinding
-  Pathfinding is a data problem
-  Flow field pathfinding is faster to compute (compared to A star), suitable for large amount of units.
-  Implement the data oriented approach for the grid and pathfinding logic, using math library.
-  Make use of custom components.
-  Pathfinding implementation: calculating a vector based on the surrounding tiles, and store it in a grid data structure. Then the unit simply has to look up at that point and follow the vector.
-  Add walls as obstacles.
-   Implement a flow field follower so they follow the calculated grid.
-  Flow fields can be stored by using dynamic buffers (multiple flow fields).
-  Use rasting to improve pathfinding and use them only when needed.
- Refactor pathfinding system code into bursted jobs to improve performance.

### RTS Mechanics

-  Implement a minimap (show unit and building icons).
-  Implement Fog of War (show only a specific area of units around units) with immediate and persistent modes.
-  Implement resources and make buildings harvest them (placed near resource nodes).
- Implement the logic to support cost for buildings and units
-  Make buildings take time to construct (using dynamic buffers to store state)
-  Implement a zombie horde spawner.
-   Implement ragdolls for when the units die, using an approach for skin mesh renders that are not yet supported by DOTS, by implementing a custom solution.
-  Create a main menu (and handle scene loading).
-  Create a full playable map with resource nodes, obstacles, zombie spawners and more.
-  Optimize existing systems with the profiler, making use of jobs and burst whenever possible.
-  Polish the game with: UI indicators, zombie counters, screen shake, destruction pieces, and an air strike mechanic.

### Absolute Basics of DOTS

-   DOTS (Data-Oriented Technology Stack) is a Unity technology stack for high performance.
-   Requires thinking in terms of data-oriented programming instead of object-oriented programming (OOP).
-   DOTS is a technology *stack* consisting of:
    -   **Entity Component System (ECS)**: Manages entities, components and systems
    -   **Job System**: Makes multi-threaded programming possible
    -   **Burst Compiler**: Optimizes code for increased performance
-   **Entity Component System (ECS)** has three main elements:
    -   **Entities:** Just IDs (numbers)
    -   **Components:** Store only data
    -   **Systems:** Contains game logic that uses component data.
-   ECS memory management:
    -   Memory is tightly packed.
    -   Faster access compared to OOP where data is scattered across memory locations
-   Data oriented design separates logic from data, which leads to faster performance.
-   Systems operate on entities based on which components they have (not based on *what* they represent)
-   **Job System:**
    -   Multi-threaded code.
    -   Uses multiple cores.
    -   Addresses limitations with multithreading programming.
    -   Provides safety nets that make it easier to debug.
-   **Burst Compiler:**
    -   Special compiler that provides a huge performance boost
    -   Has limitations: Code needs to be data-oriented.
-   When combined, the entire DOTS stack gives truly insane results.
-   Other DOTS Packages:
    -   Unity Physics (high performance Physics)
    -   Collections (data oriented lists and arrays)
    -   Mathematics (high performance math)
    -   Entities Graphics (graphics)
    -   NetCode for Entities (networking)
-   These packages can be used independently.
-   DOTS can and *should* be used alongside GameObjects as they are meant to work together. DOTS is not a replacement for GameObjects
-   DOTS is a tool in your tool box, you should know when and where to use it.
-   DOTS really shines when you need massive performance, usually when you start hitting performance bottlenecks in your game, that's when you should start using DOTS.

### DOTS Subscenes

-  Create a subscene by right clicking and adding a new "empty subscene" in the editor
-  DOTS automatically converts GameObjects within a subscene into DOTS entities.
-  The baking process takes game object data and converts it into optimized binary DOTS data.
-  Subscenes allows DOTS to do world streaming, making it really good for creating open world games with many dynamically loaded levels
-  A subscene can be closed or opened for editing (data is baked upon closing)
-  Use the entity hierarchy panel for viewing entities
- DOTS entities with default components are always automatically included when they are inside of a sub

### DOTS Subscenes (Continued)

-   DOTS entities with default components are always automatically included when they are inside of a subscene.
-   Empty gameobjects will not automatically convert to entities, they need to have components attached to them.
-  DOTS physics and graphics work directly with their corresponding GameObjects by using the baking system to convert them into their corresponding entities.
-    On the editor, the scene view can show either the authoring objects or the runtime entities.
-   The baking process happens just once when the subscene is closed (or at build time).
-   Binary data loading is what makes DOTS extremely fast.

### Custom DOTS Components

-   Create a `Unit` game object (parent object), child `mesh` object for visuals, and attach a mesh filter/renderer and material for soldier mesh.
-   Create a custom component by right-clicking on the scripts folder > create > entities > IComponentData.
-   Implement the `IComponentData` interface in a struct called `MoveSpeed`.
-   Components must be a struct not a class, since structures are value types as opposed to reference types that classes are.
-   Value types store actual data, reference types store a memory location.
    -   When assigning value types you create a copy of the underlying data.
    -   When assigning reference types you simply assign a reference to the same data.
-   Classes are reference types, structs are value types.
-   Components should only store data, not functions.
-   For the `MoveSpeed` component, add a `public float value` field to store the speed.
-  Create a corresponding MonoBehavior authoring component to specify how to bake that component to an entity
-  For the `MoveSpeedAuthoring` also add a `public float value` field.
-  Add a nested class extending from `Baker<T>` and implement bake logic for the custom components
    - This provides access to the authoring component data to be baked.
    -  Use `Add Component()` method to attach a new DOTS component to the baked entity.
-  Get a baked entity by calling the function `get entity()`, and select transform flags of dynamic.
-  Add the `MoveSpeedAuthoring` component to the `Unit` game object.
- The Baker class is going to grab the value of the authoring component to set the data of the actual DOTS component.
- To keep all related code together, the DOTS component can be added directly in the authoring component code file.
- For organization, you can create an "authoring" folder to separate all authoring components.

### First System

-   Right-click on the scripts folder > create > entities > I system
-   Create a `UnitMoverSystem`, implement `ISystem`, mark as `struct`, and as `partial`.
-  The struct should be marked as `partial` because the code is going to have other parts autogenerated behind the scenes.
-   `OnCreate`, `OnUpdate`, and `OnDestroy` methods can be added (but are optional).
- The System lifecycle:
    -  Systems are created and destroyed automatically.
    -  All systems that implement `ISystem` get registered with an automatic bootstrapper.
-    The `OnUpdate` method runs every frame like a normal monbehavior's update method.
-  Use the `[BurstCompile]` attribute for better performance, although this has a limitation related to data access.
-  `SystemAPI.Query` is a type that is used to cycle through entities with components.
-  The query itself is only going to run on entities that have all the components that you specify inside the query.
-   Inside the `OnUpdate` method, create a query with `SystemAPI.Query<RefRW<LocalTransform>>` using foreach to iterate over all entities that have a local transform.
-   LocalTransform needs to be a ref (reference) or refRW (read write). If you want to modify it it should be a refRW otherwise it should be ref for read only.
-  If a simple struct component is set as a parameter to a forEach it is going to be a copy so it needs to be a reference.
-  You can not access the `localTransform` directly instead you need to access `localTransform.ValueRW` to modify it directly.
-  You use `unity.mathematics` for float3 for working with DOTS.
-   To make movement frame rate independent use `SystemAPI.Time.DeltaTime` instead of Time.deltaTime
-  You can set this to a float3 position (moving right by one unit).
-   Systems do not care about what type of entity it processes.
-   The system applies to all the entities that match the defined query, it does not care what those entities are.
-   An entity can have many types of components, the query will only use the components used by this particular system.
-   You can add multiple types of components inside the query with a comma using `RefR` or `RefRW` syntax. The component will only match if the entity has all components specified in query.
-   You can also use with other settings for the queries using the word "with" such as "with None" "with Disable" or "with Present", that can be used for specific queries.
-   You can type "VAR" in order to not be explicit about the types you use in for each it will use var to make the code less explicit, personally I prefer to define the types explicitly.
-   By adding the `MoveSpeed` component to the query, only the `unit` entities get modified.
-   Use the move speed value in the `MoveSpeed` component to scale the unit movement.

### Moving with DOTS Physics

-   Make the unit rotate towards the move direction, so the unit faces the correct way.
    -   Create a move direction vector using target - current position.
    -   Use `math.normalized` function to normalize the move direction vector.
    -   Use Quat.LookRotation to generate the proper rotation quaternion based on that move vector, always looking up using `math.up`.
    -   Set local transform rotation to that quaternion.
    - To have smoother rotation with a quaterion use `math.slerp` instead of assigning the quaterion directly, that function needs a starting, end and Time parameter to do a smooth interpolation.
    -   To avoid code from jumping around in different ways it is good to group all the variables.
-   Add a Capsule Collider and Rigidbody to the unit.
-   Disable gravity, freeze rotation, and freeze movement on the Y axis for the Rigidbody.
-   Modify the physics velocity instead of setting the transform position.
    -   Add physics velocity to the query using `RefRW<PhysicsVelocity>`.
    -   Set the `linear` velocity field on the `PhysicsVelocity` component.
-   Do not use `Time.deltaTime` when setting physics velocity. The physics system will then apply the fixed DeltaTime.
-   Set angular velocity to zero to avoid rotation through physics collisions.
-   DOTS physics and GameObject physics are completely separate, they do not interact with each other.
-   Objects with dots components can only work with other DOTS components inside of a subscene.
-  DOTS will try to bake everything into a different archetype that is why things may "teleport" in the scene view. If you are seeing this you probably have the scene view set up to show the authoring state so to show the baked state set the scene view mode to runtime. (Edit > Preferences > Entities)

### Mouse World Position

-   Create a MonoBehaviour script for getting the mouse position.
-   Create a new empty GameObject and attach the `MouseWorldPosition` script to it.
-   Create a `GetPosition` function that returns a `Vector3`.
    -   Construct a ray from the camera using `camera.ScreenPointToRay(input.MousePosition)` (using the input manager rather than input system).
    -   Construct a plane at the origin pointing up using `new Plane(Vector3.up, Vector3.zero)`.
    -   Do a raycast against the plane using the camera ray, to get the correct position.
-   Alternatively use Unity's Physics.Raycast for more complex terrains.
-   Make the MouseWorldPosition script a singleton to make it easy to access from anywhere.
-  In general: 
    - use normal MonoBehaviour scripts for things that are not performance-critical or run only on a single object
    - use DOTS for performance-critical things.
-  Modify the `UnitMoverSystem` to get the target position from the `MouseWorldPosition` script.
-  You will have a burst error because you are accessing manage data from within bursted code.
-  That will still work but it will not run using burst, it will run on the main thread so it is a good reminder to be aware of the limitations when you have the burst compile attribute.
-   Use the `Quaternion.Slerp` function to smoothly rotate the unit, define a float variable for speed to be used here.

### Unit Selection with UI

-   Refactor the `MoveSpeed` component into `UnitMover` component and add the `rotation speed`.
-   Add the rotation speed to the authoring component and bake logic.
-  The purpose of the `UnitMover` component is to store data related to the unit movement system not necessarily just a move speed so changing the name makes a little more sense.
-   Create a `UnitSelectionManager` script (MonoBehaviour) to handle unit selection.
-   Create an empty GameObject, attach the script, and reset transform.
-   Check for left mouse button down and up in `Update()`, store the start and end mouse positions.
-   Create events for start and end selections.
-  The best way to decouple the UI logic and the game logic is through events.
-   The UI should be a separate class from the logic, making the UI dependent on logic class and not the other way around that will help decouple both components.
-   Create a `UnitSelectionManagerUI` script (MonoBehaviour) to handle UI logic.
-   Create a Canvas, set render mode to "Screen Space - Overlay" and scale mode to "Scale with screen size", and reference resolution to 1080p using a "match height".
-   Create an empty gameobject inside the canvas to stretch across the entire canvas. Create an image inside that one to represent the selection area. Anchor it into the lower left corner.
-   Listen to the `UnitSelectionManager` events to show/hide and update the selection area size.
-  Make sure that the selection area image is anchored to the lower left corner.
-   The function "GetSelectionAreaRect()" will calculate the rectangle area.
-   For updating the rect position and scale, remember to use screen coordinates and then properly calculate the scale on the UI script.
-   From the `UnitSelectionManager`, query for all the units that have a `UnitMover` component and a `selected` component and set their target positions to the mouse position.
-   To interact with the DOTS world from a MonoBehaviour you need to construct a custom entity query, using `new EntityQueryBuilder`, and using an allocator to manage memory, you should use the shortest available which is `Allocator.Temp`.
-   Use a native array to work with the components and then using the manager call copy from components array.
-   You must remember to make a copy of a struct if you are going to modify it, otherwise you are just modifying the local copy and not the one associated with that component. You need to work with pointers to memory locations or copy the data back.
-  To avoid problems when using the two component data array make sure to use the correct method which has type NativeArray<T>, since the other component data array returns a regular array which will throw an exception.
-  When assigning a structure variable you are making a copy of it, if you are working inside a foreach loop then you are making a copy at each cycle so modifying it will not affect the original data. You need to work with pointers to memory locations or copy the data back.
-   If you create a brand new component and assign its data this will make the components data default to zero unless if specified manually, you need to copy previous data if you only want to change one thing.
-   To not call a function on every unit, you can modify it on a buffer and then perform that once for all.
-  `NativeArrays` are used for working with bursted code, `lists` are for GameObjects.

### Enable Component and Single Unit Selection

-  Create an `IEnableComponent` called `Selected`, used as a tag for selected entities.
-  Enable components:
    -   Components that have a flag that's enabled or disabled.
    -   Do not trigger structural changes.
    -   Query only returns entities where this component is enabled.
-   Modify the query to include the selected component
-   Modify selection logic to set a boolean property in a struct component.
-   Use this type of component for when you need to activate or deactivate something and you do not want to trigger a structural change.
-   If you just set a component to be disabled that component still exists in that entity but it is simply disabled.
-  You can query to get components that have been disabled by including the option `withDisabled`.
-  You can query to get components if the component exists by using the option `withPresent`.
-   Add the enable component to the unit to mark it as selected.
-   Create a new system to count selected units and display a message on the console, to test out different selection queries.
-  Add a field on the selected component to store a reference to an entity.
-   Since the data in IComponents cannot be a type of Gameobject, we need to add the reference as a normal game object, so that we can select the child from the editor, and then create a transformation from this game object to an entity.
-   Use `getEntity()` to convert game object to entity, and specify `TransformUsageFlags` based on if you want the baked entity to have the transform components.
-  Make a new system to cycle all units with the selected components and show or hide a visual object.
-   Store the visual's entity in the selected component, then retrieve that entity's local transform to modify it.
-  Set to scale 0 on the y to hide it and to one or two to show the scale.
-   For smooth rotation make sure all the child object transforms are set to zero.
-   When using `Slerp` for a rotation, you need the starting quaterion, the end quaterion and a time value.
-  To implement single unit selection, you can use physics to raycast and then retrieve an entity if it is clicked.
-   Use Physics.Raycast to select units by clicking on the UI.
-   Create a check for is multiple selection to see if you should select just one or multiple.
-   Use an `EntityQuery` with `SystemAPI.Query<T>` to get a single entity at the end of a physics raycast operation.
-   Set the `select` component to `true` to select that unit.

### Creating a Job and Using Burst

-   Define a `struct` called `UnitMoverJob` that implements `IJobEntity`.
    -   All jobs must be a struct
    -   All jobs also need the `partial` keyword.
-   Use the `Execute` function to perform the operations of the job, pass in the components as parameters: `LocalTransform`, `UnitMover`, and `PhysicsVelocity`.
-   Use `ref` to modify a component, and `in` to read it.
-   To make a system multi threaded is not enough for it to be an `ISystem` or for the forEach to be a job, instead you need to use a method that actually uses the job system, such as `scheduleParallel()`
-  Jobs must be self-contained, cannot access external memory.
-  To have it work, pass in all data into the Job itself and the logic is self contained.
-  Use `time.deltaTime` from the System API instead of the normal `Time.deltaTime` (which is related to GameObjects).
- To run a job use the method `scheduleParallel()`.
-   Add the `[BurstCompile]` attribute to enable the Burst compiler for massive performance improvements.
-   First get the logic working with the main thread and then refactor it to be bursted and threaded.
-   The job system and burst compiler can be tested with the profiler window. (Window > analysis > profiler)
-   First, disable the job and use the normal forEach and take note of the main thread milliseconds.
-   Then enable the job and check the multithreaded performance of that code.
-   Then enable the burst compiler and check again and see the final time for the same logic.
-  Always remember the DOTS architecture has the following steps in mind: First get it working on a simple main-thread system then refactor it to use a job then add the burst compiler attribute and finally you'll have super fast code.
-   The performance gain is insane from for 9.13 ms to 0.035 ms, which is a 260x performance improvement, for the same logic.
-   Limit the frame rate using VSync to avoid over-rendering and over utilization of the GPU.
-   Add stopping logic:
    -   Calculate a move direction and its length.
    -   If the length is less than a certain distance, then stop moving (set the velocity to zero) and return from the job.

### Move Towards Mouse Click

-   Right now we are moving constantly but we should only move on mouse click.
-  Create a float3 for the Target Position, define it from the MouseWorldPosition component.
-   Change the unit to move only on click by setting the correct TargetPosition only when we press mouse button down, not on every update.
-   Make the rotation smoother using `math.slerp`.
-  To make use of math.slerp you need a starting rotation, an end rotation and also a float to define how fast you want to interpolate between both of them.

### Creating Custom Animation System

-  DOTS does not support built in animation systems. This is a challenge for complex projects so a custom system needs to be implemented.
-  First thing we should do is identify what are the data required to run an animation.
-  Use blob assets to store animation data (large data structures) and have a custom system to run them.

### Grid and Pathfinding

-   DOTS can improve performance in open world games.
-   Grid system: data-oriented
    -   Create a visual debug grid.
    -   Flowfield pathfinding
-   Pathfinding is a data problem
-   Flow field pathfinding is faster to compute (compared to A star), suitable for large amount of units.
-   Implement the data oriented approach for the grid and pathfinding logic, using math library.
-   Make use of custom components.
-   Pathfinding implementation: calculating a vector based on the surrounding tiles, and store it in a grid data structure. Then the unit simply has to look up at that point and follow the vector.
-   Add walls as obstacles.
-   Implement a flow field follower so they follow the calculated grid.
-   Flow fields can be stored by using dynamic buffers (multiple flow fields).
-   Use rasting to improve pathfinding and use them only when needed.
- Refactor pathfinding system code into super-fast bursted jobs to improve performance.

### RTS Mechanics

-   Implement a minimap (show unit and building icons).
-   Implement Fog of War (show only a specific area of units around units) with immediate and persistent modes.
-   Implement resources and make buildings harvest them (placed near resource nodes).
- Implement the logic to support cost for buildings and units
-   Make buildings take time to construct (using dynamic buffers to store state)
-   Adding a horde mechanic
-   Implementing ragdolls for when the units die, using an approach for skin mesh renders that are not yet supported by DOTS, by implementing a custom solution.
-   Implement a main menu (and handle scene loading).
-   Make a playable map with resource nodes, obstacles, zombie spawners and more.
-   Refactor performance using profiler
-   Polish the game with: UI indicators, zombie counters, screen shake, destruction pieces, and an air strike mechanic.

### DOTS Events

-   DOTS does not support regular C# events.
-  In DOTS there are no objects or delegates so you can't simply add a listener, so we need to construct our own system to listen to an event using a data-oriented approach
-  To do that create a new component that has some kind of data that can be set or reseted by some other systems and then make a system that listens for that data on an event.
-  The `Selected` component will have `OnSelected` and `OnDeselected` booleans that will act as events.
-   When you select, fire the event and set that boolean to true.
-   A dedicated system (ResetEventSystem) will reset the event (set to false) at the end of the frame, so we don't need to manually make a queue or anything like that
    -   To make sure this system is run last, set the update in group attribute to make it run on the LateSimulationSystemGroup.
    - The order is semi random and can change per PC so it needs to be explicitly declared.
-   A system to read a particular event can query for the specific type and use the value to decide what to do.
-  To define which events should run before or after, you can use two attributes `UpdateBefore` or `UpdateAfter` to specify that it should run before or after some other system.
-   When using the `UpdateBefore` or `UpdateAfter` make sure both systems are running inside the same group otherwise it will simply get ignored.
-    With this approach you can reuse this system in other components to detect whether some data changes, you simply add a new bool to that new component and update it, and then you can read and reset it the same way
- You can make use of the boolean value to add extra data to it. For example, an event could be of type `struct` that has a boolean as well as another field that you would like to pass to that event.

### Single Unit Select with DOTS Physics Raycasts
-  To properly use single select we should use Physics.Raycast to know exactly if the mouse has clicked on a particular unit and select it
-  When dealing with DOTS raycasts make sure to use `unity.physics.RaycastHit` since otherwise you will get collisions with GameObjects
-  Make a simple test using the unity engine and the Input Manager, and using `camera.main.ScreenToWorldPoint()` to get the world position from your mouse and do a debug log. Then implement the same with DOTS using the physics component to see if it's working correctly.
-  Use the physics collision system to do raycasts
-  `CollisionWorld` component has `CastRay()` which returns a `RaycastInput`.
-  Raycast input: has Start position, End Position and a collision filter.
    -   Collision filter has three options: a belongs to, a collides with, and a group.
        -   belongs to this ray cast will belong to this collision layer.
        -   collides with, it will only collide with those layers.
    - The layers are stored as a bitmask so a uint value.
-  After a cast ray, make sure to test if the resulting value is valid and then grab the closest result.
-  A  common practice is to start simple with a system and just get something working and then refactor it into a more complex version of that.
-    You can grab the PhysicsWorld singleton and then use the collision world to do an overlap sphere.

### Dynamic Move Positions

-   Units were getting stuck in the same position when ordered to go to the same location
-   The units were just all moving towards the target position all at once
-   Generate move positions to spread out units around the given point.
-   Implement a simple Circle formation:
    -   Create rings, and place units around them.
    -   Divide the ring into positions based on how many units you want.
-   Create a new method, make it public and make it return a `NativeArray` of `float3` this is going to be a list of all the generated positions.
-   Create this NativeArray with allocator of type temp.
-  If no positions are passed as parameters then just return the native array of positions.
-  Set the center position as the first position on the array
-   If there is only one position then that's it just return that
-  Create different types of settings for the radius distance between the rings and the current ring index to help construct new rings based on radius.
-  Use a for loop to generate all positions for a specific ring, divide the entire ring and assign all positions for it.
-  When working with the mathematical library `math` and not the `mathf` class then you use `math.PI` as the value for 180 degrees.
-  Inside the UI loop you need a `while` in order to keep generating new positions until all of them have been created.
-  In order to use the math library and all the optimizations, use `float3`, `quaternion` and so on.
-   The `Math` class has a `Rotate` function which takes in a quaternion and a vector and applies that rotation to the vector.
-  In the final result the result needs to be a `NativeArray` of positions to be used later.
-  Modify the `UnitSelectionManager` to use this logic, making sure it gets a correct position count from the amount of units selected.

### DOTS Events System

-  The usual approach of using callbacks and delegate objects is not ideal for DOTS since it is object oriented, we need to find an alternative.
-  Create a custom data-oriented approach to implementing events.
-  You will need a component with a bool (or other data) that can be set when the event triggers and then reset by another system at the end of frame.
-  Create a tag component with a boolean field that represents if the event happened or not.
-  Inside a System update the component with the true value in the event to mark that has happened.
-  Create a different system to reset all of those values back into false (the end of frame system).
-   Use a system that will listen to that bool and will run only when the event has been triggered by reading that value of the component.
-   When using a query for enable components you need to make sure they are enabled.
-   To solve this, use a `ref RW` on the event component and then using the value do a write.
-   Make sure to set the component type that you will write to.
-   You can use both with present or with disabled in case you want to find components that are present or not enabled.
-   Set component enabled only affects the enable state it does not affect the underlying data structure itself so do not mix it up.
-   This custom event logic will always require a separate system at the end of frame to reset this state and a separate system to read this and act upon it.
-  When implementing events you can also pass in extra data by defining struct type that contains the boolean trigger and all the required data.

### Zombie Spawner

-  Create a `ZombieSpawnerAuthoring` component to represent all the data related to the spawner.
-  Create a corresponding DOTS component, baker and authoring components.
-   Attach it to a new empty gameobject on the scene and offset it at a corner.
-   Implement logic for spawning based on a timer (also in this component).
-   Create a system that will count down on that timer and if timer has elapsed spawn a new zombie at that particular location.
-  Use the singleton class containing game references to access the prefab for the unit.
-  Use `state.EntityManager.Instantiate` to create new entities.
-   Set the `LocalTransform` for the entity that is going to be spawned.
-   Systems do not care about what the entity is, but instead it operates on entities that have components that match the system's queries.
-  DOTS works very well with data oriented programming as you start to think more about the component structure instead of thinking about the game object and their behavior.
-  A very important point in DOTS is data oriented programming, so what components you use, how are they structured, how do they fit in together with other systems.
-  When doing more complex things it is also a good pattern to do everything sequentially meaning, first implement a for each system, that runs on the main thread, and then refactor it to use burst and multithreading.
-   In general, you should avoid having duplicate code, use central locations to define all your settings such as this class with constant variables that will keep everything tidy.
-  As you start to build a code with DOTS there is a tendency that you will have lots more files and folders.
- The project is organized so that each main type is in a separate folder such as systems, monobehaviors, authoring scripts and so on, this will make it easier to maintain a large codebase.

### Random Walking

-   Make the enemies walk randomly around their spawn point.
-   Create a `RandomWalkingAuthoring` component to store position, distance, and other data for the walking logic.
-  You will need a target position, an origin position, and a min and max distance.
-   Use the mathematics `Random` class for random numbers and use it on your system instead of the Unity `Random`.
-   The math `random` class is not static it requires a new instantiatiation, it is also deterministic meaning that if you always use the same seed the random will always produce the same numbers.
-   Implement the random walk logic
  - When generating random numbers also use an origin position and a random float between Min and Max
  - Get the distance to the target and if you have reached that point then generate a new one
  - Normalize the vector of random values to make sure is always with the same length
    -   The class that has the normalization does not necessarily has a field to normalize the number instead you have to use a function.
-  You must reuse the same instance of `math.random` class.
-  If you always use the same seed and generate numbers with this math.random class it will always return the same sequence. You need to use a different seed to have it truly random.
-  Create a `uint` seed in the component and that way you generate different results.
- For the seed, just use the NTI index, since every entity has a unique index that is guaranteed to be different.
-  The `random` class from mathematics requires you to persist the random and always use that same instance again and again if you want to generate different values, if you call `new random(seed)`, you will always get the same set of numbers.
-  Dynamic entities that you are creating on the fly with code, they do not have a default seed so you need to add the logic to set a new unique seed every time an entity is instantiated.

### Visual Health Bars

- Create a `HealthBar` GameObject with:
    -  A `background` object, with a quad and the grey material.
    -  A `bar` object as a child of a `bar` parent, with a quad and a red health bar material.
-  The visual object should be offset to the left (-0.5) and scaled on X.
-  Create a `HealthBarAuthoring` component, with a reference to a bar visual entity and health entity.
- Create a system to read the health of an object and update the health bar scale based on that, but using a component that has a local transform instead of doing it with the normal transform.
-  Use `postTransformMatrix` for non-uniform scaling.
-  Use the component value `PostTransformMatrix` to set the scale of the bar on the X only. (Do not use the local transform scale directly)
-   If you just set a transform for something with a specific scale you are telling that transform to use that scale for all of its axes, if you want only one to have a specific value you need to use a different matrix.
-  Add logic for making the health bar visible only if the unit has been damaged to improve visual clarity on large scenes, then when the bar is full make it invisible again.
-  Optimize the system by checking if the health was changed or not, using the DOTS event logic.
-   Use the camera forward vector and the inverse transform to rotate the health bar to always look at the camera.
- The code to make the health bar visible depends on an event, then the logic is only going to run when this event has been set to true.
-   The default method `camera.main` cannot be used with burst so it needs to be done without using bursted code which is fine for this particular system.
- Use the built-in quaternion look rotation and math.up to always make the ui look at the camera.
- Remember to do a local transformation when rotating elements, and not just a world transformation, otherwise they will not be correct.
- When a gameobject has different scales for its axes, its corresponding entity will have an additional component that needs to be set to change the scale.
- You need to make sure that, when passing information between components, you explicitly set the correct reference.

###  Visual Lights

-   Add a new empty point light GameObject, name it shootLight.
    -   Set its color, intensity, range, and shadows.
-   Make the light into a prefab, then on the entites references, set the value to reference that prefab.
-   Add a system to spawn a light when the units are shooting.
-  Add a dedicated component to the light and then use a system to destroy it after a certain amount of time.
-   For the rotation, use the same logic used on previous steps, grabbing a starting quaterion, and an end quaterion, calculating the time and then interpolating between all of them.
-  Instead of having the shooting logic deal with rendering and instantiation it is better to create an event so that the shooting just focuses on what logic needs to be implemented and the rest is taken care of another system.
-    For using events and having multiple data that you want to pass through you need to create a custom `struct` and pass that data with that struct so you can add more info than just a bool.
-  You can reuse components even if they have different data inside of them, a bullet component has different data than an I unit component. They are simply just data and DOTS does not care.

### Melee Attacks

-  Add a `MeleeAttackAuthoring` component to the zombie prefab.
- Implement a melee attack to the zombies in a similar way that a shooting attack works on the soldiers.
- Zombies also have the fine target logic, which can be reused.
-  Use the same logic used on the shooting and add a new data component for a melee attack to specify timer and damage values.
- Implement a new system for melee attacks that has the same pattern as the existing `shootAttackSystem`: find target, move closer, set timer, attack target.
-  Reuse the existing find target system by making sure the units that you want to attack also have a fine target system attached to it, that's where you see the power of the data driven design.
-  Use a refRW on the unit mover to set it to stop moving when the unit is within range.
-   To have correct Melee attacks, use two checks:
        -  A regular distance check for a fast performance check, using math.distanceSquared.
        -  A raycast to test if a collision is indeed happening, use the method `Physics.CastRay()`.

### Move Override Logic

- To create a manual movement override we can use enable component.
- Create an `MoveOverrideAuthoring` component, an enable component, with a `targetPosition` field. Start it as disabled.
-  In the main `UnitSelectionManager` script set up so that when a unit is clicked it activates the move override system.
-   Create a system to move a unit to the override target position and then disable the `MoveOverride` component when the target is reached using `setComponentEnabled`.
-   To avoid conflict between the movement logic and the target logic use an `UpdateBefore` or an `UpdateAfter` to specify in which order the systems should run, this ensures that you do not get unexpected issues, using attributes `[UpdateInGroup(typeof(LateSimulationSystemGroup))]` and `[UpdateBefore(typeof(System))]`
-   To make it work, the melee attack system needs to only run when the `MoveOverride` component is disabled.
- To have the units always go to a certain position or use
### Move Override Logic (Continued)

- To have the units always go to a certain position or use a dynamic data source then we need to manually force that data to have this override and the normal AI movement should be disabled while this manual override is enabled.

### Final Thoughts and Course Conclusion

-   Congratulations on making it through the free course video, and learning a ton about Unity DOTS.
-   You have learned how DOTS works, how to create entities, components, systems and make them super fast using the job system and the burst compiler.
-   With this knowledge, you can make high-performance games that take full advantage of the CPU.
-   The full 17-hour course is available via a link in the description, with:
    -   Custom animation system
    -   Grid system in DOTS
    -   Flowfield pathfinding
    -   Refactoring into super-fast bursted jobs
    -   Persistent Fog of War
    -   Buildings
    -   Resource nodes
    -   Ragdolls
    -   And a bunch more
-   If you can't afford the full course, you can still apply the knowledge you learned to your own games.
-   You can mix and match GameObjects and DOTS. Use DOTS for performance-intensive parts.
-   DOTS is a powerful tool for your toolbox.
-   Like the video and subscribe to the channel.
-   Thanks for watching, and see you next time.

---
**DISCLAIMER:**

By using this tool to convert YouTube videos to Text, you acknowledge and agree to the following:

1.  **User Responsibility**: You are responsible for ensuring that your use of this tool complies with all applicable copyright laws and YouTube's Terms of Use. This includes obtaining any necessary permissions from the original content creators before reproducing or distributing any content.

2.  **Content Ownership**: The transcripts generated by this tool are based on publicly available content from YouTube. You do not own the rights to the original video content or its transcripts. All copyrights and ownership rights remain with the original content creators.

3.  **Attribution**: This tool provides the URL of the original YouTube video for reference purposes. You are encouraged to include proper attribution when sharing the generated Text.

4.  **Limitations of Liability**: We disclaim any liability for the misuse of the content generated through this tool. By using this tool, you agree to hold us harmless from any claims or disputes arising from your use of the content.