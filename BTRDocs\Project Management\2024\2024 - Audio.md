

**Audio & Music Related Entries**

**2022**

*   *No specific details about the project's progress during this year were available in the extracted documentation.*

**2023**

*   *No specific details about the project's progress during this year were available in the extracted documentation.*

**April 10**

*   Inspired by x men sentinel fight, flashes of color and light in time with the music dark and violent

**April 12 / 13 / 14**

*   Polyrhythm shooting tied with music!

**April 15**

*   Adding koreographer to enemy twin snake boss does not work properly, requires further refinement
*   Should i have a speed dash that affects tempo / music?

**April 16**

*   Thinking about what i wrote here, in Mid march, again
    > Thinking about how I avoid making music for the game… It's more than just procrastination. Generating sounds is easier now than ever. Anybody can make music IMO. But what is the MEANING? This is what drives this game, and why I so rarely work on the music for it. Because all of these systems, the second to second gameplay, is what drives the sound and when that becomes clear, the sound follows. The sound informs too! But I’m so acquainted with making sounds, it doesn’t need to be actively worked on constantly. I can imagine my way through the part of the audio process. This is why I avoid writing music in general. What am I writing about? Its not always words, but a feeling, and a general vibe and notion that drives the creation. And through video games and interactivity, I'm looking for new sources of meaning to drive creation, and ultimately the experience. This idea also fuels the point of the game. I see rhythm games, and the promise of Rez, and I think - the sounds could be informed with more meaning. Different meanings. More focus. More than just easily fading between stems and battle tracks. Though these are useful and effective.

**April 22**

*   How to Make Things "POP" with Audio and Color
*   [Independent Games Summit: How to Make Things](https://gdcvault.com/play/1034550/Independent-Games-Summit-How-to)
    *   audio as the player-whisperer
        *   gun sound is powerful or gun is weak

**April 24**

*   At some point, bullets are getting stuck on kinematic, and arent able to move, that’s why i cant shoot properly at times.

Not sure what triggers this, but also trips up FMOD as well

**April 30**

*   Now working! Added Koreos for alternating patterns

**August 2**

*   Bounce sound could use more note choices, more of a sense of harmony and surprise? Drive some melody? Or refer to unfold Gabor Lazar for change ideas

**August 3/5**

*   Issues with FMOD that i’m addressing
*   Cant shoot in section after snake, Koreogrpaher issue?
    *   Beleive i fixed this once before
*   SNake gets hit sound, as well as Death sound needed
    *   Done! Needs adjustments but a good start
*   Section 3 music not looping properly

**August 6/7**

*   Also, working on FMOD
*   Improving some sounds
*   Upgraded to 2.03 for new features that may be useful, like multiband, frequency sidechain, etc
*   Some issues with hard coded fmod events pointing to the wrong things, fixing this.
*   Removing firing blasts as it seems unecessary

Enemy Lock on sound is not apparent enough, need something better
Improve with CLAP but maybe could be better

- Enemy Death creating stuck time rewind / pause - need to fix this
    - reverted fmod inspector assignment changes because something is breaking this, and i dont want to look into it too deeply

**August 14**

*   Thoughts on sound design
    *   [https://www.youtube.com/watch?v=oxeD8kuCT\_g](https://www.youtube.com/watch?v=oxeD8kuCT_g)
*   Use FilterFreak and Speakerphone for processing your SFX

Plan creatures as Blue / Red - Soft / Angry

*   Pre-attack sounds
    *   design by making the first bookend (front)
    *   Copy it and it can be your pre attack
*   Granulate - seems like a cool Granular synth

**August 16/17**

*   Section 3 music not looping properly

**August 19th**

*   Fixing looping issues with Section 3 - FMOD adjustments needed
*   Do these same fixes help with Shooting? It isnt workign for some reason

**August 26**

*   Stuck in rewind music phasem, need to address this

**August 30**

*   Some recommendations on Game Design

**January 17th**

*   Need to fix various small problems with game play, audio, that i thought were already fixed???

**February 13**

*  Hyperpassive as a rubric for the music and interactivity in Beat Remake

**March 26**

**Journal**

*   Thinking about how I avoid making music for the game… 
*   Its more than just procrastination
*   Generating sounds is easier now than ever. Anybody can make music IMO
*   But what is the MEANING
*   This is what drives this game, and why i so rarely work on the music for it.
*   Because all of these systems, the second to second gameplay, is what drives the sound
*   and when that becomes clear, the sound follows. 
*   The sound informs too! But I’m so acquainted with making sounds, it doesn’t need to be actively worked on constantly. I can imagine my way through the part of the audio process. 
*   This is why I avoid writing music in general. What am I writing about? Its not always words, but a feeling, and general vibe and notion that drives the creation. and through video games and interactivity im looking for new sources of meaning to drive creation, and ultimately the experience. 
*   This idea also fuels the point of the game. I see rhythm games, and the promise of Rez, and I think - the sounds could be informed with more meaning. Different meanings. More focus. 
*   More than just easily fading between stems and battle tracks. Though these are useful and effective.

**April 24th**

*   I SEE!
*   I accidentally deleted Perc!!! Thats what broke it! Just a dumb mistake in the Koreographer interface
*   Be aware of this when creating a new track / events

**May 14**

Design Fundamentals
    - think - you’re the star of a music video!
    - Playing to the music feels better, but if you miss rhythm you’re not really punished
Make success easy to understand
Showing the player that there is room for improvements
Did a bunch of edits to fix scene transition , music choice for scene switching, and other things
 
Time / music / health - All the same. Music dies when time stops!

**June 10**

*   Added more sounds and fix transisiton between levels

**June 13**

*   Need to never be locking on when moving into next level, and need to make sure it doesnt transition into a lock on section on next level start. need to investigate how to handle this.
*   FX for time slow / rewind glitch things. very lacking right now.
    -   what are the utitily of these? give more of a reason to reach for them during gameplay - think on scenarios
    -   can slow time initiate doppler on projectiles?

*   Revisit Idea

*   -   more rhythmic? Look into this if it feels necessary - may be other ways of implementing as well
        *   Example - enemies all shoot in a pattern but it’s controlled which and when. for example projectile manager is talking to enemies, controlling when each gets its chance to shoot, this is done in rhythm.

**June 16**

*   Addressed this with an alternate ‘shoot every loop’ pattern when i nthe lock state,

*   - alternate system was fmod callbacks, but that breaks koreographer

**June 18**

*   seems to be working well! but it might be interesting!

**June 25**

*   Fixed coreography issue with lack of shooting - just a missing track name

**June 11**

*   *Fixes transisiton between levels sounds*

**July 30**

*   Koreographer track issue was breaking the infinite areas

go over how that works again - unsure why this broke things

*   - i udnerstand the track it wanted wasnt there, but i dont know why im use on collection of tracks vs another, need to evaluate how all of that works again
        Transient / Body / Tail structure for sound clips may help with setup in FMOD

**2023**

**August 3/5**

* Fixed some issues with FMOD that i’m addressing

**August 5/6**

* *Added HBAO and changed Bakery settings for rendering*
* Not a real noticeable improvement, something to revisit later in optimizing pipeline
* Updating some of projectile logic but not fully working.
* *Adding animancer pro for better animation control in unity*
* Need a proper effect for ricochet - blast no longer works?

**August 7**

* Improving some sounds
* Upgraded to 2.03 for new features that may be useful, like multiband, frequency sidechain, etc
* Some issues with hard coded fmod events pointing to the wrong things, fixing this. 

**August 12**

* Also, trying to fix previous issue with getting locked in rewind time, unsure exactly how that’s happening. Possibly FMOD issue as well

**August 14**

* *Thoughts on sound design*

**August 26**

*   Stuck in rewind music phasem, need to address this
*   other action seem find, feels like its just the music?
    *   Updated Reach menu system, figure it all out 

**August 29**

*   *Fix for FMOD - to loop properly in section 3*
*   Transprent material in snake shooting section - why do bullets do this, i forget, not sure i like it
*   Locked state material defined on projectile - problem with this level is the scale of the projecitle makes it too big when locked, looks odd - Sclaed down from 10 to 3 currently

**August 29**

*   Revert FMOD assignment changes as they break it

**September 16**

* What to try / apply to optimize game? O1, or a1. If you use the first, it makes it hard to tell apart
* Highliters or VFX

* General errors-Any lock ons must be disabled before moving to next section or there are errors,In build, there are camera issue in both Snake Infinite sections.,

* All the sound effects should be consistent/not randomly picked
*  
* What to apply to optimize game? O1, or a1. If you use the first, it makes it hard to tell apart
* Add 10 sec to start menu that allows for easy access to game options and allows for quick scene switching
* 

*   Fix for build -

* What are the issues? How can you reproduce them?

*   
*   
* *What is the problem? What is the solution?
* **September 25**

## From: December 11

*   Idea- Shrink and grow cursor to indicate target locking 
*   [Build Optimization](Dec%2011%20Build%20Optimization.md)

**December 18/19**

*   fix homing for enemy shot projectiles - not homing properly
*   Moving movement system to jobs, some considerations

**December 21**

*   [Google Thinking _Scripts Reccommendations](Beat%20Game%20Notion/Learning%20and%20Research/Code%20Optimization/Google%20Thinking%20_Scripts%20Reccommendations.md)
*   Sorted some build issues, but need to rebuild render layers and assess, get it all working properly again

**December 4**

*   Snake shouldn’t do hit anaimation and then death animation, just death
   *Done!

**Dec 26**

*The changes appear to be focused on optimization, visual improvements, and gameplay refinements, with a particular emphasis on shader and graphics pipeline updates.

##   Project settings:

*   EditorBuildSettings.asset
*   GraphicsSettings.asset
*   ProjectSettings.asset
*   QualitySettings.asset
*   URPProjectSettings.asset

**2024**

**Janaury 17th**

*  Look into music and lighting.
* Need to organize my own scripts so I can better use Cursor to provide feedback. Put them all in one folder, so they can be looked at

**Janary 24th**

* - Filter to audio no longer applied when hitting start
- Fixed, all exists on Pause Menu Manager now

**Janurary 30th**

* - Add a subtle glow/bloom effect that increases as projectiles get closer
   - Add leading lines or subtle visual guides that appear briefly when projectiles spawn
   - Add a brief time-freeze frame when projectiles first spawn
   - Add a "danger zone" visualization that shows where projectiles will be in the next few frames
   - Include a brief warning period before projectiles start moving

**Feburary 13**

* *Spatial Communication in Level Design*

**July 24**

*   *Getting stuck in rewind time state occasionally, need to address this*

**July 29**

* Section sounds into Sub / LOw / Mids / Highs and consider what type of sounds get what
* Bus Structure
   - Define busses for general soudn types, and properly sidechain them for what needs to be highlighted
Transient / Body / Tail structure for sound clips may help with setup in FMOD
* Use Spatializer to give certain sounds bigger impact

**August 14**

* Thoughts on sound design

##    August 28/29
*   Did not make enough updates over the past few days, not sure what that says, take a look into this. 
*   Going to attempt to sort out a few issues with FMOD, then take a look to the world or build - for the weekend!
*   It does look like my previous approach to addressing that broken shader, does hold. Need to keep checking and verify how this effects performance.
*   Getting sounds working
*   Add the sound I wanted in a more proper manner, does not seem to do anything
*   Look at how FMOD handles pitch shift and all that
*   The problem? I think that it's not using the project parameter, and is getting set to 0. What happens if I remove the volume setting and all that??

1.  Add 10 sec to start menu that allows for easy access to game options and allows for quick scene switching
2.  

    *   Should I have two states, playing, pause, so no conflicts?
    *   

* **Audio/Sound Issues and Resolutions:**

* Fixed FMOD to loop properly in section 3
* Fix FMOD: Stuck in rewind music phase, need to address this

