# 2022 - Tech Issues - Summary

This document summarizes the tech issues and debugging efforts from 2022, focusing on recurring problems and key fixes.

## Key Summary Points:

*   **Wave Spawner Issues (April):** Persistent problems with the wave spawner, including waves not advancing and errors upon spawning enemies. Debugging and refinement of the wave spawning logic were ongoing concerns.
*   **Enemy and Projectile Instantiation (April, Feb, May):**  Issues with enemy reuse, incorrect spawning, and projectiles behaving erratically (double shooting, wrong directions, looping). Particle systems attached to projectiles caused errors, suggesting a need to rethink particle implementation.
*   **Bullet Behavior and Lifetime (April, Feb, May):** Recurring bugs related to bullets getting stuck, rotating with enemies, circling back, and having negative lifetimes. Investigated pausing lifetime when locked and proper state management for bullets (Launching vs. Locked).
*   **Crosshair and Locking System Bugs (April, Feb):** Errors in the Crosshair class, particularly with the LockedList being out of sync with the visual UI. Problems launching inactive bullets and managing locked states.
*   **Chrono/Time Rewind Issues (April, Feb):** Time rewind mechanic breaking randomly and persisting issues with Chrono in specific level tests, indicating instability in time-related mechanics.
*   **A\* Pathfinding Problems (April, March, May):** Enemy AI pathfinding using A\* breaking at times, requiring fixes to enemy plane configurations and occasional reverts to older Unity versions. Enemies getting stuck, possibly due to building collisions.
*   **FMOD and Koreographer Integration (March, May):** Challenges integrating FMOD and Koreographer, including enemies not re-registering attack capabilities with tracks and collision issues affecting song section changes. FMOD global parameters showing as unavailable in Unity.
*   **Visual and Rendering Errors (April, May, June):** Particle system errors related to mesh shapes, Quibli shader outline alpha issues, transparency problems with locked bullets, LOD group crossfade issues, and render camera breakdowns.

This summary highlights a year dominated by debugging core mechanics, particularly around spawning, projectile behavior, and AI pathfinding. Many issues point to state management problems, especially with bullets and enemies, and integration challenges with external tools like FMOD and Koreographer. Visual and rendering glitches also required attention, indicating a broad spectrum of technical challenges addressed throughout 2022.