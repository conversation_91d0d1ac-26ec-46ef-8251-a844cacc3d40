# Summary of 2020 - 2021 Design & Mechanics.md

This file documents the design and mechanics development from March 2020 to January 2021. It covers a wide range of topics, primarily focusing on core gameplay mechanics, enemy AI, level design considerations, and puzzle integration. Key areas include:

- **Lock-on and Shooting Mechanics:** Refining the lock-on system, implementing bullet pooling, and addressing issues with target tracking and bullet behavior.
- **Enemy AI and Movement:** Developing enemy behaviors, including basic enemy AI, pathfinding with A*, and integration with Behavior Designer for more complex tactical behaviors. Experimenting with different enemy movement patterns and formations.
- **Aiming and Controls:** Iterating on aiming systems, including reticle design, raycasting, and control schemes (keyboard, mouse, and gamepad). Addressing aiming inaccuracies and rotation issues.
- **Level Design and Structure:** Greyboxing levels, experimenting with level transitions, and considering different level design approaches (linear vs. non-linear). Integrating puzzle elements like Tetris and shape-based puzzles.
- **Time Manipulation Mechanics:** Implementing and refining time-rewind mechanics using Chronos, and exploring its integration with shooting and gameplay.
- **Visual and Gameplay Ideas:** Brainstorming gameplay ideas, such as shape-based attacks, combo multipliers, and visual feedback (particle effects, line renderers).
- **Optimization Considerations:**  Briefly touching on performance considerations related to level design and enemy counts.
- **Puzzle Integration:** Exploring the integration of puzzle mechanics, specifically Tetris-like gameplay and shape-based puzzles, into the core shooter gameplay.
- **Camera and Perspective:** Experimenting with camera angles and perspective shifts, drawing inspiration from games like Panzer Dragoon and Fargo dePalma.
- **Collision and Physics Issues:** Debugging and resolving issues related to bullet collisions, object pooling, and physics interactions.