Updated project to Unity 6 Render Graph
No longer have JPG Bitcrunch, but working on alternatives
Otherwise, most things transfer fine. Need to A/B visuals to tweak appropriately

Main areas of focus, besides datamosh
- reducing CPU time, its the current bottleneck to high frame rates
- fixing hit detection / player taking damage. Hit detection might be fine, but may be the player actually taking damage is the issue. Investigating, made debug tools for this

Should throw more tools under BTR name menu, easier to find for this project. 

