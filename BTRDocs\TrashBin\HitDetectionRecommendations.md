# Projectile Hit Detection - Summary & Recommendations

## Current Approach Issues

Our current approach uses multiple redundant systems that may be interfering with each other:

1. **Multiple Hit Detection Methods:**
   - Physics-based OnTriggerEnter
   - SphereCast-based sweeps
   - Proximity-based checks
   - JobSystem movement + proximity detection
   - Spatial grid verification

2. **Critical Problems:**
   - Projectiles tunneling through colliders
   - Inconsistent hit registration
   - Multiple projectile hits not registering properly
   - Racing damage conditions and thread-safety issues

## Recommended Solution

We recommend replacing the current multi-layered approach with a **single, authoritative** hit detection system based on a fixed-timestep simulation with position history for backtracking. This offers:

1. **Better Accuracy:** Sub-step movement prevents tunneling
2. **Simpler Design:** Single source of truth for hit detection
3. **More Robust:** Less prone to race conditions and thread safety issues
4. **Performance:** Scaled computation based on projectile speed and distance

## Implementation Steps

1. **Phase 1: Create a New System (1-2 days)**
   - Implement the `EnhancedProjectileCollisionSystem` from the example
   - Set up event hooks in ProjectileManager
   - Add debug visualization for testing

2. **Phase 2: Transitional Testing (1 day)**
   - Run both systems side by side with logging
   - Compare hit detection rates and accuracy
   - Verify that the new system catches hits missed by the current system

3. **Phase 3: Remove Old Systems (1 day)**
   - Disable redundant detection methods
   - Remove spatial grid verification for hits
   - Keep core ProjectileManager functionality
   - Simplify ProjectileStateBased

## Key Technical Improvements

1. **Fixed-Timestep Simulation:**
   ```csharp
   // Use smaller steps for high-speed projectiles
   float subStepDistance = Mathf.Min(0.005f * velocity.magnitude, remainingDistance);
   Vector3 nextPos = currentPos + direction * subStepDistance;
   
   // Check collision at each step
   bool hit = CheckLineSphereIntersection(currentPos, nextPos, playerPos, radius);
   ```

2. **Position History & Backtracking:**
   ```csharp
   // Store position history
   projectilePositionHistory[id].Enqueue(currentPos);
   
   // When player moves, check if any projectiles should have hit
   for (int i = 0; i < positions.Length - 1; i++) {
       if (LineIntersectsSphere(positions[i], positions[i+1], playerPos, radius)) {
           RegisterHit(projectile);
       }
   }
   ```

3. **Direct Line-Sphere Intersection:**
   ```csharp
   // More accurate than raycasts for this specific use case
   bool CheckLineSphereIntersection(Vector3 lineStart, Vector3 lineEnd, 
                                   Vector3 sphereCenter, float radius) {
       // Calculate closest point on line segment to sphere
       // ...
       
       // Check if distance is less than radius
       return distanceToSphere <= radius;
   }
   ```

## Success Metrics

- **Hit Registration Rate:** Should approach 100% for all projectiles
- **Visual Consistency:** Visual hits should always register as damage
- **Multiple Hit Handling:** Any number of simultaneous hits should register correctly
- **Performance:** Better or equal to current system

## Integration Notes

- Continue using the job system for projectile movement
- Keep using Unity's physics for general collisions
- Use this specialized system only for projectile-player hit detection
- Consider adding similar specialized hit detection for player shots hitting enemies

## Progress Tracking

Create a small testing scene that spawns many projectiles at different speeds approaching the player, and measure hit registration rates before and after implementation. 