---
title: Unity Stats and Modifiers Implementation
tags: [Unity, Stats, Modifiers, DesignPatterns]
date: 2025-01-20
---

# [[Unity Stats and Modifiers Implementation]]

## [[Overview]]
This implementation uses the Broker Chain pattern to create a flexible stats and modifiers system, combining Chain of Responsibility and Mediator patterns. It's useful for handling pickups, equipment, environment buffs, and temporary effects.

## [[Core Components]]

### [[Stats Class]]
```csharp
public class Stats {
    private BaseStats baseStats;
    private StatsMediator mediator;
    
    public int Attack {
        get {
            var query = new Query(StatType.Attack, baseStats.Attack);
            mediator.PerformQuery(query);
            return query.Value;
        }
    }
    
    // Similar implementation for Defense
}
```

### [[Stats Mediator]]
```csharp
public class StatsMediator {
    private LinkedList<StatModifier> modifiers = new();
    private event EventHandler<Query> queries;
    
    public void PerformQuery(Query query) {
        queries?.Invoke(this, query);
    }
    
    public void AddModifier(StatModifier modifier) {
        modifiers.AddLast(modifier);
        queries += modifier.Handle;
    }
}
```

### [[Stat Modifier]]
```csharp
public abstract class StatModifier : IDisposable {
    private bool markedForRemoval;
    private CountdownTimer timer;
    
    protected StatModifier(float duration) {
        if (duration > 0) {
            timer = new CountdownTimer(duration);
            timer.OnTimerStop += Dispose;
        }
    }
    
    public abstract void Handle(object sender, Query query);
    
    public void Dispose() {
        markedForRemoval = true;
    }
}
```

## [[Implementation Details]]

### [[Pickup System]]
```csharp
public class StatPickup : Pickup {
    [SerializeField] private StatType type;
    [SerializeField] private float value;
    [SerializeField] private float duration;
    
    protected override void ApplyPickupEffect(Entity entity) {
        var modifier = new BasicStatModifier(
            type, 
            duration, 
            value => value + this.value
        );
        entity.Stats.AddModifier(modifier);
    }
}
```

### [[Entity Class]]
```csharp
public class Entity : MonoBehaviour, IVisitable {
    public Stats Stats { get; private set; }
    
    void Awake() {
        Stats = new Stats(new StatsMediator(), baseStats);
    }
    
    void Update() {
        Stats.Update(Time.deltaTime);
    }
    
    public void Accept(IVisitor visitor) {
        visitor.Visit(this);
    }
}
```

## [[Best Practices]]
1. Use clear naming conventions for stats and modifiers
2. Implement proper cleanup for timed effects
3. Use event-based systems for modifier application
4. Consider performance impact of complex modifier chains
5. Implement proper serialization for save/load systems

## [[Additional Resources]]
- [[Design Patterns in Unity]]
- [[Stat System Architectures]]
- [[Modifier Implementation Patterns]]
- [Broker Chain Pattern Documentation](https://example.com/broker-chain)
- [Unity Stats System Guide](https://example.com/unity-stats)