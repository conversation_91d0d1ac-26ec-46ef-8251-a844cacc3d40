# Feb 8

Adjusted LockOnEnemy Prefab to Cube

Changing music for level to something lighter to match brighter aesthetic

Referening <PERSON> Gamble - HyperPassive

Stuggling to get the sounds i think would work for Level Test 5

Looking at Space Vortex sounds for ref

Idea: Platforms released by Object Particle Spawner - Testing this out!

Works! Now how to get forward movement matched to tempo? 

Adjust Forward movement so it’s a more reasonable value 

Object Particle Spawner working! Trying to do new music made me think - i really need to do 

DOing tutorial on FMOD Setup for this

Did Karting + FMOD Integration Tutorial - useful!

Add FMOD Emitters for sound, tie to FMOD hierarchy and features basically

Now doing Koreographer tutorial on this

 FMOD / Koreo integration - Need to enable Unity Audio for editing files in Koreographer

Important to to NOT disable unity audio until Builds!

Each Koreography is tied to one audio file, then you can add tracks to that Koreography for different timed events

Imp Question - How to dictate which events show up when choosing Event ID???

Koreographer.Instance.UnregisterForAllEvents(this)

- need this on enemies, bullets, etc?
- Or let them stay registered from startup?

Have old version of integration package so implementation is on hold. Waiting for that!

A little later then 40 min into tutorial video

General FMOD handy tutorial for triggered sounds with loops - too advanced! GOing from the start with these

[https://www.youtube.com/watch?v=7PpSYcigCUQ&list=PLp4vT3ssm5SUqwHvbverTlYu4LwwkWHek](https://www.youtube.com/watch?v=7PpSYcigCUQ&list=PLp4vT3ssm5SUqwHvbverTlYu4LwwkWHek)

Trigger arrow and setting probability of event occuring - interesting!

Continue form Part 2 - [https://www.youtube.com/watch?v=AkKxCVnMM4E&list=PLp4vT3ssm5SUqwHvbverTlYu4LwwkWHek&index=2](https://www.youtube.com/watch?v=AkKxCVnMM4E&list=PLp4vT3ssm5SUqwHvbverTlYu4LwwkWHek&index=2)

Try Kart project with 2.1 version of FMOD - should be able to zip through quick

Build Note - enemies exist too long after death - fix this