# March 7

Disabled Auto Refresh

Trying Hot Reload for unity! Setting up properly

CTRL + R for a manual Reload - necessary sometimes

Added Master volume control to Settings 

Added code for Unity Debug console visibility on/off

- Not sure that it works, needs testing in build

Added ability to see come control mapper features in the UI Controller settings windows

- Need to adjust to make coherent with UI design

[What Makes Good Rhythm Game UX?](https://www.youtube.com/watch?v=nRJ4iVjIMAc)

Rhythm games require great UX - can say this about my portfolio!

Focused on a genre that requires UX more than most!

Some ideas but nothing huge at the moment

Looking at the following video for ideas on fixing aiming

[Accurate Aiming Options Discussed & Implemented | Gun Series 7 | Unity Tutorial](https://www.youtube.com/watch?v=x8ECpNWMmag)

Unsure that it helps but may bring implementation into project for some things

Managed to get rotation around sphere working through fake gravity unity video

[How to Walk Around a Sphere (Walk on Planet) in Unity 3D](https://www.youtube.com/watch?v=cH4oBoXkE00)

Issue with Z value of Shooting Game Object rotating, can’t seem to make it stay locked 

Changing Shooting point of Rayspawn for Bullets AND enemies to be from reticle game object isntead of further back rayspawns

Put Rayspawn game object sunder reticle, using them in shooting, and it’s working MUCH better!

DO I need cone anymore? Disabling