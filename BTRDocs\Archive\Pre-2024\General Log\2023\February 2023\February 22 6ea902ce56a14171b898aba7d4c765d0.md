# February 22

Thinking about structure of game

Music as a representation of systems

Geometry - Enemy Shapes

![Untitled](February%2022%206ea902ce56a14171b898aba7d4c765d0/Untitled.png)

Combine these + behaviour in different ways

Recursion - Serpinski Triangle!

Recursion in Music - [https://www.youtube.com/watch?v=4ydZrKcvejU](https://www.youtube.com/watch?v=4ydZrKcvejU)

Use imagery such a triptych?

Geometry of music?

![Untitled](February%2022%206ea902ce56a14171b898aba7d4c765d0/Untitled%201.png)

Like notes of a chord or overlapping rhythms

A system 

Eileen - “different enemies could be variations on a theme (musically and visually) that maybe represent parts of the player character’s journey through life (and death?)”

[Why Is It So Hard To Make Games On Rails?](https://www.youtube.com/watch?v=mUjZUPPrz-o)

Attention Economy

- No movement - what replaces this?

Spectacle can replace free movement

- visually and new patterns emerging constantly

Pacing is key!

What to learn from Starfox 64?

- secret paths and goals to discover
- dodge mechanic?
- variety of paths to reach end
- combo and high score options
- power ups
- stage medals - acheive specific things like rollerdrome?

Collect coins or powerups or something along the way?

Is there any way to controll HOW many bullets you shoot at a time?

Types of Progression

Intrinsic progression

- bring your own ability to the game
- tough for beginners
- Neon white brings speed running to beginners?

Extrinsic Progression

- set goals within the game - obvious ones!
- Level up system is an example
- cosmetic items are an example

Be careful - don’t want things to feel like a treadmill!

Use skill trees or advancement to increase musical palette? 

Bullet types for different enemies? Ikaruga method?

Souls like method for score - retrieve your score when you die and retry level?

- invincibility when you find your corpse?

motifs 

- elemental systems

what system can i use the re inforces game mechanics? what would help define enemies better? 

How do my elements play off each other?

light / dark - holy / death 

Rock paper sciccors loop - would this be good?

- Light / Dark / Digital

Enemy - # of points is # of bullets to kill

Combined designs are the addition of these things

This also defines their shooting pattern

but NOT movement pattern? 

Keep on this - look at how systems could work / how they integrate with ideas

rotation / solar system models

![Untitled](February%2022%206ea902ce56a14171b898aba7d4c765d0/Untitled%202.png)

![Untitled](February%2022%206ea902ce56a14171b898aba7d4c765d0/Untitled%203.png)

![Untitled](February%2022%206ea902ce56a14171b898aba7d4c765d0/Untitled%204.png)

Tower of Hanoi

**What are the points in more jounrey that are interesting that I should follow?**

**That I never would have written?** 

Look into Object Particle Spawner and shoot groups of bullets using this

Shmup Patterns

[Shmup Principles](February%2022%206ea902ce56a14171b898aba7d4c765d0/Shmup%20Principles%20f8fd6182542e41b2aeb09314fe226b11.md)

AM thinking very mechanically - functionally

Is this a bad thing?

Watch Level Design video

Music and visuals as language

Music + Geometry 

Enemies are mixes of basic geometry, and produce different projectiles based on this 

Pattern Recognition

“an attempt to explain the existence of [diverse](https://www.merriam-webster.com/dictionary/diverse) human languages”

- music as language

Tower as pure sound

Crashes down - divided everywhere

According to Genesis, the Babylonians wanted to make a name for themselves by building a mighty city and a [tower](https://www.britannica.com/technology/tower) “with its top in the heavens.” God disrupted the work by so confusing the [language](https://www.britannica.com/topic/language)
 of the workers that they could no longer understand one another. The city was never completed, and the people were dispersed over the face of the earth.

Tower of Babel 

Who are the Ophanim in this story?

They are the base of the throne of god - or move it around 

Tower of Babylon - what does it mean? 

[https://en.wikipedia.org/wiki/Geocentric_model](https://en.wikipedia.org/wiki/Geocentric_model)

[https://en.wikipedia.org/wiki/Celestial_spheres](https://en.wikipedia.org/wiki/Celestial_spheres)

> The Tower of the biblical myth is perhaps the grandest of these cultural reminders. This is clearly how Chiang interprets it. The workers, assembled from many foreign places, share not just a language but a devotion to YHWH. The tower itself is being built to honour him. It is the physical representation of the spiritual search for YHWH that is implicit in its changing content. Humanity is attempting to reach YHWH not out of arrogance but as a matter of devotion.
> 

> Of course, human understanding cannot reach to the exalted heights of YHWH’s existence. But only when their search stops is the covenant in jeopardy. In their continuing physical and intellectual quest for the ultimate reality human beings can put themselves in awkward and life-threatening situations. No matter what happens, however, YHWH can be relied upon not to break his covenant promise - never again will he use water against them, nor let them use it against themselves.
> 

Is this recursive? Is the journey - not the destination - something we can consider recursively? 

Like the idea of making the perfect music - as if there was some perfect sound that would feel like touching god?

building up the tour, it falls, levels are remixed and shapes are remixed

enemies are remixed