# Summary of 2023 - Tooling & Workflow.md

This file documents tooling and workflow changes, experiments, and updates throughout 2023. Key points include:

*   **Asset Extraction & Shader Tools:** Experimentation with AssetStudio GUI and Steam Asset Ripper for extracting assets (shaders, animations) from other games (Pilgrimage, Sayonara Wild Hearts).
*   **3D Modeling & Blender:** Adoption of Blender for 3D modeling tasks. Discovery and use of Blender's Split tool and Random Flow addon. Focus on Blender to Unity pipeline and integrating Mixamo animations.
*   **Unity Asset Store & Packages:** Integration and testing of various Unity Asset Store packages: Volumetric Fog (BOXOPHOBIC, Kronnect's), Beautify, SRDebugger, Fast Glitch, Quibli, GPU Instancer, Buto, Ultimate Spawner, Curvy, UModeler X, Magic Light Probes, Cinemachine, A* Pathfinding, and more. Reinstalling and updating packages to address issues.
*   **Debugging & Optimization Tools:**  Implementation of SRDebugger for on-device debugging, video gameplay recorder for bug tracking, and Asset Hunter for project slimming. Research into shader variant compilation and preloading for optimization.
*   **Workflow Experiments:** Trying Unity's Hot Reload feature, experimenting with different control schemes, and testing prefab generators for level design.
*   **Render Pipeline & Graphics Settings:** Switching between Gamma and Linear color space, experimenting with DirectX 11 to resolve memory leaks, and investigating different render pipelines (URP) and lighting setups (Bakery).
*   **Code Refactoring & Scripting:** Refactoring and cleaning up Crosshair, Enemy Basic Setup, Shooter Movement, and Projectile setup scripts. Implementing screen resolution scripts and controller-based UI navigation.
*   **ChatGPT & AI Tools:** Using ChatGPT (likely version 3.5) for problem-solving and potentially code generation. Experimenting with Unity Muse for suggestions and UModeler X for AI texturing (though Blender is considered as an alternative).
*   **Version Control & Project Management:** While not explicitly stated, the chronological nature of the notes suggests a time-based logging of progress and issues, implicitly serving as a project management and version control aid.

The "Tooling & Workflow" document illustrates a proactive approach to improving the development pipeline through tool adoption, workflow optimization, and continuous learning. It reflects a cycle of experimentation, integration, and troubleshooting of various tools and techniques to enhance both visual quality and development efficiency.