# Things to do

- Try Flat pack to achieve Channel 21-like shading and colours
- Enemy spawning approaches? Cuze asset, possibly others
- procedural animation in enemies - is this worth doing?
- enemy ai - easy ways to handle this?
- generating level elements - how to do abstract / interesting geometry
- exploring possibilities with joost shader / other amplify shader possibilties
- game feel - targetting / lock on / shooting / movement of crosshair
- Better Missile Lock system found? LOOK INTO USING THIS?
- uPattern for generating interesting Geometry - how to animate?
- Look into animation Rigging options
- **OPTIMIZE - object pooling? What else?**
- 

GROWING SQUARES LEVEL - have things grow - camera shake when enemies beaten 

Move to another set of growing square and enemies.