---
title: Twin Snake Boss
date: 2024-01-16
tags: [enemies, boss, twin-snake, technical]
aliases: [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>]
---

# Twin Snake Boss Documentation

## Overview
The Twin Snake Boss is an advanced boss type featuring two synchronized snakes with independent health pools, phase-based combat, and music-synchronized attacks. This document details its implementation and configuration.

## Component Integration
```mermaid
graph TD
    A[TwinBossControllerBehavior] --> B[Left Snake]
    A --> C[Right Snake]
    B --> D[Timeline]
    B --> E[Animator]
    B --> F[Eye Colliders]
    C --> G[Timeline]
    C --> H[Animator]
    C --> I[Eye Colliders]
    A --> J[ProjectileCombatBehavior]
    A --> K[MusicSyncedCombatBehavior]
    A --> L[VisualEffectBehavior]
```

## Configuration

### TwinSnakeBossConfiguration
```yaml
Core Settings:
  startHealth: 100
  rewindTimeScale: -2.0

Snake Settings:
  leftSnake:
    modelPrefab: GameObject
    animatorController: RuntimeAnimatorController
    position: Vector3
    rotation: Vector3
    clockKey: "LeftSnake"
    phaseHealthThresholds: float[]
    phaseAnimationTriggers: string[]
    phaseSoundEvents: EventReference[]
    eyeColliderNames: string[]
  
  rightSnake:
    # Same structure as leftSnake

Combat Settings:
  shootEventIDs: string[]
  shootDelay: 0.5

Animation:
  deathTrigger: "Death"

Audio Events:
  defeatedEvent: string
  snakeDefeatedEvent: string
  phaseTransitionEvent: string
```

## Required Components

### Core Components
1. TwinBossControllerBehavior
   - Manages both snake instances
   - Handles health and phase transitions
   - Coordinates attacks and effects

2. Per Snake Instance
   - Timeline for time manipulation
   - Animator for visual state
   - Eye colliders for targeting
   - Clock for time control

### Support Components
1. ProjectileCombatBehavior
   - Handles projectile spawning
   - Manages attack patterns
   - Controls shooting timing

2. MusicSyncedCombatBehavior
   - Synchronizes attacks with music
   - Manages Koreographer events
   - Controls attack timing

3. VisualEffectBehavior
   - Manages visual effects
   - Handles effect pooling
   - Controls effect lifecycle

## Behavior Implementation

### Initialization
```csharp
void Initialize(Transform transform, EnemyConfiguration config)
{
    // Validate configuration
    // Create snake instances
    // Setup components
    // Register events
}
```

### Snake Instance Management
```csharp
private SnakeInstance CreateSnakeInstance(SnakeConfiguration config, bool isLeft)
{
    // Create base object
    // Setup components
    // Initialize state
    return instance;
}
```

### Phase Management
```csharp
private void CheckPhaseTransition(int snakeIndex)
{
    // Calculate health percentage
    // Determine new phase
    // Trigger phase change events
    // Update animations and effects
}
```

### Death Handling
```csharp
private IEnumerator HandleSnakeDeath(int snakeIndex)
{
    // Play death animation
    // Trigger events
    // Wait for animation
    // Cleanup
}
```

## Testing Checklist

### Setup Validation
- [ ] Configuration properly loaded
- [ ] Both snakes instantiated correctly
- [ ] Components initialized properly
- [ ] Events registered successfully

### Combat Testing
- [ ] Independent health tracking works
- [ ] Phase transitions trigger correctly
- [ ] Music synchronization functions
- [ ] Projectile patterns work
- [ ] Eye targeting system functions

### Visual Verification
- [ ] Animations play correctly
- [ ] Effects spawn properly
- [ ] Phase transitions visible
- [ ] Death sequence works

### Technical Validation
- [ ] Time manipulation works
- [ ] Pooling system functions
- [ ] Cleanup handles properly
- [ ] Memory usage acceptable

## Best Practices

### Configuration
- Set reasonable health thresholds
- Configure appropriate time scales
- Test all animation triggers
- Validate sound events

### Implementation
- Use proper cleanup patterns
- Handle null checks
- Implement proper pooling
- Follow event patterns

### Performance
- Monitor animation complexity
- Optimize effect usage
- Cache component references
- Use efficient data structures

## Known Issues
- None currently documented

## Future Improvements
- Add more varied attack patterns
- Implement snake coordination mechanics
- Add visual tells for phase transitions
- Enhance music synchronization 