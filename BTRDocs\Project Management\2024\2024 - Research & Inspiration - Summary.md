**Summary of Research & Inspiration Related Entries in 2024**

**2023 Research & Inspiration Themes:**

*   **Boss Design:** Explored boss encounter designs with time manipulation and Fresnel effects.
*   **Development Approach:** Considered iterative development and version control strategies.
*   **Sound Design:** Researched sound design principles, including "Bookends and Highway method," SFX processing with FilterFreak and Speakerphone, creature sound planning (Blue/Red - Soft/Angry), pre-attack sounds, and granular synthesis.
*   **Game Design Recommendations:** General game design inspiration from GDC talks, Designer Notes Podcast, Google Scholar (game studies), and "Abstract: The Art of Design."
*   **Level Design Variation:** Studied design principles for level variation.
*   **"POP" Audio & Color:** Investigated "POP" audio and color techniques from GDC talks, focusing on themes, essences, patterns, and color saturation.

**2024 Research & Inspiration Themes:**

*   **Auto-Aim Design:** Referenced article on creating "fair" auto-aim systems in robot shooters.
*   **Art Pipeline:** Explored 3DS Max to Blender import/export workflow.
*   **Enemy Design:** Used Gravity Rush enemies as inspiration.
*   **UI/Text in Unity:** Researched applying stylized text and UI in Unity, potentially inspired by Ace Combat.
*   **Game Development Resources (Links):** Compiled a list of game development resources, including:
    *   Code repositories (Outer Wilds Black Hole, QuickSave, Cathode Retro, IRIS, BetterUnity, BasicEventBus).
    *   Unity best practices and optimization guides (Unity blog, OccaSoftware, Jason Booth).
    *   Game design and architecture resources (RGD Workshop, Unity architecture patterns, Service Locator, Flyweight pattern).
    *   VFX and shader graph tutorials (Unity VFX Graph, Shader Graph tutorials, Twisted Corridor effect, Shockwave shader, Portal effect).
    *   Sound design resources (Scott Game Sounds, sound design techniques).
    *   General game development advice (Solo development Reddit thread, 17 game dev tips, "Just Do A Little" Reddit thread, Clean Code for Cognitive Load).
    *   Visual inspiration (Harry Clarke illustrations, Shmup visual design, Evangelion cinematography, Wipeout Futurism, hazylevels, Warped Imagination, Eric Wang VFX).
    *   Marketing and business insights (Game marketing, Mirza Beig on Twitter).
    *   Project management and organization (Game Design Resources spreadsheet, Rational Game and Level Design).
    *   Animation and Blender tools (Animancer, Practical Blender generators).
    *   Rhythm game architecture discussion.
    *   Unity tips and tricks (Unknown Unity tips, Extension Methods, Singletons, Stencil buffer tutorial, URP updates, Bloom effects).
*   **Ophanim & SDF Particles:** Researched "Ring World Using Random Flow" for Ophanim design and SDF particle effects implementation in Unity VFX Graph.
*   **Game Design Resources Spreadsheet:** Referenced a comprehensive game design resource spreadsheet.
*   **Unity Optimization & Programming:** Followed SwiftRoll Medium articles for Unity optimization and programming tips.
*   **Enemy Character Inspiration:** Used "A multitude of Empty Slots (for Comissions) on Twitter / X" for enemy character inspiration.
*   **DOTS Conversion (Projectiles):** Considered converting projectile system to DOTS, referencing a YouTube tutorial.
*   **External Inspiration (Feb 13th):** Broadly referenced external sources for game design, architecture, VFX, and Blender inspiration, including specific YouTube videos and articles on:
    *   Hyperpassive music/interactivity rubric.
    *   Architecture and VFX breakdowns.
    *   Spatial Communication in Level Design.
    *   Unity VFX Graph ribbons and balls.
    *   Simple Unity game architecture tips.
    *   Practical Blender generators.
    *   Game programming patterns.
*   **Sin & Punishment:** Considered "Sin & Punishment" for attach phase ideas in enemy design.

**Overall Theme:**

*   Extensive research across various domains: game design, sound design, visual style, technical implementation, optimization, and project management. Heavy reliance on online resources, particularly YouTube tutorials, Twitter, GitHub repositories, and articles. Focus on learning from existing games, industry experts, and online communities to improve the project.