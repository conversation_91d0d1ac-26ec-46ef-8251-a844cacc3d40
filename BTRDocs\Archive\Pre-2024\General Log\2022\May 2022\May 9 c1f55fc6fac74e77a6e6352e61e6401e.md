# May 9

Unity UI in 2021 LTS - Flag this as a good thing to use? Seeing some positive coverage of it

[https://www.youtube.com/watch?v=EVdtUPnl3Do](https://www.youtube.com/watch?v=EVdtUPnl3Do)

Pause Screen 

A good way to start on menu’s and other needed game elements?

Use new Unity UI Builder for this as well?

Also need some kind of YOU DIED screen - and music section!

IDEA - game too hard initially, coming back exposes new parts to the music and game - is this a motivator??? Does this signal to the play anything interesting / motivational?

Thinking on big issues to tackle

Lock on Opacity needs to be fixed, tackling this now

Possibly acheive this using Alpha Clipping in URP??

Seems to be the trick! Looking like I’ll need to 

Orignally did this

![Untitled](May%209%20c1f55fc6fac74e77a6e6352e61e6401e/Untitled.png)

Looking at Shader Graph options

[https://www.youtube.com/watch?v=Rn_yJ516dVQ](https://www.youtube.com/watch?v=Rn_yJ516dVQ)

This may be a good thing to follow up on

DOFade works when surface type is transparent / blending mode is alpha

![Untitled](May%209%20c1f55fc6fac74e77a6e6352e61e6401e/Untitled%201.png)

DOFade actually works with Quibi shader! It’s just the outline that is the problem

Can I disable outline through script?

Looks like setting width is the trick that actually works!

No it doesn’t ☹️

In script this attribute is dictated as 

[Toggle(DR_OUTLINE_ON)] _OutlineEnabled("Enable Outline", Int) = 0

Also a section about 

![Untitled](May%209%20c1f55fc6fac74e77a6e6352e61e6401e/Untitled%202.png)

So how to disable? Int?

Using Flatkit instead!

Can set scale!

myMaterial.SetFloat("_OutlineScale", 0f);

Turned on Preload sample data for FMOD

Really need to go throw Projectile logic again to see what is working and what isnt. Can definitely be refined 

Bullet shoot from wrong side of enemy often? what is going on here?

Levels! Scene changes! refine