---
title: Phased Enemy Documentation
date: 2024-01-17
tags: [enemies, boss, phased, limb-damage]
aliases: [PhasedE<PERSON>my, BossEnemy]
---

# Phased Enemy System

## Overview
The Phased Enemy system provides a framework for creating complex boss-type enemies with multiple phases and limb-based damage mechanics. Each phase can have unique attack patterns, vulnerable points, and visual effects.

## Core Components

### EnemyPhaseCore
Extends the base [[EnemyCore]] with phase management capabilities:
- Phase state machine
- Health-based phase transitions
- Phase-specific attack pattern management
- Invulnerability during transitions
- Visual effect integration

### LimbDamageBehavior
Handles limb-based damage mechanics:
- Per-limb health tracking
- Visual indicators for vulnerable parts
- Phase-specific vulnerability states
- Persistent limb damage across phases
- Effect spawning for limb damage/destruction

## Configuration

### PhasedEnemyConfiguration
ScriptableObject that extends EnemyConfiguration with phase-specific settings:

```csharp
[CreateAssetMenu(menuName = "BTR/Enemy/Phased Enemy Configuration")]
public class PhasedEnemyConfiguration : EnemyConfiguration
{
    // Phase Settings
    public PhaseConfiguration[] phases;
    
    // Limb Settings
    public float limbHealthMultiplier = 1f;
    public bool showVulnerableLimbIndicators = true;
    public string limbDestroyEffectName;
    public string limbDamageEffectName;
    
    // Phase Transition
    public float phaseTransitionInvulnerabilityDuration = 1f;
    public bool stopAttackingDuringTransition = true;
    public bool resetPositionOnPhaseChange = false;
}
```

### PhaseConfiguration
Configuration for individual phases:
```csharp
public class PhaseConfiguration
{
    public string phaseName;
    public float phaseHealthThreshold;
    public string[] attackPatternOverrides;
    public float attackSpeedMultiplier = 1f;
    public float movementSpeedMultiplier = 1f;
    public Color vulnerableLimbColor = Color.red;
    public string phaseTransitionEffectName;
    public string phaseStartEffectName;
    public string[] vulnerableLimbNames;
}
```

## Implementation Guide

### 1. Required Components
Every phased enemy GameObject must have:
- [[EnemyPhaseCore]] component
- [[LimbDamageBehavior]] component
- [[ProjectileCombatBehavior]] component
- [[VisualEffectBehavior]] component
- Properly named limb transforms in hierarchy

### 2. Configuration Setup
1. Create PhasedEnemyConfiguration asset:
   - Configure base enemy settings
   - Set up phases array
   - Configure limb damage settings
   - Set up transition effects and durations

2. For each phase:
   - Set health threshold for transition
   - Configure attack pattern overrides
   - Set speed multipliers
   - Define vulnerable limbs
   - Configure phase-specific effects

3. Set up visual indicators:
   - Create vulnerability indicator prefab
   - Configure limb colors per phase
   - Set up damage/destroy effects

### 3. Limb Setup
1. Name limb GameObjects appropriately
2. Ensure limbs are child transforms
3. Configure limb health multipliers
4. Set up visual indicators

## Phase Transition Flow
1. Health threshold reached
2. Transition effects played
3. Combat disabled (if configured)
4. Invulnerability period
5. New phase settings applied
6. Limb vulnerabilities updated
7. Combat re-enabled
8. Phase start effects played

## Best Practices

### Component Organization
- Keep limb hierarchy clean and well-named
- Use meaningful phase names
- Group related effects in configuration

### Performance
- Use object pooling for effects
- Cache limb references
- Optimize phase checks

### Visual Feedback
- Clear indicators for vulnerable parts
- Distinct phase transition effects
- Damage state visualization

### Combat Balance
- Progressive difficulty in phases
- Clear vulnerability windows
- Balanced health thresholds

## Example Configuration

```yaml
PhasedEnemyConfiguration:
  Base Settings:
    maxHealth: 1000
    isVulnerable: true
    
  Phases:
    Phase 1:
      healthThreshold: 100%
      vulnerableLimbs: ["Head"]
      attackSpeed: 1.0
      
    Phase 2:
      healthThreshold: 70%
      vulnerableLimbs: ["LeftArm", "RightArm"]
      attackSpeed: 1.2
      
    Phase 3:
      healthThreshold: 30%
      vulnerableLimbs: ["Core"]
      attackSpeed: 1.5
      
  Effects:
    limbDamage: "LimbDamageEffect"
    limbDestroy: "LimbDestroyEffect"
    phaseTransition: "PhaseTransitionEffect"
```

## Related Documentation
- [[EnemySystemGuide|Enemy System Guide]]
- [[ProjectileSystem|Projectile System]]
- [[VisualEffectSystem|Visual Effect System]] 