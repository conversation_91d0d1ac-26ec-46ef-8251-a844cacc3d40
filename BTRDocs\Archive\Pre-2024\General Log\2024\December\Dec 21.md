# Dec 21

[Google Thinking _Scripts Reccommendations](Beat%20Game%20Notion/Learning%20and%20Research/Code%20Optimization/Google%20Thinking%20_Scripts%20Reccommendations.md)

Sorted some build issues, but need to rebuild render layers and assess, get it all working properly again

something bad with vsync logic on load, fix

Things to investigate editor vs build code

1. **Subtle Code Issues Exposed in Build:**
    - **Unintentional Boxing:** Operations that cause value types to be converted to reference types (boxing) can be more prevalent or noticeable in a built player due to optimizations.
    - **String Concatenation:** Excessive string concatenation within loops or frequently called functions can create a lot of temporary string objects, leading to GC pressure. This might be less noticeable in the editor's more forgiving environment.
    - **LINQ on Value Types:** Using LINQ queries on collections of value types can often lead to boxing and allocations, especially if not carefully optimized.
    - **Closures and Anonymous Delegates:** While convenient, closures can sometimes lead to unexpected object allocations if not managed carefully.