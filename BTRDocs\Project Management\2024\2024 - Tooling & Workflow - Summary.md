**Summary of Tooling & Workflow Related Entries in 2024**

**Build & Project Setup:**

*   **Build Issues:** Experiencing build issues when trying to build a scene, especially after upgrading and moving to a laptop.
*   **Build Time Reduction:** Need to reduce build times, exploring Assembly Definitions as a solution.
*   **Git Setup:** Re-setup Git for the project.

**Software & Tool Updates/Integration:**

*   **Amplify Shader Editor:** Updated Amplify Shader Editor.
*   **Animancer Pro:** Added Animancer Pro for better animation control in Unity (mentioned in multiple files).
*   **PrimeTween:** Replacing DOTween with PrimeTween, evaluating its performance benefits.
*   **VS 2019 Upgrade:** Received VS 2019 warning, need to upgrade Visual Studio.
*   **Cinemachine 3 Update:** Updated Cinemachine 3 camera, using Feel.SO for better integration.
*   **Unity Version:** DO NOT upgrade Unity version for creature testing project to avoid breaking things (March 9th note in Design & Mechanics).
*   **FMOD Upgrade:** Upgraded FMOD to 2.03 for new features.

**Blender & 3D Modeling Tools:**

*   **Quad Remesher:** Noted Quad Remesher as a good Blender editing tool, already installed.
*   **Quadriflow Remesh:** Considered Quadriflow Remesh as another Blender tool.
*   **Blender Mesh Editing:** Trying to edit mesh in Blender, Skinned Meshes are proving troublesome.

**Unity Editor & Tools:**

*   **Unity Profiler:** Unity Profiler Walkthrough & Tutorial (March 6).
*   **Prefab Painter:** Questioned if Prefab Painter could achieve a certain task (March 12).
*   **Post Process Volume:** May need to rebuild volume to get Post Processing working (March 30).
*   **Particle Effects Sorting:** Need to dedicate time to sort through particle effects (May 2).
*   **UI Structure:** Assessing UI structure within scenes - Prefab or Singleton? Considered additive scene loading for pause menu (May 7).
*   **Reload Scene Script:** Rewriting reload scene script, but it's failing and may not be worth pursuing (May 8).
*   **Jason Booth Optimizations:** Plan to review and apply Jason Booth's optimization techniques (May 17).
*   **Object Particle Spawner/Uni Bullet Hell:** Considered using Object Particle Spawner or adapting Uni Bullet Hell into 3D (March 21).
*   **A\* Debugging:** Reduced A\* debug message volume by changing logging level in A\* object (March 15).
*   **Volume Rebuild:** May need to rebuild volume for Post Processing (March 30).
*   **Normal/Transparent Rendering:** Investigating simpler ways to handle normal/transparent rendering (July 15).
*   **Particle System Manager:** Using Particle System Manager for particle system optimization (Oct 14).
*   **VFX Graph:** Need to assess if VFX Graph will solve particle system issues and for visual effects implementation (Oct 14, Nov 27, Nov 30).
*   **Custom SRP:** Researching Unity Custom SRP (Nov 30).

**Workflow & Organization:**

*   **Kanban Board:** Created Kanban board to log and address issues more effectively (Feb 23).
*   **Codebase Scan:** Codebase scan revealed potential issues (Aug 12).
*   **Input Checks Consolidation:** Need to consolidate input checks in code (March 22).
*   **Game Manager Splitting:** Split Game Manager, Crosshair, Projectile Manager into multiple classes for better organization (Sept 1).
*   **Design Docs & Tim Cain Videos:** Rough overview of things - Tim Cain videos & Design Docs (Oct 2).
*   **Useful Links Organization:** Organizing useful links, including VFX graph learning templates (Nov 30).

**Sound Design Tooling (from August 14 & 30 entries, also in Visual Style & Art Direction and Research & Inspiration):**

*   **Sound Design Thoughts:** General thoughts on sound design, Bookends and Highway method.
*   **FilterFreak & Speakerphone:** Use FilterFreak and Speakerphone for SFX processing.
*   **GDC Talks, Designer Notes Podcast, Google Scholar, Abstract - The Art of Design:** Recommendations for game design inspiration and learning resources.

**In-Game Settings:**

*   **UnitySettingsFramework:** Considered using UnitySettingsFramework for in-game settings (Feb 13).

**Leak Investigation:**

*   **Leak Issues:** Need to investigate leak issues (Feb 23).

**Animation Editing:**

*   **UMotion:** Using Umotion Notes (UMotion) for animation editing.
*   **Copy to Other Side Tool:** Using "Copy to other side" tool in animation workflow for mirroring adjustments.

**Rendering Order Fix:**

*   **Rendering Order Fix:** Fixed rendering order issue, which was unexpectedly cool and potentially usable for a level (March 22).

**Performance Investigation:**

*   **Locked 60FPS Performance:** Investigating why performance is strangely locked at 60FPS (June 18).