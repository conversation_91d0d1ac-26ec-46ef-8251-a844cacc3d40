# Summary of 2020 - 2021 Visual.md

This file documents the visual style and art direction explorations from April 2020 to January 2021. Key activities and ideas include:

- **Shader Experiments:**  Extensive experimentation with shaders, particularly Joost shaders for environment and bullets, and Studio CS Ghost UPR Advanced Always Visible "Ghost Shader".
- **Particle Effects:**  Developing and refining particle effects for bullets, bullet destruction, and general visual enhancements. Learning about Living Particles and their effects.
- **Visual Clarity and Style:**  Focusing on visual clarity, especially for lock-on graphics, and experimenting with outlines (Easy Performant Outline).
- **Environment and Level Visuals:**  Greyboxing levels and experimenting with skyboxes and global volumes. Implementing color-changing environment methods. Exploring fog for visual effects and LOD pop-in masking.
- **Asset Integration:**  Integrating assets like Mesh Tracer and MeshManipulation Plugin for visual effects.
- **Rendering Pipelines:**  Upgrading to HDRP and experimenting with Ethereal URP for fog and other visual enhancements, but facing difficulties with Ethereal URP.
- **Performance Considerations:**  Linking visual choices to performance, such as using performant shaders and considering mobile transparency shaders.
- **Mirror and Portal Effects:**  Exploring mirror and portal-like effects for level sections.
- **Material Adjustments:**  Researching runtime material color adjustments and DOTween for color lerping.
- **Character Visuals:**  Experimenting with Mixamo animations, local volumes on player characters, and outline shaders for the main character.