# 2022 Logs

---

## April 2022

#### April 4

*   Took a long break
*   Opened the project today to make some adjustment to camera, moves around a bit with cursor movement
*   Camera follows Shooting Reticle rather than Player now
*   Can adjust LocalMove function of Player Movement to try and get the player’s position to move as well, but needs more work on the Clamping

---

#### April 5

*   Watching Unreal stream, looking through things and getting caught up on
*   Cleaning up hard drive to install UE5
*   Moving Landforms to hard drive - worked fine
*   Watching Unity Twitch GDC stream for info - looking for Tunic Post Processing info
*   Note - Need to get wave spawner working OR need better wave spawner
    *   [https://assetstore.unity.com/packages/tools/spawn-ity-58832](https://assetstore.unity.com/packages/tools/spawn-ity-58832)
*   Tunic VFX - Scrolling textures
    *   Chromatic aberration built into the texture it seems
    *   ShaderForge used for the texture
    *   Should be able to do this all in Shader Graph

        ![Tunic VFX 1](April%205%20a010455306bb40c0b1be2dcf1a6ccff4/Untitled.png)
*   Applying similar Texture/Shader FX to a different meshes as well

    ![Tunic VFX 2](April%205%20a010455306bb40c0b1be2dcf1a6ccff4/Untitled%201.png)
*   Scrolling textures along tubes here, could maybe do the same with bezier curves easier?
*   Looks great though especially with Post Processing
*   These are UV’d shapes with scrolling textures
*   Background is a scrolling texture as well - Using minimum to blend two things
*   Colour wheel, if you’re gonna go hard on one chroma, try pulling back a little and giving some in from a colour on the opposite side of the wheel
*   4:37 GDC Day 1 - Tunic Post Processing - building it up from nothing
*   Turns on Light Mapping, shadow catches of geometric complexity - nice touch - try thinking this way?

    ![Tunic VFX 3](April%205%20a010455306bb40c0b1be2dcf1a6ccff4/Untitled%202.png)
*   Mentions turning on SSAO first but this scene doesnt have it - can I add to my game?
*   Turns on bloom next

    ![Tunic VFX 4](April%205%20a010455306bb40c0b1be2dcf1a6ccff4/Untitled%203.png)
*   Using Amplify Color with custom lookup tables next
*   Tonemapping / Colorgrading brings a lot to this
*   Amplify Color can let you import a photoshop file
*   You can screenshot your scene, bring into photoshop, make changes, then import to amplify color

    ![Tunic VFX 5](April%205%20a010455306bb40c0b1be2dcf1a6ccff4/Untitled%204.png)
*   Saturating shadows is a key trick to do
*   Applied a gradient to the whole image through one of the cameras - using BLIT camera
*   Applied upper right and lower left
*   Adds a glowing off in the distance
*   Different scenes will use different colors / types of this

    ![Tunic VFX 6](April%205%20a010455306bb40c0b1be2dcf1a6ccff4/Untitled%205.png)
*   Showing old version of the world without textures - looks too flat in some ways
*   Using some textures to add a bit of detail really brings out more
*   Maybe simplest way of bringing grit to the objects
*   Good technique to steal!
*   SSAO leaned on pretty hard for lighting
*   Added SSAO to my Forward Renderer in Beat Traveller! Experiment with this
*   Andrew - worked with PowerUp Audio
*   But bouncing it off people is super important - Finji big help!
*   Playtesting along the way is important as well
*   How was color palette established?
*   Not really a lot of concept art
*   A lot of trial in error with color palette, and what techniques
*   For example, saturated shadows
*   Lots of floating annotations -cool thing here- lol
*   Modelling the world was mostly done in Pro Builder
*   Automatic UV in Pro Builder largely does the job
*   What are UVs again?
*   Need to throughly look at my wave spawner for gameplay issues
*   Checking now - destroyed an enemy but it did not register as destroyed in GUI log

    ![Wave Spawner GUI Log](April%205%20a010455306bb40c0b1be2dcf1a6ccff4/Untitled%206.png)
*   Also this error seems to happen upon spawning

    ![Wave Spawner Error](April%205%20a010455306bb40c0b1be2dcf1a6ccff4/Untitled%207.png)
*   Investigating if there’s any special code for enemy death in demo
    *   Has SpawnableIdentity script, thought I believe this has issues with NavMesh? Meaning it wants one though I dont use NavMesh
*   Destroys gameobject in demo

    ![Wave Spawner Demo](April%205%20a010455306bb40c0b1be2dcf1a6ccff4/Untitled%208.png)
*   I dont want to destroy generally - way around this?
*   Maybe using Despawners - can they track when item becomes inactive it can be seen as destroyed?
*   Maybe add DespawnOnEvent and then I can - ‘call this method from Unity events or animation events to trigger despawns at specific times.’
*   Check if demo features any despawners
*   SEE Despawn From Code section - probably best bet!

---

#### April 6

*   **Need to go through all Electronic Music Notes for ideas and harmonic concept thoughts**
*   Looking at Wave Spawner Code example to implement - laid this out yesterday
*   Removed Spawnable Identity script as I think it just gets added when Enemy is spawned
*   Sent email for help, but just destroying the game object for now
*   Unsure how I integrated pooling, need to look into this more!
*   Look into this, seems I talked to help and implemented my own script which overrides OnUltimateSpawnerInstantiate using HandleSpawnerInstantiate
*   Maybe do the same with public static Action\<Object> OnUltimateSpawnerDestroy; ?
*   Sent email to dev, will see if I hear back tomorrow!
*   May need to load from yesterday - any changes made today that were important?
*   Weird issues with A\* that might just be fixed by going back a version - that takes a while sometimes though!
*   Doing that overnight
*   Random Flow for Blender - thought occurs, all these metallic textures and materials i see pop up for sci fi stuff. Play around with them! See what kind of things I can create. Could possibly bring a bit more of a textured look to characters and things like Tunic does

---

#### April 7

*   Looking deeper in spawner, received dev response
*   > Hi Tyler,
*   > 
*   > You can inform the spawning system that an item has been destroyed using the static ‘SpawnableItems.InformSpawnableDestroyed(…)’ method. You can simply pass in the transform of the destroyed item and that all spawners should be updated accordingly and proceed as you would expect.
*   > 
*   > I hope that helps you. Let me know if there is anything else.
*   > 
*   > Regards,
*   > 
*   > Trivial Interactive.
*   > 
*   Adding this to enemy death method
*   Works! Sort of...

    ![Spawner Issue 1](April%207%208026b6a3f195439a81bc192bcd0cd6b7/Untitled.png)
*   Not changing to a new Wave - need to see why!
*   Things to change....
*   Add SimpleWaveHud to scene (Arena Demo has it on the player)

    ![SimpleWaveHud](April%207%208026b6a3f195439a81bc192bcd0cd6b7/Untitled%201.png)
*   Add Wavehint to Canvas

    ![Wavehint](April%207%208026b6a3f195439a81bc192bcd0cd6b7/Untitled%202.png)
*   Add Enemy Hint?

    ![EnemyHint](April%207%208026b6a3f1e54afe8f3dfd4b/Untitled%203.png)
*   Add event spawn controller to object in scene?
*   Sent email because I cant seem to get this working, got a quick response so may hear back tomorrow as well
*   Did some miro documentation - need to fix this wave spawning issue but also need to make a good play moving forward
*   Once wave spawning works - how do we imaging this all to work? Go to core documents, Electronic music documents, and consider this!

---

#### April 10

*   Debugging WaveSpawner issues - why wont waves advance?
*   Realized that when i disable pooling enemies will not work properly - this is due the reference for enemies to spawn, pointing to the prefab and not the enemy in the scene, attached to A\*
*   Trying some fixes for this - can the enemy connect with A\* Graph once enabled?
*   Enemy Plane is titled EP1
*   Implemented code to attach this once enemy is awake or enabled in scene but doesnt seem to work for the enemies, they dont move properly and seem confused
*   Sent email about it to Wave Spawn Dev
*   ALSO - double shooting issues appears to be the particle system flying off lol? WHat’s happening there i wonder

---

#### April 11

*   Researching into Frank Russo and the DualSense PC Support
*   DualSense
*   Vibro pattern gets sent to the controller as essentially an audio stream.
*   Gamma Space Meeting
*   Mechanics and core narrative pillars - tying these things together
*   Is movement core or is it a method of getting from one puzzle to the next?
*   Looking at Miro boards and organizing things this way - Rocket Adrift
*   WaveSpawn Update!
*   Remove SpawnableIdentity script during death and things shoud work maybe? Waiting on developer update email

---

#### April 12

*   Wavespawning issue - have a good lead!
*   > Hi again,
*   > 
*   > Sorry it sounds like I was not very clear. The spawned items should indeed have a SpawnableIdentity component attached but the scene game object that you are cloning as part of your pooling solution should not. It sounds like everything is setup correctly though.
*   > 
*   > When you are destroying your items are you using ‘Object.Destroy’? If so, that could be the problem because that will only work correctly if you are not using a custom pooling solution and could be causing the problem. Not sure why it did not occur to me sooner to check this, as it is certainly the way most users would setup their game to start with. Instead it is recommended to use one of the ‘UltimateSpawing.Despawn(…)’ methods and pass in the game object or associated component. There will be no need to inform the spawner of the item destruction in this case as that will all be done via the single call. It will also redirect the final ‘Destroy’ call to your pooling code, and trigger all despawn events if any components on the spawned item implements ‘IDespawnEventReceiver’.
*   > 
*   > Hope that makes sense and hopefully it could be the final answer to the problem. Let me know if you are still having issues.
*   > 
*   Need to look at how im killing enemies and despawning in pool
*   [http://docs.poolmanager.path-o-logical.com/home/<USER>//docs.poolmanager.path-o-logical.com/home/<USER>
*   Seem to have a working solution! Emailed dev about it in comparison to their own recomendations, curious to hear back. Spawn and Pool assets are informed upon death of enemy, so that may be all that’s needed!
*   Enemy can pull pool name from parent object, since pool is always the parent object. Does this work moving forward?
*   IMP - when an enemy is reused it doesn’t seem to properly come back. Some refinement of the enemy class and instantiation / make active will possibly need to be made
*   IMP - don’t forget the double shot from enemies - this is likely the particle system shooting off in the opposite direction, will have to verify and fix
*   Need to read up on Pool Manager - there is a LOT to it, may be handy

---

#### April 13

*   Upgraded to Unity 2021 LTS - seem to be no issues! This appear to work fine
*   Before today all builds used 202.3.30f
*   Uninstalled earlier version - can bring back if needed!
*   **Debugging particle error**
*   Particle System can only spawn from mesh if all submeshes use the same primitive type
*   Appears to be Birth Particles of Enemy that is causing this issue?
*   > set Mesh to None and that will solve the problem... well partially because the mesh that is causing the problem still won't be usable.
*   > 
*   > You need to check all your meshes and see if they have more than one material. Emitting a mesh that has more than one material is more often than not the cause of this warning message.

    ![Particle Shape Error](April%2013%202c03467c73274cd69d0892054e903d00/Untitled.png)
*   Set shape properties of particle system to not rely on single material OR use mesh colors - no error!
*   May need to revisit this though based on above answer
*   ERROR
*   When enemy reused, they do not spawn properly
*   ERROR
*   Enable / Shoot particles of bullet are going in opposite or very different direction then the bullet
*   These are parented to the bullet, so they dont move with the enemy. These should probably just come from the enemy.
*   ERROR
*   Time Rewind seems to break randomly?
*   ERROR
*   Bullet trails not in correct direction

[https://www.youtube.com/watch?v=agr-QEsYwD0&t=4s](https://www.youtube.com/watch?v=agr-QEsYwD0&t=4s)

Run through this whole thing for proper implementation?

Changing Unlockable Bullet weight to 0 for now! Not using it in current testing

Lofelt Vibrations - is this what I should use?

---

## April 14

*   Ordered Razer haptic headphones used for testing - can send back to amazon!
*   Working on Error
    *   Bullet trails not in correct direction
    *   [https://www.youtube.com/watch?v=agr-QEsYwD0&t=4s](https://www.youtube.com/watch?v=agr-QEsYwD0&t=4s)
    *   Run through this whole thing for proper implementation?
    *   Did this - things looks better but not perfect - need to reconsider ideas
    *   Particles are on a moving platform, i want it all relative to that space, NOT to the world where it’s speeding by
    *   Custom space - relative to bullet object
    *   Not working! Hmmmmmmm
*   Quibli Shaders - Cannot get alpha of outline to work
*   According to docs, Flat Kit has better Outline options, look into this!
*   Transparency not working when bullet locked - trying to fix
*   Can make object transparent but outline will not go transparent
*   Experimenting with Lofelt Haptics studio - doesnt support my phone
*   Use it as integrated with Nice Vibrations I think? Does Feel have this whole package?
*   Error Respawning Enemies

    ![Respawn Error](April%2014%201633eaf26d58423e8d180043fcca492a/Untitled.png)
*   Textures on low poly models
    *   [https://github.com/eldskald/unity-support-textures-generators](https://github.com/eldskald/unity-support-textures-generators)
    *   This reminds me of the tunic talk
    *   Adding grit to low poly
    *   Quibli Shader has an option i think?
    *   For reference - removed this from Torus

        ![Torus FX](April%2014%201633eaf26d58423e8d180043fcca492a/Untitled%201.png)
*   Need to learn what these maps mean

    ![Texture Maps](April%2014%201633eaf26d58423e8d180043fcca492a/Untitled%202.png)
    *   Look at [https://www.youtube.com/watch?v=BD4Mpq8BqQk](https://www.youtube.com/watch?v=BD4Mpq8BqQk)
    *   Quibli Shaders tutorial? Look at tea set examples for ref

---

#### April 15

*   [https://www.youtube.com/watch?v=v5_RN8o1b3g](https://www.youtube.com/watch?v=v5_RN8o1b3g)
*   Watching this about performance enhancements - lots of good tips!
    *   Use a low poly mesh in the game - get the high poly mesh with the detail and bake this into a normal map that you can then use over your low polys - keep the nice crisp look but keep the poly count down
    *   Draw call batching!

---

#### April 16

*   Got the new Razer headphones, experimenting with writing music with these haptic headphones in mind
*   Can I translate hi hat patterns to controller? Is that even interesting?
*   Play with it on test scene!
*   Koreographer and midi to trigger motor
    *   [https://guavaman.com/projects/rewired/docs/HowTos.html#Vibration-Rumble](https://guavaman.com/projects/rewired/docs/HowTos.html#Vibration-Rumble)
    *   Something to try!

---

#### April 17

*   Mixolumia Music doc - may be interesting!
    *   [Mixolumia - Making Music](https://mixolumia.com/makingmusic/)
*   Didn’t get to my Hi Hat / Controller vibration idea today - tomorrow maybe!

---

#### April 18

*   Finally getting around to hi hat controller vibrate
*   Is this cool? Interesting? Does it work? Lol
*   Implemented in Shooting / Crosshair class as OnMusicalFakeHiHat
*   Fast speeds are not that effective - latency is part of the problem
*   In part due to the speed at which the motor can be kicked in. Can this be accounted for?
*   Is it even that interesting?
*   for FX, things happening on screen, lock on, I think the latency may be ok.
*   But representing a quick musical sound - Not working
*   Options within Unity’s new input system but not sure it’s worth pursuing
    *   [https://www.youtube.com/watch?v=WSw82nKXibc](https://www.youtube.com/watch?v=WSw82nKXibc)
*   Thinking about a few highlighted good short, minimalist, and other games
    *   [https://www.gamingscan.com/best-short-games/](https://www.gamingscan.com/best-short-games/)
        *   Sayonara Wild Hearts
        *   Going Under
        *   Incredibox - music game
        *   Monomals - another music sort-of game
*   Trying to document aspects of the game in Figma instead of Miro

    Not feeling like this is super beneficial
*   What do I currently have?
    *   Lock on system - can shoot enemies
    *   Basic enemy AI for shooting me and running around the playing field
    *   My lock on system - Can work overhead / first person / third person
    *   Narrative Idea - You are an AI auto protection system guarding a being transmitted through earth
    *   Think AI protection in Alien, Prometheus, etc.
    *   Make camera lean in opposite direction as we aim?
*   Need wide open spaces to see bullets flying at me currently
*   But we want fast movement / intense action, quick decisions
*   How to rectify this?
*   Think Panzer Dragoon
*   Think After Burner - First one is so FAST!!!

    Tilting Camera from After Burner!
*   Slight tilt, nothing too major

    Make the grab and launch a quicker action - HOW
*   Current slight slower pace work for some types of music
*   Do faster for other style of song
*   FAST enemies - die quickly
*   Approach the player more directly, Should not move between quadrants too much?
*   IF there’s lots of movement between quadrants - don’t shoot as much!
    *   How to do this with Behavior Tree?
*   Slower thoughtful enemies - move around the player, take a few hits
*   WAVES of projectiles around the player to grab and shoot at enemies in these cases
*   How does a level like this function??
    *   [https://www.youtube.com/watch?v=PjZLIiupIsQ&t=1559s](https://www.youtube.com/watch?v=PjZLIiupIsQ&t=1559s)
*   Collecting lots of bullets, pushed to the limits
*   Enemy Type - CANNOT shoot but do not want to touch,
*   Will approach you and self destruct
*   Shoot down before it does!!
*   Collect bullets up and take these guys out
*   Maybe work with opposite colours??
*   Ways in which a track will progress / change
    *   Death of an enemy Waves
    *   Pass a barrier / object
*   Also have unlockable bullets - require close proximity blast / time glitch

---

#### April 19

*   Having a hard time seeing where this game goes lately....
*   Look over - make a solid plan. Evaluate. Move forward! Important to do this tomorrow.

---

## Bug collection

*   Bug found - LockedList on Shooting is empty but Canvas number says 1
*   Bug found - Enemy continually rising? maybe an A\* / RichAI Issue
*   Bug found - if bullets are heading for target and it dies first, they will get stuck!
    *   Need to evaluate that target is still active, and if it isn’t just continue forward and reinitiate lifetime
*   Bug found - bullets rotating with enemies still happening
*   Bug found - Issues with Chrono persist in Level Test 4

---

## Crosshair Class Breakdown

**Start**

*   Setup player input
*   Load Shooting Audio files for level
*   Get Renderer component from Reticle
*   locking = true
*   Register Koreographer instances for Shooting, Locking, and Time Rewind
*   delayLoop = false
*   num of rewinds = 0
*   rewindTriggerStillPressed = false;

**Fixed Update**

*   Debug.DrawRay Called
*   OnLock method
*   clock registered to Timekeep.instance.Clock

**OnLock**

*   If the Raycast hits something & player is pressing fire & it’s either tagged bullet, launchablebullet, or enemy
    *   if my array of targets does not contain this hit.transform & it has a Projectile class & Projectile is not already locked.
        *   if locked list is less then maximum targets & targets is less then max targets
            *   Set that bullet to locked
            *   Add the transform to target array
        *   Else
            *   just return; COMMENTED THIS OUT - WAS THIS A CHOKE POINT?
    *   Else if we hit and enemy, lockedlist count > 0, player is pressing lockenemy button, and we are not already targeting this enemy
        *   Frist check if we ever locked onto any enemy, if so set locked to false and remove that aimPrefab from them
        *   Otherwise lock the enemy, give them an aimPrefab, setup the color, etc
    *   if hit.transform = null then just return

**OnMusicalLock - fed by koreo event**

*   if fire button is pressed and targets array is greater then zero & timescale matches
    *   add the aimprefab at targets [0]
    *   add value from targets to LockedList
    *   Run lockvibrate coroutine
    *   Play random locking sound
    *   ++ to Locks value
    *   Remove targets[0]

**OnMusicalShoot - fed by koreo event**

*   setup combo score value
*   tempLocks = Locks;
*   if player has released fire or triggeredLockFire is true & LockedList > 0 && Timescale matches
    *   locking = false
    *   clean the LockedList of null values
    *   if the item in LockedList is a Launchable Bullet & not null
        *   if enemyTarget is not null and enemyTarget is active
            *   Launch bullet at enemyTarget
        *   else
            *   Launch bullet at RaycastTarget
    *   else
        *   Debug null object still locked
        *   Vibrate controller
        *   Shake Camera
        *   if we’re on last item of LockedList then play a shootTag if enabled
        *   Remove item from LockedList
        *   locking = true
        *   ShotTally++, comboScore++
        *   Locks = Locks - 1
        *   Score equals combination of locks and combo score WHEN BUGS ARE FIXED LOOK AT THIS CLOSER

**RaycastTarget**

*   Returns either hit.point or ray.GetPoint(range)
*   This would be the end of the maximum range the raycast is set
*   What is LineToTarget??

---

## Crosshair / Projectile bug Fixing

![Crosshair Error 1](Crosshair%20Projectile%20bug%20Fixing%2036853ac3d16c4df08caaf2ae442ac77b/Untitled.png)

Koreo related errors here

![Crosshair Error 2](Crosshair%20Projectile%20bug%20Fixing%2036853ac3d16c4df08caaf2ae442ac77b/Untitled%201.png)

*   Player is locked on to current EnemyTarget
*   EnemyTarget is still active - not dead
*   Targets list is 0
*   Locks count is 3
*   LockedList showing 3 items - projectiles
    *   1 is parented to horizontal plane
    *   Has no projectileTarget
    *   Shows both Locked and Released

![Crosshair Error 3](Crosshair%20Projectile%20bug%20Fixing%2036853ac3d16c4df08caaf2ae442ac77b/Untitled%202.png)

*   2 Next projectile on list is inactive!
    *   Has died - everything disbaled and no projectileTarget
    *   Has NEGATIVE lifetime!
    *   Still has LockedOnPrefab
*   3 Exactly the same as #2
    *   Negative lifetime and all other attributes
*   If lifetime < 0 it just kills itself
    *   Probably should pause lifetime if locked?
    *   Also only kill itself if it’s not locked - need to look at if my locked states are setup properly
        *   CHANGED THIS TO LAUNCHING - not LOCKED state
            *   UPDATE Launching does not appear to be the correct approach either
*   Still a bunch of inactive bullets in lockedlist
*   Error appears to be Cross trying to launch inactive bullets
*   LIfetime is not the issue with any of these - all alive for only a couple seconds
*   Issue is in the Projectile class?
*   What happens if projectile collides with enemy while locked?
*   Need to write out projectile states and how it moves through system
    *   [Projectile States and system movement](Crosshair%20Projectile%20bug%20Fixing%2036853ac3d16c4df08caaf2ae442ac77b/Projectile%20States%20and%20system%20movement%20732ff3a0b1e04d4ab1675544c45b3c74.md)

---

## Feb 1

*   Adding Feel to some elements - Lock and Shoot camera shake
*   Adding transparency for projectiles so screen is less obstructed
    *   Used DOTween!
    *   Only do this within certain range? or always?
*   Added diamond enemy as shape
*   Added particle effects for Enemy Birth and Death
*   Combined Enemy Basic Setup with Enemy Rhythm
    *   This was done to allow everything to be tied to Koreographer
*   Combined ProjectileAudio and Projectile for same reasons
*   Added Movement Particle gObj to Projectiles
    *   EXPECT THIS TO BREAK AIM PREFAB CREATE / DESTROY
        *   FIX THIS
*   Looked at ShaderGraph Dissolve Dissolve metallic for enemies but decided differently
*   Bug found - LockedList on Shooting is empty but Canvas number says 1
*   Bug found - Enemy continually rising? maybe an A\* / RichAI Issue
*   Bug found - if bullets are heading for target and it dies first, they will get stuck!
    *   Need to evaluate that target is still active, and if it isn’t just continue forward and reinitiate lifetime
*   Bug found - bullets rotating with enemies still happening

---

## Feb 2

*   Bug found - Issues with Chrono persist in Level Test 4
*   Looking at A\* movement problems
    *   moved enemy prefab location in scene - may need to change back? 0 0 0
        *   In ex: Ship and target are on ignore raycast layer - remeber this!
    *   trying changing BD to Seek to see if it changes error
        *   Same errors with other BD - not a Scoot and Shoot error specifically
    *   Looking at earlier scenes to see differences
*   Takeaways to try!
    *   Layermask only Enemy Plane 1

        ![A* Takeaway 1](Feb%202%20182aecade6f441df91692d4fba03671b/Untitled.png)
    *   Local Space Rich AI is setting to Enemy Plane as graph

        ![A* Takeaway 2](Feb%202%20182aecade6f441df91692d4fba03671b/Untitled%201.png)

        ![A* Takeaway 3](Feb%202%20182aecade6f441df91692d4fba03671b/Untitled%202.png)
    *   Look at Auto Repath Settings...
    *   Nothing there
*   Setting walkable height/walkable climb too low causes movement issues cause enemy to stop moving around? Gets stuck on bridge terrain. Walkable slope important too, new settings for all seem to work. Rasterize terrain needs to be on as well
*   Now working, but movement is slow and jagged while platform is moving
*   Appears t o be a scale thing, once adjust height and radius of local space rich ai, things are good again!
*   Not perfect but promising!
*   Worked on Death Particle effect, refine process / look
*   Working on player / reticle jumpnig around while moving
    *   aim parent cant be disabled safely
    *   aim parent doesnt need to be on paritcular layer, neighter does camera
*   Is it a chrono thing? check time is working properly
*   Disabled shooting and still a problem with player
*   Player game object transform is issue, being updated somehow
*   Disabled player to see if shooting would exhibit behvaior - nope!
*   It’s in the Player GameObject
*   Issue is Local Move function - needs to be adjusted!
*   Bug found - bullets not reparenting properly, still attached to enemy at times when they’re locked by the player
*   Bug found - bullets circle back on itself

    ![Bullet Loop Error](Feb%202%20182aecade6f441df91692d4fba03671b/Untitled%203.png)
    *   Negative lifetime but still circling
        *   laucnhing is the problem
            *   need to chart out all bools and the way they’re used so that all cases are covered
*   reparenting of bullets needs to be fixed as well
*   Looking into reparenting now

START

original parent = EP1

object is parented to EP1

new parent = EP1

UNPAUSED, in a moment was reparented to Pool

Used a general Enemy Bullet Pool for all enemy bullets, located under enemy plane. This fixes enemy rotating issues since it’s not attached to the enemy.

lots of bullets released from system / no parent now, see if this is necessary, may fix other issues

LaunchBack and LaunchAtEnemy methods both set parent to null, removing for testing

suspect there are issues with targetCube implementation

Improving bullet movement partile effect

IDEA: Sensor toolkit on bullets so if they are in range of target they auto target?

Added Warp Effect into game - finish this tutorial

[https://youtu.be/VsGG5JYlqIU?t=369](https://youtu.be/VsGG5JYlqIU?t=369)

Posted to Aron about A\* issues in forum, hopefully resolve them!

---
## Feb 10

*   Investigate Compute Shaders - how easy is this? Is it worth it?

---

## Feb 15

*   Ubisoft Indie Series Video work
*   Also - Glass URP Shader from Ciconia Studios - Try a level with this!!!!!

---

## Feb 22

*   Past week has been Ubisoft stuff, lots done and submitted!
*   Now working through FMOD Tutorials for integration into project. Doing an advanced tutorial for drone setup
    *   [https://www.youtube.com/watch?v=fT32r1dvO_I](https://www.youtube.com/watch?v=fT32r1dvO_I)
*   Running into issues - go over FMOD Integration tutorial, set this up properly
*   Also may be useful to look over this
    *   [https://www.youtube.com/watch?v=Uhk5sNKoaWk&t=1s](https://

www.youtube.com/watch?v=Uhk5sNKoaWk&t=1s)

---

## Feb 23

*   More FMOD
*   Figuring out how to even get sound - currently instances!
*   Ignoring Koreogrpaher to get a basic FMOD sound to play but it’s not working
*   Need to do this!

---

## Feb 25

*   Having issues with current version, unsure why things aren’t working
*   Grabbing a pre-major Ubisoft weekend overhaul version of the project (Feb 15th) to convert to FMOD
*   Copying files to 5TB from a backup - in main folder
*   Levels and audio should be working fine in that one
*   Also watching FMOD + Koreographer video implementation again
*   Back to current interation of project.
*   Enemy Shooting didn’t work with Unity’s Audio Engine Disabled
*   Means the shooting is tied to the audio engine - most sounds are tied to Koreogrpaher
*   Need to recouple with FMOD instead

    ![FMOD Connection 1](Feb%2025%2083c2578565bb452ea9689c23bd3c0629/Untitled.png)
*   FMOD Koregraphy Set is link between FMOD names with the Koreography stuff in Unity
*   Music is playing! Forgot about this essential part !-!

    ![FMOD Koreo GUI](Feb%2025%2083c2578565bb452ea9689c23bd3c0629/Untitled%201.png)
*   CHronos doesn’t affect audio anymore- Worked for AudioSource
*   Physics Rewind - [https://www.youtube.com/watch?v=eqlHpPzS22U&t=47s](https://www.youtube.com/watch?v=eqlHpPzS22U&t=47s)
*   **Ask Rob about rewind and glitch in FMOD? - Others like Master Audio? Fabric?**
*   **Try AudioSource Implementation Again**
*   Analyze possibilities for internal system vs Middleware
*   Tried using instances with projectiles but not hearing anything
*   Notes on Master AAA
    *   Has a randomization feature built in, no need for coded array
    *   Set sounds to events - wonder if custom options exist?

---

## Feb 26

*   Adapting to Master Audio for testing - is this better than just unity’s audio system?
*   Also realizing Rewind System needs to be programmed better - no delay between number of rewinds, some errors that need to be fixed here - also too man if’s!!!
*   Is rewind important to my game? Might not be able to use FMOD if so !
*   Was having issue with Master Audio implementation, appears to be 2D/3D issue
*   Shooting/Locking/Tag all work when forced to 2D
*   Really need to fix lock on - doesn’t seem to highlight and lock target. visualize this with debug tools?
*   Use new song! Make a level using playlists etc?
*   Look through this

---

## Feb 28

*   Going to use a new song as a model! 2022 - 02 - 11 wait for it
*   Breaking up into parts for FMOD

---

## Feb 3

*   Naming conventions! Get more into this workflow type-stuff
    *   [https://www.youtube.com/watch?v=Z6qBeuN-H1M](https://www.youtube.com/watch?v=Z6qBeuN-H1M)
*   Adding Unity video recorder plug
    *   [https://learn.unity.com/tutorial/working-with-the-unity-recorder-2019-3#5e1efe8bedbc2a0029fbc0d6](https://learn.unity.com/tutorial/working-with-the-unity-recorder-2019-3#5e1efe8bedbc2a0029fbc0d6)
*   Continuing warp drive effect from yesterday
    *   [https://www.youtube.com/watch?v=VsGG5JYlqIU](https://www.youtube.com/watch?v=VsGG5JYlqIU)
    *   Moving texture map is here! May want this as well sometime
*   Trying with Koreographer implemention - looks cool!
*   Imported a projectiles package to make proj look better
*   Added muzzle effects to reticle? or just launch or particles?
*   Need impact effect as well
*   Koreo Note - how to organize all track names in a project? Need to look into this
*   Idea - Living Particles for zipping through walls with a break away effect
*   Bug found - bullets moving in circles after presumably missing target, not sure why.
*   RE-Enable Target Cube to see if it’s related to this issue? Write a check that it used TargetCube and that it hit it? Not seeing any Target Cube’s created though... but maybe all were destroyed?

---

## Feb 4

*   Flying Animations Asset pack - Icuzo is the dev
*   Doesnt seem to work properly, and see multiple complaints about it not being setup for Humanoid
*   May have to look up animation controller more and look into how to adopt this
*   Comment in reviews suggested it could be converted to Humanoid with some settings
*   Adding Magic Arsenal kit, some interesting effects to work with here
*   Added NanoLOD - may be better LOD tool thenAutoLOD?
*   SHould watch this video on code structure: [https://www.youtube.com/watch?v=rHRbBXWT3Kc](https://www.youtube.com/watch?v=rHRbBXWT3Kc)
*   THis one on Unity Architecture: [https://www.youtube.com/watch?v=tE1qH8OxO2Y&t=222s](https://www.youtube.com/watch?v=tE1qH8OxO2Y&t=222s)

---

## Feb 5

*   Saturday, slow start! Realized im not on latest LTS so reimporting entire project to that
*   Plan to refer back to starfox example for better camera movement - i know more now so probably wont hit the same issues?
*   Also looking over some files and tutorials - Metroid Dread Tutorial
    *   [https://www.youtube.com/watch?v=myH829zACPQ](https://www.youtube.com/watch?v=myH829zACPQ)
*   Spent a bunch of time waiting on imports only to realize I screwed it all up
*   All back to original Unity Build / State I had the previous day

---

## Feb 6

*   Messed around with Camera a bit. Looking at including more of a starfox approach, so dodging projectiles is an option. Not sure how to do it yet, need to consult Mix and Jam tutorial again. Base around just the player, then maybe enable shooting?

---

## Feb 7

*   Overview of Coding tips for Games
    *   [https://www.youtube.com/watch?v=8Hy4JvtfUb8&list=WL&index=4](https://www.youtube.com/watch?v=8Hy4JvtfUb8&list=WL&index=4)
    *   Read up on State Machines?
    *   Large Classes - split things out as you’re moving forward
        *   Consider how to separate
    *   Everything is Public! That’s not good
        *   Spagetti code issues
            *   everything referencing everything else in a way that is confusing to follow
            *   really think about if it needs to be public - private by default
            *   is there a better way to modify without being public
    *   Having setters with side effects - ex: Damage method
        *   Damage calls out several method that does network calls (slow)
        *   We dont realize as we use Damage that we are also doing lots of network calls
            *   could be doing those calls collectively and not individually
        *   Would accidentally cause performance problems, and hides complexity
        *   **ADVICE** Have setters do nothing special, only set. Make a set method for special things
    *   Giant Prefabs
        *   hard to save, use functionality, work with other people, etc
        *   Split them up!
        *   Use nested prefabs
        *   Use prefab variants
            *   Can swap out components or visuals easily (enemies I suppose?)
    *   Not using Interfaces
        *   Get into it!
        *   Ex. of Damagable Interface as useful example
    *   Ignoring Garbage Collection
        *   Get into the habit of investigating garbage allocations every so often
    *   Not sharing work!
        *   Let other people see your work
        *   Look at other people’s work!
*   Prominent current issues overview to reach playable state
    *   A\* Pathfinding not working when moving
        *   posted in forum
    *   Ultimate Spawner waves not regening
    *   Bullets spinning in circles / general behavior issues
        *   Have some leads for this in previous notes
*   Playing with some lighting and camera settings
*   Tunnel Light as Sun in Fog Camera Settings
*   Investigating A\* Issue for a bit
    *   Adjusting paramters to see if any effect movement
        *   Max Slope - NO
*   **FIX FOUND I THINK**
    *   Rigidbody is being used for physics, and if set to interpolate it fixes things!
*   Next big issue - wave spawning
    *   Not sure if i’ve locked on to target, need to fix this to ease testing
    *   Fixed but trouble even locking targets - making new rayspawn to see specifically for this to see if it helps
*   FIxing Lock On system

    ![Lock on problem 1](Feb%207%20594206d95a7f43e4b370fd920e865e55/Untitled.png)

    ![Lock on Problem 2](Feb%207%20594206d95a7f43e4b370fd920e865e55/Untitled%201.png)
    *   This issue keeps turning up
    *   I believe issue is, if previous enemy is killed, then there is still a target. So it tries to set this on enemy that is inActive, messes up.
    *   Cleaned this up. Maybe dont need a seperate Ray for this? Leaving it for now
*   Seems to work! Visually need a more present LOCKED ON representation though
*   Bug Found - Player Character lost on turning - due to new camera movement that’s been added
*   Fixing character lost issues
    *   Need to follow in Scene View! [https://www.youtube.com/watch?v=4sUXxaXlYY8](https://www.youtube.com/watch?v=4sUXxaXlYY8)
    *   its all in localMove of playerMovement - dig into this deeper

---

## Feb 8

*   Adjusted LockOnEnemy Prefab to Cube
*   Changing music for level to something lighter to match brighter aesthetic
*   Referening Lee Gamble - HyperPassive
*   Stuggling to get the sounds i think would work for Level Test 5
*   Looking at Space Vortex sounds for ref
*   Idea: Platforms released by Object Particle Spawner - Testing this out!
*   Works! Now how to get forward movement matched to tempo?
*   Adjust Forward movement so it’s a more reasonable value
*   Object Particle Spawner working! Trying to do new music made me think - i really need to do
*   DOing tutorial on FMOD Setup for this
*   Did Karting + FMOD Integration Tutorial - useful!
*   Add FMOD Emitters for sound, tie to FMOD hierarchy and features basically
*   Now doing Koreographer tutorial on this
*   FMOD / Koreo integration - Need to enable Unity Audio for editing files in Koreographer
*   Important to to NOT disable unity audio until Builds!
*   Each Koreography is tied to one audio file, then you can add tracks to that Koreography for different timed events
*   Imp Question - How to dictate which events show up when choosing Event ID???
*   Koreographer.Instance.UnregisterForAllEvents(this)
    *   need this on enemies, bullets, etc?
    *   Or let them stay registered from startup?
*   Have old version of integration package so implementation is on hold. Waiting for that!
*   A little later then 40 min into tutorial video
*   General FMOD handy tutorial for triggered sounds with loops - too advanced! GOing from the start with these
    *   [https://www.youtube.com/watch?v=7PpSYcigCUQ&list=PLp4vT3ssm5SUqwHvbverTlYu4LwwkWHek](https://www.youtube.com/watch?v=7PpSYcigCUQ&list=PLp4vT3ssm5SUqwHvbverTlYu4LwwkWHek)
*   Trigger arrow and setting probability of event occuring - interesting!
*   Continue form Part 2 - [https://www.youtube.com/watch?v=AkKxCVnMM4E&list=PLp4vT3ssm5SUqwHvbverTlYu4LwwkWHek&index=2](https://www.youtube.com/watch?v=AkKxCVnMM4E&list=PLp4vT3ssm5SUqwHvbverTlYu4LwwkWHek&index=2)
*   Try Kart project with 2.1 version of FMOD - should be able to zip through quick
*   Build Note - enemies exist too long after death - fix this

---

## Feb 9

*   Lofelt Nice Vibrations - is this worth using? Free on Unity Store
*   Writing up gaming documents

---

## Feb Round-up

*   Added Dev features like
    *   Lock and Camera shake
    *   Transparency for locked projectile
    *   particle effects for enemy birth/death
*   Feb 2 - was still trying to fix A\* Issues! Adjusting scanning of area for better enemy movement
    *   Slow/Jagged movement issues were occuring
    *   Bullet movement and reparenting issues were occuring
*   Added war drive effect!
*   Referred back to Mix and Jam for adjustments to camera movement
*   Feb 7th - Coding tips for games! Revisit this
*   Still was having A\* issues at this point!
*   A\* fix - Rigidbody is being used for physics, and if set to interpolate it fixes things!
*   Tried fixing wave spawning issues, doesn’t seem to move through cycles properly
*   Added platform level objects spawned by tempo OPS
*   Feb 8th - Start of looking at basics of FMOD - kart tutorial
*   Also Koreographer - register / unregister for events
*   Feb 10th - 20th
    *   Mostly Ubisoft video materials, not well documented!
*   Feb 22nd
    *   Started getting back into FMOD again
    *   Was a rough week ahead of here, not a whole lot done
*   Feb 25th
    *   Started getting FMOD working with current audio in a very basic sense
    *   Realize Chronos no longer works for audio glitching - got advice from Robby Duguay
*   Spent a whole day get Master Audio AAA working because I thought I was out of luck on FMOD LOL
*   Silly but I did learn it, get it working, and thought about using it
*   Later realized I could fake audio glitching in FMOD - RObby suggested!
*   Decided on 2022-02-11 wait for it as new track for FMOD implementation of audio

---

## Ideas

*   Looking at old docs
    *   Starfox Movement - Mix and Jam [https://www.youtube.com/watch?v=yuQXeaYBuuM](https://www.youtube.com/watch?v=yuQXeaYBuuM)
        *   Fix Camera to allow slight movement like in this example? Also fluid Character movement across screen like this example?
    *   Asteroid belt of projectiles that you can grab
    *   Dead Eye Lock on - Mix and Jam [https://www.youtube.com/watch?v=jPnMVeWnZLc](https://www.youtube.com/watch?v=jPnMVeWnZLc)
    *   Ribbon trail on particles effects for creating lines
    *   Change particle speed when locked?
    *   At the end of odd grouping of locked on targets, play a tag to even it out!
        *   Evolve approach to tags a bit more?
    *   Try Flat pack to achieve Channel 21-like shading and colours
    *   Change Camera perspective in different level sections
    *   exploring possibilities with joost shader / other amplify shader possibilties
    *   Look into animation Rigging options
    *   Mario Galaxy gravity - useful in some enemy type?
    *   [https://www.reddit.com/r/Unity3D/comments/ml4loa/having_some_fun_with_my_nearly_complete/](https://www.reddit.com/r/Unity3D/comments/ml4loa/having_some_fun_with_my_nearly_complete/)
        *   Score is a Timer - you are buying yourself time by getting enemies - HEALTH
        *   select targets - freeze time lets you move their position - targets explode or chain with other enemies - No dolly, your location changes based on moving across selected targets in time.
        *   If moving selected objects while time is frozen, lock on is disabled? Only able to move object during the time freezes. this would make time freeze mechanic used quiet frequently. Also makes me think - how to do this in 3d? visually hard to read. sort of like super hyper cube
        *   Slowly emit shapes that can be drawn through lock on. emitted through bursts. completing shape does something? extra points?
        *   MATCH 2 / 3 style gameplay? Lock on / colours mean anything?
        *   GROWING SQUARES LEVEL - have things grow - camera shake when enemies beaten

Move to another set of growing square and enemies.
*   Alternate cross hairs per target / area?? Are there **combos** per enemy?
    *   Particle Spawner releases different patterns that can be drawn?
    *   Draw shapes with target selections - could this be a shield to protect against an enemy?
    *   Should I have an open world game area? You latch on to rail sections? Jet streams?
    *   Voiceover with lip sync? evangelion face in horizon - looming head - type of thing
    *   Fargo dePalma multiple camera angles - could use splitting the screen effectively like this?
    *   Look at Patapon / Vib Ribbon
    *   Lumines - integrate these ideas?
    *   Intelligent Qube - [https://www.youtube.com/watch?v=c9bilp-9HVY](https://www.youtube.com/watch?v=c9bilp-9HVY)
    *   What can be learned from Every Extend Extra?
    *   Music for 18 Musicians - In/Out of Sync elements
    *   Evangelion UI [https://www.reddit.com/r/evangelion/comments/nczhg8/i_rescored_evangelion_ui_with_my_take_on_the/](https://www.reddit.com/r/evangelion/comments/nczhg8/i_rescored_evangelion_ui_with_my_take_on_the/)
    *   I am sitting in a room
    *   Rotating bullets to next view - Do this or Drop it? Is Lock On better?
    *   Rubik’s cube but you’re the cube
    *   lock to targets - generate mesh from locations - we have a shape now - assign material?
    *   Maybe think backwards - what should happen - what sounds would then coincide with that?
    *   Puzzle Game Thinking
        *   [Mechanics](Ideas%206ba6388d395e4c1bbdb588443c610442/Mechanics%204f1c116e937e4517bb7af82809b3243a.md)

---

## Jan 29

*   Outline of key things to fix in Level Test 4 / in General
    *   Not in order of importance
        *   Chronos time control no longer working
        *   Bullets dont hits targets / loop in circles
        *   Error in Crosshair class break everything - tied to Koreographer
            *   Unregister for events necessary?
        *   Wave Spawner not working correctly
            *   Tie this to muscial cue / viz fx once working
        *   Bullet visibility still a big issue
        *   Locking on to enemy is still difficult
            *   Ray needs to see throguh certain objects?
            *   Set Reticle to be flat and not curve with camera?
        *   A\* Graph or enemies have problems when moving
            *   Post on forum about this?

---

## Jan 30

*   Working through entire Crosshair class
    *   SIDENOTE: Figure out how to hide elements in debug panel versus general inspector
*   Weird Emerald AI Projectile class issues - I THINK - deleted EmeraldAI to try and rectify this
    *   This was due to some paritcle prefabs I think! I didnt totally dissociate them from EmeraldAI
    *   Believe I can safely re-add if needed
    *   [Crosshair / Projectile bug Fixing](Jan%2030%20c3d856e971c741c29901f69bcc0c3dd3/Crosshair%20Projectile%20bug%20Fixing%2036853ac3d16c4df08caaf2ae442ac77b.md)
        *   Analyzing system - need to continue with how gameobjects move through states - what might be going wrong
*   Also need to get Wave Spawner properly working
*   Bullet homing issues - can this be solved easily?
*   Pause button not working??
*   Spawner points properly working with Spawn Master
*   Need to get Wave Graphs working - now it’s working!!
*   Need to look over Koreographer files to figure out if my implementation still makes sense
*   Updates for Slack channel ! January roundup
    *   A star pathfinding - moving platform - moving enemies
    *   Wave spawning - inspired by Boomerang X
    *   Code revisions!
    *   Music tools - Output Arcade, Slate + Ash libraries, HARD sampling
*   Things to look investigate
    *   Update Radar
    *   Investigate FMOD integration
        *   [https://www.youtube.com/watch?v=Dg32zkZt5e0&](https://www.youtube.com/watch?v=Dg32zkZt5e0&)

---

## Jan 31

*   Did a roundup of items done in December / January
*   Posted highlights of that to Slack channel
*   Adjusting Lifetime of bullets now due to issues with them staying alive forever
*   IMP: Consider which constraints need to be set in the bool values of Projectile vs the method calls
*   Pause is working again! Due to Time Koreo stuff I expect
*   Error popping up here!

    ![Error Log 1](Jan%2031%204e4bdfce8fd0416abce16f6b903570fd/Untitled.png)
    *   Maybe moving this locked config doesnt work, changed it back to Target phase
*   Now this is the issue

    ![Error Log 2](Jan%2031%204e4bdfce8fd0416abce16f6b903570fd/Untitled%201.png)
    *   Something about this section is breaking - maybe related to lock on system working badly
    *   Triggering new sounds
*   Need an event system based on dead enemies to trigger this? Wave Spawner not working or is unrealiable
*   Circle Enemy where bullets fly out of center?? cool particle effect as well

---

## January Round-up

*   Starting with mid Dec.- last time I posted on Slack
*   Last goal - “Behaviour Designer + A\* Pathfinder + Ultimate Spawner + Pool Manager. This sets me up to quickly prototype enemies + level design. Can spawn enemies with different AI patterns across a moving nav mesh in timed / staggered waves”
*   Troubleshooting!
    *   Pooled Enemies not finding their Navmesh (was developer issue!)
    *   Spawn From Pool class - careful updating! I made my own changes it looks like
    *   Looking into Behavior Designer’s setup - basic movement working but many features were not
        *   Global variables, references, conditional aborts, etc
        *   Waypoint vs random wandering - static or while navmesh moving
        *   Had Dynamic waypoints working successfully, but unsure of future utility
    *   Added basic enemy targeting. Can choose and enemy and have all collected projectiles shoot at them
    *   Bullets inheiriting enemy rotation - not fully fixed?
    *   Realized an altered or different Minimap solution is needed
        *   Due to map not updating points - want to change color of locked on vs free bullets
    *   Added some more fluid movement to projectiles
        *   Caused spinning in circles issues, but mostly resolved
    *   Adjusting camera distance / angle for better view of field
        *   Trying to make each rotated perspective see more / enough of others
    *   Tried bringing in Deathmatch AI but too many features I don’t need
        *   Complex but also simplifies some aspects, would take a lot of customizing and digging MAYBE
    *   Sped up editor → play time with a cool trick!
        *   [https://www.youtube.com/watch?v=P7cYVg5fAvY](https://www.youtube.com/watch?v=P7cYVg5fAvY)
    *   Tried solutions like Mesh Combine / Mesh Simplify / Mesh Baker to make 1000’s of things work in a scene
        *   Mesh Baker / Texture Baker worked best - bake in quadrants so things can be culled
    *   Used Automatic LOD free asset to generates LODS and it works well!
    *   Tried various ‘paint prefab onto scene’ assets and Prefab Brush+ worked well!
    *   GPU Instancer seems better then all Mesh combining assets for scene optimization
        *   Works great, remember if you add things to scene, need to reregister objects
    *   Have Ethereal URP somewhat working? Not entirely sure how I feel about it
        *   Could be cool for Control-like effects
    *   Imlemented IDamageable from Beahavior Designer for Attack / Damage
    *   Behavior Designer
        *   Scoot and Shoot / Flank both working, so Tactical pack things should all work?
    *   Modeled some enemies with ‘corruption’ shader on them
    *   Disabled target cube for visual clutter, but need to look at it’s use again!
    *   Lots of different assorted bugfixing!
*   Ideas!
    *   Use a similar effect for one level? [https://www.youtube.com/watch?v=3AIsleRlx5Q](https://www.youtube.com/watch?v=3AIsleRlx5Q)
    *   Use Judgement Silversword Shield effect ?
    *   Lerp colors! [https://oxmond.com/changing-color-of-gameobject-over-a-period-of-time/](https://oxmond.com/changing-color-of-gameobject-over-a-period-of-time/)
        *   Probably use DOTween or something
    *   how to hide stuff in debug but remain public?
    *   Reference Area X for visual clarity when lots of far away bullets / items are on screen moving around

---

## July 11

*   Aiming Issues
*   Idea: Fire from central turret poisiton
*   Adjusted the way the raycast works, now coming form single position and rotates depending on Aim Calibration postion - working better! Could use refinement
*   Fixed LookAtCamera script bug - finds player objects to look at now. Likely need some reworking

---

## July 12

*   Aim of Enemy Lock on is off
*   Realized this is due to being child of reticle, when it should probably just follow reticle by rotating
*   Is RaySpawnEnemyLock being used for anything? verify this / delete if not needed
*   Many problems with Cone enemy detection and Look At Constraint
*   Looking to switch back to raycast implementation of Enemy Locking
*   Changed to use current default Rayspawn, not a new one.
*   Does it work?
*   EXTREMELY hard to lock onto enemies this way!
*   Trying with seperate RaySpawn for Enemies
*   Used seperate Ray
*   Works better! Working great!

    Three bugs to address
*   Turn off locking in transitions - need to jump into next wave without locking music
*   Bullets locked mid air - whats going wrong here?
*   Assets value errors - Pooling / Object Particle Spawner problems - debug!
*   [Optimistic Nihilism - YouTube](https://www.youtube.com/watch?v=MBRqu0YOH14)
*   Purpose as rhythm? Is there a rhythmic aspect i should try locking in to this game? a wind up start? repeatedly pressing some button?
*   [VIDEOBALL final gameplay trailer!](https://www.youtube.com/watch?v=dUxJspLDWdw&t=45s)
*   manipulate time more, as happens in the video ball trailer?

---

## June 1

*   Shape matching decals in URP
    *   [https://www.reddit.com/r/Unity3D/comments/v1ombe/ive_always_used_planar_quads_for_decals_but_with/](https://www.reddit.com/r/Unity3D/comments/v1ombe/ive_always_used_planar_quads_for_decals_but_with/)
*   Cool mobile rhythm game concept / control scheme
    *   [https://www.reddit.com/user/GitaMan14/](https://www.reddit.com/user/GitaMan14/)

---

## June 10 - Links

*   Shader Graph
    *   [Zii](https://www.youtube.com/channel/UCGZfhO-5gXpTeS10pXGvXoA/videos)
    *   [Gabriel Aguiar Prod.](https://www.youtube.com/c/GabrielAguiarProd/videos)
*   Shaders
    *   [Glass Shader - URP | VFX Shaders | Unity Asset Store](https://assetstore.unity.com/packages/vfx/shaders/glass-shader-urp-187470)
*   Trails
    *   [Ara Trails | Particles/Effects | Unity Asset Store](https://assetstore.unity.com/packages/tools/particles-effects/ara-trails-102638)
*   Datamosh
    *   [https://github.com/keijiro/KinoDatamosh](https://github.com/keijiro/KinoDatamosh)
    *   [Trigger Camera Component (activate and deactivate)](https://answers.unity.com/questions/1571899/trigger-camera-component-activate-and-deactivate.html)
    *   [Creating Your Own Datamoshing Effect](https://ompuco.wordpress.com/2018/03/29/creating-your-own-datamosh-effect/)
        *   May be something within this
    *   [How to Use Shaders for Simulations - Alan Zucconi](https://www.alanzucconi.com/2016/03/02/shaders-for-simulations/)
    *   [Adding a modifable variable to a shader](https://www.reddit.com/r/Unity3D/comments/9hlfp2/adding_a_modifable_variable_to_a_shader/)

---

## June 10

*   Fixed UI crosshair bug - probably want to do more with how this design looks and works eventually
*   Keep hitting this error once the level progressess

    ![BD AI Issue 1](June%2010%206e1131e0df804ca19f1cbc3716c29ae6/Untitled.png)

    ![BD AI Issue 2](June%2010%206e1131e0df804ca19f1cbc3716c29ae6/Untitled%201.png)

    ![BD AI Issue 3](June%2010%206e1131e0df804ca19f1cbc3716c29ae6/Untitled%202.png)
*   Thinking about A Star problems - should I make a whole new project for testing A Star?
*   AI Testing is a project for figuring out if A Star will work with Behavior Designer options
*   Does simple smooth on bot work? Left it on the bot
*   BD Movement seems to work fine.
*   Removed AIPATH and replaced with LOcal SPace Rich AI
*   Trying BD Tactical now…
*   Complaining there is no RVO controller - different asset needed?
*   No RVO controller needed - was just camera confused about what assets to control
*   Issue when agents and enemies are repositoned upon scene start - change code to fix thsi?
*   Otherwise cant test properly
*   SOrt of fixed that, but behaviors working intermittenly - not sure it’s stable

---

## June 13

*   Scan effect as time reverse?
    *   [Released an update to my FREE post-processing scan effect for URP (GitHub link inside).](https://www.reddit.com/r/Unity3D/comments/vai8f3/released_an_update_to_my_free_postprocessing_scan/)
*   Work on AI issues
*   AI Issues
*   What is possible with the moving AI system?
*   Do I have to restrict myself to this?
*   Cant seem to get many features working with moving platform AI
*   Disabled rotation! Just does not seem to work properly
*   Some other issues - Agents getting stuck on other agents
*   Local Avoidance - This can be done with RVO - “RVOController to a GameObject with an AIPath or RichAI component.”
    *   [Local Avoidance](https://arongranberg.com/astar/docs/localavoidance.html)
*   Two approaches come to mind for enemy out of sight issues
    *   Make AI only go to certain locations in the gameplay plane
    *   Widen the perspective / allow horizontal movement to see corners better
*   Tagging is a good way to restrict walkable areas
    *   [Working with tags](https://arongranberg.com/astar/docs/tags.html)
*   Boomerang X does not have crazy AI - reference this !
*   Looking at camera movement possibilities.
*   Reticle now looking at Aim Wall to free up connection with Camera
*   Increased Repeat Attack Delay on Diamonds to 2 for less frantic bullet hell!

*   Changed Reticle - Not sure what I think! SOmething like this could good butnot totally there yet
*   RIGHT glitch as BOMB that destroys current bullets on playing field
*   For all bullets that are not locked - Death
*   Narrative - Judeo-CHristian stories
    *   [Ophanim](https://the-demonic-paradise.fandom.com/wiki/Ophanim)
*   Use the Kitbash Spaceships models in Blender and decimate a bit, make them look a bit more interesting
*   Take concept from reboot a bit - travelling systems - surfing the net? These are the environments
*   Sci Fi Industrial buidlings will work well for a level - editing this now

---

## June 14

*   Select all vertices and Delete Loose was helpful, though it might delete too much
    *   Mesh → Cleanup → Delete Loose
*   In Unity

    AutoLOD needs to have Save Meshes enabled and they need to be reassigned hwen dragging a prefab into it. Otherwise we get the GPU Instacer errors cause there are actually no meshes assocaited with other lod groups.
*   Dolly relative to player perspective - figure this out!
*   Quick way in scene / editor mode to positon player at dolly points
    *   Just swap paths file! Doesnt break anything
*   Is it differnet if I transfer into new dolly than start at it?
*   Not an issue if I got from city to datastream
*   Pidi Planar reflections - look into it!
*   Level Progression
    *   Datastream → City → Weird Void
*   Adjust Scale script on void / pipe to create cool visual effects
*   Need to either start game on DATASTREAM or need to have gameplay plane set to proper starting position - one of these seems to make the enemy AI work
*   Unity Profiler Webinar
    *   Disk IO is extremly expensive ! Watch out for performance
    *   WHen youre in frame budget - stop profiling! Choose per device
    *   Mobile - 30 FPS is solid
    *   Console - 60 FPS
    *   Shader tool profiler FOR ARM - Mali Offline Compiler
    *   Something like this for pc / console / desktop?
    *   Top tip form ebook unity profiling!
        *   Profiler Analyzer - Before and After an optimization
        *   But you can aggreegate a bunch of frames at once for one capture and it can
*   Play with this!
*   Now learning more about the profiler
*   Shader Variants
*   Taking 20 minutes to build! Way too long. Shader issue I believe

    ![Build time errors](June%2014%20d4befec92fa24d3a98c8f0d20881abef/Untitled.png)
*   TIPS
    *   Graphics API settings
        *   Reduce this, we dont necessarily need both
    *   If things dont need shadows on certain objects, fog, gpu instancing, then disable it. Will lower shader variants.
    *   Use mono for compiling, IL2CPP is not necessary during development
        *   Can use for final build but it takes WAY longer according to people online

        ![Building Settings](June%2014%20d4befec92fa24d3a98c8f0d20881abef/Untitled%201.png)
    *   You can do into shaders and see how many variants they have
    *   Make a Shader variant collection or use Shader Control to disable un needed shaders?
*   Made an Eye Basics script for the eyes that follow player in the Ophanim loop
*   Putting the scanner script on my rewind fx - not working due to no geometry i think?
*   Look at other wave emission effects to use?
*   ALSO Glitch effect - destroy all current bombs with this and gain back some stamina? Could balance out time reverse stamina usage
*   Go over June 13 as well
*   ANGELS
    *   Ophanim
        *   group of angels that are famous for their knowledge and wisdom
        *   Thrones / Wheels
        *   who are never asleep and are always guarding the throne of the god
        *   Astrolabe?
        *   Maybe the Ophanim are lost but their rings maintained
        *   Wiki - According to this conception, the heavenly Seraphim and Cherubim as well as the Ophanim continue to **aid humans in spiritual evolution**
            ; as do the heavenly Archangels and Angels.

---

## June 17

*   Think Neon WHite - how to expand on mechanics?
*   Still caught up on this!
*   Need more enemy variations - help mix up the waves
*   Need varying behaviours / movement patterns
*   Maybe not all shoot bullets?
*   Need one hit enemies, at least at first.
*   Narrative: You’re showing up in a sector - these are old data beings trying to communicate.
*   You’re sending the communications back at them. They expect a response, so getting these sent message back corrupts them. THOUGHT you alter it when you lock on, then send it back and it confuses them.
*   They’re not real beings just mindless protocols.
*   Think Reboot Internet ideas, more wild and pirate like.
*   You’re a digital archaeologist, venturing to old parts of the web. Or maybe it’s religious studies?
*   **According to QM, before you look inside the box, the cat is both in an alive state, and in a dead state, and the bomb both did and did not go off**. It is only when you look inside an see either an alive or dead cat that the cat actually becomes either alive or dead. Prior to looking in, it was both at the same time.
*   Could the old data / old ways of doing things VS the new be a nod to quantum mechanics?
*   On a personal level, it touches on how what we believe to be true is truth.
*   On Quantum Mechanics
    *   "Okay, see this stick? If I swing it, it moves swoosh. If I hit it against something, it stops moving smack. If I let go of it, it falls down clatter. You're not very surprised by this, right? That's because everything in the world uses the same rules. About three hundred years ago, a guy named Isaac Newton wrote down all the rules, and we call them the laws of physics. Scientists still use his versions of the laws for all sorts of stuff, but there are a couple places they found where things are a little bit different. One of them is for things that are very, very small. So if I break this stick in half crack, both halves work just like they did before, right? swoosh, smack, clatter And if I break one of the halves snap it does too. If you had a tiny saw and a microscope, you could keep making smaller and smaller sticks, and they would all work the same way, right? Well it it turns out that when things get small enough, smaller than things that are too small to see, they start to act a little bit weird. So imagine this stick is just one of those tiny tiny pieces inside the stick. If I throw it to you and you catch it, then someone uses a stick-finding machine, it might turn out to still be in my hand, or it might be in your hand like it would if it was a normal stick. Yeah, it's weird: scientists were really confused about this when they started seeing it, and a whole lot of them working together took about fifty years to get it right, because it's so strange. Eventually they figured out that the tiny stick, and everything else that small, is actually always in multiple places at once. So even though we think it's in my hand, it's actually also in my other hand, and already on the ground, and still in my hand but just a tiny bit to the side of where we thought it was. Even stranger, the stick is more in some of these places than it is in others: most might be in this one spot in my hand, but less in my other hand, and just a tiny bit on the ground. And it's all still the same stick. This is all really weird, but one thing about it is still perfectly normal: all of the places the stick is in still follow Newton's laws. If I drop the stick from my hand onto the stick on the ground, it'll stop when it hits the ground, and the pieces will add up, so most of the stick will be on the ground. (I think now would be a good point to mention that yes, a real conversation with a 4 year old wouldn't go like this. You'd have to stop and answer questions and re-explain pieces of it. I'm just proof-of-concepting this.) Okay, so remember when I said the stick could be in more than one place? It can also be going more than one speed! So some of the stick could be on the ground and not moving, and some could be in the middle of falling down, and some could be just starting to fall out of my hand so it's still moving slowly. Another very smart man, this one is named Werner Heisenberg, figured out that the more different places the stick is in, the less speeds it is moving in, and the more speeds it has, the less different places its in. So if all the tiny parts of everything are acting this strange, how is everything so normal when you get back to big things like us? Well, there is one more odd thing about tiny tiny things that I haven't told you yet. Say I have TWO tiny tiny sticks. You know how one can be in a whole bunch of places at once? Well it turns out that sometimes you have to take both sticks together to figure out how much is in any place. So maybe for most of the tiny sticks, stick one is in my hand and stick two is on the ground, and there's also some where stick two is in my hand, and stick one is on the ground. But, there's no sticks where both are in my hand at all, even though both sticks by themselves are at least a little in my hand. (This is where the kid will have the most questions, and also probably where anyone reading this is going to have questions, so ask away. Though tell me whether I'm allowed to use grown-up words like "particle" or I have to keep saying "tiny stick.") When how much of one tiny tiny thing is in one spot depends on how much of another tiny tiny thing is in another spot, scientists call those two things "entangled." That makes it sound like it's a special, weird case, but it's really the other way around. Scientists go through lots of trouble to get tiny things that aren't entangled so they can study them, but just about everything is entangled most of the time. All the tiny tiny pieces of stick in this actual stick are very entangled with each other. That's how big things like us and this stick don't seem like they're in more than one place at a time. The pieces of stick are all in a few places at once, but every different group of tiny sticks adds up to the big stick in my hand, even if the tiny pieces could be swapped around a little.”
*   Environment Assets - Sci Fi city Scape - kind of cool, could be useful!
*   THink about Dune and negative space for visual aesthetic - making the player feel small
*   Chart out a level narrative?
*   Need to work on player animations as well!
*   **Blender**
    *   [https://www.youtube.com/watch?v=ij_y6rBawl0](https://www.youtube.com/watch?v=ij_y6rBawl0)
    *   How to use Random Flow extension
*   Dodging - Take a risk to look and feel cool.
    *   [20Min fGREATNESS - BEST #nitytip](https://www.yuub.m/wtch?v=QnAc2EDI0)
        *   Panzer Dragoon Saga turned based combat - how does dodging work there?
        *   Geluniypvie
        *   I frames when turning?
        *   CotrollblSKybox?r
        *   Mly baeuutfulsn eup-o jutlkpiskdofing
        *   [https://asstst.unity.m/ckageo/vtx/khedeps/retrowove-kmes-dynsm-synwve-kyx-asst-pk-197258](tps://ssetste.uity.com/ptktgcs/vfx/shakers/retrodave-skies-dyeami -sy thwavgtskybrx-assep- ack-197258)
        *   Nced - athinkr- ahsthama/ointing? What intxt
        *   **Think Rvz**
        *   Th iwaag a eauhsbet shatev l RUNNINGOum n fivilize,ion,
        *   Thd btsses repabs steuhe lvolutso of lifeoin carre, life
        *   Your avatar ripnesgcts staoesllf "enlcthtehaent"lts you level up.e
        *   Whtdoe he woldlekd ake?
        *   Digitalocurrnpdion - buildang FdXstfoyed thb!edaudltrsrucur
        *   Japot loves wiresorninlightp los sd suchr- v sysacst etpa!!
        *    to fingiqei etabshc- makessessethsarodealnviometsndin ttructofromirheo
            kAtObject is breaking things? Not sure why

---

## June 2

*   Notes on assets
    *   Used C Scape for Procedural city? or CiDy2?
    *   Prefab World Building for organizer prefab use?
    *   Quick puzzle sectiosn with a puzzle asset?
    *   Tanks multiplayer rhythm idea?
    *   FX packs? Wall FX
    *   Motion Mathcing pack? What is motion matching?
    *   Mesh Animator for crowds or other objects?
    *   Animation designer asset - is this useful???
    *   SpriteLights for point lighting effect?
    *   Mirror asset - cool effect! Many Mirrors!
    *   Is Forever Endless runner useful for levels? Look at the setup?
    *   Whats a good AI solution besides Beahvior Designer?
    *   Look at missile and rocket behaviors for ideas on projectile movement (asset)
    *   Any procedural level generators useful for what im doing, or no?
    *   Advanced DIsolve - cool effect?
    *   Sci Fi Corridor set - any utility here?
    *   Distortion shader pack!! Looks cool
*   Links to follow up on
    *   [https://unityassetcollection.com/sci-fi-forcefield-hologram-free-download/](https://unityassetcollection.com/sci-fi-forcefield-hologram-free-download/)
    *   [https://unityassetcollection.com/sci-fi-environment-free-download/](https://unityassetcollection.com/sci-fi-environment-free-download/)
    *   [https://unityassetcollection.com/sci-fi-vehicle-constructor-free-download/](https://unityassetcollection.com/sci-fi-vehicle-constructor-free-download/)
    *   [https://unityassetcollection.com/hololens-shader-pack-free-download/](https://unityassetcollection.com/hololens-shader-pack-free-download/)

---

## June 22

*   Playing some games and Steam demo stuff to see what’s out there
    *   Sonority - musical puzzle game for children - memorize musical sequences to solve puzzles. cute but not that interesting for me
    *   Spikair Volleyball Demo - Neat concept, works well. simplifed dodgeball with cool controls for swithcing players. could get into this!
    *   Mortal Sin - cool first person action game, sword melee thing. distinct visual style. fun action!
    *   Graze Counter GM Demo - Good easy-ish bullet hell game, set some attack patterns and attributes before you start and then its general bullet hell mode. Would play more!
        *   Has a cool start to they story worth thinking on

            A malfunction in the EDEN VR system puts the 100 inhabitants at risk, you’re going into take a look and try to fix it / save them
    *   Twin Edge - typical keyboard tapping rhythm game, nothing special. uses circles as playing field
    *   Redout 2 - cool style but maybe too slick and controls aren't feeling great for me. Requires higher precision for turns
    *   Nex Machina - good game! Bullet hell thats not too hard and moves quick
        *   You save people in each stage while managing to evade and destroy enemies. Maybe some type of save people / collect items mechanic would work in my game?
    *   Kromaia - some interesting visual choices but gameplay is lacking
    *   Anger Foot - fun straightforward melee fps combat. Simple mechanic with fun aesthetic , refined.
        *   Good one to think about!
    *   Fashion police squad - good gimmicky shooter, not a lot to the gameplay but entertaining
    *   Hypercore - some interesting ideas for boss mechanics, worth considering that. Bullet patterns are fun and dodge is good, but not sure how to implement yet
    *   Subway might - good atmosphere! Interesting game, visually well done!
        *   Good example of simple premise and restraint paying off

---

## June 23

*   Favelas abandoned places - private business and government gave up on them
*   Parallel with the old internet
*   Parallel with old gods and angels / religion
*   A moral backbone to society (for better or worse ) ???

---

## June 28

*   MentalCheckpoint Rhythm game thoughts - 2 parts!
    *   [Why Rhythm Games Haven't Changed for 20 Years](https://www.youtube.com/watch?v=wb45O2NciL8&list=PL67pghfWOIM3r3fd_ydsyr0HvuGH7gcXd)
        *   Whats the hook?
        *   Catching notes on a judgement line - need good telegraphing!
        *   Flexible mechanic that can have depth
        *   Stretch an idea out to many iterations!
        *   Flow as most important aspect of rhythm game!
        *   Beatmap / charting
        *   Continuous clusters of notes to create Flow
        *   Intensity curve important! cant go all hard all the time
        *   Teaching mechanics through visual and audio cues
        *   Rhythm Doctor example highlighted
        *   Performative play
        *   How the player feels and looks while they play the game
        *   IDEA: Dance Rush Stardom applied to a controller?
        *   Difficulty Depth
        *   As much depth as possible out of as little complexity as possible
        *   Difficulty in sight reading and Difficulty in control
        *   Hitboxes for timing - how much lenience is important - too forgiving its too easy!
        *   Community Content
        *   Keeps games going on for super long! Clone Hero, Step Mania, Osu, etc
        *   Account for modding and level editors
        *   Progression Systems
        *   Campaign or story mode → how does this progress? Need flow
        *   Taiko game - layer of collectibles built into rhythm gameplay
        *   Sound Voltex - Boss songs as a way of unlocking another song. finish 3 specific songs to unlock the 4th
        *   Accessibility
        *   Visual accessibility options can be helpful
        *   More customization helpful for access to game
        *   How to instill a sense of wonder?
        *   What do we do that causes Breaking sense of wonder?
        *   [https://www.youtube.com/watch?v=Xd7u6r5IvGQ](https://www.youtube.com/watch?v=Xd7u6r5IvGQ)
            *   Evoking Wonder
                *   Witholding info from your player
                *   Leave some things out on purpose!
                *   subvert expectations and skeptcism!
                *   Acknowledge the players efforts when they dont think you will
                *   Negative possibility Space
*   Advice - learn not to fall in love with your own work
*   Be good at being wrong!
*   May need to change direction and throw ideas aside
*   But keep a document of those old ideas!
*   Limiting Creativity
*   Limitations to stoke creativity - remember a game is never done, only released
*   Fog in silent hill as limitation made creativity
*   What am I even making anymore????
*   SIDENOTE: Thumper meets Pacman idea
*   On story, use of technology, and more
    *   think eight grade - what are the kids doing?
*   On BLade Runner:
    *   Rutger Hauer was the antagonist to Decker's protagonist but the villain was the world/Tyrell corporation.
    *   reminds me of ‘simulacra & simulation’: culture has become a copy without an original
*   Scaled Down Scene - How to improve?
    *   Are bullets too far away / outline makes them appear closer?
    *   Made some adjustments! Minor improvement
    *   Need to adjust rotation of characters at edge of screen - attempting something!
    *   Referencing originam ax and jam starfox code
        *   Top line commented out is error - WHats wrong here?

            ![Old Code Error](June%2028%20a14d15ce6eb74fc596072af22d236133/Untitled.png)
*   Can I add boomerang x teleport mechanic? Think about this? Surfboard ? Teleport away from it but need to return back / quick button for returning back?
*   How does it integrate with music?

---

## June 29

*   Render Camera broken - this is for the UI overlay
*   Consult documentation on how to fix this
*   Don’t recall it being too hard!
*   Need to get my backup system going again!

---

*   Using backup form June 27th
*   Should be able to recover everything from there or a few days earlier
*   Important things now
    *   Better aiming
    *   Deeper mechanics
    *   Thematic elements beyond cyber setting / world of decay
    *   Storyboard this game / progression
        *   [Storyboard Intro](June%2029%20c2cb95adb0fb4a818f5372fb34a60952/Storyboard%20Intro%20ebc813b2c63340b69614cab102afa4aa.md)
*   FInd turrent shooting mechanism, try it in scene, copy approach for my perspective
    *   Its just a turret where you dont see the barrel?

---

## June 3

*   Looking at cover of Rez PS2
*   What if you could lock on to other things besides bullets? environment? Anything?
*   Musical choices as data
    *   *All* data is semiotic. Even choosing what not to count is a political act.
*   Similar mechnics idea! How to NOT be that? or Evolve this??
    *   [Hayd_n on Twitter: "TARGET ACQUIRED please ignore the HUD elements that don't do anything yet - unless you think they're really cool then feel free to comment on that pic.twitter.com/ZBVh7y9Anl / Twitter"](https://twitter.com/Hayd__n/status/1533104933914005504)

        ![UI Inspiration](June%203%20e34c2a79d2794cf8b7f980dbc7e7c7db/Untitled.png)
        *   Not the UI that tells you your options / is a combo display, explained in tweet. Interesting!
*   Burst compiler to speed up game instead of gonig full ECS? Look into this

---

## June 6

*   Breadcrumb AI?
*   Curved World
*   Polarith AI Pro
*   uPattern for geometry
*   [Having some fun with my nearly complete recreation of Mario Galaxy's gravity system, with physics!](https://www.reddit.com/r/Unity3D/comments/ml4loa/having_some_fun_with_my_nearly_complete/)
*   ['Ynglet' Is a Musical Platformer That Plays Like a Beautiful Instrument](https://www.vice.com/en/article/5dbd9a/ynglet-musical-platformer-review?utm_source=waypoint_twitter&utm_medium=social+)
*   [Mesh Effects Notes](../../../../Old%20Rez-Like%20Docs%206ac117418ee14c468f301157e3343721/Unity%20Assets%208c7cfbbc2e434620acf53d6c11bfff1d/Mesh%20Effects%20Notes%2099e381df18d54e349d1f82c1f2b00d6e.md)
*   [Puzzle Ideas](../../../../Old%20Rez-Like%20Docs%206ac117418ee14c468f301157e3343721/Puzzle%20Ideas%2025a0685d0da94720b2f6fc45bbc3da6b.md)
*   [Level Design](../../../../Old%20Rez-Like%20Docs%206ac117418ee14c468f301157e3343721/Level%20Design%2040b5bce85a6b406baf4d1e08b9d01c7f.md)
*   [General Advice](../../../../Old%20Rez-Like%20Docs%206ac117418ee14c468f301157e3343721/General%20Advice%20b91d1af183ca47f4af72f0121445466a.md)
*   [Graphics Ideas](../../../../Old%20Rez-Like%20Docs%206ac117418ee14c468f301157e3343721/Graphics%20Ideas%20d27c1c46acf3423d888e0b27f87017c2.md)
*   [Thoughts on Gaming](../../../../Old%20Rez-Like%20Docs%206ac117418ee14c468f301157e3343721/Thoughts%20on%20Gaming%2047b946bda433443aa3e7400c277e0c76.md)
*   [Inspo](../../../../Old%20Rez-Like%20Docs%206ac117418ee14c468f301157e3343721/Inspo%20b70c1319099c442b9b4f8247de61cef2.md)
*   [Design Document](../../../../Old%20Rez-Like%20Docs%206ac117418ee14c468f301157e3343721/Design%20Document%2014aa543c3a9d4cb19f7169afebf8a169.md)
*   [Core Design](../../../../Old%20Rez-Like%20Docs%206ac117418ee14c468f301157e3343721/Design%20Document%2014aa543c3a9d4cb19f7169afebf8a169/Core%20Design%201bb119e9b6aa4cfdbbd87e661dfa3baf.md)
*   [Story / Concept](../../../../Old%20Rez-Like%20Docs%206ac117418ee14c468f301157e3343721/Story%20Concept%2091b21355c78d472b96d314b94c9b3646.md)
*   [Ideas](../../../../Old%20Rez-Like%20Docs%206ac117418ee14c468f301157e3343721/Ideas%206743893e6df5439ca8c9b0b5f7e0f5d4.md)
*   [Vision / Systems](../../../../Old%20Rez-Like%20Docs%206ac117418ee14c468f301157e3343721/Vision%20Systems%200d6ec68a6d5943929626c73725b7be65.md)

A painting conveys what it’s like to experience the subject as an image; a game conveys what it’s like to experience the subject as a system of rules. The player of games doesn’t just play within the system of rules, Anthropy explains, she plays with the rules. As she plays, she continually assesses the state of the system, looks for opportunities to take advantage, identifies and implements the best strategy, modifies or glitch the rules to improve the experience. - Anna Anthropy
*   [What is "videogame literature"?](https://iblog.iup.edu/thisprofessorplays/2018/01/09/what-is-videogame-literature/comment-page-1/)
*   What can be learned from Every Extend Extra?
*   Maybe think backwards - what do you want to happen - what sounds would then coincide with that?
*   FX
    *   Keijiro VFX Graph TestBed 2
    *   Watercolor + Other assets?

---

## June 7

*   What is a game?

A painting conveys what it’s like to experience the subject as an image; a game conveys what it’s like to experience the subject as a system of rules. The player of games doesn’t just play within the system of rules, Anthropy explains, she plays with the rules. As she plays, she continually assesses the state of the system, looks for opportunities to take advantage, identifies and implements the best strategy, modifies or glitch the rules to improve the experience. - Anna Anthropy
*   Core game
    *   Rez + Panzer Dragoon + Dodgeball?
        *   Waves of enemies + Different areas per wave + different musical loops
        *   Movement - Forced forward + look around
*   Beat Blast
    *   Sequencer decides when you can shoot - adjust when collecting items
    *   Movement / dodging more critical when you cant decide when to shoot?
*   Ideating
    *   Movement possibilities
        *   Thumper rhythmic action?
        *   Avoid objects?
*   What are mechanics in making music? How does one interact in a performance and also in composition?

    Added stamina for LockOn so that it can’t be held for too long!
*   Automatic fire when running out placed on OnMusicalLock
    *   Seems better than OnLock logically
*   Enemy bullet shooting sound for melodies / sonic element that we are responding to?
    *   Doesn’t work with a lot of bullets?

    RESET added to stamina controller on lock on transition - release everything, Stamina bar should be reset to default
*   Reverse time to deal with overhwleming bullets - implement proper musical chnage with this
*   Reverse Audio - use a cue tow swap currently playing track - swap back!

    Added Rewind Parameter to Drum and Drum reverse track, so when rewinding we hear reverse perc
*   Need more impressive reverse sounds!
*   Need more content in music !
*   Make a visually dynamix level
*   There’s a rhythm to being surrounded by enemies
*   Push / Pull of Reverse time and Lock on to destroy
*   Overwhelmed by bullets

    Added Drum FX logic and Parameter for more sound variation

    Added harmonic noise audio tracks to each loop sections
*   Attributes that could be influenced by power ups
    *   Lock On Stamina
    *   Total Lock Ons
    *   Time Rewind Stamina
*   Data Mosh Unity
    *   [https://ompuco.wordpress.com/2018/03/29/creating-your-own-datamosh-effect/](https://ompuco.wordpress.com/2018/03/29/creating-your-own-datamosh-effect/)
    *   Can’t seem to get this or Kino Datamosh (cinemachine incomparitbility) working
    *   Thinking on VFX Graph for this

---

## June 8

    Edited version of Warp Effect for Long Rewind - this is temporary!
*   VFX graph works well for this - can control particles nicely
*   Making sure dolly switcher works
*   Looking into Rendering enemies and bullets on top of everything else.
*   Editing QUibli Renderer for this
    *   Tried this, didn’t work
    *   [CREATE YOUR OWN RENDERER WITHOUT CODE in Unity!](https://www.youtube.com/watch?v=szsWx9IQVDI)
    *   May need to seperate Projectile object from model for proper RenderOnTop Layer support
    *   Why do I have Projectile Layer? For Radar?
    *   Things are darker now using Render on Top - why?
    *   Used this video
        *   [Stealth Vision in Unity Shader Graph and Universal Render Pipeline](https://www.youtube.com/watch?v=eLIe95csKWE)

    RenderOnTop was added and is working! Colors are strange but it looks cool?
*   Looking at ghost trail options for Rewind effect
*   I like trails in general, trying them for now

    Feedback Long Rewind happens when rewinding now
*   Use right glitch as bomb? Need lots of stamina for it?
*   Need environments to ignore raycast so that I can see enemies through the muck?
*   Seems lighting depends on the current skybox color when generating
*   Find out what things are set to ignore raycast and why - maybe dont need it on certain environment
*   Seems like lighting and specular aren’t being applied to Ignore Raycast - look at that!
*   Look liks Culling mask of light source needed Ignore Raycast added to it - should help all objects on RenderOnTop as well!
*   Eveyry time i build asked about shift outline - what is it?
*   Amplify animation pack causes errors on build as well- fails - why?

---

## March 1

*   FMOD Tutorials
*   Transition Regions and Transition Markers in old tutorials
*   Does it make sense to just use Magnet regions? Can move forwards and backwards with them
*   Do all these separated audio clips mean a lot of work for Koreographer implementation?
*   Double click logic tracks to bring in faces etc for better transitions - can add new sounds this way too - like stingers
*   Not looping cleanly - can fake this with a Transition Marker that pulls you back? Is this a bad practice?
*   Investigate transitioning between Magnet Regions - Can this happen smoothly?
*   What are FMOD Playlists?
### [**Event workflow**](https://www.fmod.com/resources/documentation-studio?version=2.02&page=welcome-to-fmod-studio-new-in-200.html#event-workflow)

New features have been added to assist in keeping events tidy. Firstly, it is now possible to name [**loop regions**](https://www.fmod.com/resources/documentation-studio?version=2.02&page=glossary.html#loop-region). This allows you to transition directly to a loop region without needing to add a [**destination marker**](https://www.fmod.com/resources/documentation-studio?version=2.02&page=glossary.html#destination-marker).

### [**Action sheets**](https://www.fmod.com/resources/documentation-studio?version=2.02&page=welcome-to-fmod-studio-new-in-201.html#action-sheets)

Action sheets provide a new, simplified way to trigger [**instruments**](https://www.fmod.com/resources/documentation-studio?version=2.02&page=glossary.html#instrument). Instruments placed on an action sheet are triggered when an [**event**](https://www.fmod.com/resources/documentation-studio?version=2.02&page=glossary.html#event) is played. Action sheets can be set to play instruments in a consecutive or concurrent fashion. Instruments on action sheets are always routed to the event's [**master track**](https://www.fmod.com/resources/documentation-studio?version=2.02&page=glossary.html#master-track).

### [**Output Ports**](https://www.fmod.com/resources/documentation-studio?version=2.02&page=welcome-to-fmod-studio-new-in-202.html#output-ports)

Sending audio to auxiliary output ports has traditionally been a programmer task, but now it's part of the sound designer workflow within FMOD Studio.

With ports, you can make use of your target platforms' features for handling online voice chat and in-game music, route sound to individual controller and headset speakers, control controller vibration, and more.

Looking over FMOD 2.0 + Additions - Things to Investigate
*   Labeled Parameters
*   Global Parameters to effect many event instances
*   Built-in Speed Parameter
*   Flexible Paramters - One parameter changes another parameter
*   Nested Multi-Instruments
*   Command Instruments - lots to this i think!!

Trying implemnetation with Koreographer

**Can use code in script  FMOD Studio Parameter Trigger as basis for swithcing sections**

Set player to Trigger - does this break anything? Seems ok!

Now need to make sure locking and shooting work fine

---

## March 11

*   Looking at state machines to clean up my code
    *   [https://www.youtube.com/watch?v=G1bd75R10m4&t=2s](https://www.youtube.com/watch?v=G1bd75R10m4&t=2s)
    *   Not sure if relevant to what I need right now
*   Doing inventory of March issues and things done
*   Listening to Infallible Code Stream while working on a few things!

---

**Grants**

*   [https://www.arts.on.ca/grants/media-artists-creation-projects](https://www.arts.on.ca/grants/media-artists-creation-projects)
    *   April 7th Deadline!
*   [https://cmf-fmc.ca/program/innovation-experimentation-program/#referencedocs](https://cmf-fmc.ca/program/innovation-experimentation-program/#referencedocs)
    *   May 4th!

---

**Things to Implement**

*   Stamina Bar for Rewinding
    *   [https://www.youtube.com/watch?v=sUvwKH7qyQQ](https://www.youtube.com/watch?v=sUvwKH7qyQQ)
    *   Another example [https://www.youtube.com/watch?v=Fs2YCoamO_U](https://www.youtube.com/watch?v=Fs2YCoamO_U)
*   Paramemter: User Labeled instead of using values
*   Collision to change song section not reliable - Alternative Methods
    *   Waves of Enemies killed
    *   Time Passed
    *   Kill certain enemies?
*   Magnet Regions - Can transition areas be used with this?
*   State Machines - valuable to my framework?
*   Too Many If statements!
*   Also - Command Patterns - why use?
    *   [https://www.youtube.com/watch?v=f7X9gdUmhMY&t=3s](https://www.youtube.com/watch?v=f7X9gdUmhMY&t=3s)
*   Projectiles that are launchable VS unlaunchable
    *   What is defense for unlaunchable Projectile? Shield? Blast radius?

---

Book rec: [Playful Production](https://www.amazon.ca/Playful-Production-Process-Designers-Everyone/dp/0262045516/ref=sr_1_1?crid=3SYVFO1Y0ORKR&keywords=playful+production&qid=1647020200&sprefix=playful+production%2Caps%2C76&sr=8-1)

Adding trail effect to standard bullets

Disabled LineRenderer and TrailRenderer

Changing Music Section changes from number based to names in FMOD - DONE

Trigger Enter/Exit not reliable for changing sections though - sometimes doesn’t work

Bullets are coming out of both ends of enemy? Not sure, possibly collision issue with bullets

Investigating this - looking at Projectile collisions

FOVSensor from SensorToolkit - use for Enemy Lock on instead??

Fleshing out Unlaunchable bullet more

Prefab Variant of regular bullets - changes are refelcted - working nicely!

It’s red and it works! Switched them to half speed of launchable projectiles

How should I stop/defend against these?

*   Time Rewind effects it? BEST and cool musical FX
*   Shield blast?

Added this! Destorys Unlaunchable bullets - balancing issues important to implement

Need Shield FX to coincide with this

TEMPORAL BLAST

Looking through Ultimate VFX - some options

pf_vfx-ult_demo_psys_oneshot_turbulentImpact - using this!!!!!!

elements of pf_vfx-ult_demo_psys_oneshot_solar

elements of pf_vfx-ult_demo_psys_oneshot_organica

Looking through various FX packs for something good

Possibly HOLOFX pack for shield?

Could use more but have a particle system in place for now

Uni Bullet Hell for interesting patterns of bullets?

Do I need launched bullets on radar? maybe dull them instead of black?

Changed to faded!

Character / Aniamtion broke! No idea how LOL - need to fix

Editing Radar to look nicer - now it looks nicer!

Adding Stamina Bar - it works! Need refinement though

Need to make sure it fits to the musical timeline as well

Good menu tutorial?

[https://www.youtube.com/watch?v=Cq_Nnw_LwnI](https://www.youtube.com/watch?v=Cq_Nnw_LwnI)

Looking at cross hair for Reticle aiming - downloading some stuff to work with

Added cross hair - not sure I like it. Maybe needs to be actually ON the 3d reticle

Things to figure out
*   Graphical Style
*   Enemies shooting two bullets
*   UltimateWaveSpawner issues
    *   Can use OnWaveEnded event for changing sections

IDEA: Multiple Dolly paths with animation for transition between them?

Testing if OnWaveEnded actually works with stopping music

Wave not ending!! not sure why

Did not have “SpawnableIdentity” Class on enemies - lets wave spawner know enemies are dead

Still didn’t work - but seems like progress! This class references NavMeshAgent though

Need to look into this future, what’s need or not - Read manual!

ALSO - revisit core of what you’re trying to do - early documents and ideas

FIND that AGAIN

Think like thumper - lock on and fire to get past target - like Aaero sort of too

Think of the action in Rez

Lock on to many targets - then fire

BT is lock on to many bullets - fire at something - more possible reactions

Think Olli Olli world? Is quickly restarting a good thing to try?

---

## March 12

Looking over original ideas - compositing goals - what was intended?

[Ideas](March%2012%20e665e825584f43ec83880af640687bdc/Ideas%206ba6388d395e4c1bbdb588443c610442.md)

Try Quibi Shaders for CHANNEL#21 Look

Looking COOL but very dark?

Trying out dithcing Ethereal pipeline - Quibli one instead

See in I can get Quibli shaders to look right

Issue with Fog so it’s disabled for now

Visually coming together a bit! Cannot get outline on Quibli shader to have transparency though, need to fix this

Adding **UI Target Tracker (For Radar Builders) to see if that’s useful for bullets or enemies - similar ideas as outline - may not work - need to test**

WORKS but doesnt get destroyed when hitting players - needs adjustment

follow tutorial [https://www.youtube.com/watch?v=dW_lRqITu7g&t=378s](https://www.youtube.com/watch?v=dW_lRqITu7g&t=378s)

Looking at Projectile Trails - not actually movement direction currently

Need to do a tutorial on making it to make it work

Starfox movement - bring this back! Look at original tutorial to achieve it

Fix WaveSpawner issues

Designate unwalkable area for enemy next to player

Look at Ideas sheet!

Recreate this - [https://www.youtube.com/watch?v=PjZLIiupIsQ&t=596s](https://www.youtube.com/watch?v=PjZLIiupIsQ&t=596s)

Mugs with Shaders in scene as well - master this shader! why no transparency?

Evangelion UI! - [https://www.youtube.com/watch?v=k0oQr7ZVtBQ](https://www.youtube.com/watch?v=k0oQr7ZVtBQ)

SSAO in URP - [https://www.youtube.com/watch?v=z7LwThH-Ayc](https://www.youtube.com/watch?v=z7LwThH-Ayc)

Try faster glithcing! like in CHANNEL video - maybe use effect like in CHANNEL video too

Base musical approach around this kind of energy

---

## March 17

Been sick for better part of a week

Ableton session from today's date gives clearer ideas i think!

Do lot of different effects drums / sounds - alternating between these based on video game action

 like Channel 21 Kejirio Video - try to make this happen

Use Freya’s SHAPES to make better vector UI

---

## March 2

Adding Lock / Shoot / Tag sound effects for FMOD

Found an issue -

all enemies need to reregister attack capabilitiles with currently playing track

player as well!

Depending on section of song, everything needs to unregister and reregister for new events

How to structure this?

Player - shooting / All living Enemies? Just ones that exists for more then one section / environment attributes

Game Manager

Knows what current FMOD status is / playback area of song

Sends a call out to all applicable game objects to unregister current event and register new one

Game Object

Needs to register itself with the game manager

Needs to unregister itself as well

Contacted Koreographer support about this.

![Koreo Trouble 1](March%202%206d0124c221e8460398062c22150ceb01/Untitled.png)

Example of issue - maybe im overthinking it? Just output tracks as one whole audio file, do analysis over all of that. May just be easier

Try wait for it with one solid track, giving enemies full koreo for the entirety of the time

Response! Can do this I think? Will seamlessly switch? Have to test this out

![Koreo Support Response](March%202%206d0124c221e8460398062c22150ceb01/Untitled%201.png)

---

## March 3

[https://www.arts.on.ca/grants/media-artists-creation-projects](https://www.arts.on.ca/grants/media-artists-creation-projects)

April 7th Deadline!

[https://cmf-fmc.ca/program/innovation-experimentation-program/#referencedocs](https://cmf-fmc.ca/program/innovation-experimentation-program/#referencedocs)

May 4th!

Troubleshooting Koreo is not picking up events of same name upon transition to new koreo

FIXED - Reassociating Koreography with the audio file seems to have fixed it. Check the path, but also may just need a reload when this happens.

Can use same event names for multiple koreo and it automatically carries over registration - this is ideal!

Several sections done and working - need to see how glitching time works now as well. Starting off with fixing Pause - need to pause FMOD as well

Pause working!

Next, time skip

Partially working! Reference implemntation here

[https://qa.fmod.com/t/assistance-using-settimelineposition-in-unity-studioeventemitter/16846](https://qa.fmod.com/t/assistance-using-settimelineposition-in-unity-studioeventemitter/16846)

Need to have seperate Koreo Tracks for Rewind Time and Shooting Time

Need to stop time from happening endlessly. Looking over the code

WORKING

can repeat 3 times

5 second cool down where you cant use it - how to visually communicate this to the player?

Stamina bar? [https://www.youtube.com/watch?v=sUvwKH7qyQQ](https://www.youtube.com/watch?v=sUvwKH7qyQQ)

Another example [https://www.youtube.com/watch?v=Fs2YCoamO_U](https://www.youtube.com/watch?v=Fs2YCoamO_U)

*   more detailed, can show how much is used up

How should it look?

Need to work out the timeline int true value and the coroutine timing difference needed

Seems like collision to change song section is unrealiable - other possible methods?

Maybe Set variable, check variable, if not new value, attempting setting again, otherwise exist - loop until done?

Multi instruments with async values just keep playing and ignore timeline - need to consider this when setting things up

Magnet regions - investigating these

[https://www.youtube.com/watch?v=81sYxgHvx-U](https://www.youtube.com/watch?v=81sYxgHvx-U)

Look at Paramemter: User Labeled instead of using values

Magnet Regions - Can make a relative transition point instead of starting at the beginning of each next section, as shown in video, need to use Offset relative for the regions you’d live to see this behavior

Look into state machine behaviour, previously saw tutorials on this pop up

*   infallible code?

Looking at code refactoring tutorials

[https://www.youtube.com/watch?v=7oZBfpI_hxI&t=1s](https://www.youtube.com/watch?v=7oZBfpI_hxI&t=1s)

Tips

Frame refactoring sessions on a specific goal

*   scalabilty - decouple tightly coupled code
*   testability - externalising classes dependencies to be injected
*   readability - probably your most important

Readability Tips

*   Consistency - indentations, naming, code styling
*   Look at Microsoft’s C# conventions, available online
*   Bake intent into code your writing as much as possible
    *   extract portions of logic into methods that make things clearer
    *   specific ex: naming coroutines DoesThingCoroutine
    *   making variable value explicit ex: rename duration to durationInSeconds
    *   create well named local variables when it makes sense
*   Cut out any of the fat - dont use this. in c# if you dont need to
*   Try not to use comments if you can make code self explanatory

IMPORTANT - too many IF statements, find video about fixing this again!

WHere is the video? Does command patterns help?

Command Patterns - [https://www.youtube.com/watch?v=f7X9gdUmhMY&t=3s](https://www.youtube.com/watch?v=f7X9gdUmhMY&t=3s)

STRUCTURE

who do I want things to progress? When should music advance?

---

## March 4

*   Did some game design research
*   **Aaero**
    *   two movement based mechanics, essentially inversions of go here/avoid here and a shooting mechanic
    *   Will need to play more to see if there’s something more fruitful later on
*   **Oco**
    *   Great one button platform / rhythmic game.
    *   Interesting Level editor! good short experiences as well
*   WOrking on Spatialized audio
*   PlayOneShot - just the initial locaiton
*   PlayOneShotAttachked - checks every frame? Resource intensive? Look this up
*   SPatialized working with Min/Max distances adjusted
*   Implemented enemy type in scripting for folder path of OneShots, etc
*   Starting with Basic for building up needed sounds
*   GS_HB_Fx_08.wav COOL SOUND - look at these
*   Adding some sounds to the game
    *   Enemy Fire
        *   Using Spatialized OneShot - Should I be using instances? Are they iterrupting each other?
    *   Birth
    *   Death
*   Watching REZ for inspiration
    *   Should use different end tags for different combos of sounds
*   Need a design language for projectiles vs enemies vs more decorative sounds
*   Also - what is the need of this when you have a map? Level idea - DISABLED map
*   Seperate Projectile and Enemy folders for sounds
*   Different enemies could shoot same projectile types, and vice versa
*   Trying to time projectile pulse with unity - thinking of this all wrong! Can exist as a loop in FMOD
*   Pulse visual should probably loop the same way, otherwise they’re all the same - unless I setup a counter per FMOD instance to accept or deny a play of the sound?? try this
*   Need to set the loop of the particles to the same speed as the koreo - look into this! Or seperate these things into other systems
*   DId this for audio! Need to do things for particles - ALSO
*   Need to STOP the audio loop at some point as well. On death for bullets???
*   Need to consult FMOD docs for create instance abiltiies, so that i can stop it when object dies
    *   [https://gist.github.com/colin-vandervort/2d20b92d17e8a4aab27d8d03398d2d8d](https://gist.github.com/colin-vandervort/2d20b92d17e8a4aab27d8d03398d2d8d)

---

## March 8

*   Need to recap issues from last week! But first
*   How to stop projectile audio? Not disappearing
*   trying this with projectile movement sound NOT tied to koreographer- will be out of sync with particles this way though
*   Pulsing sound - hard not to be annoying !!
*   Changing Cubes that trigger sound to gates - visually - looks better
*   Added rewind sound to action - 50/50 on whether its needed!
*   Enemy Fire sound is not great - would like a better one! Trying a replacement
*   Reduced enemy fire rate to see if i can get things to sit better
*   IDEA: Interesting pattern in midi Koreo + low enemy fire rate = patterns?
*   TRY THIS - for hi hat bullets?
*   IDEA: Representing the music through background color changes
*   Koreographer good for this - SPAN for draw out sounds
*   Adding shooting particle effect from enemies
*   Added Unlauchable Projectile to the mix
    *   What does this add to the gameplay?
    *   Should I be able to shoot these or have to shield from them?
*   MMFeedbacks not playing - fixing this and adding a Rewind Feedback
*   Looks like there’s a new MMFeedbacks system - attempting to integrate to fix these issues
*   Nothing seems to work? Upgrading to latest LTS Unity
*   UPGRADED - fixed issues from upgrading
*   ALSO got MMFeedbakcs new version working, trying out some cool new things
*   Attempt to fix enemy lock on aiming!
*   A\* broke for some reason? Reverting to old unity, hopefully dont break anything else ☹️
*   FIXED Enemy Plane was out of wack and causing the issue
*   Cone aiming works!!!!
*   MM Feedbakcs work!!!
*   Need to tighten up Cone aiming and possibly convert Projectile collection this way too?
*   Looking at options for changing out radar
*   Pro Radar Build - dig into this tomorrow! Also look over all March assessments to date

---

## March 9

*   IDEA in SLEEP
    *   Silver Surfer type of thing? Surfer through the galaxy
    *   On rails for surfing, can jump off possibly for some free roam element
*   Attempting new radar with Pro Radar Builder
*   Important Camera conversion tip
    *   [https://forum.unity.com/threads/no-camera-clear-flags.785204/](https://forum.unity.com/threads/no-camera-clear-flags.785204/)
*   New radar working! 2D for now - thinking on whether 3D would be useful
*   Looking at changing colour or sprit for locked on vs available projectiles
    *   Cant seem to do this! Could do it with change of Tag but that seems inefficient
    *   May experiment with that idea later though
*   Changing Tags works well! No apparent issues
*   Tried importing Space Combat Kit but seemed like it would introduce project errors - try another time!
*   FMOD issue with tranisiton area - removing transition area to fix the drop out - is this a common problem?
*   Added lock on sound - fixed constant locking that occurs (realized when sound goes off multiple times 🙂
*   Inventory of March issues tomorrow!!

---

## May 10

*   Change material properties at runtime
    *   [http://gyanendushekhar.com/2018/09/16/change-material-and-its-properties-at-runtime-unity-tutorial/](http://gyanendushekhar.com/2018/09/16/change-material-and-its-properties-at-runtime-unity-tutorial/)
*   Focus on objects in Scene view
    *   [https://www.youtube.com/watch?v=4sUXxaXlYY8](https://www.youtube.com/watch?v=4sUXxaXlYY8)
*   Alpha on Outline shader issues
    *   [](https://forum.unity.com/threads/alpha-not-working-on-outline-shader.929733/)
    *   [Particle System Trails | Unity Particle Effects | Visual FX](https://www.youtube.com/watch?v=agr-QEsYwD0&t=4s)
*   Pool Manager Docs - Refer for issues with pooling enemies!
    *   [2. Per-Prefab Options - PoolManager4 Docs](http://docs.poolmanager.path-o-logical.com/home/<USER>
    *   [.Despawn() - PoolManager4 Docs](http://docs.poolmanager.path-o-logical.com/code-reference/spawnpool/spawnpool-despawn)
*   Doing some tutorial videos
    *   [Better Coding in Unity With Just a Few Lines of Code](https://www.youtube.com/watch?v=r-RCfmQqLA0)
        *   Dealing with loads of nested if statements and code clarity
            *   The solution is a statemachine - UGH it’s unavoidable!
            *   This is quite contested in the comments - not always the best approach to make a bunch of classes and a state machine! Maybe I don’t need this - but I do need something better than all the flags in my update method. I could possibly organize those flags better as well
*   Using Unity UI Builder to make a Pause Menu
    *   [Unity's UI Toolkit & UI Builder: What is It, and How Do You Make It Work ? - Space's Aces Devlog #03](https://www.youtube.com/watch?v=EVdtUPnl3Do)
        *   Seems interesting but from Unity themselves - a conundrum!
        *   > For 2021.2, UI Toolkit is recommended as an alternative to Unity UI for creating screen overlay UI that runs on a wide variety of screen resolutions. It should be considered by those who: **Produce work with a significant amount of user interfaces, Require familiar authoring workflows for artists and designers, Seek textureless UI rendering capabilities**. Otherwise, if you need **UI positioned and lit in a 3D world, as well as VFX with custom shaders and materials**, Unity UI is stil the recommended solution.
*   Going with old method for pause menu
    *   Starting with good ol Brackeys
        *   [PAUSE MENU in Unity](https://www.youtube.com/watch?v=JivuXdrIHK0)
        *   Interruption!
            *   Major crashing from Visual Studio - going back to an old version
            *   Back to 2019 version. Did not crash as much previously!
            *   Not sure why Visual studio is crashing so much
            *   Seems to be working okay now
            *   May want to animate menu later! Mentioned in later half of brakeys video
        *   Integrating with my controller
            *   [Using Menus with A Controller/Keyboard in Unity](https://www.youtube.com/watch?v=SXBgBmUcTe0)
                *   Some difficulty - need to replace parts with Rewired
                *   There’s documentation for this from Rewired - replacing standard event system
                    *   [Rewired Standalone Input Module](https://guavaman.com/projects/rewired/docs/RewiredStandaloneInputModule.html)
                *   Got Rewired working with the pause menu finally!!
                *   Can continue this into shifting to an options menu like shown in the video below
                    *   [START MENU in Unity](https://www.youtube.com/watch?v=zc8ac_qUXQY)
                        *   Rewired has control remapping example scenes that can be pulled in for the options menu
                            *   This is important!
*   Likely need to define Pause as an action and setup better than I currently have it setup - DID THIS!!!
*   Need to include a CURRENT CONTROLS pop up for when CONTROLS are choosen
*   Should I integrate the radar target tracking UI stuff??? Look into it
*   Look into Making lock on cube on enemies transparent! Interesting look right now but could probably be better

---

## May 12

*   Unity Tips
    *   [11 Things You (Probably) Didn't Know You Could Do In Unity](https://www.youtube.com/watch?v=mCKeSNdO_S0)
        *   Start can be a coroutine (IEnumerator)
            *   can define time based execution of code this way!
            *   Use this for a timer?
        *   There is a backup-autosave hidden in unity when scene plays, worth looking into if I run into this!
        *   Inspector → Add tab! Multiple Inspectors!
        *   Choose properties on a component to get a floating window of it.
        *   Hold Alt when hovering over variable in Debug mode to see name to use for referencing the variable!
        *   Replace variant classes in debug mode to have all properties carry over!
*   [START MENU in Unity](https://www.youtube.com/watch?v=zc8ac_qUXQY&t=384s)
    *   Bringing in other menus
        *   Did this, attempting to ad Rewired Remap Controls menu - not working currently! Not sure why
        *   Need to put UI controls in categories like so

            ![UI Categories](May%2012%204edbc82a5775487bb9d4cb423775bd15/Untitled.png)
            *   Broke UI keyboard control when adding this - not sure why! Should not affect anything according to Rewired docs
                *   [https://guavaman.com/projects/rewired/docs/Actions.html#:~:text=Action Categories are only for,affect input in any way](https://guavaman.com/projects/rewired/docs/Actions.html#:~:text=Action%20Categories%20are%20only%20for,affect%20input%20in%20any%20way).
        *   Realized I should be using Control Remapper
            *   [https://guavaman.com/projects/rewired/docs/ControlMapper.html](https://guavaman.com/projects/rewired/docs/ControlMapper.html)
            *   It’s working!
            *   Making a Default Theme Variant for nice menu in control remapping
            *   Need to disable Pause button when deeper in menus or figure out an alternative
            *   Need to clean up my Rewired file, it seems like it has errors and repeat listings accoridng to Control Remapper - possibly start it over?
        *   UI Changes to make
            *   Reorder these to this

                ![UI Reorder](May%2012%204edbc82a5775487bb9d4cb423775bd15/Untitled%201.png)
            *   Input Grid

                ![Input Grid](May%2012%204edbc82a5775487bb9d4cb423775bd15/Untitled%202.png)
        *   Added options menu features like Graphics quality and full screen mode.
        *   Wrote up a review of mid-ish point of month! Looking at best things to tackle

---

## May 13

*   [https://www.youtube.com/watch?v=YaUdstkv1RE](https://www.youtube.com/watch?v=YaUdstkv1RE)
    *   Great video about solo dev.
    *   Advice on making it through the journey!
*   [Classic Game Postmortem: 'Q*bert'](https://www.youtube.com/watch?v=FhkLfz0GKYU)
    *   Checking this out as well
    *   Did a half hour, more of a history lesson than much else
*   [LUTious Color: Grading for Games](https://www.youtube.com/watch?v=-fb3QXR5spE)
    *   Advice for color grading for games
        *   Saturation map can help figure out the grading of a certain image
*   Thinking about movement in Boomerang X - is this something to take influence from? A game mode?
*   Looking at GPU Instancer again to setup city environment again
    *   [GPU Instancer Tutorial - Prefab Manager (Part 1) - Prefabs in the Scene](https://www.youtube.com/watch?v=Ns_d88Ol4SY&list=PLWEl7wzzCgPjYO3_9M6QwvoPOXV9R6P1K&index=2)
        *   Have lots of buildings! But its just too system intensive. Need to find a different way to do this / reduce vertices in the scene
            *   Maybe adjust lod levels? Trying this in the scene
        *   WOuld Impostors be better?

            ![Vertex count](May%2013%202c2fdf0130fc4ea0b40ebea10de7314f/Untitled.png)
        *   Need to dig into understandign the profiler
        *   But it’s clear that these buildings are causing the issue
        *   Cant use Tessera cause it’s not compatible with GPU Instancer
        *   Saw advice - always profile a build not playmode in editor
            *   How to do this?
*   NOTE: GO over May Overview #2 and also make an intro scene that I can easily attach to build for demoing purposes
*   Wrong scene in build! Try building the buildings scene :)
*   Idea - ratchet and Clank portals to different parts or scenes

---

## May 14

*   Buidlings have no issue in build! It’s just in editor play mode.
*   Need to check profile during build mode playing
*   Profiler auto connects to build - does so by IP. Just open it in Unity Editor
*   Enemies getting stuck - is this due to colliding with buildings?
*   Whats the best LOD group Fade Mode?
*   Only using LOD group 2 and 3 - can I delete the others?
*   Maybe use Cross Fade instead of Speed Tree due to only 2 LOD groups?
*   May need to adjust this approach
*   Some info
    *   [Level of Detail in Unity Using LODGroups, Dithering, and Crossfade! ✔️ 2020.3 | Game Dev Tutorial](https://www.youtube.com/watch?v=-mE4qreuqJY)
        *   Can setup LOD levels in Blender when making things and export that properly to Unity
        *   Verify Crossfade is working - may not be in URP. The video has instructions on how to deal with this
        *   Doesn’t seem to work to me! May need work around
*   Load a screen with controls on start? Explain gameplay then throw you in?
    *   Intro Setup will do this
    *   Immediately Pauses game to show controls and explain how things work
*   Basic screens setup - need to show actions and write out controls
*   Resource for changing scenes - how quick can this happen?
    *   [Unity Scenes 2: Keeping Objects When Changing Scenes (DontDestroyOnLoad)](https://www.youtube.com/watch?v=vGxNLVXYIto)
*   Any way to give a red blue glasses shifted lines / perspective view ?
*   Render objects on one layer as blue and slightly off? Others as red?
*   Typography ideas
    *   [https://fontsinuse.com/uses/28760/neon-genesis-evangelion](https://fontsinuse.com/uses/28760/neon-genesis-evangelion)

---

## May 15

*   Value and reference types
    *   [Value & Reference types in C#. Write Better Code!](https
[https://www.youtube.com/watch?v=ldq7ZByB4p8](https://www.youtube.com/watch?v=ldq7ZByB4p8)
        *   Difference between structs and class illustrated to show the difference between values and reference
        *   struct is value type, b = a copies code
        *   class if reference type, b = a references code, just a new pointer to the same data
        *   C# - data is stored in the stack and the heap
            *   stack is for super fast allocation and deallocation, only works with fixed data size though
            *   Heap
                *   Grow and shrink at run time, this data we have no idea at compile time how big these things will actually be
                *   but ex if size allocated to a list is exceeded, an entirely new list allocation is created
        *   Value types hold the data themselves on the stack
        *   Referenece types hole a reference to the data while is held on the heap
            *   THis isnt totally right, need verification
        *   When to use a struct and when to use a class?
        *   Heap is fast, so why wouldn’t we do this all the time?
        *   Strings are immutable, so when we add to a string it actually throws it away
*   [Object Pooling in Unity 2021 is Dope AF](https://www.youtube.com/watch?v=7EZ2F-TzHYw&)
    *   Important thing to revisit! Forgot about this. New object pooling in Unity 2021 - Can probably do a lot with this
*   Need more aggreesive LOD groups
*   What should a second level look like?
*   Trying out deconstrucitng and destroying a model in blender to create interesting results
    *   This is called Decimate
    *   Add Modifier → Decimate → Planar gives really cool decimated look
    *   Control J to join all selected mesh together
    *   Can I export items batch at different decimate levels?
*   [Best Modifiers in Blender](https://www.youtube.com/watch?v=X7ueTvXzoFE)
    *   Lattice modifier to stretch and adjust things
    *   Build Modifier → Can I turn this into an animation? Build up model by component parts
    *   Displace Modifier → Adda texture that will make the object displaced. Distort a mesh
    *   Boolean → Can use to cut a chunk out of a building or object, watch video for how
    *   Solidify → Create bevels and markings easily
    *   Wireframe - Easily wireframe objects! Pretty cool
    *   IMPORTANT - how to reduce number of materials needed for a mesh? How do i pair it down so one material can work for entire mesh? Do I want to do this in all cases?
*   Joost shader can add movement / wiggle to objects - can i implement this type of thing on other shaders for movement? Can i make it transparent?
*   Scripts like Joost glitch effect these properties - expand on this with scripts for size -maybe make size relative to scale of obejct? that way it might scale properties appropriately?
*   Digital debris look is interesting!
*   I can create digital debris worlds pretty easy i think - good scenery
*   Find out how the planet transparency thing works and implement that
*   Reference twitter for more visual inspo

---

## May 16

*   [20 Mins of GREATNESS - BEST #unitytips](https://www.youtube.com/watch?v=dQnAc2mEDI0)
    *   General unity tips video
        *   Controllable SKybox?
            *   May be useful in setup - or just look up this kind of thing
            *   [https://assetstore.unity.com/packages/vfx/shaders/retrowave-skies-dynamic-synthwave-skybox-asset-pack-197258](https://assetstore.unity.com/packages/vfx/shaders/retrowave-skies-dynamic-synthwave-skybox-asset-pack-197258)
*   Need to think - what am i making? What is the context?
    *   **Think Rez**
        *   The stages represent the evolution of human civilization,
        *   The bosses represent the evolution of life on earth,
        *   Your avatar represents stages of "enlightenment" as you level up.
        *   What does the world look like?
            *   Digital corruption - buildings destroyed - based around infrastructure
            *   Japan loves wires and lightpoles and such - very aesthetic!!
*   Thinking quite abstract - but makes sense to base this around real environments and destruct from there
*   What about serial experiments lain?
    *   “Today, Lain’s story resonates more so as an allegory about the perils of forging one’s identity—an alternative identity, however false, misguided, perverse, delusional—using the internet”
*   What if you’re excavating digital memories?
*   Talk about things in Layers liek Serial Experiments Lain Layer01, Layer02
*   What are the scenery / objects I have access to?
*   Design video on this!
*   [Why Is It So Hard To Make Games On Rails?](https://www.youtube.com/watch?v=mUjZUPPrz-o)
    *   1) Density of action and spectacle is necessary - WOW setpieces needed
        *   Camera work to liven things up
        *   Changing up attack strategies to keep things interesting
    *   2) Good pacing! A lot like a roller coaster
        *   house of the dead and time crisis are not as much spectacle but paced well
        *   very much a thrill ride
        *   quick action!
*   Sense of progression - im flying around but where am i headed?
*   Consider the slower pace of Pokemon Snap - and how it features a puzzle-ish element added to it
*   Sayonara Wild Hearts is a QTE / Rail Shooter vibe. Big Spectacle!
*   Ring Fit Adventure is sort of a rail shooter as well!
*   Ex Zodiac - modern star fox
*   [Game Templates - Rail Shooter Preview](https://www.youtube.com/watch?v=nhivR66tT5k)
    *   Should I implemenent an aim system more similar to this?
*   [MORE Movement Shooters - FUNKe Study](https://www.youtube.com/watch?v=oRTh2aMgPY4)
    *   Should consider, current more popular genre are movement shooters
*   Rail shooter games throguhout the ages
    *   [Evolution of Rail Shooter games (1978-2021)](https://www.youtube.com/watch?v=DsI-Bte1ZYI)
        *   Highlights
            *   Starblade - 1991 - Visual Inspo
            *   Wild Guns - 1994 - How to dodge bullets?
            *   Starfox 64 - Look for full level layouts for design
            *   Panzer Dragoon Orta - Tunnel gameplay and levels
            *   Killer 7 - weird interesting game
            *   Sin & Punishment Star Successor - spectacle, wild!
*   Starfox Design
    *   [The Design Secrets Behind Star Fox 64](https://www.youtube.com/watch?v=PnvYCc-I8w0)

        ![Star Fox Game Flow](May%2016%20b7ef64070a5c433c95c6a9829f55b930/Untitled.png)
    *   Layout for moving through Starfox systems
    *   Star fox on rail VS all range mode - would alternate movement modes be good?

    Figure out why enemies are glitching / Editor view - is it path recalculation?

    Is it the speed of the latform moving? Is it the turning of the platform messing them up?

    Make them move faster as well!

---

## May 17

*   Revisit previous document - but right now! Look at some music
*   Added Game Over background loop and Death sounds
*   Added to Trans drum fills (Trans FX)
    *   Also added Drum FX
*   Looking at
*   enemy movement issuesf
    *   SHift- F to follow game object in scene view
    *   Enemies move smoother with higher number (0.8 Seconds) of path recalculation
        *   But they get stuck on dolly path turning
            *   Testing - what if no curves, only sharp movement and turns?
            *   Lowering dolly resolutitoin to 1 for testing
                *   Still seems to get stuck! But the example has a boat turning, what’s different?
    *   Still jittery movement when no rotation, so not necessarily a rotation issue
    *   Upgrading to A\* Beta to see if any issues with gizmos / etc are resolved.
        *   Beta appears to work better! Gizmos are back!
        *   BIG tip at beginning of this for equally distributing objects in positions!
*   [4 new Unity Features YOU'RE MISSING!](https://www.youtube.com/watch?v=mCkjwJGtRI0)
    *   L and R in inspector
    *   Right click and get proeprties from any game object as well!

        ![Unity Properties Tip](May%2017%20c478e3eee6b94f5d8f5d12520647c821/Untitled.png)
    *   Getting stuck like this - what’s happening?
    *   Disabled gravity on enemy rigid bodies and it seems to have fixe dthis
*   Weird behavior in enemies - still jittery but gizmos work and on the right track i think
*   Mayeb try new enemy type that isn’t shoot and scoot - combine others
*   Read through docs and set these up again

---

Testing Enemy movement - seems initially ok and fast, but runs into issues a few second later

Seems to be reasonable at 500 Gameplay Plane Movement

Definitely some issue after 500 - tried 700. Might be update times conflicting with speed of platform - worth further testing, but sticking with 500 for now

Turning of platform no longer seems to cause issues for enemies as well

---

Fixing BackgroundFX issues - doesnt work with current skybox

SkyPortal Cubemap is awesome - do a level with this!
*   Using Marble Flow Currently - anyway to get this moving and trippy?
*   Really cant get BackgroundFX to work! Not sure why it keeps failing - placement of Unity events? Something else?
*   Writing up some basic movement scripts for objects in scenes - refactoring some previous scripts
*   Need to first fix 3dmodels being used from blender
    *   Recenter the Origin
        *   [How to recenter an object's origin?](https://blender.stackexchange.com/questions/14294/how-to-recenter-an-objects-origin)
*   Trying much faster enemies and gameplay in Cyber City 2
*   Going for 64th notes for enemy fire
*   Not sure its the difference im looking for!
*   Put things in a working state for new area
    *   Enemies move and shoot okay
    *   Any way to make things move faster? feel slow right now, maybe smaller objects closer? Experiment
    *   Refer to earlier notes on what needs to be done
*   A little work on reload / load next scene. Probably not as quick a transition as i want - how to handle this
*   Some tips on how to do this
    *   [https://forum.unity.com/threads/seamless-scene-transition.930945/](https://forum.unity.com/threads/seamless-scene-transition.930945/)
*   Cannot seem to get further Behavior Designer things working!!!Only my Scoot and SHoot original works, Octagons do not. Need to go through a full test scene of setting up some of these guys- bare bones.
*   Meanwhile…..
*   Maybe I should just scale everything way smaller? This may help things, seems like a good direction to move towards.

IMP to do:
*   Scale down - might be plenty efficient!
*   Try Level without moving navmesh - what does that level look like?
    *   Try now, - somewhat successful! random distribution helps as well

---

## May 2

*   Charles Amat Advice
    *   OOP Techniques, SOLID priciples
    *   encapsualte behavior in calses and objects
    *   control behviour through polymorphism
    *   Jason Weinmann videos
    *   Jason Storey videos - cleanly structuring the code vids
    *   Clean coding techniques
    *   Design Patterns - be careful about using this right away
    *   Really try to understand the concepts
*   Viewing Jason Storey videos for advice on working on a new project
    *   [https://www.youtube.com/watch?v=nVieP57TD20](https://www.youtube.com/watch?v=nVieP57TD20)
        *   Uses Everything for searching the system
        *   _FolderName to keep folders at the top
        *   Organize into Scripts / Art / Scenes Folders
        *   Project settings - Root namespace
            *   Name after yourself - this is important but not stated in video
        *   Uses Rider for Unity Scripting
        *   Uses his own default script structure - just an Awake
        *   [ MenuItem(”path”)]
            *   use this above a function to make it show up in the Editor
        *   Can autocreate folders and refresh assetDatabase to initiate a template for starting a new project
        *   [https://www.youtube.com/watch?v=45fFcZwkbBs](https://www.youtube.com/watch?v=45fFcZwkbBs)
*   Can edit the packages manifest file to remove packages you dont want and install the ones you do
*   Text files for arrays of names / items / or more
    *   [https://www.youtube.com/watch?v=p7GfSsQvR78](https://www.youtube.com/watch?v=p7GfSsQvR78)
*   SOftware Architecture !
    *   [https://www.youtube.com/watch?v=sh7f4K9Wbj8&t=1s](https://www.youtube.com/watch?v=sh7f4K9Wbj8&t=1s)
*   Local Functions
    *   [https://www.youtube.com/watch?v=D7Kf1CY1PNQ](https://www.youtube.com/watch?v=D7Kf1CY1PNQ)
*   Delegates and Events
    *   [https://www.youtube.com/watch?v=6NRqwL3N5Go](https://www.youtube.com/watch?v=6NRqwL3N5Go)
        *   Delegate - a variable that holds a method instead of data
        *   It’s just the setup, similar to a class defintition, but not the instance of the class
        *   public delegate void DoSomething(int num)
        *   public DoSoemthing something; ←— now we have an instance
        *   public EmapleClass() {
            something = FunctionOne;
            something?.Invoke(123)
            }
        *   private void FunctionOne(int num) {
            Console.WriteLine($”Function on called with value: [num]”);
            }
        *   Above we assign a fumction to something and run data.
        *   ? is null propagation operator, will check that its not null
        *   You could then assign this to another function afterwards
        *   You coudl also subscribe it to another function;
            *   example
                *   something += FunctionTwo;
        *   This is like events, these are two types
            *   public EventHandler Something
            *   public Action SomethingTwo
            *   Can be assgined an anonymous fucntion as written below
                *   Something = () ⇒ { };
        *   Look up this different between Action and EventHandler! Seems useful
        *   Seems we never really need a return type for Events, but need to look into this
*   **Thinknig about drum patterns applied to a controller**
    *   Breakbeats, syncopation with one drum at a time
        *   Is this called Broken Beats?
            *   Related - [https://www.youtube.com/watch?v=qWfvFKVxFRc](https://www.youtube.com/watch?v=qWfvFKVxFRc)
            *   Jungle / DnB - [https://www.youtube.com/watch?v=XNeW40Quu5w](https://www.youtube.com/watch?v=XNeW40Quu5w)
            *   Think this applied to similar music -
                *   ZULI - [https://www.youtube.com/watch?v=yTFEmyPwOyc](https://www.youtube.com/watch?v=yTFEmyPwOyc)
                *   150 BPM
            *   How can this be call and response?
            *   Need to look up some videos on how to make -
                *   Experimental Electronic
                *   Experimental Club
                *   Experimental Techno
                *   Experimental Jungle
                *   IDM
                *   Jungle/Drum'n'Bass
                *   Techno
            *   sampling groovebox clips
                *   [https://www.youtube.com/watch?v=qu2Ep_qAFOQ](https://www.youtube.com/watch?v=qu2Ep_qAFOQ)
            *   **Classic Jungle - SUPER GOOD TUTORIAL**
                *   [https://www.youtube.com/watch?v=ss13J-wnVS0&t=21s](https://www.youtube.com/watch?v=ss13J-wnVS0&t=21s)
                *   restart and move around drum loop based on locked on targets?
            *   Floating Points Arp Tutorial - Ableton Filters
                *   [https://www.youtube.com/watch?v=BGMyN0cncCQ](https://www.youtube.com/watch?v=BGMyN0cncCQ)
            *   Another Floating Points - does a lot of electronic music
                *   120 BPM - Close to a House tempo and beat but not all the way there
                *   turn off retrigger for more organic synth sound for lfo
                *   Analog synths
                *   [https://www.youtube.com/watch?v=7QF9zgeNo4c](https://www.youtube.com/watch?v=7QF9zgeNo4c)
            *   **Basic of Techno**
                *   [https://www.youtube.com/watch?v=Tbvn_cEux3U](https://www.youtube.com/watch?v=Tbvn_cEux3U)
                *   909 Kick and 808 Snare
                *   Stilted feel / quanitzed is typical
                *   130ish ? that’s the choice here
                *   Core groove is king - a singular groove
                *   Tom and hi hat interplay highlights this
                *   Building on the central groove
                *   Kick Drum Reverb - quite popular!
                *   Subvert expectations - a bit of randomness adds humanity to the robotic grooves
                *   Messing with attack / release values on hihat and snare and synth
                    *   Is this possible in FMOD?
            *   **Autechre style beats**
                *   [https://www.youtube.com/watch?v=GC15wiSyIUw](https://www.youtube.com/watch?v=GC15wiSyIUw)
                *   Good for random beep boops
            *   **Breakcore done in Simpler**
                *   [https://www.youtube.com/watch?v=C2_slXoFgIw](https://www.youtube.com/watch?v=C2_slXoFgIw)
            *   **Rival Consoles**
                *   [https://www.youtube.com/watch?v=APe2kwvGGZc](https://www.youtube.com/watch?v=APe2kwvGGZc)
                *   Using Ableton and DRC
*   Plugins usually in Steinberg and need to be moved
*   DIfferent color bullets are different parts of the beat? Like triggering differnet parts of the sample?
*   So you lock on to several and its retriggered a few times

---

## May 20-22

*   Testing faster build times abiltiy
*   Seems to take just under 10 minutes, not much better
*   Added a new scene to current list and rebuilding - looking at final build time- not looking great!
*   Still took just under 10 minutes
*   Not really better!
*   Gonna try asset cleaner
*   Not sure how it works - doesnt imply that theres much i can do either.
*   Deleted librayr and rebuilt it - maybe save 10 gb? still quite large.
*   Found something in reference to creating large worlds in unity - Floating Origin
    *   [https://manuel-rauber.com/2022/04/06/floating-origin-in-unity/](https://manuel-rauber.com/2022/04/06/floating-origin-in-unity/)
        *   If we move the origin than we can avoid errors that occur due to large numbers and floating point math that unity does
        *   Also relveant to this apporach

            ![Floating Origin 2](May%2020-22%2043c0298f9a4646d6aaa3672536ff3002/Untitled.png)
*   Reticle bouncign around - error is within Shooter Movement script
*   Looking at Vertices problem in blender - not able to reduce them even though face count is no  very low
    *   Select all vertices and Delete Loose was helpful, though it might delete too much
    *   Mesh → Cleanup → Delete Loose
*   Forward speed 300
*   Dolly at 3000
*   Takes just over 1 minute

    Need to make LODS before using Prefabs, or else it screws them all up!

    AutoLOD needs to have Save Meshes enabled and they need to be reassigned hwen dragging a prefab into it. Otherwise we get the GPU Instacer errors cause there are actually no meshes assocaited with other lod groups.
*   Now I really need to learn how to use the PRofiler. TIme to watch yotuube videos and deep dive!
    *   Update loop taking a lot of resources - rotations cause of this?
        *   Disable rotation and was correct - it works!
*   ShaderGraph Skybox and VFX Graph ideas - look into whats possible and what i can do here
    *   Can Iuse my desctructed meshes for shader graph / vfx graph stuff?
        *   [Skinned Mesh Sampling in Unity VFX Graph](https://www.youtube.com/watch?v=bIMyCKr0bFs)
*   Behavior Designer
    *   Attack
        *   Stopping distance not working
*   Probably best to create my own task based off Scoot and Shoot - look into it!
*   Setting up new enemies
*   Assing Local Space Graph appropriately
*   Assign Enemy Bullet Pool in Scene according to rubric
*   FOV - 50 Might be a better adjustment
*   Testing FOV 30
*   Can I have a lower FOV and have the camera move around a bit more?
*   Need a RESET of reticle and camera position on transition phase
    *   Added the function to the FMOD phase but dont seem to work
    *   Need to add function to disable / re enable controls as well
    *   This is all in Scaled Down - BD and ASTAR Testing
    *   Reset posoiton not working - goes to bottom corner of screen
    *   Trying to set player movement from shooter script to see if it’s more accurate
        *   Wroked! PLayer movement coming from Shooting works best for opposite movemen

---

*   If you’re shooting, you can’t lock on to more bullets can you? What happens?
*   I think you’re locked out?
*   You have to wait while you’re in firing mode
*   You can retarget while in firing mode
    *   You can redirect bullets to a new target if you learn the patterns and pay attention to how many it takes to kill an enemy
*   Think - how does this relate to changing the music? Could it affect anythnig?
*   Is there a way to highlight this??

    Could I lock on to more than one enemy, and choose how many to send to each?

    duple or triplet pattern? Could this be automatic?
*   Attempting to replace enemy targetting with enemyTargetQueue
*   Seems a Queue was better than a stack or a list - but maybe not when i need to remove a specific element! maybe a list is what really works
*   Back to a list!
*   Multi-lock on seems to be working
*   But some bullets are getting stuck now
*   Do they need their target reassigned ? What is making them stuck?
*   May need to review how lists work when you remove an item. Do all the others move index to account for this?
*   Bullet issue
*   Stuck in a locked state - 3 in current scene while playing

    ![Bullet Stuck State](May%2020-22%2043c0298f9a4646d6aaa3672536ff3002/Untitled%201.png)
*   All seem to have same attributes generally - negative lifetime, etc
*   Locked and released - but no launching state?
*   If Laucnhback was called on these, laucnhnig would be true - but its not!
*   CUrrent active bools also suggests that LauchAtEnemy was never called
*   Debugging with a PAUSE GAME if locked and released both occur
*   DId almost immediately - seems it’s not unusual! Maybe not why it’s getting stuck
*   Why isn’t it getting assigned one of two scenarios - shoot at enemy or shoot in direction?
*   Really think it’s something in OnMusicalShoot
*   When I only have MaxTargetLocks set to 1, I don’t have the issue!
*   **Could it be how the iterated index is set?**
*   How do I want to handle avoiding getting hit? Maybe have bullets aim for center of screen always - and then being at the edges allows you to escape getting hit
*   Changed so projecitle now aim for PlayerTarget instead of the player directly
*   To know that this is working / effective, might need a particle effect / FLASH to indicate player was hit
*   SOmethin gto this dodge to side of screen thing - think it needs faster aiming and smaller bullets
*   Need to adjust bullet speed - quite fast up close!! Maybe just a bit slower? Currently it is 700
*   Possible correction for bullets constantly spinning by player - if the bullet hits PlayerTarget but not Player - it loses tagret and proceeds forward
*   See if thsi clears up visual intensity with many bullets!
*   Nothing seems to be actually hitting Player Target - No debug message, no change in any bullets. Investigate this! And consider fi this is best approach

---

## May 23

*   Drop bullets lower when locked to clean up visual field a bit?
*   Added this to bullet script! does help clean up sight lines
*   UI Target Tracker not working - look into this - Lock on feature could be useful
*   Trying this to get bullets to stop spinning in circles

    ![Preventing Bullets From Circling](May%2023%2030145d0228ee4f4f857f0925c57eafd6/Untitled.png)
*   DIDNT WORK, neither did homing = false
*   Need to get bullets to just move straight forward once they enter the player area
*   Rethink from start! Use Player Target!
*   Created Page for prioritizing work
    *   [Issue Tracking](../../../../Issue%20Tracking%20a7b8efc25f594d25b8558fc230255e64.md)
*   Experimenting with camera tilt around player - trying different options to ahcieve this

    Camera Dutch in level as effect would be cool!
*   Removed Camera Follow script from CM vcam1 becasue Cinemachine seems to work great instead
*   Ignore Time Scale on CinemachineBrain is now enabled
*   [How to use Cameras in Unity: Cinemachine Virtual Cameras Explained](https://www.youtube.com/watch?v=asruvbmUyw8)
*   Working on Camera movement
    *   FOLLOW will affect position of camera
    *   LOOK AT will affect rotation of camera
    *   Brought back Camera Follow cause of Limit Feature
        *   May not use it. Need to investigate camera movement more - could be a big game feel thing
*   Tackling spinning bullets
*   Projectile Target is being set to Player again
*   When projectile hits PlayerRadius it should reset and keep going forward now
*   Bad if / else tags seems to be the problem with my collision not working
*   Seems to work now! Probably some bug testing necessary
*   Bunch of bullet sin scene floating like this

    ![floating Bullets](May%2023%2030145d0228ee4f4f857f0925c57eafd6/Untitled%201.png)
*   Seem to have fixed it with bullet death code refinement
*   need to figure out a good way release any help locks by the player

---

## May 24

*   Testing dolly switching and debugged a movement issue
*   Jittery playe rmovement issue seesm to be from going far out of play area - causes math errors
*   Keep new levels / areas close - stack them veritcally? Just out of eyesight aswell?
*   Trying out new level designs with GPUInstancer in scene as well
*   debuggig multiple enemy lock on too
*   Importing modern Sci Fi building back to Blender file for Level Design
*   Need a real Concept / Level Design day where I assess modelling options ideas an lay out a plan

---

## May 25

*   Disabled vertical movement of player, just left and right movement exists for dodging
*   Breaking error that happens

    ![Bullet Error Out of Bounds](May%2025%2098d127f8725e40448db18110e5748904/Untitled.png)
*   Comes from various Enemy Shooting Particles
*   Lots of bullets in bullet pool so unsure that that is the issue
*   Coming from active enemies - they still seem to be shooting
*   I think this slows down the game as well
*   Code referenced

    ![Bullet Shooting Code](May%2025%2098d127f8725e40448db18110e5748904/Untitled%201.png)
*   Shooting is always looking at camera, but maybe best to have it look at a wall instead? That way I can switch camera angles and have it looking in different directions based on wall position. f
*   Dynamic Crosshair
    *   [How to Create a Simple Dynamic Crosshair (Unity3D, C#, Game Design)](https://www.youtube.com/watch?v=-7DIdKTNjfQ)
*   Need a real Concept / Level Design day where I assess modelling options ideas an lay out a plan
*   Made documents Mechanics Refernces and Scenario Ideation

---

## May 26

*   AI Tricks
    *   [The Simplest AI Trick in the Book](https://www.youtube.com/watch?v=iVBCBcEANBc)
        *   Notes:
            *   AI Reaction Time
            *   Go / No reaction time - 0.4 seconds
            *   Not a lot of insight here actually
*   Maybe this site is useful
    *   [http://www.gameaipro.com/](http://www.gameaipro.com/)

---

## May 3

*   Stemming from yesterday’s work - lock on to trigger different parts of the drum loop?
*   172 Flux
*   Add element when locking on
*   also change drum pattern in response
*   magnet transitions - can these move me back and forth easily to places in the song?
*   FMOD Error - Global paramters howing unavailable in Events in Unity, set to Local
*   In lock on of Crosshair - reference musicPlayback event emitter
*   States of Player Shooting
    *   Not Locking / Locking / Holding / Firing
*   Need consistent event ID’s for Koreographer
    *   “Perc” for drums / baseline track
    *   Can use
    *   musicPlayback.EventInstance.setParameterByName("Lock State", 2);
        *   to change lock state in Crosshair class
*   Locking tag is probably a good idea, some transitory tag between locking and firing really
*   ALso screen shake when lcoked on until fired? screen shake off on shoot tag
*   Updating Feel to try and get Cinemachine camera shake working again
*   WORKS! Shaking Camera!
*   Some tips on using Feedbacks
    *   [https://www.youtube.com/watch?v=pMOPgoTNYFk](https://www.youtube.com/watch?v=pMOPgoTNYFk)
*   Add Shoot Feedbacks to game
*   Figure out bullet speeds
*   Transition to new sections with targets taken out or something else?
*   Prototype!

---

## May 4

*   File naming for Kore assets
*   Drums 1-8
*   Drums 1-16
*   Drums 1-32
*   Naming scheme for Perc Koreo Track ID
*   Perc
*   Perc Half
*   Perc Half Half
*   These names can carry over to all sections and Koreography will automatically pick it up
*   Trying to update Background Color Changer
*   Using Unity Events - that seems to work fine!

    But the colors only switch a couple times and stop - not sure why.
*   Doesn

t appear to be Koreographer issue. Tried to fix multiple ways there and nothing changed the behaviour
*   NEXT UP
*   Advance section of song when certain criteria met
*   Every wave? No, should be able to set how many waves it takes before next section
*   New Script - Change Song Section
*   How many waves per section? I may want to adjust this
*   Read in a text file that allows me to adjust number of waves per song section?
*   Maybe using Json is best!
*   Working through Game States
*   Basic version of Music / Wave controller based around JSON descriptions implemented!
*   Need to flesh out more - Start section also not working
*   Changing sections - I think sections in song and sections in AdjustSongParameters need to be different, Can increment through the sections listed on the json file, but some can be repeats and in a differnet order
*   Seems to be working / SORT OF
*   Need to feel out these transition areas more, add a visual effect for when they’re kicked
*   Need to bug test bullet issues as well
*   Need to find current song section and initiate transition effects when a transition occurs
*   What are transition effects?
*   Can we change levels? Do we fly to a new area? Do a camera zoom and move?
*   BIG COUNT DOWN ON SCREEN maybe?
*   Note - wave delay will happen every time currently - even if there is no transition!
*   Need to set Wave Spawner files to match the JSON files I think?
*   Think about this one some more - split off waves
*   Need to write out the entirety of this system! See where things could be better connected
*   Start with FMOD → Build Drum loops and transitions here
*   Try some wave patterns in WaveSpawner
*   Write those Patterns in JSON for proper section transitions
*   Reinstall and troubleshoot amplify animation pack

---

## May 5

*   Watching Brandon Sanderson course on sci fi while doing other things
*   Found Smhup zine that seems interesting!
    *   [https://www.magcloud.com/browse/issue/1820005](https://www.magcloud.com/browse/issue/1820005)
*   Bullet patterns and such listed
*   Adding State driven camera switching for transition sections
    *   [https://www.youtube.com/watch?v=Ri8PEbD4w8A](https://www.youtube.com/watch?v=Ri8PEbD4w8A)
    *   Using this tutorial!
    *   Works! Just need to refine what it should do
    *   Tried to have a Countdown on screen while transitioning but cant seem to get this working quite right - need to look at again sometime
*   Destroy all active bullets when in a transition phase? Think about how to do this
*   Edited WaveMap and works well! Can do lots here
*   What are the possibilties for trainsitioning scenery ?
*   Seems every problem bullet has a negative lifetime. Recycle bullets with negative life time?
*   WOrking on projectile class to clean up and fix issues
*   What am I doing with OnTargetDestroy Event on Projectiel Target (TargetCube) ???
    *   Don’t recall what I was going for here
*   Setting LaunchAtEnemy and LaunchBack to make launching = false
*   See what happens! Does it break everything?
*   Would make a lot of sense to use a switch case for the states of the bullet
*   State machine i suppose? Think about this
*   What are the Projectile’s states?
    *   ProjectileLaunchedByEnemy
    *   ProjectileLockedByPlayer
    *   ProjectileLaunchedByPlayer
    *   ABADONDED STATE MACHINES
        *   If I simplify Porjectile class, not really necessary
*   Also fixed Projectile class issues with Laucnhing - there are still some error cases but much better now!
*   Can now go 9 rounds with things getting exceedingly difficult!!
*   Pretty cool!!!

---

## May 6

*   Just jumping into the game for a moment to get some initial things worth improving
    *   Crosshair animations? Lock on animation for bullets?
    *   Make enemy birth / death particle animation quicker. Enemy active time while dying shorter as well
    *   Fix transparency of bullets
*   Better animations - Amplify animation pack?
*   Try buildings or other scenery for the current level, see how taht performs / enemy avoidance as well

---

## May 7

*   Brandon Sanderson course
*   Promise, Progress, Payoff -twists, Plot Types
    *   **Promises** - Tone (consistent, understandable)
        *   Arc - signify what kind of arc the character will be going on, following through on that structure
        *   Plot - Umbrella Plot and Core Plot
            *   Ex. Core plot is a romance, but Umbrella plot is ‘we need to do X\` and while thats happening, you care about the romance
            *   Sometimes they’re the same thing, sometimes split off and takes a bit of time - but you’re making a promise of what these things are going to be
            *   These promises are all in the introduction of the book
                *   short story, first couple paragraphs
                *   massive fantasy - could be many chapters deep
        *   Character Arc promise is how they char will change over course of story - they’re gonna change or their situation is gonna change and give them what they want. Luke Skywalker being told by obiwan he has to become a jedi, take part in war we saw at beginning of movie
        *   Plot promise - Get the plans to the rebels so we can defeat the empire
            *   How not to be too predictable with these?
                *   Plot can be more predictable than you think if the setting and characters are a bit more interesting
                *   Pretty much every plot has been done, and they way to buck the trend is to do something unexpected that breaks your promises - can bea feature of the story, but mostly you want to do subtle inversions of your promises
                *   The ‘Strange attractor\` idea for a story - you want it to be familiar but new. twist on an old idea or mash up two ideas that dont feel like theyd make sense, but is intrigiung
                *   Do all good characters need arcs? James Bond / Sherlock Holmes is a good example of a character who needs no arc but works because they other elements are strong. Tone and Plot promises are much more important there
    *   **Progress**
        *   This is the most important part
        *   Classic example of Inferno and putting a Map at the beginning, allowing the reader to see how we progress through the book toward the center of hell
        *   Reader is always watching a timebomb, seeing how they progress through the book
            *   It’s an illusion of progress of course, you can control how fast things move forward in writing - you have complete control of time
            *   Why are people turning the page? What questions are they looking to be answered?
            *   Promises can totally shift the view of the progress - diversion story from lecture about this.
            *   Progress - think about how every scene advances the main points - Star Wars example

                ![Star Wars Character Arc](May%207%20df684ac7b1d44dfabe2c3d2e74e5197a/Untitled.png)
            *   Look at your plot / outline - what are small increments i can make along this path that will be interesting and show the reader we’re progressing. Can backslide to and be interesting but need to be very careful about it. Step backs are useful in basically every story in relation to twists, but very common in romance stories.
            *   But important - the reader sees the step back differently than the characters, so it may seem major in the book but you know its smaller than it looks
            *   Identify what type of plot you have - very useful for understanding how to progress
            *   Progress should involve problems arising and dealing with them
            *   Nesting plots like you think of code - this is in relation to sub plots

                ![Nested Plots](May%207%20df684ac7b1d44dfabe2c3d2e74e5197a/Untitled%201.png)
    *   **Payoff**
        *   You make good on all this, the trick is you don’t entirely make good on it all
        *   Star wars uses plot expansion for its twist
        *   Ex. You promise your kid a toy car
            *   They wait until christmas like they’re supposed to
            *   They a brand new real car for christmas
            *   Star wars promise at the beginning is smaller than what is delivered at the end - lukes arc becomes defeat the entire empire
            *   Giving the reader more then they expected
            *   While you were sleeping (film) is a great example of a substitution plot - convince the reader that you actually want something else. This can be hard to do!
*   **Lecture 3 Part 2**
    *   How do we make an outline and construct a plot?
    *   Whats an outline? For editors, 3 - 5 pages telling your story. A Summary
    *   Outline looks like this
        *   Character
            *   Main Char Name
                *   Intro, and Paragraph of who they are
                *   Arc 1
                *   Arc 2
            *   Side Characters
                *   One paragraph on each or so
        *   Setting - Each category below written defining terms, encyclopedicly written
            *   Magic /Tech
            *   Worldbuilding physical setting
            *   Cultural setting
        *   Plot - Plot Archtypes!
            *   Heist Plot - Mystborn was going to be a heist - this was the main framework
            *   Master/Apprentice plot was also decided
            *   Info Plot - History about characters teased as clues
            *   Relatioship plot - a romance subplot between two chracters
    *   How do we make all this into a story?
        *   Brandon watched a bunch of heist films to determine his achetype there
        *   Being able to strip these down helps you to understand your progress
        *   How does Brandon make an outline?
            *   Relationship
                *   Char A and B are a couple at the ene
                *   Bullet points taking them from the beginning of this until the end
                    *   Scene showing how Char A is rally competent in one area but also missing something
                    *   Scene showing Char B also competent, but is missing something different (reader will realize these people compliment each other here)
                    *   Next you introduce the issue/conflict that demonstrates why they’re not together. They’re intereactions are disasters for various reasons
                    *   Scene where they are working together (braiding roses) the metaphor is great - the thorns that kept them apart will be the throns that prevent others from breaking them apart when they are together
                    *   Ex bullet point - Char A sees Char B with their sister, showing Char A that they can be a caring person to others
            *   Mystery
                *   Discover X - explain why discovering X is gonna be awesome
                *   Bullet points of clues that will help us along the way to achieving this
    *   Outline is ordered by section
    *   When we start writing, start grabbing bullet points and organize them into the sections of how the story will progress - chronological order
    *   Building scenes out of them as we imagine where they’re going to be
    *   Sit down a writing means “you need to write a scene that will achieve A, B, and C
    *   Plot brainstorming - a lot of simmering on it before even writing it
    *   9 point story, 7 pointy story ,save the cat screenwriting, lots of books on this stuff
        *   Dan Wells 7 point story structure video
        *   Hero’s journey has issues, dont want to apply to everything or be to slavish towards it
    *   Discovery writing - Yes but No and
        *   Taking a character, throwing them into a bad situation, think of the best thing they could do in that situation, and see if that works. Depending on how that’s resovled, see how you can make things worse, and where things go from there
*   A short note on Brandon Sandersons

---

## May 8

*   Bring over FMOD setup to Ableton for building on the song
*   This was helpful! Getting some ideas of how to effect sounds and elevate the current song `Flux 171`
*   Made the transitions more hype, doubling up on drum fills

---

## May 9

*   Unity UI in 2021 LTS - Flag this as a good thing to use? Seeing some positive coverage of it
    *   [https://www.youtube.com/watch?v=EVdtUPnl3Do](https://www.youtube.com/watch?v=EVdtUPnl3Do)
        *   Pause Screen
            *   A good way to start on menu’s and other needed game elements?
            *   Use new Unity UI Builder for this as well?
*   Also need some kind of YOU DIED screen - and music section!
*   IDEA - game too hard initially, coming back exposes new parts to the music and game - is this a motivator??? Does this signal to the play anything interesting / motivational?
*   Thinking on big issues to tackle
    *   Lock on Opacity needs to be fixed, tackling this now
        *   Possibly acheive this using Alpha Clipping in URP??
            *   Seems to be the trick! Looking like I’ll need to
            *   Orignally did this

                ![Opacity Setup 1](May%209%20c1f55fc6fac74e77a6e6352e61e6401e/Untitled.png)
*   Looking at Shader Graph options
    *   [https://www.youtube.com/watch?v=Rn_yJ516dVQ](https://www.youtube.com/watch?v=Rn_yJ516dVQ)
        *   This may be a good thing to follow up on
*   DOFade works when surface type is transparent / blending mode is alpha

    ![DOFade Properties](May%209%20c1f55fc6fac74e77a6e6352e61e6401e/Untitled%201.png)
    *   DOFade actually works with Quibi shader! It’s just the outline that is the problem
    *   Can I disable outline through script?
    *   Looks like setting width is the trick that actually works!
        *   No it doesn’t ☹️
        *   In script this attribute is dictated as

            [Toggle(DR_OUTLINE_ON)] _OutlineEnabled("Enable Outline", Int) = 0

            Also a section about

            ![UI Outlines](May%209%20c1f55fc6fac74e77a6e6352e61e6401e/Untitled%202.png)
        *   So how to disable? Int?
        *   Using Flatkit instead!
            *   Can set scale!
            *   myMaterial.SetFloat("_OutlineScale", 0f);
            *   Turned on Preload sample data for FMOD
*   Really need to go throw Projectile logic again to see what is working and what isnt. Can definitely be refined
*   Bullet shoot from wrong side of enemy often? what is going on here?
*   Levels! Scene changes! refine

---

## May Overview

Initial thoughts - not a lot of progress in April! Pretty burned out on the game right now

Spent a couple weeks trying to debug WaveSpawner issues. Have a solution for moving between waves, but enemies are not capable of being resued properly. Can possibly delay fixing this for a while. Maybe look into Pool Manager more for these issues

Talk to Frank Russo at Ryerson and did various haptic device testing. Latency in controller too high for hi hats in controller to be effective. more blunt haptics seem to work on, less precise

Narrative Idea:

My lock on system - Can work overhead / first person / third person

You are an AI auto protection system guarding a being transmitted through earth

Think AI protection in Alien, Prometheus, etc.

Some notes
*   Camera follows Shooting Reticle rather than Player now
*   April 5 - good tips from Tunic dev on Post-Processing
*   Random Flow for Blender - cool asset to try on models!
*   Upgraded to newest Unity LTS 2021
*   Unlockable bullets - not currently using while testing
*   Need to reassess music system integration with game systems
    *   Look at original core documents
*   Mechanics and core narrative pillars - tying these things together
    *   Is movement core or is it a method of getting from one puzzle to the next?
*   Cannot get alpha of Quibli Shader Outline to work
    *   Look into Flat Kit Outline / alpha docs - supposedly better!
*   Good basic tips for performance enhancement
    *   [https://www.youtube.com/watch?v=v5_RN8o1b3g](https://www.youtube.com/watch?v=v5_RN8o1b3g)
*   Coming back to early inspirations
    *   [https://www.youtube.com/watch?v=PjZLIiupIsQ&t=1559s](https://www.youtube.com/watch?v=PjZLIiupIsQ&t=1559s)
*   Possibly interesting system reference for music documentation
    *   [https://mixolumia.com/makingmusic/](https://mixolumia.com/makingmusic/)

Did some decent work for over half the month, but due to confusion / lack of inspo / etc I moved to hitting web dev a lot harder.

Need to find a balance moving through May

---

## May Overview Midpoint

Look over these things

May Overview Points to carry forward
*   Possibly interesting system reference for music documentation
    *   [https://mixolumia.com/makingmusic/](https://mixolumia.com/makingmusic/)
*   Coming back to early inspirations
    *   [https://www.youtube.com/watch?v=PjZLIiupIsQ&t=1559s](https://www.youtube.com/watch?v=PjZLIiupIsQ&t=1559s)
*   Mechanics and core narrative pillars - tying these things together
    *   Is movement core or is it a method of getting from one puzzle to the next?
*   Need to reassess music system integration with game systems
    *   Look at original core documents
    *   May 2 tutorial links and ableton setup - good influence on direction!
*   Random Flow for Blender - cool asset to try on models!
*   April 5 - good tips from Tunic dev on Post-Processing
*   Could clean up code more! always an opportunity for that and the many assets in the project
*   Revisit JSON / WaveSpawner tie ins - could this be better? Easier?
*   Look up this different between Action and EventHandler! Seems useful
*   GO over some of these notes on electronic music to compile ideas on them outside of the video game documents
*   Color switching lock on not fully working - what is going on there?
*   Tried a countdown on screen in transitions - how to revisit this?
    *   Start function timer with tempo influenced numbers on screen?
    *   Can do coroutine for this! Start as Coroutine!
*   Reinstall Animation pack from Amplify and see if anything is useful?
*   More animations for crosshair / lock on
    *   Use the radars lock on info attachment?
*   Bring in other scenery / buildings / environments - much like Tetris Effect!
    *   Find a best method for doing this?
*   Fix bullet direction firing issues
    *   Fires from enemy in a weird direction?
*   Need to figure out Pool Manager issues eventually! Cant currently reuse enemies
    *   Pretty sure reusing bullets is fine, but is that per enemy or in general?
*   Look into making lock on cube on enemies transparent or something else
*   More FX in general! Shader Graph? Other things?
*   Disable PAUSE in certain menus, or have it close out everything! Don’t want players glitching out of menus

File naming for Kore assets

Drums 1-8

Drums 1-16

Drums 1-32

Naming scheme for Perc Koreo Track ID

Perc

Perc Half

Perc Half Half

These names can carry over to all sections and Koreography will automatically pick it up

Revisit this video on Delegates and Events?

[https://www.youtube.com/watch?v=6NRqwL3N5Go](https://www.youtube.com/watch?v=6NRqwL3N5Go)

---

## Mechanics

Use Puzzle game approach

[https://www.youtube.com/watch?v=B36_OL1ZXVM&](https://www.youtube.com/watch?v=B36_OL1ZXVM&)

Solution Sentences

Reduce the noise

Mix two mechanics and see what happens!

---

## Nov 24

Updated Packages - Need to check Koreographer andothers for added features

Check Koreo thread in discord regarding tempo!

Profiler Tutorials

CPU in Milliseconds

3-4 and 5 - 7

10 average player loop performance?

![Profiler Scales](Nov%2024%204f14f2f511534d299efde2134cc4fd98/Untitled.png)

Resolution settings and issue for steam deck performance?

Add Graphics settings to menu! Add ability to save these as well

Recording specific pieces of code using the following!

Profliler.BeginSample( method name here )

Profile.EndSample ( method name here )

[How to profile and optimize a game | Unite Now 2020](https://www.youtube.com/watch?v=epTPFamqkZo)

My performance during play mode in-editor VS editor mode - BUSY SCENE

![Editor mode Profiler](Nov%2024%204f14f2f511534d299efde2134cc4fd98/Untitled%201.png)

![Play mode profiler](Nov%2024%204f14f2f511534d299efde2134cc4fd98/Untitled%202.png)

Tri drop from 15 Mil to 60 K using GPU Instancer

Process for Optimization

![Optimize Process](Nov%2024%204f14f2f511534d299efde2134cc4fd98/Untitled%203.png)

If no GPU time is listed, look for the following

WaitforGFX or Waitfo GPU call in Profiler

If this comes up, we know that the GPU is taking longer and the CPU is waiting

Remote profiling from target device - try this in Windows builds?

![Remote Profiling](Nov%2024%204f14f2f511534d299efde2134cc4fd98/Untitled%204.png)

Will this make a difference in my game?

Bake lighting!

Ensure Dynamic Batching is working / on by verifying Render Pipeline settings

Verify this is not an issue for other assets (GPU Instancer)

Look into Occlusion Culling

![Occlusion Settings](Nov%2024%204f14f2f511534d299efde2134cc4fd98/Untitled%205.png)

Need to marker renderers as static in the scene - how does this affect GPU Instancer???

[How to customize performance metrics in the Unity Profiler | Unite 2022](https://www.youtube.com/watch?v=ixEk6EWrL6Y)

Interesting but not useful to me at this moment

[How to Actually optimize your game in Unity - Complete Game Optimization Guide](https://www.youtube.com/watch?v=ysk7ATmIeOs)

Adjusting performance based around Medium settings

Changed Datastream to static - Minor performance difference at most - What about in GPU Instancer?

Many other performance tips in this video

Physics - Auto Sync Transforms - Dsiabled but what does it do? Off by default

Turn down smoothness is recommended

Lighting is in subtractive mode - does this work for my project?

Currently no Lightmaps - not sure why? Nothing casts shadows?

[GPU Instancer Tutorial - Detail Manager - Terrain Grass and Vegetation](https://www.youtube.com/watch?v=ydeYDg_Ncno&list=PLWEl7wzzCgPjYO3_9M6QwvoPOXV9R6P1K&index=2&t=6s)

Doing an overview of GPU Instancer tutorials because I think this is a bottleneck

GPU vs CPU

Looking into Floating Origin Point - Cant use Gameplay Plane - What else?

Try painting scene with GPU Instancer Terrain

Move into Rigidbodies and Post Processing Performance Tutorial videos

---

## October 13

High Level - what does the game need?

List our requirements - how can others help?

Try my build on steam deck - curious on performance!

Mechanics

*   Foundation exists for this

Graphics

*   destroyed / destructured asset idea for most enviroment
*   maybe for enemies as well

Sound

*   Can do this, not worried, but a workload nonetheless

Narrative

*   workshopping many ideas
*   who are the characters?

Levels

*   need key narrative decisions made for this
*   [12:16 PM] Tyler G
    *   https://www.youtube.com/watch?v=ixEk6EWrL6Y

---

## Projectile States and system movement

**OnEnable**

*   Set lifetime
*   Set projectileTarget as Player
*   Set parent object as EP1 - Enemy Plane?
*   Register with Koreographer for events
*   homing enabled on start attribute - likely need to remove this?

**Start**

*   locked = false
*   released = false
*   launching = false
*   set originalparent - This is enemy plane. Is this what’s needed?

**FixedUpdate**

*   if projectileTarget isnt null
    *   Missile movement math is done
*   if projectileTarget is null - Removed as it seems redundant
    *   just move forward
*   if homing is true and projectileTarget is not null
    *   look at the projTarget - Is this conflicting with any other math?
        *   could disable homing when close - currently not doing this
*   if launching lifetime countdown is paused
    *   if not launching then resume lifetime countdown - Lots to consider on lifetime, need to redefine how it works when shot vs never locked on
*   if timescale > 0 and not locked and not launching
    *   just move forward
*   if locked is true, released is false, launching is false, collectable is false
    *   release object rom oParticles system
        *   homing = false
        *   Freeze in place! Rigidbody
        *   Look at HorizontalPlane - WHY
        *   Set parent to horizontal plane - WHY
        *   Rewindable by time is False
        *   release is true
*   if locked is true and launching is false and LINE RENDERER is on
    *   draw some line renderer stuff
*   if laucnhing is true and locked is true
    *   UNFREEZE the projectile
    *   draw some line renderer stuff
    *   set projectile forward as same forward as player aiming
    *   Move bullet forward
    *   TLine.globalClockKey = "Test"; - WHY IS THIS HERE?

**Launchback**

**LaunchatEnemy - takes transform target**

*   if child exists at 0, destroy it. this should be aimPrefab - CHANGING THIS TO 1 for PARTICLE FX
    *   This may explain why bullets were inactive but showing aimPrefab, the particles effects were deteled because they exist at 0
*   projectile target is set to target
*   set parent to null
*   homing is true
*   lanuching is true

---

if homing is true and launching is true
*   will look at projectileTarget
*   no RB constraints, local forward is being set to player rayspawn forward, rb velocity

What are all bool states when a projectile gets locked?

Crosshair class sets locked = true

locked should be true and other attributes all false, so it freezes the projectile

end of this action sets released to true, causing this to NOT be run again

locked is true and released is true

---

## Sept 15

Reducing bullet speed

Reducing character speed

THINGS TO DO

*   Tutorial Screens
*   Adjust Reticle size for defensive / consume bullets
*   make bullet lock on more obvious
    *   visual effect ?

Tried tunnel visual effect - maybe cool to use in general?
*   Change it when locked on? Disable?
*   Adjust Warp Amount so that it works / looks cool in busy looking levels

---

## Sept 16

Working on tutorial level and effects

Made a video walking through the mechanics of the game

Seems better than writing up a tutorial currently! Explains the game

---

## Sept 17

Thinking about tutorial design

[Can we Improve Tutorials for Complex Games?](https://www.youtube.com/watch?v=-GV814cWiAw)

Sprinkle tutorial elements throughout ‘playing the real game’

deliver tutorial in chunks

---

## Sept 22

Theme / Game / Lore buidling

Start in a test tutorial level
*   Introduce / Immerse you into the way things work in ‘the net’

SUPERHOT

Computer console intro

[SUPERHOT - Full Game Walkthrough 【No Deaths】](https://www.youtube.com/watch?v=n5lu4Gyb1KM)

Boring description of being an anti virus / IT guy,
*   X person has a virus on their computer, you have to go in and remove it
*   interspersed with silly conversations about the virus / what’s wrong with the pc
*   can bring this back to my own experience dealing with people about computer issues

What are the games I see in a similar camp?

Thumper, Rez, Rollerdrome, Superhot, Neon White, Boomerang X

Hyper Demon?

How do I setup some vibe / lore without going too deep?

Watching Rollerdrome stream on Unity Twitch

Node Canvas vs Behaviour Designer

Seems interesting! Node Canvas may be better?? Used in Rollerdrome

Refine elements
*   aiming
    *   enemy
    *   projectile
*   absorb bullet aiming needs fixing
*   fix projectiles locking up out of nowhere? whats happening?

---

## Sept 23

Realized my locking / shooting sounds are way too quiet

Bringing up the volume

Making the mix more impactful!

Changed Multi-instrument from Consecutive to Concurrent

*   does this help the sounds playing?

Added chorus to shooting sound

*   cool effect, not sure if it cements the sound. a different sound may work better
*   also need more notes / options / modes / scales?

Need a different sound for absorbing bullets!

Lots of room to play with scale / melody in the shooting / lock on sounds. Make some decisions here, decide how it elevates / changes between sections. This could be a good way of colouring things differently as the game evolves / the player progresses.

---

## Sept 28

Had a meeting with Henry yesterday

Some takeaways on developing Beat Traveller

> Get back into the project
> Make updates on what's happening,
> walk through ideas of what needs to be done
> do i have a good work environment for this project right now?
> what might i need to work on to advance things?

Made mechanics demonstration video

Outlining current mechanics of the game

Takeaways

*   Adjusted Lock / Shooting sound to LOUDER
    *   Bringing more musical elements to the forefront
*   Need different sound for absorbing projectiles
*   Lots of room to play with scale / melody in the shooting / lock on sounds. Make some decisions here, decide how it elevates / changes between sections. This could be a good way of colouring things differently as the game evolves / the player progresses.
*   When bullets are fired from player, locked on to a target, they will still move backwards in time.
    *   Do I want this? Could be fun for setting up multiple locked targets
    *   Currently this seems to introduce a bug where the bullets could die before they reach the target

Have basic ideas of Rez environment / digital world. Need to really pick at that more and make some strict choices about the the world the game inhabits.

What games do I see existing in a similar camp?

Thumper, Rez, Rollerdrome, Superhot,

Mechanical mostly - Neon White, Boomerang X

Hyper Demon

Shadow of the Colossus - simplicity of story, small bottle tale

I like things implied by environment more than outright said / dialogue

Mood board

[https://www.figma.com/file/INP1bi0nvSmmcCCu4RVie2/Game-Design-Mood-Board?node-id=0%3A1](https://www.figma.com/file/INP1bi0nvSmmcCCu4RVie2/Game-Design-Mood-Board?node-id=0%3A1)

---

## September 13

Taking a look at things again after a roughly 2 month break

Issues with aiming reticle flying everywhere, want to fix to make game functional during movement.

Only works when player speed is 0 currently

Debugging with shoot modes scene
*   Look at sphere script causing all the issues with movement
    *   Disable this, things work fine
    *   How to make movement of reticle rotate around sphere with controls? Is the answer in how Raycasts are moving?

Looking at GPU Instancer to see if I can reduce time for testing in editor
*   Disabled Occlusion Culling, seemed to help
*   Advanced features for improvements can be enabled once scene is fully designed

IDEAS

Player character changes for different types of bullet
*   indicates what you’re about to shoot

Two colours like ikaruga? Polarity mechanic

[List of Games with Polarity Mechanics](https://videochums.com/category/duality-gameplay)

What’s my story? What’s my game about?

Removing viruses from infected systems

log on through a terminal

like superhot

Add a time slow down?

Glitch time Right Trigger breaking things! Not sure why!

Changed RewindToBeat2 to slow down time

instead of -1 and .44s

0.1 and 2s

Not working, not sure why! Look into this

---

## Storyboard Intro

Below / Boomerang X

![Ship crash](Storyboard%20Intro%20ebc813b2c63340b69614cab102afa4aa/Untitled.png)

Ship crashes on shore

![Title card and Credits](Storyboard%20Intro%20ebc813b2c63340b69614cab102afa4aa/Untitled%201.png)

Title flashes and short credits as well

Shadow of the Colossus

![Wide shot of Enviro](Storyboard%20Intro%20ebc813b2c63340b69614cab102afa4aa/Untitled%202.png)

Slowly enjoy sweeping scenery as our protag approaches destination

![Enter temple](Storyboard%20Intro%20ebc813b2c63340b69614cab102afa4aa/Untitled%203.png)

Enters Temple

![Talking to gods](Storyboard%20Intro%20ebc813b2c63340b69614cab102afa4aa/Untitled%204.png)

You speak to the gods

![Price](Storyboard%20Intro%20ebc813b2c63340b69614cab102afa4aa/Untitled%205.png)

They tell you you’ll pay a hefty price to bring the girl back to life

IDEA: Heist - currently on the run? thrown into an intro of escaping some cyber world.

*   think FF7 Intro?

IDEA: You’re Jesus, crucified, and on your journey through ‘ hell ‘ before the resurrection

---
