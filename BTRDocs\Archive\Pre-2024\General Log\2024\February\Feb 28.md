# Feb 28

Model / Animation pipeline adjustments

**Umotion Notes**

Editing Animations 

[(2) Editing Existing Animations - UMotion In Practice](https://www.youtube.com/watch?v=TPzCp6Ezy4o)

Uses Animation Layers - make adjustments without getting rid of original animation

- can use override to adjust original

Animation layers seem to be a simple quick way to make broad adjustments across base animation 

FK vs IK - read up on this - seems IK preferred

Investigate root motion

Seeing curve view of the animations is very useful - can find immediate discrepancies

Copy to other side tool - good for adjustment of mirror things!

Setting up a shortcut for this and enabling Generate allows for quick copying of animation frames

- Shown well in video!

Editing Mesh 

[How to edit mesh without editing rig?](https://www.reddit.com/r/Unity3D/comments/12tovbq/how_to_edit_mesh_without_editing_rig/)

Trying to edit mesh in Blender, Skinned Mesh proving troublesome

[Export Synty Characters from Unity to Blender and Back](https://www.youtube.com/watch?v=QLiseo8s45s)

Looking for tips from this 

- Adjust scale to 1 before export from unity

Scale tip helpful, but bones / rig is off from imported model (snake in this case)

Animation isnt applying to my model because  of a naming problem! the bones are being renamed with ‘__’ instead of ‘_ ‘

Made a tool called Protofactor Bone Renamer to address this within Unity.

Snake infinite track - charging attack that you have to hit enough times before it's dismissed