# March 7

Code optimization in Crosshair - be wary of breaking things!

Doing this in part to fix Infinite track / Collider Hit Callbacks

Not using A* in Infinite Track example - important to error messahes

Lock on working in Infinite Track if the object is on the Enemy Layer and has the Enemy tag. 

But Enemy layer seems invisible or obscured? Need to look into this

Enabled Enemy layer on camera - see what that breaks!

LockedStatus set to false on colliderhitcallback and enemybasicsetup when they get hit

This may break something, need to verify

Also I want to lose the lock on after an enemy gets hit. I think this occurs at two levels

1 - <PERSON>hai<PERSON> loses it’s lock on a moment after shooting at it

2 - once the enemy is hit with a lock on bullet, the lock on anim goes away

These should happen in conjunction with each other - i think?

if crosshair loses it’s lock on, it’s because the enemy got hit by a locked on bullet

Adding this into cycle of ProjectileStateBased - ProjecitleManager - Crosshair

— Upload to Github here

Now considering - i want lock on success to be dependent on how closs your crosshair is to the target when you fire. if you’re relatively close then it will be a success - i think air combat games have a version of this at times

Bullet speed and Rotation speed appear to be the limiting factors in hitting the target

Currently have predictive rotation effect by the crosshair accuracy, not sure how effective this actually is but its implemented!

Havce basic animations for getting hit happening in the Infinite Snake, butthe inherent problem with the scale and positioing seems messed up, and the animation behaves weird. Need to fix at the root of it! 

See if original snake has this problem 

Added dotween shake to elements getting hit 

Tbh not noticeable - emphasize or rethink this