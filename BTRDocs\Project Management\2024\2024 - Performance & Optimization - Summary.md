**Summary of Performance & Optimization Related Entries in 2024**

**Shader Optimization:**
*   **Shader Compilation Time:** Addressed excessively long shader compilation times during builds (over an hour). Attempted preloading shader variants as a solution.
*   **Shader Modifications:** Extensive modifications to Shapes package shaders, suggesting optimization or visual improvements.
*   **Ty Fast Ghost Shader:** Added GPU instancing to Ty Fast Ghost Shader.
*   **Broken Shaders/Effects:** Worked on fixing broken shaders and effects.

**Render Optimization:**
*   **Rendering Bottleneck:** Identified rendering as the current performance bottleneck.
*   **Texture Analysis:** Created a texture analyzer to identify and delete unused textures.
*   **HBAO & Bakery Settings:** Added HBAO and changed Bakery settings for rendering (though initial impact was not noticeable).

**Code & Script Optimization:**
*   **Assembly Definitions:** Explored Assembly Definitions to reduce build times.
*   **A\* Update:** Updated A\* (pathfinding) to a newer version (*.95) for potential performance improvements.
*   **Custom AI Path:** Made improvements to custom AI path alignment, but unsure of overall impact and noted bugs needing fixes.
*   **Crosshair Script Refactor:** Split Crosshair script into 3 classes.
*   **Projectile Lifetimes:** Spent significant time resetting projectile lifetimes, aiming to prevent indefinite bullet holding (considered Bomberman-like mechanic).
*   **Projectile System Changes:** Implemented projectile system changes (Dec 18/19 notes), likely for optimization.
*   **DOTS Conversion:** Considered converting projectile system to DOTS (Data-Oriented Technology Stack).
*   **Job System for Collisions:** Implemented Jobs system for projectile collisions (Oct 14).
*   **Particle System Manager:** Using Particle System Manager for better tracking and optimization of particle systems (Oct 14).
*   **VFX Graph:** Need to assess if VFX Graph can solve particle system performance issues.
*   **ECS Effects System:** Attempted to build ECS (Entity Component System) effects system (Nov 4).

**General Optimization & Performance Testing:**
*   **General Optimizations:** Made "major optimizations" (Dec 4), gameplay testing needed.
*   **Performance Monitoring:** Monitoring performance after various optimizations.
*   **Performance Locked at 60FPS:** Noted performance strangely locked at 60FPS, needs investigation (June 18).
*   **Performance Testing - Gameplay:** Key theme: Performance testing of gameplay.

**Build Time Reduction:**
*   **Build Time Issues:** Need to reduce build times, initial shader compilation and general build times are too long.

**Code Issues & Refinement:**
*   **Subtile Code Issues:** Identified subtle code issues: Unintentional Boxing, String Concatenation, LINQ on Value Type.
*   **Enemy Damagable Parts:** Fixed issues with enemy damagable parts and maintained burst.
*   **Homing Projectiles:** Fixed homing for enemy shot projectiles.

**Boss/Enemy/Level Optimization Strategy:**
*   **Rigid Decisions Needed:** Need to make rigid decisions on Bosses/Enemies/Levels for optimization.
*   **Boss Look Strategy:** Strategy for creative boss looks: phases, base shapes, geometry nodes for deconstructed look, unique third phases.

**Tooling & Workflow for Optimization:**
*   **Unity Project Auditor:** Referenced Unity Project Auditor for project settings analysis.
*   **Texture Analyser:** Created texture analyser tool.
*   **Profiler:** Need to optimize gameplay based on profiler (July 22).
*   **PrimeTween:** Replacing DOTween with PrimeTween for potential performance benefits (June 18, Aug 12).

**Key Themes:**
*   Shader Optimization
*   Render Optimization
*   Performance Testing - Gameplay