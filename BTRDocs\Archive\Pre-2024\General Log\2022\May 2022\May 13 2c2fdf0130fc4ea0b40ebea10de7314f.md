# May 13

[https://www.youtube.com/watch?v=YaUdstkv1RE](https://www.youtube.com/watch?v=YaUdstkv1RE)

Great video about solo dev. 

Advice on making it through the journey!

[Classic Game Postmortem: 'Q*bert'](https://www.youtube.com/watch?v=FhkLfz0GKYU)

Checking this out as well

Did a half hour, more of a history lesson than much else 

[LUTious Color: Grading for Games](https://www.youtube.com/watch?v=-fb3QXR5spE)

Advice for color grading for games

Saturation map can help figure out the grading of a certain image

Thinking about movement in Boomerang X - is this something to take influence from? A game mode?

Looking at GPU Instancer again to setup city environment again 

[GPU Instancer Tutorial - Prefab Manager (Part 1) - Prefabs in the Scene](https://www.youtube.com/watch?v=Ns_d88Ol4SY&list=PLWEl7wzzCgPjYO3_9M6QwvoPOXV9R6P1K&index=2)

Have lots of buildings! But its just too system intensive. Need to find a different way to do this / reduce vertices in the scene

Maybe adjust lod levels? Trying this in the scene

WOuld Impostors be better?

![Untitled](May%2013%202c2fdf0130fc4ea0b40ebea10de7314f/Untitled.png)

Need to dig into understandign the profiler

But it’s clear that these buildings are causing the issue

Cant use Tessera cause it’s not compatible with GPU Instancer

Saw advice - always profile a build not playmode in editor

How to do this?

NOTE: GO over May Overview #2 and also make an intro scene that I can easily attach to build for demoing purposes

Wrong scene in build! Try building the buildings scene :)

Idea - ratchet and Clank portals to different parts or scenes