# May 2

Charles Amat Advice

OOP Techniques, SOLID priciples
encapsualte behavior in calses and objects
control behviour through polymorphism
<PERSON> videos
Jason Storey videos - cleanly structuring the code vids
Clean coding techniques
Design Patterns - be careful about using this right away
Really try to understand the concepts

Viewing Jason Storey videos for advice on working on a new project

[https://www.youtube.com/watch?v=nVieP57TD20](https://www.youtube.com/watch?v=nVieP57TD20)

Uses Everything for searching the system

_FolderName to keep folders at the top

Organize into Scripts / Art / Scenes Folders

Project settings - Root namespace

- Name after yourself - this is important but not stated in video

Uses Rider for Unity Scripting

Uses his own default script structure - just an Awake

[ MenuItem(”path”)]

use this above a function to make it show up in the Editor

Can autocreate folders and refresh assetDatabase to initiate a template for starting a new project

[https://www.youtube.com/watch?v=45fFcZwkbBs](https://www.youtube.com/watch?v=45fFcZwkbBs)

Can edit the packages manifest file to remove packages you dont want and install the ones you do

Text files for arrays of names / items / or more

[https://www.youtube.com/watch?v=p7GfSsQvR78](https://www.youtube.com/watch?v=p7GfSsQvR78)

SOftware Architecture !

[https://www.youtube.com/watch?v=sh7f4K9Wbj8&t=1s](https://www.youtube.com/watch?v=sh7f4K9Wbj8&t=1s)

Local Functions

[https://www.youtube.com/watch?v=D7Kf1CY1PNQ](https://www.youtube.com/watch?v=D7Kf1CY1PNQ)

Delegates and Events

[https://www.youtube.com/watch?v=6NRqwL3N5Go](https://www.youtube.com/watch?v=6NRqwL3N5Go)

Delegate - a variable that holds a method instead of data

It’s just the setup, similar to a class defintition, but not the instance of the class

public delegate void DoSomething(int num)

public DoSoemthing something;  ←— now we have an instance

public EmapleClass() {

something = FunctionOne;

something?.Invoke(123)

}

private void FunctionOne(int num) {

Console.WriteLine($”Function on called with value: [num]”);

}

Above we assign a fumction to something and run data. 

? is null propagation operator, will check that its not null

You could then assign this to another function afterwards

You coudl also subscribe it to another function;

example

something += FunctionTwo;

This is like events, these are two types

public EventHandler Something

public Action SomethingTwo

Can be assgined an anonymous fucntion as written below

Something = () ⇒ { };

Look up this different between Action and EventHandler! Seems useful

Seems we never really need a return type for Events, but need to look into this

**Thinknig about drum patterns applied to a controller** 

Breakbeats, syncopation with one drum at a time

Is this called Broken Beats?

Related - [https://www.youtube.com/watch?v=qWfvFKVxFRc](https://www.youtube.com/watch?v=qWfvFKVxFRc)

Jungle / DnB - [https://www.youtube.com/watch?v=XNeW40Quu5w](https://www.youtube.com/watch?v=XNeW40Quu5w)

Think this applied to similar music - 

ZULI - [https://www.youtube.com/watch?v=yTFEmyPwOyc](https://www.youtube.com/watch?v=yTFEmyPwOyc)

150 BPM

How can this be call and response?

Need to look up some videos on how to make - 

Experimental Electronic
Experimental Club
Experimental Techno
Experimental Jungle
IDM
Jungle/Drum'n'Bass
Techno

sampling groovebox clips

[https://www.youtube.com/watch?v=qu2Ep_qAFOQ](https://www.youtube.com/watch?v=qu2Ep_qAFOQ)

**Classic Jungle - SUPER GOOD TUTORIAL**

[https://www.youtube.com/watch?v=ss13J-wnVS0&t=21s](https://www.youtube.com/watch?v=ss13J-wnVS0&t=21s)

- restart and move around drum loop based on locked on targets?

Floating Points Arp Tutorial - Ableton Filters

[https://www.youtube.com/watch?v=BGMyN0cncCQ](https://www.youtube.com/watch?v=BGMyN0cncCQ)

Another Floating Points - does a lot of electronic music

- 120 BPM - Close to a House tempo and beat but not all the way there
- turn off retrigger for more organic synth sound for lfo
- Analog synths

[https://www.youtube.com/watch?v=7QF9zgeNo4c](https://www.youtube.com/watch?v=7QF9zgeNo4c)

**Basic of Techno**

[https://www.youtube.com/watch?v=Tbvn_cEux3U](https://www.youtube.com/watch?v=Tbvn_cEux3U)

909 Kick and 808 Snare

Stilted feel / quanitzed is typical

130ish ? that’s the choice here

Core groove is king - a singular groove

Tom and hi hat interplay highlights this 

Building on the central groove

Kick Drum Reverb - quite popular!

Subvert expectations - a bit of randomness adds humanity to the robotic grooves

Messing with attack / release values on hihat and snare and synth

- Is this possible in FMOD?

**Autechre style beats**

[https://www.youtube.com/watch?v=GC15wiSyIUw](https://www.youtube.com/watch?v=GC15wiSyIUw)

Good for random beep boops

**Breakcore done in Simpler**

[https://www.youtube.com/watch?v=C2_slXoFgIw](https://www.youtube.com/watch?v=C2_slXoFgIw)

**Rival Consoles**

[https://www.youtube.com/watch?v=APe2kwvGGZc](https://www.youtube.com/watch?v=APe2kwvGGZc)

Using Ableton and DRC

Plugins usually in Steinberg and need to be moved

DIfferent color bullets are different parts of the beat? Like triggering differnet parts of the sample?

So you lock on to several and its retriggered a few times