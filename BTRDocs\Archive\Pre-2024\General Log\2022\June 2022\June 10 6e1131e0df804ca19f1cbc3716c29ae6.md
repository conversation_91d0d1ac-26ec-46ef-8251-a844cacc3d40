# June 10

<aside>
💡 Fixed UI crosshair bug - probably want to do more with how this design looks and works eventually

</aside>

Keep hitting this error once the level progressess

![Untitled](June%2010%206e1131e0df804ca19f1cbc3716c29ae6/Untitled.png)

![Untitled](June%2010%206e1131e0df804ca19f1cbc3716c29ae6/Untitled%201.png)

![Untitled](June%2010%206e1131e0df804ca19f1cbc3716c29ae6/Untitled%202.png)

Thinking about A Star problems - should I make a whole new project for testing A Star?

AI Testing is a project for figuring out if A Star will work with Behavior Designer options

Does simple smooth on bot work? Left it on the bot

BD Movement seems to work fine.

Removed AIPATH and replaced with LOcal SPace Rich AI

Trying BD Tactical now…

Complaining there is no RVO controller - different asset needed?

No RVO controller needed - was just camera confused about what assets to control 

Issue when agents and enemies are repositoned upon scene start - change code to fix thsi?

Otherwise cant test properly

SOrt of fixed that, but behaviors working intermittenly - not sure it’s stable