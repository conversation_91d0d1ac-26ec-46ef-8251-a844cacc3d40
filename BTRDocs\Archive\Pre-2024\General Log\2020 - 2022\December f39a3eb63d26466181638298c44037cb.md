# December

**December 7**

Cinemachine tutorials

Blend between cameras - important aspect!

Ignore Timescale property may be important for me

**Q** Use Timeline for certain cinematic sections? Or better solution?

---

Integrating Object Pooling into current scene as part of A* / Behavior Designer Pipeline

Object pooling fixes issues with <PERSON>* <PERSON>v<PERSON><PERSON>

Made an ovveride script for UltimateSpawner's Instantiate method

 - will this be needed on every instance?

How do I get this working for multiple types of enemies being pulled from the pool?

EnemyObjectPool.cs is working! Have a hacky way to make it work

But how do I get it to work with multiple enemy type being called by Ultimate Spawner?

How to compare if prefab and instance of object match?

[https://docs.unity3d.com/ScriptReference/PrefabUtility.GetPrefabObject.html](https://docs.unity3d.com/ScriptReference/PrefabUtility.GetPrefabObject.html)

THIS IS OUTDATED

THink about how Object Particle Spawner works as a comparison

Spawns objects by weight - maybe not so useful

I want Ultimate Spawner's call to Instantiate a specific Objec tto pull from the necessary pool

Q? - Do I need to use custom spawnable item providers? Look into this tomorrow

Downloaded Pool Manager Asset incase it's useful for this at all 

**December 9**

Thinking on the solution - match the pool tag to the prefab tag? Every enemy type needs a unique tag then

Maybe Layers is better? Can assign more then one. Limited number of Layers! Best not to use for this

Currently use LaunchableBullet tag for projectiles

![Untitled](December%20f39a3eb63d26466181638298c44037cb/Untitled.png)

Some of these tags are not necessary - need to audit

Relevant? [https://forum.unity.com/threads/test-if-prefab-is-an-instance.562900/](https://forum.unity.com/threads/test-if-prefab-is-an-instance.562900/)

**NEW APPROACH**

Try using Pool Manager 

It's working! Renamed class to "Spawn From Pool"

Need to get spawn from pool to pull name from SpawnPool class - NOT HARDCODE

**December 13**

Previous solution broken. Possibly due to renaming? May have been last step after working

NO not the issue. Spawn Pool Per Prefab Pool Options had not saved. The prefab object was not selected and number to preload not given. This caused A* problems. For moving nav mesh you need to preload these elements or it does not work properly. 

changed poolname to pull from component in same gameObject

**December 14**

Did some research on behaviour designer 

need to download some more examples and dig into some good AI setups

**December 15**

Bringing in example scenes from tie in assets for BD

- Pool Manager, Sensor Toolkit,

Tried adding a Player Radius to the Player, so that enemies using Seek stop at this radius - never too close to player

Not sure how target object is being assigned to Behavior Designer Prefabs. Look into this for appropriate behaviour

Also need to look into proper structure for Seek → Wander - Repeat type BD loop 

Not currently working after Wander

**December 16**

Fixing the long nagging DOTween issue. Seems like it's the *.meta file associated with the folder that kept bringing it back? We'll see!

WRONG it seems to be after a SourceTree upload

Seems to be when Git LFS file kicks in, it redownloads this stuff

Found error on defining enemy transform in Spaw Pool. Referenced an instance within scene NOT the one that's a prefab in assets

Problem with setting things by prefab. Seems Seek in Behavior Designer does NOT successfully act if it's set to find the prefab, needs to be set to the thing in the SCENE

This could be an issue with setting up different levels. Can I automate some type of Find Target In Scene?

[https://opsive.com/support/documentation/behavior-designer/referencing-scene-objects/](https://opsive.com/support/documentation/behavior-designer/referencing-scene-objects/)

Still not fully working and not sure why

Local Space AI issues - look through notes on how to resolve

**December 19**

Realized error in enemy AI is likely that due to player radius game object not existing to the NavMesh - when AI tries to seek it doesnt see that object and cause no movement. 

Player Radius purpose is to get enemy to stop at a specific radius - other means of doing that?

Changed Layer of Player Radius to Default instead of Enemy Plane so it is not ignored - 

Actually not sure if this made a difference lol. Changing to directsly the playing radius object in scene for testing and no result there either

Many issues here. Dont think they’re actually find their targets, just defaulting to the coordinates that can be set (default is 0,0,0)

Targetting of game objects is working, 1 was player, other was player radius, retargetted approrpiately

PROBLEM is that it heads for center of object. Does just approach next to object depending on size at all. When player radius and player in same position and both targetted, both AI will end in same spot tho Player radius MUCH bigger then player

Conditional Aborts - [https://www.youtube.com/watch?v=GFsK5x6ZW7k](https://www.youtube.com/watch?v=GFsK5x6ZW7k)

**December 20**

Followed advice in this video

Conditional Aborts - [https://www.youtube.com/watch?v=GFsK5x6ZW7k](https://www.youtube.com/watch?v=GFsK5x6ZW7k)

Solved problem of distance and a good idea on game logic

Done on ship04 COPY enemy. More testing and experimenting to be done!

**December 21**

Behaviour Designer Testing and learning 

Can inherit a reference from another Behavior Tree - may be useful for multiple enemies to inherit target

Found in RTS Sample Project

**December 27**

Trying wandering AI - previously not working

Working now but pretty dumb

May just need points to wander between? May be a way to fix Wander function

Changing Field of view due to 4 quadrant turning not capturing complete persepctive

Previously 40

Now 55

CHRONOS: Do enemies move backward in time or no??

Wander gets stuck while moving forward - cannot calculate position properly? Stuck on walls / corners. Seems the point chosen is not contexual to a moving navmesh - work arounds to this?

Might need reference points to navigate between - patrol but move out the way of incoming objects / scenery??

How to stay center on moving object unity scene view????

**December 29**

Building enemies in a scene

Working on getting 1 enemy functioning properly on battlefield

Need to evaluate Wander ability / try out way points instead for when moving player

Trying out waypoints now

NOTE: Need a way for BD to auto pick up all waypoints in scene for Patrol function, by tag / layer / name / whatever

Works but bullets inheirit rotation - meaning, when enemy moves around bullets move with them as well - mainly the rotating part is bad

Can stop rotating but bullets all move in wrong direction and sometimes towards the sky?

WEIRD

Also might want to face Player at all times? Or maybe some enemy types would be good for that

**December 30**

Weapon Demo Scene from Obj Particle Spawner may be helpful for bullet issues

Problem appears to be something in Enemy Basic Script - is disabled nothing even happens

Change in direction of bullet appears to be bullet released from system. 

Disable Shape in particle effects to shoot straight ahead

Homing features dont on/off dont seem to effect bullet trajectory issues

Tried using Standard Bullet - Boss Test 1 and still happening! Still trajectory issues! Something odd happening

When assigned a different pool, enemyshootingbullets started exhibiting same issues with trajectory - always shooting to one thing. this appears to be the cause!

Orientation always seems to be an issue. Need the local space of the bullet not to be enemy as parent. However this causes shooting direction issues. 

**December 31**

Looking at best way to implement bullet movement - current translate implementation seems to be problem with current trajectory issues

Fixed Trajectory issues! Much simpler implenetation in Enemy Basics class. Seems some of the Chronos stuff is not necessary for me?

Need to make enemy basics into a bullet script

Then make a seperate script for enemies health, etc- different pooling system needed etc. 

Working on BD finding all objects tagged enemy waypoints and adding them to global variable list, which then it use for patrol

May help - [https://www.opsive.com/forum/index.php?threads/patrol-waypoints-disappear-after-changing-scene.4727/](https://www.opsive.com/forum/index.php?threads/patrol-waypoints-disappear-after-changing-scene.4727/)