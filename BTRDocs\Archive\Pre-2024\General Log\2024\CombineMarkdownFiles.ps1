# Get the current directory where the script is located
$currentDir = $PWD
$outputFile = Join-Path $currentDir "CombinedMarkdown.md"

# Create timestamp for the operation
$timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

try {
    # Create the output file with a header
    [System.IO.File]::WriteAllText($outputFile, "# Combined Markdown Files`nGenerated on: $timestamp`n`n---`n", [System.Text.Encoding]::UTF8)

    # Get all markdown files recursively and sort them by name
    $files = Get-ChildItem -Path $currentDir -Filter "*.md" -Recurse | Sort-Object Name

    # Display total count
    Write-Host "Found $($files.Count) markdown files to process"

    # Counter for progress
    $current = 0

    foreach ($file in $files) {
        $current++
        Write-Progress -Activity "Combining Markdown Files" -Status "Processing $($file.Name)" -PercentComplete (($current / $files.Count) * 100)
        
        try {
            # Skip the output file if it exists
            if ($file.FullName -eq $outputFile) {
                continue
            }
            
            # Read content with UTF8 encoding
            $content = [System.IO.File]::ReadAllText($file.FullName, [System.Text.Encoding]::UTF8)
            
            # Prepare the new content
            $newContent = "`n## From: $($file.Name)`n`n$content`n`n---`n"
            
            # Append to file with UTF8 encoding
            [System.IO.File]::AppendAllText($outputFile, $newContent, [System.Text.Encoding]::UTF8)
        }
        catch {
            Write-Warning "Error processing $($file.Name): $_"
        }
    }

    Write-Host "Complete! Combined markdown file saved to: $outputFile"
}
catch {
    Write-Error "Failed to create or write to output file: $_"
    exit 1
}
