# Crosshair / Projectile bug Fixing

![Untitled](Crosshair%20Projectile%20bug%20Fixing%2036853ac3d16c4df08caaf2ae442ac77b/Untitled.png)

Koreo related errors here

![Untitled](Crosshair%20Projectile%20bug%20Fixing%2036853ac3d16c4df08caaf2ae442ac77b/Untitled%201.png)

Player is locked on to current EnemyTarget

EnemyTarget is still active - not dead

Targets list is 0

Locks count is 3

LockedList showing 3 items - projectiles

1 is parented to horizontal plane 

Has no projectileTarget

Shows both Locked and Released

![Untitled](Crosshair%20Projectile%20bug%20Fixing%2036853ac3d16c4df08caaf2ae442ac77b/Untitled%202.png)

2 Next projectile on list is inactive!

Has died - everything disbaled and no projectileTarget

Has NEGATIVE lifetime!

Still has LockedOnPrefab

3 Exactly the same as #2

Negative lifetime and all other attributes

If lifetime , 0 it just kills itself

Probably should pause lifetime if locked?

Also only kill itself if it’s not locked - need to look at if my locked states are setup properly 

CHANGED THIS TO LAUNCHING - not LOCKED state

UPDATE Launching does not appear to be the correct approach either

Still a bunch of inactive bullets in lockedlist

Error appears to be Cross trying to launch inactive bullets

LIfetime is not the issue with any of these - all alive for only a couple seconds 

Issue is in the Projectile class?

What happens if projectile collides with enemy while locked?

Need to write out projectile states and how it moves through system 

[Projectile States and system movement](Crosshair%20Projectile%20bug%20Fixing%2036853ac3d16c4df08caaf2ae442ac77b/Projectile%20States%20and%20system%20movement%20732ff3a0b1e04d4ab1675544c45b3c74.md)