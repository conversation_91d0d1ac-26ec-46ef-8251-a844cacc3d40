# May 17

Revisit previous document - but right now! Look at some music 

Added Game Over background loop and Death sounds

Added to Trans drum fills (Trans FX)

 Also added Drum FX

Looking at 

enemy movement issuesf

SHift- F to follow game object in scene view

Enemies move smoother with higher number (0.8 Seconds) of path recalculation

But they get stuck on dolly path turning 

Testing - what if no curves, only sharp movement and turns?

Lowering dolly resolutitoin to 1 for testing

Still seems to get stuck! But the example has a boat turning, what’s different?

Still jittery movement when no rotation, so not necessarily a rotation issue

Upgrading to A* Beta to see if any issues with gizmos / etc are resolved.

Beta appears to work better! Gizmos are back!

BIG tip at beginning of this for equally distributing objects in positions! 

[4 new Unity Features YOU'RE MISSING!](https://www.youtube.com/watch?v=mCkjwJGtRI0)

L and R in inspector

Right click and get proeprties from any game object as well!

![Untitled](May%2017%20c478e3eee6b94f5d8f5d12520647c821/Untitled.png)

Getting stuck like this - what’s happening?

Disabled gravity on enemy rigid bodies and it seems to have fixe dthis

Weird behavior in enemies - still jittery but gizmos work and on the right track i think

<PERSON><PERSON> try new enemy type that isn’t shoot and scoot - combine others

Read through docs and set these up again

---

Testing Enemy movement - seems initially ok and fast, but runs into issues a few second later

Seems to be reasonable at 500 Gameplay Plane Movement

Definitely some issue after 500 - tried 700. Might be update times conflicting with speed of platform - worth further testing, but sticking with 500 for now

Turning of platform no longer seems to cause issues for enemies as well

---

Fixing BackgroundFX issues - doesnt work with current skybox

<aside>
💡 SkyPortal Cubemap is awesome - do a level with this!

</aside>

Using Marble Flow Currently - anyway to get this moving and trippy?

Really cant get BackgroundFX to work! Not sure why it keeps failing - placement of Unity events? Something else?

Writing up some basic movement scripts for objects in scenes - refactoring some previous scripts

Need to first fix 3dmodels being used from blender

Recenter the Origin 

[How to recenter an object's origin?](https://blender.stackexchange.com/questions/14294/how-to-recenter-an-objects-origin)

Trying much faster enemies and gameplay in Cyber City 2

Going for 64th notes for enemy fire

Not sure its the difference im looking for!

Put things in a working state for new area

Enemies move and shoot okay

Any way to make things move faster? feel slow right now, maybe smaller objects closer? Experiment

Refer to earlier notes on what needs to be done

A little work on reload / load next scene. Probably not as quick a transition as i want - how to handle this

Some tips on how to do this

[https://forum.unity.com/threads/seamless-scene-transition.930945/](https://forum.unity.com/threads/seamless-scene-transition.930945/)

Cannot seem to get further Behavior Designer things working!!!Only my Scoot and SHoot original works, Octagons do not. Need to go through a full test scene of setting up some of these guys- bare bones.

Meanwhile…..

Maybe I should just scale everything way smaller? This may help things, seems like a good direction to move towards.

IMP to do:

Scale down - might be plenty efficient!

Try Level without moving navmesh - what does that level look like?

Try now, - somewhat successful! random distribution helps as well