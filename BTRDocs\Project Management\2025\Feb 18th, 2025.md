# Progress Journal - Feb 18th, 2025

## Radar System Investigation

Today, I started looking into the feasibility of writing our own radar system.  A key focus is ensuring full support for jobs to leverage Unity's job system for performance. This is crucial for handling potentially many radar targets efficiently. We are outlining our approach in the Custom Radar Implementation file.

I also spent time understanding the "radar effect" as described in this video: [https://www.youtube.com/watch?v=J0gmrgpx6gk](https://www.youtube.com/watch?v=J0gmrgpx6gk).  The visual style and feedback mechanisms presented in the video are interesting and could be valuable for our radar implementation.  Specifically, the way the radar pulses and the visual representation of targets fading in and out are worth considering.

Next steps will involve prototyping a basic radar system and experimenting with different job-based approaches for target detection and processing. I'll also be researching existing radar implementations in Unity to identify best practices and potential challenges.