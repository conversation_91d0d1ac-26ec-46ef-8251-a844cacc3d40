---
title: Game Development Learning Resources
tags: [gamedev, learning, resources]
created: 2024-02-13
updated: 2025-02-06
type: resource-collection
---

# Game Development Learning Resources

## Resource Categories

### Game Design
- [[Designer Notes Podcast]]
- [[Abstract - The Art of Design]]
- Academic Resources:
  - [[Google Scholar]] - games and culture
  - [gamestudies.org](https://gamestudies.org)

### Unity Development
- [[Unity Profiler Walkthrough & Tutorial|Unity Profiler Tutorial]] 
- [[Code Monkey Intermediate Course]] 
- [[Unity Settings Framework|In-Game Settings Framework]] 

### Programming Patterns
- [[3 Game Programming Patterns WE ACTUALLY NEED|Essential Game Programming Patterns]] 
- [[SIMPLE Tip For Better Unity Game Architecture|Unity Architecture Tips]] 

### Visual Effects & Design
- [[Unity VFX Graph：Ribbons and Balls|VFX Graph Tutorial - Ribbons & Balls]] 
- [[Practical generators for blender|Blender Generator Tools]] 

### Level Design
- [[Spatial Communication in Level Design]] 

## Technical Notes

### Scene Management
![[Unity SceneManager Documentation]]

#### Key Concepts
- Loading scenes additively for pause menus
- GameManager pattern:
  - Single GameManager object in initial scene
  - Set to DontDestroyOnLoad
  - Parents UI elements for persistence
  - Handles scene transitions and global logic

#### Implementation Notes
Consider:
- Parenting Timekeeper and UI to GameManager
- Testing persistence across scene transitions

## Game Analysis

### Hi-Fi RUSH Design Analysis
![[Developing Hi-Fi RUSH Backwards]]

#### Core Design Elements
1. **Rhythm Action Fundamentals**
   - Music video aesthetic
   - Forgiving rhythm mechanics
   - Adaptive animation speeds

2. **Positive Feedback Loop**
   - Natural interpolation
   - Rewarding beat matching
   - Accessible design

3. **Player Success Indicators**
   - Audio feedback (HEY sound, hi-hat effects)
   - Visual reinforcement
   - Progressive improvement system

## Chronological Updates

### 2024

#### May
- Scene Management research
- Hi-Fi RUSH GDC analysis
- Unity architecture patterns

#### April
- Code Monkey course progress
- Projectile system implementation

#### March
- Unity Profiler deep dive
- Performance optimization studies

#### February
- Level design principles
- VFX implementation
- Architecture patterns