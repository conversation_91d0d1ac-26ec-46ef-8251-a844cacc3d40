using UnityEngine;
using BTR;
using Stylo.Epoch;
using Stylo.Cadance;

namespace BTR
{
    /// <summary>
    /// A self-contained static enemy that shoots projectiles in time with Cadence events.
    /// Integrates with the epoch system for proper time scaling and works independently of EnemyManager.
    /// Uses ProjectileManager for spawning projectiles and follows the established architecture patterns.
    /// </summary>
    public class StaticShooter : MonoBehaviour
    {
        [Header("Configuration")]
        [SerializeField] private StaticShooterConfiguration configuration;
        [SerializeField] private bool overrideConfigDebugLogs = false;
        [SerializeField] private bool enableDebugLogs = false;

        [Header("Shooting Direction")]
        [SerializeField] private Transform shootDirection;
        [SerializeField] private Vector3 defaultShootDirection = Vector3.up;

        private Transform cachedTransform;
        private float lastShotTime;
        private bool canShoot = true;
        private EpochGlobalClock epochClock;
        private bool isInitialized = false;

        // Cadence integration
        private string currentCadenceEventID;
        private bool isCadenceRegistered = false;

        private void Awake()
        {
            cachedTransform = transform;

            if (configuration == null)
            {
                LogError("StaticShooterConfiguration is required but not assigned!");
                enabled = false;
                return;
            }

            // Set debug logging preference
            bool shouldEnableDebugLogs = overrideConfigDebugLogs ? enableDebugLogs : configuration.EnableDebugLogs;

            if (shouldEnableDebugLogs)
            {
                LogDebug($"StaticShooter initializing on {gameObject.name}");
            }

            // Initialize epoch clock
            InitializeEpochClock();
        }

        private void Start()
        {
            // Register with Cadence system
            RegisterCadenceEvents();

            // Initialize shooting state
            lastShotTime = GetCurrentTime();
            isInitialized = true;

            LogDebug($"StaticShooter fully initialized on {gameObject.name}");
        }

        private void InitializeEpochClock()
        {
            string clockKey = configuration?.EpochClockKey ?? "Global";

            // Find the EpochGlobalClock with the specified key
            EpochGlobalClock[] globalClocks = FindObjectsByType<EpochGlobalClock>(FindObjectsSortMode.None);
            foreach (var clock in globalClocks)
            {
                if (clock.clockKey == clockKey)
                {
                    epochClock = clock;
                    LogDebug($"Connected to Epoch clock '{clockKey}'");
                    break;
                }
            }

            if (epochClock == null)
            {
                LogWarning($"EpochGlobalClock with key '{clockKey}' not found. Using Time.time fallback.");
            }
        }

        private void RegisterCadenceEvents()
        {
            if (configuration == null)
            {
                LogError("Cannot register Cadence events - configuration is null");
                return;
            }

            currentCadenceEventID = configuration.CadenceEventID;

            if (string.IsNullOrEmpty(currentCadenceEventID))
            {
                LogWarning("Cadence event ID is not set in configuration");
                return;
            }

            if (Cadance.Instance == null)
            {
                LogWarning("Cadance.Instance is null - will retry registration");
                StartCoroutine(RetryRegistration());
                return;
            }

            try
            {
                Cadance.Instance.RegisterForEvents(currentCadenceEventID, OnCadenceShootEvent);
                isCadenceRegistered = true;
                LogDebug($"Successfully registered for Cadence event '{currentCadenceEventID}'");
            }
            catch (System.Exception e)
            {
                LogError($"Failed to register Cadence events: {e.Message}");
            }
        }

        private System.Collections.IEnumerator RetryRegistration()
        {
            int attempts = 0;
            const int maxAttempts = 10;

            while (Cadance.Instance == null && attempts < maxAttempts)
            {
                yield return new WaitForSeconds(0.1f);
                attempts++;

                if (Cadance.Instance != null)
                {
                    RegisterCadenceEvents();
                    yield break;
                }
            }

            if (Cadance.Instance == null)
            {
                LogError($"Failed to register with Cadance after {maxAttempts} attempts");
            }
        }

        private void UnregisterCadenceEvents()
        {
            if (isCadenceRegistered && Cadance.Instance != null && !string.IsNullOrEmpty(currentCadenceEventID))
            {
                try
                {
                    Cadance.Instance.UnregisterForEvents(currentCadenceEventID, OnCadenceShootEvent);
                    isCadenceRegistered = false;
                    LogDebug($"Unregistered from Cadence event '{currentCadenceEventID}'");
                }
                catch (System.Exception e)
                {
                    LogError($"Failed to unregister Cadence events: {e.Message}");
                }
            }
        }

        private void OnCadenceShootEvent(CadanceEvent evt)
        {
            if (!isInitialized || !canShoot)
            {
                return;
            }

            LogDebug($"Cadence shoot event received: {evt.EventID}");
            Shoot();
        }

        public void Shoot()
        {
            if (!CanShoot())
            {
                LogDebug($"Cannot shoot - conditions not met");
                return;
            }

            if (ProjectileManager.Instance == null)
            {
                LogError("ProjectileManager.Instance is null - cannot spawn projectile");
                return;
            }

            // Get shooting direction
            Vector3 shootDir = GetShootDirection();
            Quaternion rotation = Quaternion.LookRotation(shootDir);

            // Get configuration values
            float speed = configuration.ShootSpeed;
            float lifetime = configuration.ProjectileLifetime;
            float scale = configuration.ProjectileScale;
            float damage = configuration.ProjectileDamage;
            bool enableHoming = configuration.EnableHoming;

            LogDebug($"Shooting projectile - Speed: {speed}, Lifetime: {lifetime}, Damage: {damage}, Homing: {enableHoming}");

            // Spawn projectile using ProjectileManager interface
            var projectile = ProjectileManager.Instance.SpawnProjectileInterface(
                cachedTransform.position,
                rotation,
                speed,
                lifetime,
                scale,
                damage,
                enableHoming,
                null, // material - let ProjectileManager handle default
                null, // target - will be set by homing system if enabled
                false // isPlayerProjectile
            );

            if (projectile != null)
            {
                LogDebug($"Successfully spawned projectile with direction {shootDir}");
                lastShotTime = GetCurrentTime();

                // Trigger audio event if available
                TriggerShootAudioEvent();
            }
            else
            {
                LogWarning("Failed to spawn projectile");
            }
        }

        private Vector3 GetShootDirection()
        {
            if (shootDirection != null)
            {
                return shootDirection.forward;
            }

            // Use configured default direction or transform up
            if (defaultShootDirection != Vector3.zero)
            {
                return cachedTransform.TransformDirection(defaultShootDirection.normalized);
            }

            return cachedTransform.up;
        }

        private bool CanShoot()
        {
            if (!isInitialized || !canShoot || !gameObject.activeInHierarchy)
            {
                return false;
            }

            float currentTime = GetCurrentTime();
            float timeSinceLastShot = currentTime - lastShotTime;
            float minInterval = configuration?.MinShotInterval ?? 0.1f;

            return timeSinceLastShot >= minInterval;
        }

        private void TriggerShootAudioEvent()
        {
            // Audio events can be handled by other systems listening for projectile spawns
            // The ProjectileManager already handles audio integration through its systems
            LogDebug("Projectile fired - audio handled by ProjectileManager");
        }

        /// <summary>
        /// Get current time from Epoch clock or fallback to Time.time
        /// </summary>
        private float GetCurrentTime()
        {
            return epochClock != null ? epochClock.Time : Time.time;
        }

        /// <summary>
        /// Enable shooting functionality
        /// </summary>
        public void EnableShooting()
        {
            canShoot = true;
            LogDebug("Shooting enabled");
        }

        /// <summary>
        /// Disable shooting functionality
        /// </summary>
        public void DisableShooting()
        {
            canShoot = false;
            LogDebug("Shooting disabled");
        }

        /// <summary>
        /// Check if shooting is currently enabled
        /// </summary>
        public bool IsShootingEnabled => canShoot && isInitialized;

        /// <summary>
        /// Get the current configuration
        /// </summary>
        public StaticShooterConfiguration Configuration => configuration;

        /// <summary>
        /// Update the configuration at runtime
        /// </summary>
        public void SetConfiguration(StaticShooterConfiguration newConfiguration)
        {
            if (newConfiguration == null)
            {
                LogError("Cannot set null configuration");
                return;
            }

            // Unregister old events if needed
            if (configuration != null && configuration.CadenceEventID != newConfiguration.CadenceEventID)
            {
                UnregisterCadenceEvents();
            }

            configuration = newConfiguration;

            // Re-register events if event ID changed
            if (isCadenceRegistered && currentCadenceEventID != configuration.CadenceEventID)
            {
                RegisterCadenceEvents();
            }

            LogDebug("Configuration updated");
        }

        private void OnEnable()
        {
            if (isInitialized && !isCadenceRegistered)
            {
                RegisterCadenceEvents();
            }
        }

        private void OnDisable()
        {
            UnregisterCadenceEvents();
        }

        private void OnDestroy()
        {
            UnregisterCadenceEvents();
        }

        #region Logging Helpers

        private void LogDebug(string message)
        {
            bool shouldLog = overrideConfigDebugLogs ? enableDebugLogs : (configuration?.EnableDebugLogs ?? false);
            if (shouldLog)
            {
                Debug.Log($"[StaticShooter] {message}");
            }
        }

        private void LogWarning(string message)
        {
            Debug.LogWarning($"[StaticShooter] {message}");
        }

        private void LogError(string message)
        {
            Debug.LogError($"[StaticShooter] {message}");
        }

        #endregion

        #region Editor Gizmos

#if UNITY_EDITOR
        private void OnDrawGizmosSelected()
        {
            // Draw shooting direction
            Vector3 shootDir = GetShootDirection();
            Gizmos.color = new Color(1, 0, 0, 0.7f);
            Vector3 directionLine = shootDir * 5f;
            Gizmos.DrawLine(transform.position, transform.position + directionLine);
            Gizmos.DrawWireSphere(transform.position + directionLine, 0.3f);

            // Draw configuration info
            if (configuration != null)
            {
                Gizmos.color = new Color(0, 1, 0, 0.3f);
                Gizmos.DrawWireSphere(transform.position, 1f);
            }
        }

        private void OnDrawGizmos()
        {
            // Always show a small indicator
            Gizmos.color = canShoot ? Color.green : Color.red;
            Gizmos.DrawWireCube(transform.position, Vector3.one * 0.5f);
        }
#endif

        #endregion
    }
}
