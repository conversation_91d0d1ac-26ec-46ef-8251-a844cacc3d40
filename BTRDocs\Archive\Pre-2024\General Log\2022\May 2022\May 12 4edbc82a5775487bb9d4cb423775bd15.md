# May 12

Unity Tips

[11 Things You (Probably) Didn't Know You Could Do In Unity](https://www.youtube.com/watch?v=mCKeSNdO_S0)

Start can be a coroutine (IEnumerator)

- can define time based execution of code this way!

Use this for a timer?

There is a backup-autosave hidden in unity when scene plays, worth looking into if I run into this!

Inspector → Add tab! Multiple Inspectors!

Choose properties on a component to get a floating window of it.

Hold Alt when hovering over variable in Debug mode to see name to use for referencing the variable!

Replace variant classes in debug mode to have all properties carry over!

[START MENU in Unity](https://www.youtube.com/watch?v=zc8ac_qUXQY&t=384s)

Bringing in other menus

Did this, attempting to ad Rewired Remap Controls menu - not working currently! Not sure why

Need to put UI controls in categories like so 

![Untitled](May%2012%204edbc82a5775487bb9d4cb423775bd15/Untitled.png)

Broke UI keyboard control when adding this - not sure why! Should not affect anything according to Rewired docs

[https://guavaman.com/projects/rewired/docs/Actions.html#:~:text=Action Categories are only for,affect input in any way](https://guavaman.com/projects/rewired/docs/Actions.html#:~:text=Action%20Categories%20are%20only%20for,affect%20input%20in%20any%20way).

Realized I should be using Control Remapper

[https://guavaman.com/projects/rewired/docs/ControlMapper.html](https://guavaman.com/projects/rewired/docs/ControlMapper.html)

It’s working!

Making a Default Theme Variant for nice menu in control remapping

Need to disable Pause button when deeper in menus or figure out an alternative

Need to clean up my Rewired file, it seems like it has errors and repeat listings accoridng to Control Remapper - possibly start it over?

UI Changes to make

Reorder these to this

![Untitled](May%2012%204edbc82a5775487bb9d4cb423775bd15/Untitled%201.png)

Input Grid 

![Untitled](May%2012%204edbc82a5775487bb9d4cb423775bd15/Untitled%202.png)

Added options menu features like Graphics quality and full screen mode. 

Wrote up a review of mid-ish point of month! Looking at best things to tackle