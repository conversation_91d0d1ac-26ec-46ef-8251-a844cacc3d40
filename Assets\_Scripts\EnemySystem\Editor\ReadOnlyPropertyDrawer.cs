using UnityEngine;
using UnityEditor;
using BTR.EnemySystem;

namespace BTR.Editor
{
    /// <summary>
    /// Property drawer for ReadOnlyAttribute that displays fields as disabled in the inspector.
    /// </summary>
    [CustomPropertyDrawer(typeof(ReadOnlyAttribute))]
    public class ReadOnlyPropertyDrawer : PropertyDrawer
    {
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            // Store the original GUI enabled state
            bool wasEnabled = GUI.enabled;

            // Disable the GUI for this property
            GUI.enabled = false;

            // Draw the property field as disabled
            EditorGUI.PropertyField(position, property, label, true);

            // Restore the original GUI enabled state
            GUI.enabled = wasEnabled;
        }

        public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
        {
            // Return the default height for the property
            return EditorGUI.GetPropertyHeight(property, label, true);
        }
    }
}
