# March 14

Was very tired yesterday! Very little done. Added custom debugger to game, SRDebugger. Seems cool! 

Thinking about level design. Maybe more distinct variations among rings would liven up each wave.

Variation on enemy per wave as well? 

Updated Vcam2 so it looks a bit better

May need to disable some Post Processing effects on during transitions - like motion blur

Added this is camera switch feature, motion blue should be disabled and re enabled on next wave

This is working! May just want to lower motion blur instead of disable - not sure yet!

Cant seem to figure out rotation issues with Shooting/Reticle. 

Not a game breaking issue but annoying!

Adding video gameplay recorder to builds for bug tracking?

Haven’t found a good way to do this. May not be that important?

Looking at adjusting movement of bullets so they’re properly affected by time changes.

Seems like they aren’t by slow time, but they are by reverse time. 

**Trying an ‘Area Clock’ to slow bullets around the player**

Works! For 0. But also doesn’t work for slow. Revealing the issues with bullet movement implementation. Looking at fixing that

Can’t seem to figure out why it’s not working for slowing down the projectiles. 

Need to revisit this!

Could implement destruction possibly!

[Time Manipulation in DestroyIt with Chronos](https://www.youtube.com/watch?v=B6hk1dNeY50)

Interesting resource that may help me solidify my game!

[Practical Tools for Empowering the Relationship between Theme & Mechanics](https://polarisgamedesign.com/2022/practical-tools-for-empowering-the-relationship-between-theme-mechanics/)

Tone as a means to bridge the communication and conceptual gap between theme and mechanic

> **Now, let’s look at the other side. You have a great mechanic, say, a character that can create a mirror of himself in order to complete the various puzzles in the game. This is fun to play with, and you’ve created a ton of fun puzzles, but the game sort of just feels like a toy, with no underlying meaning or way of really connecting with the player. How do you find a theme to map to this mechanic? Utilize the Tone Bridge to find your way from mechanic to theme. The mirroring mechanic produces lots of tones: Duality, the need for precision, a constant companion, and more, depending on what you see. Putting that mechanic into a game with increasingly-complex puzzles also creates some tones, like an increasing difficulty, a gradual slope up to a natural climax. You can take these tones and see what themes fall out of them for you. Duality, and a constant companion: Maybe a theme of friendship, or perhaps one of rivalry? Increasing difficulty: Perhaps the game takes place within a mountain, traversing symmetrical caverns with your clone or friend, ending with a triumphant crest at the top, all signifying a struggle through adversity, maybe even with yourself? (If this sounds familiar, you’ve probably played Celeste.) Taking each tone, you can define what each means to you thematically, until you have a complete set of thematic ideas which you can then weave into your game’s overall theme. This game’s theme could well be, “You must learn to be your own friend before you can truly conquer your internal demons and break through to an enlightened life.” Or something completely different! Again, the Tone Bridge can possibly reveal different themes and mechanics depending on the person using it, and what they interpret as they go through the exercise.**
> 

Mechanics → Tone → Themes

> **Shooting mechanics are frequently assimilated with more action tones, which narrows down some themes it can relate to.**
> 

**Mechanics**

Dodgeball-like Shooting

- Call and response

Move to the music / affect the music

4 quadrant turning

Lock on to enemies

Lock on the Projectiles

You can rewind time 

You can pause time

**Tone**

Fast / Frantic

Flow state like playing music

In the moment, like a dance club

DJ?

Abstract 

Futuristic

Meditative

Hypnotic

Time can be sped up as well? 

Bullet absorb mode - change music to something more chill when this happens? 

Instead of a glitch

What if enemies can control time as well? 

They can be unaffected by the time rewind

Testing half speed with this… not working! Need to look into how this works again

Can pause bullets briefly

ChatGPT suggestions

- exploration of this digital world, as players uncover its secrets and discover the true nature of the cybernetic rhythms that power it.
- Combo Chains: Players can be awarded extra points for hitting a series of notes in a row. The more notes they hit in a row, the higher the combo chain becomes.
- Power-Ups: Power-ups can be used to help the player hit more notes or gain extra points. For example, a power-up could slow down the song briefly, giving the player more time to hit the notes.

No luck with Gravitational Waves Spacetime, need to figure out how to scale the mesh up larger

Or use a different mesh?

Combine these things for interesting visual effects - reference Solar Ash possibly