using UnityEngine;
using UnityEditor;
using BTR.EnemySystem;
using BTR;

namespace BTR.Editor
{
    /// <summary>
    /// Custom inspector for ProjectileCombatStrategy that shows configuration status
    /// and makes configuration-driven values appear read-only.
    /// </summary>
    [CustomEditor(typeof(ProjectileCombatStrategy))]
    public class ProjectileCombatStrategyEditor : UnityEditor.Editor
    {
        private SerializedProperty? configurationProp;
        private SerializedProperty? projectileOriginsProp;
        private SerializedProperty? projectileSpeedProp;
        private SerializedProperty? projectileDamageProp;
        private SerializedProperty? projectileLifetimeProp;
        private SerializedProperty? projectileScaleProp;
        private SerializedProperty? projectilesPerAttackProp;
        private SerializedProperty? spreadAngleProp;
        private SerializedProperty? useHomingProp;
        private SerializedProperty? attackSoundEventProp;

        private void OnEnable()
        {
            // Find all serialized properties
            configurationProp = serializedObject.FindProperty("configuration");
            projectileOriginsProp = serializedObject.FindProperty("projectileOrigins");
            projectileSpeedProp = serializedObject.FindProperty("projectileSpeed");
            projectileDamageProp = serializedObject.FindProperty("projectileDamage");
            projectileLifetimeProp = serializedObject.FindProperty("projectileLifetime");
            projectileScaleProp = serializedObject.FindProperty("projectileScale");
            projectilesPerAttackProp = serializedObject.FindProperty("projectilesPerAttack");
            spreadAngleProp = serializedObject.FindProperty("spreadAngle");
            useHomingProp = serializedObject.FindProperty("useHoming");
            attackSoundEventProp = serializedObject.FindProperty("attackSoundEvent");
        }

        public override void OnInspectorGUI()
        {
            serializedObject.Update();

            // Get the target component
            ProjectileCombatStrategy strategy = (ProjectileCombatStrategy)target;
            EnemyConfiguration? config = configurationProp?.objectReferenceValue as EnemyConfiguration;
            bool hasConfiguration = config != null;

            // Header
            EditorGUILayout.Space(5);
            EditorGUILayout.LabelField("Projectile Combat Strategy", EditorStyles.boldLabel);

            // Configuration status info box
            if (hasConfiguration && config != null)
            {
                EditorGUILayout.HelpBox($"Configuration Active: {config.name}\nValues marked as read-only are controlled by the configuration file.", MessageType.Info);
            }
            else
            {
                EditorGUILayout.HelpBox("No configuration assigned. All values are editable.", MessageType.Warning);
            }

            EditorGUILayout.Space(5);

            // Projectile Settings Header
            EditorGUILayout.LabelField("Projectile Settings", EditorStyles.boldLabel);

            // Always editable fields
            if (projectileOriginsProp != null)
                EditorGUILayout.PropertyField(projectileOriginsProp);

            // Configuration-driven fields (read-only when config is present)
            GUI.enabled = !hasConfiguration;
            if (projectileSpeedProp != null)
                EditorGUILayout.PropertyField(projectileSpeedProp);
            if (hasConfiguration && config != null)
            {
                EditorGUILayout.LabelField($"  → Config Value: {config.projectileSpeed}", EditorStyles.miniLabel);
            }

            if (projectileDamageProp != null)
                EditorGUILayout.PropertyField(projectileDamageProp);
            if (hasConfiguration && config != null)
            {
                EditorGUILayout.LabelField($"  → Config Value: {config.projectileDamage}", EditorStyles.miniLabel);
            }

            if (projectileLifetimeProp != null)
                EditorGUILayout.PropertyField(projectileLifetimeProp);
            if (hasConfiguration && config != null)
            {
                EditorGUILayout.LabelField($"  → Config Value: {config.projectileLifetime}", EditorStyles.miniLabel);
            }

            if (projectileScaleProp != null)
                EditorGUILayout.PropertyField(projectileScaleProp);
            if (hasConfiguration && config != null)
            {
                EditorGUILayout.LabelField($"  → Config Value: {config.projectileScale}", EditorStyles.miniLabel);
            }

            if (useHomingProp != null)
                EditorGUILayout.PropertyField(useHomingProp);
            if (hasConfiguration && config != null)
            {
                EditorGUILayout.LabelField($"  → Config Value: {config.enableHoming}", EditorStyles.miniLabel);
            }

            GUI.enabled = true; // Re-enable for remaining fields

            // Always editable fields
            if (projectilesPerAttackProp != null)
                EditorGUILayout.PropertyField(projectilesPerAttackProp);
            if (spreadAngleProp != null)
                EditorGUILayout.PropertyField(spreadAngleProp);

            EditorGUILayout.Space(5);

            // Configuration Header
            EditorGUILayout.LabelField("Configuration", EditorStyles.boldLabel);
            if (configurationProp != null)
                EditorGUILayout.PropertyField(configurationProp);

            EditorGUILayout.Space(5);

            // Audio Settings Header
            EditorGUILayout.LabelField("Audio Settings", EditorStyles.boldLabel);
            if (attackSoundEventProp != null)
                EditorGUILayout.PropertyField(attackSoundEventProp);

            // Debug section in play mode
            if (Application.isPlaying)
            {
                EditorGUILayout.Space(10);
                EditorGUILayout.LabelField("Debug Info (Play Mode)", EditorStyles.boldLabel);

                GUI.enabled = false;
                EditorGUILayout.IntField("Active Projectiles", strategy.ActiveProjectileCount);
                EditorGUILayout.Toggle("Homing Enabled", strategy.IsHomingEnabled);
                EditorGUILayout.ObjectField("Current Configuration", strategy.CurrentConfiguration, typeof(EnemyConfiguration), false);
                GUI.enabled = true;
            }

            serializedObject.ApplyModifiedProperties();
        }
    }
}
