Performance & Optimization
April 10th

Optimize?

April 20

Upgrading to newest Unity 2019 LTS to fix TextMeshPro error - see if it helps anything else

May 26

Learned a bit about Unity Profiler and Deep Profiling

Suggests to me that I don't have much to worry about currently! Not seeing any big performance problems there

July 19

Early level is not performant - Greybox 12

Need a better building → Polyfew → TessGen pipeline

Need to use more performant shaders that can be batched

Look at mobile transparency shaders?

THING TO TRY - Polyfew Combine Meshes for level layouts - only when finalized?

July 21

Optimized buildings in Blender - much better!

Game running much faster - no crazy draw calls

Need to try using ghost shader now too

construct more of level, see what I can do

Can build a few TessGen tiles before performance tanks

Can BatchFew the TessGen tiles - bit of placement required after

HUGE decrease in batch calls but performance isn't better?

MESH takes up too much memory it seems

How to reduce? Look into this. Can blender do this?

July 23

Keep seeing that number of verts and triangles could be the issue for batching. Trying severly reduced models to see how that fairs

July 26

Mesh Combine for scene optimazation

Can use Occlusion culling when full scene is decided upon

November 6

Upgraded to newest 2020 LTS

Had to remove <PERSON><PERSON> <PERSON> / <PERSON><PERSON> Baker LOD

Jan 19

Figure out what is taking so long to get to play mode

Jan 20

Installed and using Mesh Baker instead of Mesh Combine

Insight note - Baking the whole mesh may be harder on the system. Baking quadrants looks like it might be better, because then some of them will be culled. Sound like culling will not occur if all baked into one mesh

Mesh Baker Grouper Video

Trying out Ultimate LOD System while downloading Automatic LOD

Jan 21

Trying many different optimization tools

AutoLOD for all the buildings

Using GPU Instancer with Prefabs seems to help performance - not as much as I thought!