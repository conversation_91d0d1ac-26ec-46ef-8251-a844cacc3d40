
## April 2022

#### April 4

*   Opened the project today to make some adjustment to camera, moves around a bit with cursor movement
*   Camera follows Shooting Reticle rather than Player now
*   Can adjust LocalMove function of Player Movement to try and get the player’s position to move as well, but needs more work on the Clamping

---

#### April 5

*   Note - Need to get wave spawner working OR need better wave spawner
    *   [https://assetstore.unity.com/packages/tools/spawn-ity-58832](https://assetstore.unity.com/packages/tools/spawn-ity-58832)

---

#### April 11

*   Is movement core or is it a method of getting from one puzzle to the next?

---

#### April 18

*   What do I currently have?
    *   Lock on system - can shoot enemies
    *   Basic enemy AI for shooting me and running around the playing field
    *   My lock on system - Can work overhead / first person / third person
    *   Narrative Idea - You are an AI auto protection system guarding a being transmitted through earth
    *   Think AI protection in Alien, Prometheus, etc.
    *   Make camera lean in opposite direction as we aim?
*   Need wide open spaces to see bullets flying at me currently
*   But we want fast movement / intense action, quick decisions
*   How to rectify this?
*   Think Panzer Dragoon
*   Think After Burner - First one is so FAST!!!

    Tilting Camera from After Burner!
*   Slight tilt, nothing too major

    Make the grab and launch a quicker action - HOW
*   Current slight slower pace work for some types of music
*   Do faster for other style of song
*   FAST enemies - die quickly
*   Approach the player more directly, Should not move between quadrants too much?
*   IF there’s lots of movement between quadrants - don’t shoot as much!
    *   How to do this with Behavior Tree?
*   Slower thoughtful enemies - move around the player, take a few hits
*   WAVES of projectiles around the player to grab and shoot at enemies in these cases
*   How does a level like this function??
    *   [https://www.youtube.com/watch?v=PjZLIiupIsQ&t=1559s](https://www.youtube.com/watch?v=PjZLIiupIsQ&t=1559s)
*   Collecting lots of bullets, pushed to the limits
*   Enemy Type - CANNOT shoot but do not want to touch,
*   Will approach you and self destruct
*   Shoot down before it does!!
*   Collect bullets up and take these guys out
*   Maybe work with opposite colours??
*   Ways in which a track will progress / change
    *   Death of an enemy Waves
    *   Pass a barrier / object
*   Also have unlockable bullets - require close proximity blast / time glitch

---

## Feb 3
*   IDEA: Sensor toolkit on bullets so if they are in range of target they auto target?

## March 4
*   Added spatializaiton to sound
*   Different enemies could shoot same projectile types, and vice versa
*   What is the need of this when you have a map? Level idea - DISABLED map
*   Need to consult FMOD docs for create instance abiltiies, so that i can stop it when object dies
---

## March 8

*   What does this add to the gameplay?
*   Should I be able to shoot these or have to shield from them?
## March 9
*   Tried importing Space Combat Kit but seemed like it would introduce project errors - try another time!

## May 3

*   What is defense for unlaunchable Projectile? Shield? Blast radius?
*   TRANSITION SECTION IDEA
*   STATES OF PLAYER - not locking - locking - holidng - firing
*   Transition to new sections with targets taken out or something else?
*   Every wave? No, should be able to set how many waves it takes before next section

## May 4

*   JSON for track transition stuff? Wave related things -
*   Transition effects -

*   Try some wave patterns in WaveSpawner
*   BT is lock on to many bullets - fire at something - more possible reactions
*   Think Olli Olli world? Is quickly restarting a good thing to try?

## May 5

*   What are the Projectile’s states?
    *   ProjectileLaunchedByEnemy
    *   ProjectileLockedByPlayer
    *   ProjectileLaunchedByPlayer
    *   ABADONDED STATE MACHINES

*   What are the possibilties for trainsitioning scenery ?

## May 6
*   Try buildings or other scenery for the current level, see how taht performs / enemy avoidance as well

## May 7

*   How can others help?
*   Should I lock on to other things besides bullets? environment? Anything?
*    What are mechanics in making music? How does one interact in a performance and also in composition?
* Added stamina for LockOn so that it can’t be held for too long!

## May 10

*   Should I integrate the radar target tracking UI stuff??? Look into it

## May 12

*   Thinking about movement in Boomerang X - is this something to take influence from? A game mode?

## May 16

*   *   Star fox on rail VS all range mode - would alternate movement modes be good?
 * Need to think - what am i making? What is the context?
*   **Think Rez**
*   The stages represent the evolution of human civilization,
*   The bosses represent the evolution of life on earth,
*   Your avatar represents stages of "enlightenment" as you level up.
*   What does the world look like?
* Digital corruption - buildings destroyed - based around infrastructure
  *    Japan loves wires and lightpoles and such - very aesthetic!!
   *  What if you’re excavating digital memories?
*   What is your game about?
*   Rail shooter games throguhout the ages
*   What are the scenery / objects I have access to?
*   The Design Secrets Behind Star Fox 64]

## May 17
 *   Changing Cubes that trigger sound to gates - visually - looks better
  * IDEA: Representing the music through background color changes

*   Putting things in a working state for new area
 * Need to aRESET of reticle and camera position on transition phase

## May 20-22

* Try Level without moving navmesh - what does that level look like?

## May 23

*   When projectile hits PlayerRadius it should reset and keep going forward now
 *  What am I doing with OnTargetDestroy Event on Projectiel Target (TargetCube) ???
    *   Don’t recall what I was going for here

## May 25
*   Levels! Scene changes! refine
**THEMES**

*SHOOTING**

*   If you’re shooting, you can’t lock on to more bullets can you? What happens?
  *   Do I want to keep this function? How to balance / expand

## May 26
AI Tricks
"What’s my story? What’s my game about?"

## June 3

*   What if you could lock on to other things besides bullets? environment? Anything?
*   How to NOT be that? or Evolve this??
---

## June 6

*   [Puzzle Ideas](../../../../Old%20Rez-Like%20Docs%206ac117418ee14c468f301157e3343721/Puzzle%20Ideas%2025a0685d0da94720b2f6fc45bbc3da6b.md)
*   [Level Design](../../../../Old%20Rez-Like%20Docs%206ac117418ee14c468f301157e3343721/Level%20Design%2040b5bce85a6b406baf4d1e08b9d01c7f.md)
*   [Thoughts on Gaming](../../../../Old%20Rez-Like%20Docs%206ac117418ee14c468f301157e3343721/Thoughts%20on%20Gaming%2047b946bda433443aa3e7400c277e0c76.md)
*   [Design Document](../../../../Old%20Rez-Like%20Docs%206ac117418ee14c468f301157e3343721/Design%20Document%2014aa543c3a9d4cb19f7169afebf8a169.md)
*   [Core Design](../../../../Old%20Rez-Like%20Docs%206ac117418ee14c468f301157e3343721/Design%20Document%2014aa543c3a9d4cb19f7169afebf8a169/Core%20Design%201bb119e9b6aa4cfdbbd87e661dfa3baf.md)
*   [Story / Concept](../../../../Old%20Rez-Like%20Docs%206ac117418ee14c468f301157e3343721/Story%20Concept%2091b21355c78d472b96d314b94c9b3646.md)
*   [Ideas](../../../../Old%20Rez-Like%20Docs%206ac117418ee14c468f301157e3343721/Ideas%206743893e6df5439ca8c9b0b5f7e0f5d4.md)
*   [Vision / Systems](../../../../Old%20Rez-Like%20Docs%206ac117418ee14c468f301157e3343721/Vision%20Systems%200d6ec68a6d5943929626c73725b7be65.md)

A painting conveys what it’s like to experience the subject as an image; a game conveys what it’s like to experience the subject as a system of rules. The player of games doesn’t just play within the system of rules, Anthropy explains, she plays with the rules. As she plays, she continually assesses the state of the system, looks for opportunities to take advantage, identifies and implements the best strategy, modifies or glitch the rules to improve the experience. - Anna Anthropy

## June 7

    [https://www.reddit.com/r/Unity3D/comments/ml4loa/having_some_fun_with_my_nearly_complete/](https://www.reddit.com/r/Unity3D/comments/ml4loa/having_some_fun_with_my_nearly_complete/)

*   What is a game?

A painting conveys what it’s like to experience the subject as an image; a game conveys what it’s like to experience the subject as a system of rules. The player of games doesn’t just play within the system of rules, Anthropy explains, she plays with the rules. As she plays, she continually assesses the state of the system, looks for opportunities to take advantage, identifies and implements the best strategy, modifies or glitch the rules to improve the experience. - Anna Anthropy
*   Core game
    *   Rez + Panzer Dragoon + Dodgeball?
        *   Waves of enemies + Different areas per wave + different musical loops
        *   Movement - Forced forward + look around
*   Beat Blast
    *   Sequencer decides when you can shoot - adjust when collecting items
    *   Movement / dodging more critical when you cant decide when to shoot?
*   Ideating
    *   Movement possibilities
        *   Thumper rhythmic action?
        *   Avoid objects?
*   What are mechanics in making music? How does one interact in a performance and also in composition?

Added stamina for LockOn so that it can’t be held for too long!
* Automatic fire when running out placed on OnMusicalLock
  *Seem better than OnLock logically
*Reverse time to deal with overhwleming bullets - implement proper musical chnage with this

## June 13

*   Boomerang X does not have crazy AI - reference this !
 Two approaches come to mind for enemy out of sight issues
        *   Make AI only go to certain locations in the gameplay plane
        *   Widen the perspective / allow horizontal movement to see corners better
    *   Reticle now looking at Aim Wall to free up connection with Camera
    *   Increased Repeat Attack Delay on Diamonds to 2 for less frantic bullet hell!
        *   For all bullets that are not locked - Death

## June 16

*   "Today, Lain’s story resonates more so as an allegory about the perils of forging one’s identity—an alternative identity, however false, misguided, perverse, delusional—using the internet”
*   “Rutger Hauer was the antagonist to Decker's protagonist but the villain was the world/Tyrell corporation."

*   Consider the slower pace of Pokemon Snap - and how it features a puzzle-ish element added to it

*   I frames when turning?
    *   Th iwaag a eauhsbet shatev l RUNNINGOum n fivilize,ion,
        *   Thd btsses repabs steuhe lvolutso of lifeoin carre, life
        *   Your avatar ripnesgcts staoesllf "enlcthtehaent"lts you level up.e
        *   Whtdoe he woldlekd ake?
        *   What do I do that causes Breaking sense of wonder?

## June 22

*   Graze Counter GM Demo - Good easy-ish bullet hell game, set some attack patterns and attributes before you start and then its general bullet hell mode. Would play more!
    * Has a cool start to they story worth thinking on

A malfunction in the EDEN VR system puts the 100 inhabitants at risk, you’re going into take a look and try to fix it / save them
*Nex Machina - good game! Bullet hell thats not too hard and moves quick
  *You save people in each stage while managing to evade and destroy enemies. Maybe some type of save people / collect items mechanic would work in my game?
*Anger Foot - fun straightforward melee fps combat. Simple mechanic with fun aesthetic , refined.
* Fashion police squad - good gimmicky shooter, not a lot to the gameplay but entertaining

## June 28

*   The solution is a statemachine - UGH it’s unavoidable!
Need to get my backup system going again!

*   For all bullets that are not locked - Death
  Does command patterns help?
*   What is defense for unlaunchable Projectile? Shield? Blast radius?

Limiting Creativity
*   Limitations to stoke creativity - remember a game is never done, only released
*   Fog in silent hill as limitation made creativity
*   What am I even making anymore????

-Can I add boomerang x teleport mechanic? Think about this? Surfboard ? Teleport away from it but need to return back / quick button for returning back?
*   How does it integrate with music?

## June 29

Does this break anything? Seems ok!

Now need to make sure locking and shooting work fine
* storyboard the game

### July 11

*   Purpose as rhythm? Is there a rhythmic aspect i should try locking in to this game? a wind up start? repeatedly pressing some button?
*   manipulate time more, as happens in the video ball trailer?

### July 12

*   Turn off locking in transitions - need to jump into next wave without locking music
*   manipulate time more, as happens in the video ball trailer?

## September 13
Add a time slow down?

Two colours like ikaruga? Polarity mechanic

log on through a terminal

Like superhot

## September 17

Sprinkle tutorial elements throughout ‘playing the real game’
*   deliver tutorial in chunks

## September 22

*   What games do I see existing in a similar camp?

Hyper Demon

## October 13

*   High Level - what does the game need?

List our requirements
* Mechanics
 *   Foundation exists for this

## November 24
*   I think this slows down the game as well
"If you’re shooting, you can’t lock on to more bullets can you? What happens?
## November 28

" *   [Mechanics](../../../../Old%20Rez-Like%20Docs%206ac117418ee14c468f301157e3343721/Mechanics%204f1c116e937e4517bb7af82809b3243a.md)

### December 1

*   if homing is true and launching is true
Will look at Projectile Target - no RB constraints - local forward is being set to player rayspawn forward, rb velocity

How do I want to handle avoiding getting hit? Maybe have bullets aim for center of screen always - and then being at the edges allows you to escape getting hit" - how will the target move