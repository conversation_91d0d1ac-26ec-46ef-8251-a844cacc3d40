%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4881f9a2c4d568047b316028d20a8dca, type: 3}
  m_Name: Ouroboros - Base Baking Set
  m_EditorClassIdentifier: 
  singleSceneMode: 0
  dialogNoProbeVolumeInSetShown: 1
  settings:
    m_Version: 1
    dilationSettings:
      enableDilation: 0
      dilationDistance: 1
      dilationValidityThreshold: 0.25
      dilationIterations: 1
      squaredDistWeighting: 1
    virtualOffsetSettings:
      useVirtualOffset: 1
      validityThreshold: 0.25
      outOfGeoOffset: 0.01
      searchMultiplier: 0.2
      rayOriginBias: -0.001
      collisionMask:
        serializedVersion: 2
        m_Bits: 4294967291
  m_SceneGUIDs:
  - 2925ddd508b014d4dab9e72ac5f8f881
  - 5f3e9991878c3bc42a4b721285e7076f
  obsoleteScenesToNotBake: []
  m_LightingScenarios:
  - Default
  cellDescs:
    m_Keys: 
    m_Values: []
  m_SerializedPerSceneCellList:
  - sceneGUID: 2925ddd508b014d4dab9e72ac5f8f881
    cellList: 
  cellSharedDataAsset:
    m_AssetGUID: 
    m_StreamableAssetPath: 
    m_ElementSize: 0
    m_StreamableCellDescs:
      m_Keys: 
      m_Values: []
    m_Asset: {fileID: 0}
  scenarios:
    m_Keys: []
    m_Values: []
  cellBricksDataAsset:
    m_AssetGUID: 
    m_StreamableAssetPath: 
    m_ElementSize: 0
    m_StreamableCellDescs:
      m_Keys: 
      m_Values: []
    m_Asset: {fileID: 0}
  cellSupportDataAsset:
    m_AssetGUID: 
    m_StreamableAssetPath: 
    m_ElementSize: 0
    m_StreamableCellDescs:
      m_Keys: 
      m_Values: []
    m_Asset: {fileID: 0}
  chunkSizeInBricks: 128
  maxCellPosition: {x: 0, y: 0, z: 0}
  minCellPosition: {x: 0, y: 0, z: 0}
  globalBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  bakedSimplificationLevels: 3
  bakedMinDistanceBetweenProbes: 1
  bakedProbeOcclusion: 0
  bakedSkyOcclusionValue: 0
  bakedSkyShadingDirectionValue: 0
  bakedProbeOffset: {x: 0, y: 0, z: 0}
  bakedMaskCount: 1
  bakedLayerMasks:
    x: 0
    y: 0
    z: 0
    w: 0
  maxSHChunkCount: -1
  L0ChunkSize: 0
  L1ChunkSize: 0
  L2TextureChunkSize: 0
  ProbeOcclusionChunkSize: 0
  sharedValidityMaskChunkSize: 8192
  sharedSkyOcclusionL0L1ChunkSize: 0
  sharedSkyShadingDirectionIndicesChunkSize: 0
  sharedDataChunkSize: 0
  supportPositionChunkSize: 0
  supportValidityChunkSize: 0
  supportTouchupChunkSize: 0
  supportLayerMaskChunkSize: 0
  supportOffsetsChunkSize: 0
  supportDataChunkSize: 0
  lightingScenario: Default
  version: 2
  freezePlacement: 0
  probeOffset: {x: 0, y: 0, z: 0}
  simplificationLevels: 3
  minDistanceBetweenProbes: 1
  renderersLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  minRendererVolumeSize: 0.1
  skyOcclusion: 0
  skyOcclusionBakingSamples: 2048
  skyOcclusionBakingBounces: 2
  skyOcclusionAverageAlbedo: 0.6
  skyOcclusionBackFaceCulling: 0
  skyOcclusionShadingDirection: 0
  useRenderingLayers: 0
  renderingLayerMasks: []
  m_SceneBakeData:
    m_Keys:
    - 2925ddd508b014d4dab9e72ac5f8f881
    - 5f3e9991878c3bc42a4b721285e7076f
    m_Values:
    - hasProbeVolume: 0
      bakeScene: 1
      bounds:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 81, y: 81, z: 81}
    - hasProbeVolume: 1
      bakeScene: 1
      bounds:
        m_Center: {x: -324, y: 81, z: -243}
        m_Extent: {x: 162, y: 81, z: 162}
