# Technical Issues
  
## January 2024

### January 5th, 2024
- Having issues importing rigged characters into Blender. 
    - Need to see if Importing with 3DS Max, then exporting to Blender, may work. 
    - Also will try with <PERSON>.

### January 10th, 2024
- Compiling shaders causes build to take over an hour or more. 
    - Tried preloading shader variants. 
    - Need to do more research if that doesn't fix the problem.
- Error happening on transition - player mesh had disappeared - maybe dodge related?

### January 17th, 2024
- Reticle control jerky issues. 
- Camera rotation up/down not working properly.
- Build times too long. 
    - Need to look into Assembly Definitions.

### January 22nd, 2024
- **Lock-on Indicators:** Fixing lock-on indicators on shooter - currently can't see them. 
    - Need to look at new material or different rendering methods.
    - Maybe make slightly emissive?

### January 23rd, 2024
- **Reticle Issue:** May have broken reticle partially while adjusting camera.

### January 24th, 2024
- **Editor Controls Jank:** Controls janky in editor but okay on device / build. 

### January 29th, 2024
- **Enemy Material Shader Issue:** Went back to regular Lit shader because of better light properties when using Ultimate Lit Shader, need to look into why.

### January 30th, 2024
- **Bullet Scaling Issue:** Scaling issues of bullets, wildly off depending on what object shoots them. 
- **FindObjectOfType Issue:** Using FindObjectOfType in the class, this could be a problem. 
- **Projectile Pooling Issue:** Projectile pooling issue, finding tag with player when player is currently disabled.

## February 2024

### Feb 1st
- **Blender Tool:** Quad Remesher add-on for Blender. Consider Quadriflow Remesh as well.
- **SDF VFX Reference:** "Unity VFX Graph: Use SDF to make model particle effects" YouTube video. Model for how I implemented SDF particle effects - May need as reference. (Link to YouTube video: [https://www.youtube.com/watch?v=FBP9k6W48vM&t=905s](https://www.youtube.com/watch?v=FBP9k6W48vM&t=905s))

### Feb 6th
- **Projectile Logic Bug:** Can lock on and shoot but enemy’s not dying - why? 
- **Bullet Gathering Bug:** Bullet gathering above player not working currently - not entirely sure why. 
- **Kinematic Bullets Error:** Getting errors related to the shooting and the bullets being kinematic.
- **Shooting Not Working:** Doesn’t seem like shooting is working though. Need to work through the whole process and understand what’s happening.

### Feb 8th
- **Reticle/Shooter Movement Bug:** Turning the player on and off fixes issues with the reticle/shooter movement. Investigate this!
- **Projectile Not Shooting from Reticle Bug:** Projectile not shooting from reticle - it's in front but not moving on button release. Investigate this!
- **Bullet Stuck to Reticle Bug:** Found a bullet stuck to reticle, noticed this is happening. Should be disabled, need to reassess how this is disabled so nothing gets stuck.
- **Eyeballs Stop Shooting Bug:** The eyeballs seem to stop shooting, need to find out why…. it's the same reason! many of the bullets are stuck at the reticle spot.
- **Projectiles Staying Locked Bug:** A bunch of projectiles are staying locked, therefore lifetime is not decreasing and they’re not being pooled. 
- **Projectiles Disappearing Bug:** Issue also explains why when I lock on some projectiles seem to disappear completely. I think they are going to the reticle spot.
- **Launchback Projectiles Not Holding Bug:** Launchback launches projectiles now, but it's not holding on to them, so that needs to be addressed.
- **Controls Bug:** Turning the player on and off fixes issues with the reticle/shooter movement. It's one of the scripts on the player object - maybe? Investigate this!

### Feb 11th
- **Bullets Not Shooting Properly Bug:** Bullets aren't shooting properly from player regardless of enemy locked or not - need to trace this and fix it up.

### Feb 13th
- **Player Rotation Bug:** Player Movement, or other script, seem to be causing the player object to rotate gradually, spinning around on the y axis. still unsure why.
- **Curvy Spline Gizmo Rotation Issue:** Curvy Spline asset update, hoping it fixes my issues with gizmos not showing up for the rotations.

### Feb 22nd
- **Player Rotation Long Term Solution TODO:** Implemented hacky LookAt solution so player game object stops rotating. Need a better long term solution.

### Feb 23rd
- **Leak Issues:** Need to investigate Leak issues. "Leak Detected : Persistent allocates 4 individual allocations. To find out more please enable 'Preferences > Jobs > Leak Detection Level > Enabled With Stack Trace' and reproduce the leak again."
- **Material Swap Not Working:** Working on Materials swap, not properly working currently. Leaving for now.

### Feb 24th
- **Snake Dolly Not Working:** Snake Dolly doesn’t really work with Curvy.
- **Infinite Track Tail/Head Attributes Issue:** Trying Infinite track applied to game. Issues with getting the tail / head attributes to work properly, but fixable I think!

### Feb 28th
- **Bone Renaming Issue:** Animation isn't applying to my model because of a naming problem! the bones are being renamed with ‘__’ instead of ‘_ ‘.

### Feb 29th
- **Animation Rotation Issue:** Rotation is off on Idle Open Mouth animation compared to the others. Scale of the object changes massively when play mode vs not. unsure why exactly. Seems like its issues with ROOT_ or above.

## March 2024

### March 4th
- **Enemy Lock-on Debugging:** Not working for lock on, need to take a look at why.
- **Curvy Generator Spikes:** Curvy Generator causing spikes - may not be great to use, try in isolation.

### March 7th
- **Enemy Layer Visibility Issue:** Enemy layer seems invisible or obscured? Need to look into this.
- **LockedStatus Reset on Hit Verification:** LockedStatus set to false on colliderhitcallback and enemybasicsetup when they get hit. This may break something, need to verify.
- **Infinite Snake Animation Scale/Positioning Issue:** Hit animations in Infinite Snake behaving weirdly due to scale and positioning issues. Need to fix at the root of it! See if original snake has this problem.
- **Dotween Shake on Hit Unnoticeable:** Dotween shake on hit not noticeable - emphasize or rethink this.

### March 9th
- **Snake Mesh Edits:** Still need to do mesh edits to make snake more unique. Need to bring over eyes and other aspects for infinite track section.

### March 10th
- **Snake Eyes Prefab Pooling Coroutine Issues:** Snake Eyes Prefab Pooling still has Coroutine issues.
- **Infinite Track Tail Extension Issue:** Need to still figure out why I can extend the tail in the example scene, however in my scene it’s not working properly, I'm always at the end of it. It seems this becomes a problem over time - needs to be addressed.

### March 11th
- **Mobius Tube 6 Stuck Issue:** Mobius Tube 6 - also too big? Also getting stuck?

### March 15th
- **Crosshair Improvement with Claude Broke Stuff:** Using Claude to improve Crosshair.cs. Didn't work! broke stuff I think! or maybe it was good, hard to tell because other stuff broke. Likely will give this a second try once I better understand issue with locking projectiles.

### March 16th
- **Crosshair Improvement with Claude (Attempt 2) Issues:** Tired to use Claude on crosshair again and ran into issues AGAIN. maybe not worth it. Will try a more targeted approach.

### March 28th
- **Build Issue Persists:** Still bad in a build and unsure why.
- **BT Visual Effects Bug Comparison TODO:** Need to compare BT current to BT fix to see why visual effects are behaving weirdly. 
- **Post Processing Editor vs Build Visuals Issue:** Post Processing visuals different in editor vs build.

### April 1st
- **Player Movement Bopping/Rotating Issues:** Player character bopping and rotating issues. Rotating aimlessly, jittery / bopping.
- **Player Rotation Issue Still Debugging:** Player rotation issue with kinematic rigidbody not fully fixed, still debugging.

### April 5-6th
- **Twin Snakes Timeline Component Issue:** Issue with the twin snakes not finding their timeline components properly.

### April 8-9th
- **Twin Snakes Rotation Intersect Issue:** Have a successful rotation of twin snakes looking at player but they do intersect in weird ways.

### April 12-14th
- **Parry Shot Implementation Problems:** Having problems with implementing parry shot, needs rethinking and adjustment. Will come back to this.
- **Movement Sensor Script Issues:** Movement sensor script for twin snakes disabled due to causing problems. Will see if worthwhile in the future.
- **Twin Snake Boss Homing Missing:** Twin snake boss did not have homing enabled.
- **Code Cleanup Needed:** Need to clean up classes.
- **Projectile Accuracy at High Speeds:** Adjusting projectile logic for better accuracy at high speeds.
- **Player Getting Hit Issue:** Player appears to be getting hit unexpectedly.
- **Shoot Speed Effecting Homing Bug:** Shoot speed was effecting homing and non homing, now fixed.

### April 15th
- Adding koreographer to enemy twin snake boss does not work properly, requires further refinement

### April 24th
- lockandshoot - playerlocking on and player locking off events - Are they causing errors once ive edited them / commented out?
- slowTime variable not assigned in boss twinsnake scene- what should it be? THIS IS WHATS BREAKING SLOW TIME - FIND APPROPRIATE EFFECT
- Bullets getting stuck around player - not sure why - is this only in the Twin enemy snake level?
- Getting errors when i shoot (Look direction getting 0 or NaN values given, need to avoid those situations?)
- Bullet speeds too slow in some circumstances. maybe default is too slow?
- Bullets are getting stuck on kinematic, and arent able to move, that’s why i cant shoot properly at times. Not sure what triggers this, but also trips up FMOD as well
- Seems something like being stuck in locked by player state, some transition not working properly
- May have to do with updates made dureing twin boss bullet lock on decisions, to make them transparent and follow reticle. Test other scenes, see how this behaviour manifests there
- Is it the same problem that occurs in regular ouroboros scene?

### April 28th
- Beat Render missing features (Buto issues)
- Rebuild FMOD project (Do a build within FMOD software)
- Lighting needs to be generated again (Bakery)
- Regenrate A* navmeshes
- Errors likely stem from April 24th changes
- Replaced Enemy Twin Snake boss and Controller, now shooting not working properly? Unsure what’s broken this time around?
- I accidentally deleted Perc!!! Thats what broke it! Just a dumb mistake in the Koreographer interface
- Koreographeron Enemy Twin snake boss needs fixed

### April 5-6th
- Issue with the snakes not finding their time line components properly, need to company to wroking example and see whats wrong here

## May 2024

### May 1st, 2024

**Work!**

Basic implementation of Motion Extraction in Unity by Vel GD

Code brought over but not fully working - need to go through the setup - simple, somewhat mindless thing to do

Need to investigate Bloom settings by Occa - struggling to get ideal settings

Changed Projectile Death to properly accomodate imapct sound but also trying to resolve how they seem to hang out next to the player for extra time - collision for happening as quick as i want? some logic error? unsure

Using a new Debug message during projectile Death to try and figure out what’s happening

NEw debug message but also just not havign the projectile issue anymore? Not sure why. Maybe updating Death method fixed it? IF I close and reopen project, will this persist? Unsure

Wondering if Hot Reload not fully reloading domain could be cause for fixes not having immediate effect?

Replacing Projectile impact sound it’s TOO MUCH

Is the latency from desktop too much when refining audio issues? check on this. 

Moving sound to onTrigger of enemy shot - when it hits the player - to make it faster, possibly avoid false calls

Seems much better now! Need to improve the sound though

Need much nicer explosiosn sound and particle effect for when i get hit I think!

Occa for this? Addded VFX Red Sparks but not refined at all

Projectiles collisions broke again? hmmmmm whats going on? I didnt change anything beside particle explosions effect - is this only in editor? What causes the issue? Need to refine how the collision is called i bet  - investigate options 

Two things that could help 

- put projectiles on their own layer that do not interact with each other, to prevent 

- interpolate or extrapolate rigidbody, to prevent jittery movement
    - FOr fast environments it seems extrapolation is the preferred method
        - have enabled extrapolation!

SourceTree downloaded git LFS files before i could upload - this may cause errors

NOTE IF SO!

### May 2nd, 2024

May have improved things, im unsure. Seems like some Projectiles are still appearing on the Default layer? Not sure whats wrong there.

These type of errors seem to be the problem and are intefereing with order of operations

rigidbody.rotation assign attempt for 'StandardBullet - Ouroboros StateBased Emissive(Clone)' is not valid. Input rotation is { NaN, NaN, NaN, NaN }.

Starts with

```jsx
Assertion failed on expression: 'CompareApproximately(det, 1.0F, .005f)'
UnityEngine.Quaternion:LookRotation (UnityEngine.Vector3)
ProjectileManager:PredictAndRotateProjectile (ProjectileStateBased) (at Assets/_Scripts/ProjectileManager.cs:310)
ProjectileManager:Update () (at Assets/_Scripts/ProjectileManager.cs:255)
```

```jsx
Assertion failed on expression: 'fRoot >= Vector3f::epsilon'
UnityEngine.Quaternion:LookRotation (UnityEngine.Vector3)
ProjectileManager:PredictAndRotateProjectile (ProjectileStateBased) (at Assets/_Scripts/ProjectileManager.cs:310)
ProjectileManager:Update () (at Assets/_Scripts/ProjectileManager.cs:255)
```

then i get

```jsx
rigidbody.rotation assign attempt for 'StandardBullet - Ouroboros StateBased Emissive(Clone)' is not valid. Input rotation is { NaN, NaN, NaN, NaN }.
```

Added some logic to deal with this, may breka things, but hopnig for the best! easy to remove if needed. simulating an on trigger did not work.

Making player box collider bigger, see if that helps. It did not

It’s some major issue in the projectile logic. Getting lots of kinematic warnings but things seem to be working? Needs further investigation

### May 3-5th, 2024

Trying to debug this build graphics issue, have posted in a couple places and trying to use error logs to debug this

### May 7-9th, 2024

Still encountering Text Mesh Pro Issues, investigating. Seems like it’s not properply installed, strangely’

Fixed! It seems

### May 7-9th, 2024

Still encountering Text Mesh Pro Issues, investigating. Seems like it’s not properply installed, strangely’

Fixed! It seems

### May 10th, 2024

Trying to debug Y-axis rotation issues
 
Player Movement is playing some part? May need to disable what it does 
 
But it doesnt seem to be affected things… Seems any kind of ground fitting script, wether A* or actually Ground Fitter, always causes the issue.

### May 10th, 2024

Trying to debug Y-axis rotation issues
 
Player Movement is playing some part? May need to disable what it does 
 
But it doesnt seem to be affected things… Seems any kind of ground fitting script, wether A* or actually Ground Fitter, always causes the issue.

### May 11th, 2024

Wave delay before switch scene, may want to do it, but some technical things to solve if so. Need to re enable appropriate things, its a special transition. for example, player character. 
 
WaveCustomEventSorter is what handles a change to the next scene
 
Need to ensure on scene load that certain elements are active. maybe a scriptable object that defines this - may be more than one, since some scenes may have different requirements

### May 11th, 2024

Wave delay before switch scene, may want to do it, but some technical things to solve if so. Need to re enable appropriate things, its a special transition. for example, player character. 
 
WaveCustomEventSorter is what handles a change to the next scene
 
Need to ensure on scene load that certain elements are active. maybe a scriptable object that defines this - may be more than one, since some scenes may have different requirements

### May 14th, 2024

It appears bullets dont need their own local clocks so i’ve removed it, need to keep watching to make sure this doesnt break anything though, seems like it already has, so i put it back on but made it additive

Added this to update method of projectiles in attempt to get them to respond to time changes more effectively, but doesnt appear to be working - look more into the clock part

### May 14th, 2024

It appears bullets dont need their own local clocks so i’ve removed it, need to keep watching to make sure this doesnt break anything though, seems like it already has, so i put it back on but made it additive

Added this to update method of projectiles in attempt to get them to respond to time changes more effectively, but doesnt appear to be working - look more into the clock part