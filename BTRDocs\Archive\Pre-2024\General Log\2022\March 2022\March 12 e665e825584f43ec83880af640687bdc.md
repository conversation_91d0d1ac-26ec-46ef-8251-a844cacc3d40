# March 12

Looking over original ideas - compositing goals - what was intended?

[Ideas ](March%2012%20e665e825584f43ec83880af640687bdc/Ideas%206ba6388d395e4c1bbdb588443c610442.md)

Try Quibi Shaders for CHANNEL#21 Look 

Looking COOL but very dark? 

Trying out dithcing Ethereal pipeline - Quibli one instead

See in I can get Quibli shaders to look right

Issue with Fog so it’s disabled for now

Visually coming together a bit! Cannot get outline on Quibli shader to have transparency though, need to fix this

Adding **UI Target Tracker (For Radar Builders) to see if that’s useful for bullets or enemies - similar ideas as outline - may not work - need to test**

WORKS but doesnt get destroyed when hitting players - needs adjustment

follow tutorial [https://www.youtube.com/watch?v=dW_lRqITu7g&t=378s](https://www.youtube.com/watch?v=dW_lRqITu7g&t=378s)

Looking at Projectile Trails - not actually movement direction currently

Need to do a tutorial on making it to make it work

Starfox movement - bring this back! Look at original tutorial to achieve it

Fix WaveSpawner issues

Designate unwalkable area for enemy next to player

Look at Ideas sheet!

Recreate this - [https://www.youtube.com/watch?v=PjZLIiupIsQ&t=596s](https://www.youtube.com/watch?v=PjZLIiupIsQ&t=596s)

Mugs with Shaders in scene as well - master this shader! why no transparency?

Evangelion UI! - [https://www.youtube.com/watch?v=k0oQr7ZVtBQ](https://www.youtube.com/watch?v=k0oQr7ZVtBQ)

SSAO in URP - [https://www.youtube.com/watch?v=z7LwThH-Ayc](https://www.youtube.com/watch?v=z7LwThH-Ayc)

Try faster glithcing! like in CHANNEL video - maybe use effect like in CHANNEL video too

Base musical approach around this kind of energy