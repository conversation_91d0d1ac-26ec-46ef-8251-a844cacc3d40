---
title: Unity Optimization Tips
tags: [Unity, Performance, Optimization, Rendering, Scripting]
date: 2025-01-20
---

# [[Unity Optimization Tips]]

## [[Rendering Optimization]]

### [[Frame Debugger]]
- Use Window > Analysis > Frame Debugger
- Step through draw calls
- Identify rendering bottlenecks

### [[Sprite Atlas]]
```csharp
// Create Sprite Atlas
// Add sprites to Objects for Packing
// Set appropriate import settings
// Pack Preview
```

## [[Scripting Optimization]]

### [[Tick System]]
```csharp
public class Ticker : MonoBehaviour {
    public static float tickTime = 0.2f;
    private static float tickTimer;
    
    public delegate void TickAction();
    public static event TickAction OnTickAction;
    
    void Update() {
        tickTimer += Time.deltaTime;
        if (tickTimer >= tickTime) {
            tickTimer = 0;
            OnTickAction?.Invoke();
        }
    }
}
```

### [[Benchmark Testing]]
```csharp
Stopwatch sw = new Stopwatch();
sw.Start();
// Code to test
for (int i = 0; i < iterations; i++) {
    // Test code
}
sw.Stop();
Debug.Log(sw.ElapsedMilliseconds);
```

## [[Performance Tips]]

### [[Vector Operations]]
```csharp
// Option 1
float distance = Vector2.Distance(a, b);

// Option 2
float distance = (a - b).sqrMagnitude;
```

### [[Animation Optimization]]
```csharp
int attackTrigger = Animator.StringToHash("Attack");
animator.SetTrigger(attackTrigger);
```

### [[Material Properties]]
```csharp
int tintColorID = Shader.PropertyToID("_TintColor");
material.SetColor(tintColorID, color);
```

## [[Memory Optimization]]

### [[ScriptableObjects]]
```csharp
[CreateAssetMenu]
public class EnemyConfig : ScriptableObject {
    public float health;
    public float speed;
    public float damage;
}

// Usage
public class Enemy : MonoBehaviour {
    public EnemyConfig config;
}
```

## [[Best Practices]]
1. Profile before optimizing
2. Use appropriate tools (Frame Debugger, Profiler)
3. Balance readability and performance
4. Optimize both CPU and GPU usage
5. Test changes thoroughly
6. Use object pooling for frequent instantiation
7. Remove empty functions
8. Optimize import settings

## [[Additional Resources]]
- [[Unity Documentation: Optimization]]
- [[Rendering Best Practices]]
- [[Scripting Performance Guide]]
- [Unity Performance Optimization](https://docs.unity3d.com/Manual/OptimizingGraphicsPerformance.html)
- [Advanced Optimization Techniques](https://example.com/unity-optimization)