---
title: Additive Async Multi-Scene Loading in Unity
tags: [Unity, SceneManagement, AsyncProgramming]
date: 2025-01-20
---

# Additive Async Multi-Scene Loading in Unity

## Overview
This tutorial covers how to implement additive asynchronous scene loading in Unity, allowing for persistent scenes and dynamic loading of scene groups.

## Implementation Steps

### 1. Import Scene Reference Library
```csharp
// Import via Package Manager
// URL: [Scene Reference Library v4.0]
```

### 2. Define Scene Metadata
Create a `SceneData` class to store scene information:
```csharp
public class SceneData {
    public SceneReference sceneRef;
    public string Name => sceneRef.Name;
    public SceneType Type { get; set; }
}
```

### 3. Create Scene Group Manager
```csharp
public class SceneGroupManager {
    private List<AsyncOperation> operations;
    
    public float Progress => operations.Average(op => op.progress);
    public bool IsDone => operations.All(op => op.isDone);
    
    public async Task LoadScenes(SceneGroup group, IProgress<float> progress, bool reloadExisting = false) {
        // Implementation details...
    }
}
```

### 4. Implement Scene Loading
```csharp
public async Task LoadScenes(SceneGroup group, IProgress<float> progress, bool reloadExisting = false) {
    activeSceneGroup = group;
    
    // Unload existing scenes
    var remainingScenes = await UnloadScenes();
    
    // Load new scenes
    var loadOperations = new AsyncOperationGroup(group.Scenes.Count);
    
    foreach (var sceneData in group.Scenes) {
        if (!reloadExisting && IsSceneLoaded(sceneData)) continue;
        
        var operation = SceneManager.LoadSceneAsync(
            sceneData.sceneRef.Path, 
            LoadSceneMode.Additive
        );
        
        loadOperations.Add(operation);
        OnSceneLoadStarted?.Invoke(sceneData);
    }
    
    while (!loadOperations.IsDone) {
        progress?.Report(loadOperations.Progress);
        await Task.Delay(100);
    }
    
    // Set active scene
    var activeScene = group.Scenes.FirstOrDefault(s => s.Type == SceneType.Active);
    if (activeScene != null) {
        SceneManager.SetActiveScene(SceneManager.GetSceneByName(activeScene.Name));
    }
    
    OnSceneGroupLoaded?.Invoke(group);
}
```

### 5. Create Scene Loader MonoBehaviour
```csharp
public class SceneLoader : MonoBehaviour {
    [SerializeField] private Image loadingBar;
    [SerializeField] private float fillSpeed = 0.5f;
    [SerializeField] private SceneGroup[] sceneGroups;
    
    private SceneGroupManager manager;
    private float targetProgress;
    private bool isLoading;
    
    private void Start() {
        LoadSceneGroup(sceneGroups[0]);
    }
    
    private async void LoadSceneGroup(SceneGroup group) {
        isLoading = true;
        targetProgress = 1f;
        loadingBar.fillAmount = 0f;
        
        var progress = new LoadingProgress();
        progress.Progressed += (p) => targetProgress = Mathf.Max(p, targetProgress);
        
        await manager.LoadScenes(group, progress);
        
        isLoading = false;
    }
    
    private void Update() {
        if (!isLoading) return;
        
        float currentFill = loadingBar.fillAmount;
        float progressDiff = Mathf.Abs(targetProgress - currentFill);
        float fillAmount = currentFill + progressDiff * fillSpeed * Time.deltaTime;
        
        loadingBar.fillAmount = Mathf.Lerp(currentFill, targetProgress, fillAmount);
    }
}
```

## Key Features
- [[Additive Scene Loading]]
- [[Async Operations Management]]
- [[Progress Reporting]]
- [[Scene Group Organization]]
- [[Persistent Bootstrapper Scene]]

## Best Practices
1. Use SceneReference instead of string names
2. Implement proper error handling
3. Consider using [[Addressables]] for better asset management
4. Optimize scene loading with:
   - [[Object Pooling]]
   - [[Asset Bundling]]
   - [[Asynchronous Loading]]

## Example Scene Group Setup
```csharp
[CreateAssetMenu]
public class SceneGroup : ScriptableObject {
    public List<SceneData> Scenes;
}

public enum SceneType {
    Persistent,
    Gameplay,
    UI,
    Cinematic,
    Environment
}
```

## Performance Considerations
- Use `Resources.UnloadUnusedAssets()` carefully
- Implement proper scene unloading
- Monitor memory usage
- Optimize scene dependencies

## Additional Resources
- [[Unity Documentation: Scene Management]]
- [[Addressables System]]
- [[Async/Await Best Practices]]