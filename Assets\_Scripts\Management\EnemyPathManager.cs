using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using BTR;
using BTR.EnemySystem;
using ZLinq;

namespace BTR
{
    public class EnemyPathManager : MonoBehaviour
    {
        public static EnemyPathManager Instance { get; private set; }

        // Domain reload handling
        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.SubsystemRegistration)]
        private static void ResetStaticData()
        {
            // Only reset instance if it exists to avoid unnecessary operations
            if (Instance != null)
            {
                Instance = null;
            }
        }

        // Legacy enemy tracking
        private List<EnemyCore> trackedEnemies;

        // New entity tracking
        private List<IEntity> trackedEntities;

        [Header("Pathfinding Optimization")]
        [SerializeField] private float pathCacheTime = 0.3f;
        [SerializeField] private float pathCacheDistance;
        [SerializeField] private int maxPathRequestsPerFrame;
        [SerializeField] private float gridCellSize;
        [SerializeField] private float updateInterval;
        [SerializeField] private float significantMovementThreshold;

        // Spatial grid for quick neighbor lookups
        private Dictionary<Vector2Int, List<EnemyCore>> spatialGrid;
        private Dictionary<EnemyCore, Vector3> lastKnownPositions;
        private float nextUpdateTime;
        private int spatialGridUpdateIndex = 0;

        // Path caching system
        private class CachedPath
        {
            public Vector3 startPos;
            public Vector3 endPos;
            public List<Vector3> path;
            public float timestamp;
        }
        private Dictionary<EnemyCore, CachedPath> pathCache;
        private Queue<(float priority, EnemyCore enemy, IPathHandler handler)> priorityPathQueue;

        private const int ENEMIES_PER_SPATIAL_UPDATE = 20;
        private const int MAX_CACHED_PATHS = 100;
        private const float PATH_PRIORITY_DISTANCE_WEIGHT = 1.5f;

        [Header("Debug Visualization")]
        [SerializeField] private bool showDebugVisualization = true;
        [SerializeField] private Color gridColor;
        [SerializeField] private Color cachedPathColor;
        private Color requestQueueColor;

        private void Awake()
        {
            if (Instance != null && Instance != this)
            {
                Destroy(gameObject);
                return;
            }
            Instance = this;

            // Only create new collections if they don't already exist
            // Initialize the tracked enemies if it doesn't exist
            if (trackedEnemies == null) trackedEnemies = new List<EnemyCore>();
            else trackedEnemies.Clear();

            // Initialize the spatial grid if it doesn't exist
            if (spatialGrid == null) spatialGrid = new Dictionary<Vector2Int, List<EnemyCore>>();
            else spatialGrid.Clear();

            // Initialize the path cache if it doesn't exist
            if (pathCache == null) pathCache = new Dictionary<EnemyCore, CachedPath>();
            else pathCache.Clear();

            // Initialize the last known positions if it doesn't exist
            if (lastKnownPositions == null) lastKnownPositions = new Dictionary<EnemyCore, Vector3>();
            else lastKnownPositions.Clear();

            // Initialize the priority path queue if it doesn't exist
            if (priorityPathQueue == null)
            {
                priorityPathQueue = new Queue<(float priority, EnemyCore enemy, IPathHandler handler)>();
            }
            else
            {
                priorityPathQueue.Clear();
            }

            // Initialize the tracked entities if it doesn't exist
            if (trackedEntities == null)
            {
                trackedEntities = new List<IEntity>();
            }
            else
            {
                trackedEntities.Clear();
            }

            // Initialize the request queue color if it doesn't exist
            if (requestQueueColor == default(Color))
            {
                requestQueueColor = Color.red;
            }

            // Initialize the cached path color if it doesn't exist
            if (cachedPathColor == default(Color))
            {
                cachedPathColor = Color.green;
            }

            // Initialize the grid color if it doesn't exist
            if (gridColor == default(Color))
            {
                gridColor = new Color(0.5f, 0.5f, 0.5f, 0.3f);
            }

            // Initialize the significant movement threshold if it doesn't exist
            if (significantMovementThreshold == 0)
            {
                significantMovementThreshold = 0.3f;
            }

            // Initialize the update interval if it doesn't exist
            if (updateInterval == 0)
            {
                updateInterval = 0.05f;
            }

            // Initialize the grid cell size if it doesn't exist
            if (gridCellSize == 0)
            {
                gridCellSize = 5f;
            }

            // Initialize the max path requests per frame if it doesn't exist
            if (maxPathRequestsPerFrame == 0)
            {
                maxPathRequestsPerFrame = 5;
            }

            // Initialize the path cache distance if it doesn't exist
            if (pathCacheDistance == 0)
            {
                pathCacheDistance = 2f;
            }
        }

        private void Start()
        {
            try
            {
                // Sync with EnemyManager if it exists
                if (EnemyManager.Instance != null)
                {
                    SyncWithEnemyManager();
                }

                // Start path update coroutine only if there are enemies to process
                if (trackedEnemies.Count > 0 || trackedEntities.Count > 0)
                {
                    StartCoroutine(UpdatePaths());
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[{GetType().Name}] Error in Start(): {e.Message}\n{e.StackTrace}");
            }
        }

        // Reusable variables to reduce GC allocations
        private PathfindingMovementBehavior cachedMovementBehavior;

        private System.Collections.IEnumerator UpdatePaths()
        {
            // Use a more efficient approach - process a subset of enemies each frame
            int enemyIndex = 0;
            int enemiesPerUpdate = Mathf.Max(1, trackedEnemies.Count / Mathf.Max(1, Mathf.RoundToInt(1f / updateInterval)));

            while (true)
            {
                try
                {
                    // Process a subset of enemies each frame to distribute the load
                    int processedCount = 0;

                    while (processedCount < enemiesPerUpdate && enemyIndex < trackedEnemies.Count)
                    {
                        var enemy = trackedEnemies[enemyIndex];
                        if (enemy != null && enemy.gameObject != null)
                        {
                            // Reuse the cached component reference to reduce GetComponent calls
                            if (cachedMovementBehavior == null || cachedMovementBehavior.gameObject != enemy.gameObject)
                            {
                                cachedMovementBehavior = enemy.GetComponent<PathfindingMovementBehavior>();
                            }

                            if (cachedMovementBehavior != null)
                            {
                                UpdateEnemyPath(enemy, cachedMovementBehavior);
                            }
                        }

                        enemyIndex++;
                        processedCount++;

                        // Wrap around if we've reached the end of the list
                        if (enemyIndex >= trackedEnemies.Count)
                        {
                            enemyIndex = 0;
                            break;
                        }
                    }

                    // If we've processed all enemies, reset the index
                    if (enemyIndex >= trackedEnemies.Count)
                    {
                        enemyIndex = 0;
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"[{GetType().Name}] Error in UpdatePaths coroutine: {e.Message}\n{e.StackTrace}");
                    // Reset index to prevent getting stuck
                    enemyIndex = 0;
                }

                yield return new WaitForSeconds(updateInterval);
            }
        }

        private void UpdateEnemyPath(EnemyCore enemy, PathfindingMovementBehavior movementBehavior)
        {
            // Apply separation adjustment before pathfinding
            Vector3 originalTarget = movementBehavior.GetTargetPosition();
            Vector3 adjustedTarget = ApplySeparationToTarget(enemy, originalTarget);

            // Update the target if separation adjusted it
            if (Vector3.Distance(originalTarget, adjustedTarget) > 0.1f)
            {
                movementBehavior.SetDestination(adjustedTarget);
                return; // SetDestination will trigger a new path request
            }
            if (enemy == null || movementBehavior == null) return;

            // Additional null checks for safety
            if (enemy.transform == null || enemy.PlayerTransform == null) return;

            try
            {
                Vector3 currentPos = enemy.transform.position;
                Vector3 targetPos = enemy.PlayerTransform.position;

                // Check if we need to update the path based on cache settings
                if (ShouldUpdatePath(enemy, currentPos, targetPos))
                {
                    // Queue path request with priority based on distance
                    // Use squared distance for efficiency and avoid expensive square root calculation
                    float distanceSquared = Vector3.SqrMagnitude(currentPos - targetPos);
                    float priority = Mathf.Sqrt(distanceSquared) * PATH_PRIORITY_DISTANCE_WEIGHT;
                    priorityPathQueue.Enqueue((priority, enemy, movementBehavior));
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[{GetType().Name}] Error updating enemy path for {enemy.gameObject.name}: {e.Message}");
            }
        }

        private bool ShouldUpdatePath(EnemyCore enemy, Vector3 currentPos, Vector3 targetPos)
        {
            // Check if we have a cached path for this enemy
            if (pathCache.TryGetValue(enemy, out CachedPath cachedPath))
            {
                float timeSinceLastUpdate = Time.time - cachedPath.timestamp;

                // If the path is still valid (not too old), don't update
                if (timeSinceLastUpdate < pathCacheTime)
                {
                    return false;
                }

                // If the target hasn't moved significantly, don't update
                // Only calculate distance if we're past the time threshold
                float distanceSquared = Vector3.SqrMagnitude(targetPos - cachedPath.endPos);
                float thresholdSquared = pathCacheDistance * pathCacheDistance;

                if (distanceSquared < thresholdSquared)
                {
                    return false;
                }
            }

            // If we don't have a cached path or it's invalid, we need to update
            return true;
        }

        // Legacy enemy registration
        public void RegisterEnemy(EnemyCore enemy)
        {
            // Use Contains is expensive, check if enemy is null first
            if (enemy == null) return;

            // Use a more efficient approach to check if enemy is already registered
            // Avoid using Contains which can be expensive on large lists
            for (int i = 0; i < trackedEnemies.Count; i++)
            {
                if (trackedEnemies[i] == enemy)
                {
                    return; // Enemy already registered
                }
            }

            trackedEnemies.Add(enemy);
        }

        public void UnregisterEnemy(EnemyCore enemy)
        {
            trackedEnemies.Remove(enemy);
            lastKnownPositions.Remove(enemy);
            pathCache.Remove(enemy);

            // Clean up spatial grid more efficiently by tracking which cells contain the enemy
            // This avoids iterating through all cells and prevents dictionary modification during iteration
            var cellsToRemove = new List<Vector2Int>();

            foreach (var kvp in spatialGrid)
            {
                var cell = kvp.Value;
                // Remove enemy from cell without LINQ
                for (int i = cell.Count - 1; i >= 0; i--)
                {
                    if (cell[i] == enemy)
                    {
                        cell.RemoveAt(i);
                        // Break since an enemy should only be in one cell at a time
                        break;
                    }
                }
                // Mark empty cells for removal
                if (cell.Count == 0)
                {
                    cellsToRemove.Add(kvp.Key);
                }
            }

            // Remove empty cells after iteration is complete
            foreach (var cellKey in cellsToRemove)
            {
                spatialGrid.Remove(cellKey);
            }
        }

        // New entity registration
        public void RegisterEntity(IEntity entity)
        {
            // Use Contains is expensive, check if entity is null first
            if (entity == null) return;

            // Use a more efficient approach to check if entity is already registered
            // Avoid using Contains which can be expensive on large lists
            for (int i = 0; i < trackedEntities.Count; i++)
            {
                if (trackedEntities[i] == entity)
                {
                    return; // Entity already registered
                }
            }

            trackedEntities.Add(entity);
        }

        public void UnregisterEntity(IEntity entity)
        {
            trackedEntities.Remove(entity);
            // Note: New entities don't use the legacy pathfinding cache system
            // They use the centralized pathfinding service in EnemyManager
        }

        private void SyncWithEnemyManager()
        {
            if (EnemyManager.Instance != null)
            {
                // Sync legacy enemies without LINQ
                var enemies = EnemyManager.Instance.Enemies;
                // Only register enemies that aren't already registered
                for (int i = 0; i < enemies.Count; i++)
                {
                    // Use ZLinq to check if enemy is already tracked
                    // Check if enemy is already registered using a more efficient approach than Contains
                    bool alreadyRegistered = false;
                    for (int j = 0; j < trackedEnemies.Count; j++)
                    {
                        if (trackedEnemies[j] == enemies[i])
                        {
                            alreadyRegistered = true;
                            break;
                        }
                    }

                    if (!alreadyRegistered)
                    {
                        RegisterEnemy(enemies[i]);
                    }
                }

                // Sync new entities without LINQ
                var entities = EnemyManager.Instance.CombatEntities;
                // Only register entities that aren't already registered
                for (int i = 0; i < entities.Count; i++)
                {
                    // Use ZLinq to check if entity is already tracked
                    // Check if entity is already registered using a more efficient approach than Contains
                    bool alreadyRegistered = false;
                    for (int j = 0; j < trackedEntities.Count; j++)
                    {
                        if (trackedEntities[j] == entities[i])
                        {
                            alreadyRegistered = true;
                            break;
                        }
                    }

                    if (!alreadyRegistered)
                    {
                        RegisterEntity(entities[i]);
                    }
                }
            }
        }

        private void Update()
        {
            try
            {
                // Only update if there are enemies or entities to process
                if ((trackedEnemies.Count == 0 && trackedEntities.Count == 0) || Time.time < nextUpdateTime) return;
                nextUpdateTime = Time.time + updateInterval;

                UpdateSpatialGrid();
                ProcessPathRequests();
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[{GetType().Name}] Error in Update(): {e.Message}\n{e.StackTrace}");
            }
        }

        private Vector2Int GetGridCell(Vector3 position)
        {
            // Use direct calculation instead of creating new Vector2Int to reduce GC allocations
            int x = Mathf.FloorToInt(position.x / gridCellSize);
            int z = Mathf.FloorToInt(position.z / gridCellSize);
            return new Vector2Int(x, z);
        }

        private void UpdateSpatialGrid()
        {
            try
            {
                int enemiesPerFrame = Mathf.Min(ENEMIES_PER_SPATIAL_UPDATE, trackedEnemies.Count);
                int processedCount = 0;

                while (processedCount < enemiesPerFrame && spatialGridUpdateIndex < trackedEnemies.Count)
                {
                    var enemy = trackedEnemies[spatialGridUpdateIndex];
                    if (enemy == null || enemy.gameObject == null || !enemy.gameObject.activeInHierarchy)
                    {
                        // Clean up null or inactive enemies
                        trackedEnemies.RemoveAt(spatialGridUpdateIndex);
                        if (enemy != null)
                        {
                            lastKnownPositions.Remove(enemy);
                            pathCache.Remove(enemy);
                        }
                        continue;
                    }

                    // Additional null check for transform
                    if (enemy.transform == null)
                    {
                        trackedEnemies.RemoveAt(spatialGridUpdateIndex);
                        lastKnownPositions.Remove(enemy);
                        pathCache.Remove(enemy);
                        continue;
                    }

                    Vector3 currentPos = enemy.transform.position;
                    if (!lastKnownPositions.TryGetValue(enemy, out Vector3 lastPos) ||
                        Vector3.SqrMagnitude(currentPos - lastPos) > significantMovementThreshold * significantMovementThreshold)
                    {
                        // Combine the dictionary lookup and removal operations to reduce lookups
                        if (lastKnownPositions.ContainsKey(enemy))
                        {
                            Vector2Int oldCell = GetGridCell(lastPos);
                            // Try to get the old cell enemies list and remove the enemy in one operation
                            if (spatialGrid.TryGetValue(oldCell, out var oldCellEnemies))
                            {
                                oldCellEnemies.Remove(enemy);
                                if (oldCellEnemies.Count == 0)
                                {
                                    spatialGrid.Remove(oldCell);
                                }
                            }
                        }

                        Vector2Int newCell = GetGridCell(currentPos);
                        // Combine dictionary lookup and list creation to reduce lookups
                        if (!spatialGrid.TryGetValue(newCell, out var cellEnemies))
                        {
                            cellEnemies = new List<EnemyCore>();
                            spatialGrid[newCell] = cellEnemies;
                        }
                        // Add enemy to cell without unnecessary checks
                        cellEnemies.Add(enemy);

                        lastKnownPositions[enemy] = currentPos;
                    }

                    spatialGridUpdateIndex++;
                    processedCount++;
                }

                if (spatialGridUpdateIndex >= trackedEnemies.Count)
                {
                    spatialGridUpdateIndex = 0;
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[{GetType().Name}] Error in UpdateSpatialGrid(): {e.Message}\n{e.StackTrace}");
                // Reset index to prevent getting stuck
                spatialGridUpdateIndex = 0;
            }
        }

        public void RequestPath(EnemyCore enemy, IPathHandler handler)
        {
            if (enemy == null || handler == null) return;

            // Use a more efficient duplicate check by limiting the search
            // Only check the last few requests to avoid O(n) complexity
            const int MAX_DUPLICATE_CHECK = 10;
            int checkCount = 0;
            bool isDuplicate = false;

            foreach (var existingRequest in priorityPathQueue)
            {
                if (existingRequest.enemy == enemy && existingRequest.handler == handler)
                {
                    isDuplicate = true;
                    break;
                }

                checkCount++;
                if (checkCount >= MAX_DUPLICATE_CHECK) break;
            }

            if (isDuplicate) return;

            float priority = CalculatePathPriority(enemy, handler);
            var request = (priority, enemy, handler);
            priorityPathQueue.Enqueue(request);
        }

        private float CalculatePathPriority(EnemyCore enemy, IPathHandler handler)
        {
            if (enemy == null || handler == null) return 0f;

            Vector3 targetPos = handler.GetTargetPosition();
            // Use squared distance for comparison to avoid expensive square root calculation
            float distanceSquared = Vector3.SqrMagnitude(enemy.transform.position - targetPos);
            // Use approximation for distance calculation to reduce CPU overhead
            float distance = Mathf.Sqrt(distanceSquared);
            return 1f / (1f + distance * PATH_PRIORITY_DISTANCE_WEIGHT);
        }

        // Object pool for temporary lists to reduce GC allocations
        private static List<(float priority, EnemyCore enemy, IPathHandler handler)> tempListPool;

        private void ProcessPathRequests()
        {
            if (priorityPathQueue.Count == 0) return;

            // Initialize the temp list pool if it doesn't exist
            if (tempListPool == null)
            {
                tempListPool = new List<(float priority, EnemyCore enemy, IPathHandler handler)>();
            }

            // Process up to maxPathRequestsPerFrame requests without sorting if queue is small
            int requestsToProcess = Mathf.Min(maxPathRequestsPerFrame, priorityPathQueue.Count);

            // Only sort if we have more requests than we can process
            if (priorityPathQueue.Count > maxPathRequestsPerFrame)
            {
                // Use pooled list to reduce GC allocations
                tempListPool.Clear();
                foreach (var request in priorityPathQueue)
                {
                    tempListPool.Add(request);
                }

                // Sort by priority (descending)
                tempListPool.Sort((a, b) => b.priority.CompareTo(a.priority));

                // Process the highest priority requests
                for (int i = 0; i < requestsToProcess; i++)
                {
                    ProcessPathRequest(tempListPool[i]);
                }

                // Put remaining requests back in the queue
                priorityPathQueue.Clear();
                for (int i = requestsToProcess; i < tempListPool.Count; i++)
                {
                    priorityPathQueue.Enqueue(tempListPool[i]);
                }
            }
            else
            {
                // Process all requests without sorting using a more efficient approach
                int processed = 0;

                // Process requests directly from the queue without creating temporary queues
                while (priorityPathQueue.Count > 0 && processed < requestsToProcess)
                {
                    var request = priorityPathQueue.Dequeue();
                    ProcessPathRequest(request);
                    processed++;
                }
                // Any remaining requests stay in the queue for next frame
            }
        }

        // Reusable path list to reduce GC allocations
        private static List<Vector3> reusablePath;

        private void ProcessPathRequest((float priority, EnemyCore enemy, IPathHandler handler) request)
        {
            var (priority, enemy, handler) = request;

            if (enemy == null || handler == null) return;

            // Additional safety checks
            if (enemy.gameObject == null || !enemy.gameObject.activeInHierarchy) return;
            if (enemy.transform == null) return;

            try
            {
                // Initialize the reusable path list if it doesn't exist
                if (reusablePath == null)
                {
                    reusablePath = new List<Vector3>(2);
                }

                Vector2Int cell = GetGridCell(enemy.transform.position);
                bool foundCachedPath = false;

                // Use a more efficient approach to remove oldest paths
                if (pathCache.Count > MAX_CACHED_PATHS)
                {
                    // Find and remove oldest paths more efficiently
                    EnemyCore oldestEnemy = null;
                    float oldestTimestamp = float.MaxValue;

                    // Use foreach with early exit for better performance
                    foreach (var kvp in pathCache)
                    {
                        if (kvp.Value.timestamp < oldestTimestamp)
                        {
                            oldestTimestamp = kvp.Value.timestamp;
                            oldestEnemy = kvp.Key;
                            // Early exit if we found a very old path
                            if (oldestTimestamp < Time.time - pathCacheTime * 2)
                            {
                                break;
                            }
                        }
                    }

                    if (oldestEnemy != null)
                    {
                        pathCache.Remove(oldestEnemy);
                    }
                }

                for (int dx = -1; dx <= 1; dx++)
                {
                    for (int dz = -1; dz <= 1; dz++)
                    {
                        if (dx == 0 && dz == 0) continue;

                        Vector2Int neighborCell = cell + new Vector2Int(dx, dz);
                        if (TryGetCachedPath(enemy, neighborCell, Time.time, out var cachedPath))
                        {
                            handler.OnPathComplete(cachedPath.path);
                            foundCachedPath = true;
                            break;
                        }
                    }
                    if (foundCachedPath) break;
                }

                if (!foundCachedPath)
                {
                    Vector3 startPos = enemy.transform.position;
                    Vector3 targetPos = handler.GetTargetPosition();

                    // Create a new list to avoid memory issues with reusable lists
                    var newPath = new List<Vector3>(2);
                    newPath.Add(startPos);
                    newPath.Add(targetPos);

                    var newCache = new CachedPath
                    {
                        startPos = startPos,
                        endPos = targetPos,
                        path = newPath,
                        timestamp = Time.time
                    };
                    pathCache[enemy] = newCache;
                    handler.OnPathComplete(newPath);
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[{GetType().Name}] Error processing path request for {enemy.gameObject.name}: {e.Message}\n{e.StackTrace}");
            }
        }

        // Reusable list to reduce GC allocations
        private static List<Vector3> reusablePathList;

        private bool TryGetCachedPath(EnemyCore enemy, Vector2Int cell, float currentTime, out CachedPath cachedPath)
        {
            cachedPath = null;
            if (!spatialGrid.TryGetValue(cell, out var cellEnemies)) return false;

            // Initialize the reusable path list if it doesn't exist
            if (reusablePathList == null)
            {
                reusablePathList = new List<Vector3>();
            }

            foreach (var otherEnemy in cellEnemies)
            {
                if (otherEnemy == enemy) continue;

                if (pathCache.TryGetValue(otherEnemy, out var existingPath))
                {
                    if (currentTime - existingPath.timestamp <= pathCacheTime &&
                        Vector3.Distance(enemy.transform.position, existingPath.startPos) <= pathCacheDistance)
                    {
                        // Create a new list to avoid memory issues with shared references
                        var newPath = new List<Vector3>();
                        if (existingPath.path != null)
                        {
                            newPath.AddRange(existingPath.path);
                        }

                        cachedPath = new CachedPath
                        {
                            startPos = enemy.transform.position,
                            endPos = existingPath.endPos,
                            path = newPath,
                            timestamp = currentTime
                        };
                        pathCache[enemy] = cachedPath;
                        return true;
                    }
                }
            }
            return false;
        }

        private void OnDrawGizmos()
        {
            if (!showDebugVisualization || !Application.isPlaying) return;

            // Only draw gizmos if there's something to visualize
            if (spatialGrid.Count == 0 && pathCache.Count == 0 && priorityPathQueue.Count == 0) return;

            DrawSpatialGrid();
            DrawCachedPaths();
            DrawPathRequests();
        }

        private void DrawSpatialGrid()
        {
            // Only draw grid if there are enemies to visualize
            if (spatialGrid.Count == 0) return;

            Gizmos.color = gridColor;
            foreach (var cell in spatialGrid.Keys)
            {
                Vector3 cellCenter = new Vector3(
                    (cell.x + 0.5f) * gridCellSize,
                    0,
                    (cell.y + 0.5f) * gridCellSize
                );
                Vector3 cellSize = new Vector3(gridCellSize, 0.1f, gridCellSize);
                Gizmos.DrawWireCube(cellCenter, cellSize);

                // Only draw connections if there are multiple enemies in the cell
                var enemies = spatialGrid[cell];
                if (enemies.Count > 1)
                {
                    Gizmos.color = Color.cyan;
                    // Draw connections between enemies in the same cell without LINQ
                    for (int i = 0; i < enemies.Count; i++)
                    {
                        if (enemies[i] == null) continue;
                        for (int j = i + 1; j < enemies.Count; j++)
                        {
                            if (enemies[j] == null) continue;
                            Gizmos.DrawLine(
                                enemies[i].transform.position + Vector3.up,
                                enemies[j].transform.position + Vector3.up
                            );
                        }
                    }
                }
            }
        }

        private void DrawCachedPaths()
        {
            float currentTime = Time.time;
            foreach (var kvp in pathCache)
            {
                if (kvp.Key == null) continue;

                var cachedPath = kvp.Value;
                float pathAge = currentTime - cachedPath.timestamp;

                // Only calculate color if the path is not too old
                if (pathAge < pathCacheTime)
                {
                    float ageRatio = pathAge / pathCacheTime;
                    Color pathColor = Color.Lerp(cachedPathColor, Color.clear, ageRatio);
                    Gizmos.color = pathColor;

                    if (cachedPath.path != null && cachedPath.path.Count > 0)
                    {
                        for (int i = 0; i < cachedPath.path.Count - 1; i++)
                        {
                            Gizmos.DrawLine(
                                cachedPath.path[i] + Vector3.up,
                                cachedPath.path[i + 1] + Vector3.up
                            );
                        }

                        foreach (var point in cachedPath.path)
                        {
                            Gizmos.DrawWireSphere(point + Vector3.up, 0.2f);
                        }
                    }

                    Gizmos.DrawLine(
                        kvp.Key.transform.position + Vector3.up,
                        cachedPath.startPos + Vector3.up
                    );
                }
            }
        }

        private void DrawPathRequests()
        {
            Gizmos.color = requestQueueColor;

            // Use foreach directly on the queue to avoid creating snapshot arrays
            int requestCount = 0;
            foreach (var (priority, enemy, handler) in priorityPathQueue)
            {
                requestCount++;
                if (enemy != null && handler != null)
                {
                    Vector3 start = enemy.transform.position + Vector3.up;
                    Vector3 end = handler.GetTargetPosition() + Vector3.up;
                    Gizmos.DrawLine(start, end);
                    Gizmos.DrawWireSphere(end, 0.3f);
                }
            }

            if (requestCount > 0)
            {
#if UNITY_EDITOR
                UnityEditor.Handles.BeginGUI();
                var view = UnityEditor.SceneView.currentDrawingSceneView;
                if (view != null)
                {
                    Vector3 screenPos = view.camera.WorldToScreenPoint(Vector3.zero + Vector3.up * 5);
                    if (screenPos.z > 0)
                    {
                        var style = new GUIStyle();
                        style.normal.textColor = requestQueueColor;
                        style.fontSize = 12;
                        style.fontStyle = FontStyle.Bold;
                        UnityEditor.Handles.Label(
                            Vector3.up * 5,
                            $"Path Requests: {requestCount}",
                            style
                        );
                    }
                }
                UnityEditor.Handles.EndGUI();
#endif
            }
        }

        private void OnGUI()
        {
            if (!showDebugVisualization) return;

            // Only draw GUI if there's something to visualize
            if (pathCache.Count == 0 && priorityPathQueue.Count == 0 && spatialGrid.Count == 0) return;

            GUILayout.BeginArea(new Rect(10, 10, 250, 200));
            GUILayout.Label("=== Legacy Pathfinding ===");
            GUILayout.Label($"Active Paths: {pathCache.Count}");

            // Show separation integration status
            bool separationAvailable = EnemyManager.Instance?.SeparationService != null;
            bool spacingAvailable = EnemyManager.Instance?.MandatorySpacingEnforcer != null;
            GUILayout.Label($"Separation: {(separationAvailable ? "Active" : "Inactive")}");
            GUILayout.Label($"Spacing: {(spacingAvailable ? "Active" : "Inactive")}");
            GUILayout.Label($"Queue Size: {priorityPathQueue.Count}");
            GUILayout.Label($"Grid Cells: {spatialGrid.Count}");

            // Display centralized pathfinding stats if available
            if (EnemyManager.Instance != null)
            {
                GUILayout.Space(10);
                EnemyManager.Instance.DisplayPathfindingStatsGUI();
            }

            GUILayout.EndArea();
        }

        private void OnEnable()
        {
            // Only sync with EnemyManager if there are no enemies registered yet
            if (EnemyManager.Instance != null && trackedEnemies.Count == 0 && trackedEntities.Count == 0)
            {
                SyncWithEnemyManager();
            }
        }

        private void OnDisable()
        {
            // Only clear collections if they exist to avoid unnecessary operations
            if (trackedEnemies != null) trackedEnemies.Clear();
            if (lastKnownPositions != null) lastKnownPositions.Clear();
            if (pathCache != null) pathCache.Clear();
            if (spatialGrid != null) spatialGrid.Clear();

            // Clear the priority path queue if it exists
            if (priorityPathQueue != null) priorityPathQueue.Clear();

            // Clear the tracked entities if it exists
            if (trackedEntities != null) trackedEntities.Clear();

            // Clear reusable lists to free memory
            if (tempListPool != null) tempListPool.Clear();
            if (reusablePathList != null) reusablePathList.Clear();
            if (reusablePath != null) reusablePath.Clear();
        }

        /// <summary>
        /// Apply separation forces to target position before pathfinding (for legacy system)
        /// </summary>
        private Vector3 ApplySeparationToTarget(EnemyCore enemy, Vector3 originalTarget)
        {
            if (enemy == null) return originalTarget;

            Vector3 adjustedTarget = originalTarget;

            // Apply soft separation forces if available
            if (EnemyManager.Instance?.SeparationService != null)
            {
                adjustedTarget = EnemyManager.Instance.SeparationService.GetSeparationAdjustedPosition(
                    enemy.transform, adjustedTarget);
            }

            // Apply hard spacing constraints if available
            if (EnemyManager.Instance?.MandatorySpacingEnforcer != null)
            {
                adjustedTarget = EnemyManager.Instance.MandatorySpacingEnforcer.EnforceMinimumSpacing(
                    adjustedTarget, enemy.transform);
            }

            return adjustedTarget;
        }
    }
}
