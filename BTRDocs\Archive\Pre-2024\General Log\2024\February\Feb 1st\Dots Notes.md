# Dots Notes

Converting a traditional Unity MonoBehaviour system like ProjectileStateBased.cs to DOTS involves several steps and considerations. From the provided code snippets, you've made a significant start in transitioning to DOTS. However, there are a few areas that need attention to ensure the systems are properly linked and functional. Here's an overview of potential gaps and suggestions for improvement:

### 1. **Component Registration and Entity Conversion**

Ensure that all necessary components are registered with the EntityManager and that your GameObjects are being converted to entities correctly. This typically involves using a GameObjectConversionSystem or the ConvertToEntity component in the Editor. It seems you have an initialization system 1, but make sure it's correctly converting your GameObjects and assigning initial component values.

### 2. **State Management**

Your original ProjectileStateBased.cs script uses a state pattern for managing projectile behavior. In DOTS, state management can be achieved by adding or removing components to represent different states. It looks like you've started this with ProjectileDotsStateTransitionSystem, but ensure that:

- Each state (e.g., homing, launched) has a corresponding component.
- Systems check for these components to determine behavior.
- State transitions are handled by adding/removing these components based on game logic.

### 3. **Collision Handling**

Your ProjectileDotsCollisionSystem is set up to handle collisions, but the actual collision handling logic (e.g., responding to hits, applying damage) needs to be implemented. Use the CollisionEvent system in DOTS to detect and respond to collisions between entities.

### 4. **Event Systems**

If your original system uses events (e.g., OnDeath, OnTriggerEnter), you'll need to adapt this to DOTS. This can involve creating specific systems for these events or using shared components to flag entities for processing by other systems.

### 5. **External Libraries and Features**

Your original script references several external libraries and Unity-specific features (e.g., DOTween, Koreographer). Integrating these with DOTS can be challenging, as DOTS operates on a different paradigm. You'll need to find equivalent DOTS-compatible solutions or create custom systems to achieve similar functionality.

### 6. **Linking Systems Together**

To ensure your systems work together:

- Use UpdateInGroup and UpdateBefore/UpdateAfter attributes to control the execution order of your systems.
- Make sure systems that depend on the output of other systems are reading the correct components and that those components are being updated as expected.

### 7. **Debugging and Validation**

- Use the Entity Debugger to inspect your entities and their components at runtime. This can help identify missing components or unexpected state configurations.
- Validate that your systems are being executed and that entities are transitioning through states as expected.

### 8. **Performance Considerations**

- Utilize the Burst compiler for your systems where possible to improve performance.
- Consider the impact of your systems on performance, especially those running every frame. Use ScheduleParallel for parallel execution where possible.

### Final Steps:

- 

**Integration Testing**

: Once all systems are in place, thorough testing is crucial to ensure that the behavior matches the original MonoBehaviour system and that performance meets your requirements.

- 

**Optimization**

: After confirming functionality, profile your game to identify and address any performance bottlenecks.

This conversion process is complex and requires a deep understanding of both the original system and DOTS. The transition to DOTS offers significant performance benefits but may require rethinking certain approaches to fit the data-oriented design.