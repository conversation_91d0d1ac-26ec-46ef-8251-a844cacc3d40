---
title: Building an Advanced Event Bus Architecture in Unity
tags: [Unity, Architecture, EventBus, Decoupling]
date: 2025-01-20
---

# [[Building an Advanced Event Bus Architecture in Unity]]

## [[Overview]]
The event bus pattern is a powerful solution for decoupling components in Unity. This tutorial will guide you through building an advanced event bus system with automatic bootstrapping.

## [[Core Components]]

### [[Interfaces]]
```csharp
public interface IEvent { }

public interface IEventBinding<T> where T : IEvent
{
    public Action<T> OnEvent { get; }
    public Action OnNoArgsEvent { get; }
}
```

### [[Event Binding Implementation]]
```csharp
public class EventBinding<T> : IEventBinding<T> where T : IEvent
{
    private Action<T> onEvent = _ => { };
    private Action onNoArgsEvent = () => { };

    Action<T> IEventBinding<T>.OnEvent => onEvent;
    Action IEventBinding<T>.OnNoArgsEvent => onNoArgsEvent;

    public EventBinding(Action<T> onEvent) => this.onEvent = onEvent;
    public EventBinding(Action onNoArgsEvent) => this.onNoArgsEvent = onNoArgsEvent;

    public void Add(Action<T> action) => onEvent += action;
    public void Remove(Action<T> action) => onEvent -= action;
}
```

### [[Event Bus Implementation]]
```csharp
public class EventBus<T> where T : IEvent
{
    private readonly HashSet<IEventBinding<T>> bindings = new();

    public void Register(IEventBinding<T> binding) => bindings.Add(binding);
    public void Deregister(IEventBinding<T> binding) => bindings.Remove(binding);

    public void Raise(T @event)
    {
        foreach (var binding in bindings)
        {
            binding.OnEvent?.Invoke(@event);
            binding.OnNoArgsEvent?.Invoke();
        }
    }
}
```

## [[Event Definitions]]
```csharp
public struct TestEvent : IEvent { }

public struct PlayerEvent : IEvent
{
    public int Health;
    public int Mana;
}
```

## [[Bootstrapping the System]]

### [[Assembly Scanning]]
```csharp
public static class EventBusInitializer
{
    [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSceneLoad)]
    private static void Initialize()
    {
        var eventTypes = AssemblyScanner.FindTypesImplementing<IEvent>();
        var buses = CreateEventBuses(eventTypes);
        EventBusRegistry.RegisterBuses(buses);
    }

    private static List<Type> CreateEventBuses(IEnumerable<Type> eventTypes)
    {
        var busType = typeof(EventBus<>);
        return eventTypes
            .Select(eventType => busType.MakeGenericType(eventType))
            .ToList();
    }
}
```

### [[Editor Integration]]
```csharp
[InitializeOnLoad]
public class EventBusEditorCleanup
{
    static EventBusEditorCleanup()
    {
        EditorApplication.playModeStateChanged += OnPlayModeStateChanged;
    }

    private static void OnPlayModeStateChanged(PlayModeStateChange state)
    {
        if (state == PlayModeStateChange.ExitingPlayMode)
        {
            EventBusRegistry.ClearAllBuses();
        }
    }
}
```

## [[Usage Examples]]

### [[Subscribing to Events]]
```csharp
public class Hero : MonoBehaviour
{
    private IEventBinding<TestEvent> testEventBinding;
    private IEventBinding<PlayerEvent> playerEventBinding;

    private void OnEnable()
    {
        testEventBinding = new EventBinding<TestEvent>(OnTestEvent);
        EventBus<TestEvent>.Register(testEventBinding);

        playerEventBinding = new EventBinding<PlayerEvent>(OnPlayerEvent);
        EventBus<PlayerEvent>.Register(playerEventBinding);
    }

    private void OnDisable()
    {
        EventBus<TestEvent>.Deregister(testEventBinding);
        EventBus<PlayerEvent>.Deregister(playerEventBinding);
    }

    private void OnTestEvent() => Debug.Log("Test Event Received");
    private void OnPlayerEvent(PlayerEvent playerEvent) => 
        Debug.Log($"Player Event: Health={playerEvent.Health}, Mana={playerEvent.Mana}");
}
```

### [[Raising Events]]
```csharp
public class Hero : MonoBehaviour
{
    private void Update()
    {
        if (Input.GetKeyDown(KeyCode.A))
        {
            EventBus<TestEvent>.Raise(new TestEvent());
        }

        if (Input.GetKeyDown(KeyCode.B))
        {
            EventBus<PlayerEvent>.Raise(new PlayerEvent { Health = 100, Mana = 100 });
        }
    }
}
```

## [[Best Practices]]
1. Use [[Structs]] for events when possible
2. Always [[Deregister Bindings]] when disabling components
3. Keep [[Event Data]] minimal and focused
4. Use clear [[Naming Conventions]] for events
5. Consider using [[Editor Tools]] to visualize event flows

## [[Additional Resources]]
- [[Unity Documentation: Event Systems]]
- [[Decoupling Patterns in Game Development]]
- [[Advanced C# Techniques for Unity]]
- [Complete Implementation on GitHub](https://github.com/Unity-Technologies/EventBusSystem)
- [Event Bus Design Patterns](https://example.com/event-bus-patterns)