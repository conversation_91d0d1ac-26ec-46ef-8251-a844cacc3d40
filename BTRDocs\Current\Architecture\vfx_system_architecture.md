---
systems: [vfx]
components: [particles, shaders, post-processing]
tags: [visual, game-system, rendering]
priority: p1
last_touched: 2025-04-09
links:
  - ![[Systems/Player/Architecture#^vfx-integration]]
  - ![[Tech Recommendations/Optimization/ShaderOptimization.md]]
---
# BTR VFX System Architecture
[[Visual Design Principles]] → [[Rendering Pipeline]]

```mermaid
flowchart TB
    %% Core VFX Components
    DigitalLayer[DigitalLayerEffect]
    MotionExtract[MotionExtractionBaseEffect]
    JPGEffect[JPGEffectController]
    BackgroundFX[BackgroundFX]
    WarpSpeed[WarpSpeed]
    
    %% Event System
    VFXTrigger[KoreoVFXTrigger]
    PlayVFXEvent[PlayVFXOnEvent]
    
    %% Technical Components
    ComputeShader[DigitalLayerCompute]
    Shader[DigitalLayerSprite]
    Scaler[JPGResolutionScaler]
    
    %% External Systems
    EventSystem[Event System]
    TimeManager[Time Manager]
    ProjectileSystem[Projectile System]
    
    %% Core VFX Relationships
    DigitalLayer --> ComputeShader
    DigitalLayer --> Shader
    JPGEffect --> Scaler
    
    %% Event Integration
    EventSystem --> VFXTrigger
    EventSystem --> PlayVFXEvent
    VFXTrigger --> |Triggers| DigitalLayer
    VFXTrigger --> |Triggers| MotionExtract
    PlayVFXEvent --> |Triggers| BackgroundFX
    PlayVFXEvent --> |Triggers| WarpSpeed
    
    %% System Integration
    TimeManager --> MotionExtract
    TimeManager --> WarpSpeed
    ProjectileSystem --> |Visual Effects| DigitalLayer
    ProjectileSystem --> |Motion Trails| MotionExtract
    
    %% Performance Components
    Scaler --> JPGEffect
    ComputeShader --> DigitalLayer

    %% Styling
    classDef core fill:#f9f,stroke:#333,stroke-width:2px
    classDef effect fill:#bbf,stroke:#333,stroke-width:2px
    classDef technical fill:#bfb,stroke:#333,stroke-width:2px
    classDef external fill:#fbb,stroke:#333,stroke-width:2px
    
    class DigitalLayer,MotionExtract core
    class JPGEffect,BackgroundFX,WarpSpeed effect
    class ComputeShader,Shader,Scaler technical
    class EventSystem,TimeManager,ProjectileSystem external
```

## Color Legend
- 🟪 Core (Purple): Central VFX components
- 🟦 Effects (Blue): Visual effect implementations
- 🟩 Technical (Green): Shaders and compute
- 🟥 External (Red): System integrations

## System Description
This diagram details the VFX System's architecture, showing:

1. **Core Components**
   - Digital layer effects
   - Motion extraction
   - JPG post-processing
   - Background effects
   - Warp speed effects

2. **Technical Implementation**
   - Compute shader integration
   - Custom shader effects
   - Resolution scaling
   - Performance optimization

3. **Event Integration**
   - VFX event triggers
   - Event-based playback
   - Time-based effects
   - System synchronization

4. **Performance Features**
   - Compute shader utilization
   - Resolution scaling
   - Event-based activation
   - Optimized rendering

5. **System Integration**
   - Event system triggers
   - Time system synchronization
   - Projectile system effects
   - Background management

[[Architecture/architecture_overview|Back to Overview]]