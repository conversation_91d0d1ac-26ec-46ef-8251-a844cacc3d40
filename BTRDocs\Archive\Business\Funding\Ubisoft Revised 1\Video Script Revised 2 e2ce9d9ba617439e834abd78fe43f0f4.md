# Video Script Revised 2

CC on video for hearing impaired

Need a logline - or aspects of it - in the dialogue

Focus on feelings?

Hi, I’m <PERSON>, 

I operate WaveJam Studios, 

where I’ve brought together - my love for music and games - to create an exciting,

interactive - musical action experience called Beat Project.   

Music has always been part of my life, 

I’ve been lucky to create  and share music with lots of amazing people

I’ve worked in audio-post production and moved to design & technology

Always finding a way back to music, creating new interactive experiences that allow me to share my passion for sound

People feel a deep connection with the music they love. 

and, Today, they can access all of their favorite music for just a few dollars a month. 

So where can people find new, deeper connections with music?

With Beat Project, It’s about giving the listener agency within the music, moving through it, affecting and flowing through the rhythms
Players experience a deeper connection with the beat through interaction, existing in an accessible, playful world between listening and composing the music

Your actions affect and add to the music, like the melodies heard here

All while travelling through musically reactive digital worlds

It’s not about punishing players if they miss the beat, it’s about being taken on a journey where you get to explore the music 

**Script direction 1**

Many music games are all about quick and precise reactions, punishing the player when things aren’t done correctly

But that’s not how a good performance feels, or how people listen to music

- It’s not the only way to engage with music

In Beat Project, players see the creativity in their approach (playstyle) reflected in the music around them

**Refer to Powerpoint**

Where did this idea come from?

Strong Love of music all my life – going to shows, performing, recording in musical projects etc.
Pandemic – Solo music experiences
Listening or writing music

Thinking about large gap between listening and writing music

As a listener, I wanted to go deeper into the sounds I heard, and how they fit together
As a writer, I wanted to give people the sense of play and access I had with how all these sounds fit together

Is there any accessible way for people to play in, and enjoy the same musical process that musicians do?

PROBLEM
Electronic music / DJ culture / Dance music touches on this
Remixing sounds is a large part of the style
But what if we could go further
Responsive Visuals
Accessible Game Systems

Build on that interaction and playfulness, existing in a space between linearity of listening to music and playfulness of writing and performing it

**Script direction 2**

Start with more of a question?

What if you could interact with a song?

Not just following the beat, but dictating where it goes?