# March 11

Compilation of past comments / issues

- Enemies need to shoot a different colour bullet,
    - Material swap for this?
- Need to still figure out why i can extend the tail
- Coroutine issues still happening (doesnt break gameplay?)
- Edit snake mesh
- Not using A* in Infinite Track example - adjust rotation / ground detection accordingly
- refine aspect: reticle aim influences locked projectiles - currently only rotation
- Remove or refine dotween shake on hit ColliderCallBacks
- Infinite Snake - figure out gameplay structure here
- Also could use Infinite Snake as part of travelling inside of one?
    - Would interior of regular ouroboros be good enough here? Or preferred?
- Update all Mobius Tube
    - All need adjustment, but 6 needs a lot of work - no enemies? Maybe a bad structure

Inspo

- Hyperpassive as a rubric for the music and interactivity in Beat Remake
- Possible Settings guidelines for Unity
    - [https://github.com/xZenvin/UnitySettingsFramework](https://github.com/xZenvin/UnitySettingsFramework)

**Ophanim**

running along moving rings

- how could infinite track be applied here??

IMPORTANT

to make things brighter / more emissive / stand out, HDR intensity needs to be adjusted along with color. Makes a big difference!

Today

Working on improving the Ouroboros 

Cleaning up scene to be more playable

Need alternate ideas on what the eyes littering the snakes could be,,,,

![Untitled](March%2011%20a3e4e0318a5e46d089e6e1b0e895b48d/Untitled.png)

Thinking they are shot off and caught by hanging items. Lat think shooting them off… not sure what yet. But having them caught means you can see where they’re going. Need to reposition them to have it work a bit better

Thiking shape somewhat like an enemy,, but differnet enough to not confuse them. half way submerged in the snake. Use hexagon / ostagon like things here. like a crystal!

Added Sensor Toolkit for enabling / disabling these static enemy shooters

Need to add shooters to other ourobors sections

Prefab painter should be able to do this?

Mobius Tube 7 - Feels too big maybe? Scale down? 

Mobius Tube 6 - also too big? Also getting stuck?

IMPORTANT

Even with all SnakeEYes/Static Shooters inactive - INTENTIONALLY - getting the cannot start coroutine thing and they appear to still be shooting. HOW IS THIS POSSIBLE?

Projectile manager maybe? Investigate