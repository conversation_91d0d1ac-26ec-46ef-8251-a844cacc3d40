---
title: Unity Performance Tips - Draw Calls
tags: [Unity, Performance, Optimization, Graphics]
date: 2025-01-20
---

# [[Unity Performance Tips - Draw Calls]]

## [[Overview]]
This guide covers essential techniques for optimizing draw calls in Unity, a critical factor in game performance. Draw calls are commands sent to the GPU to render objects, and reducing them can significantly improve frame rates.

## [[Implementation]]

### [[Understanding Draw Calls]]
- Each draw call tells GPU to render a mesh
- More meshes = more draw calls
- More materials = more draw calls
- CPU processes draw calls before sending to GPU

### [[Optimization Techniques]]

#### 1. Combine Meshes
```csharp
// Example of combining meshes programmatically
Mesh combinedMesh = new Mesh();
combineInstances = new CombineInstance[meshes.Length];
for (int i = 0; i < meshes.Length; i++) {
    combineInstances[i].mesh = meshes[i];
    combineInstances[i].transform = transforms[i].localToWorldMatrix;
}
combinedMesh.CombineMeshes(combineInstances);
```

#### 2. Reduce Materials
- Aim for one material per object
- Exceptions:
  - Different shaders
  - Tiling textures
- Use texture maps to color different parts

#### 3. Use Static Batching
- Enable by checking Static checkbox
- Only for non-moving objects
- Exclude from navigation/light baking if needed
- View batches in Stats window during play mode

#### 4. GPU Instancing
```csharp
// Enable GPU instancing in material
material.enableInstancing = true;
```

## [[Best Practices]]
1. Combine meshes in 3D software
2. Reduce number of materials
3. Mark static objects as Static
4. Use GPU instancing where appropriate
5. Test optimizations incrementally

## [[Performance Considerations]]
| Optimization Step | FPS | Batches |
|-------------------|-----|---------|
| [[Original]]          | 0.5 | 1.6M    |
| [[Combined Meshes]]   | 80  | 20K     |
| [[Single Material]]   | 180 | -       |
| [[Static Batching]]   | 210 | -       |

## [[Additional Resources]]
- [[Unity Documentation: Optimizing Graphics Performance]]
- [[Understanding GPU Instancing]]
- [[Static Batching Best Practices]]
- [Unity Performance Optimization Guide](https://docs.unity3d.com/Manual/OptimizingGraphicsPerformance.html)