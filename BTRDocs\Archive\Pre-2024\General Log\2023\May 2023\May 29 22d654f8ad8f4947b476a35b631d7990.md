# May 29

Making Crosshair script more efficient, can see some issues in the profiler

Largely minor improvements

Also looking into job leak issue, full stack trace leaking enabled

Updating to latest A* Pathfinding for potential perforamnce improvements - 

contains this - helpful?

[FollowerEntity - A* Pathfinding Project (arongranberg.com)](https://arongranberg.com/astar/documentation/beta/followerentity.html#isTraversingOffMeshLink)

Bug - wont rotate right! Why not? Unsure…

![Untitled](May%2029%2022d654f8ad8f4947b476a35b631d7990/Untitled.png)

Had falling off navmesh issues again - seems fixed now. Remember - all was rescan and save graphs when updating A* Pathfinding. WIll have issue otherwise

Improved Ground Check script, appears to working much better now.

though no error, seems on time rewind enemies are glitching out. 

Turning on rewindable to see if it changes anything?

Seems a bit better, but seems if two enemies dies at once it screws things up

Or SOEMTHING is still happening, causing the enemy to teleport

Need to look into this