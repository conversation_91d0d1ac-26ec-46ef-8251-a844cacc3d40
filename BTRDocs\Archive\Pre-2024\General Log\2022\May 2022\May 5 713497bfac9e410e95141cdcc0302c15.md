# May 5

Watching <PERSON> course on sci fi while doing other things

Found Smhup zine that seems interesting!

[https://www.magcloud.com/browse/issue/1820005](https://www.magcloud.com/browse/issue/1820005)

Bullet patterns and such listed

Adding State driven camera switching for transition sections

[https://www.youtube.com/watch?v=Ri8PEbD4w8A](https://www.youtube.com/watch?v=Ri8PEbD4w8A)

Using this tutorial!

Works! Just need to refine what it should do

Tried to have a Countdown on screen while transitioning but cant seem to get this working quite right - need to look at again sometime

Destroy all active bullets when in a transition phase? Think about how to do this

Edited WaveMap and works well! Can do lots here

What are the possibilties for trainsitioning scenery ?

Seems every problem bullet has a negative lifetime. Recycle bullets with negative life time?

WOrking on projectile class to clean up and fix issues

What am I doing with OnTargetDestroy Event on Projectiel Target (TargetCube) ???

Don’t recall what I was going for here

Setting LaunchAtEnemy and LaunchBack to make launching = false

See what happens! Does it break everything?

Would make a lot of sense to use a switch case for the states of the bullet

State machine i suppose? Think about this

What are the Projectile’s states?

ProjectileLaunchedByEnemy

ProjectileLockedByPlayer

ProjectileLaunchedByPlayer

ABADONDED STATE MACHINES

If I simplify Porjectile class, not really necessary

Also fixed Projectile class issues with Laucnhing - there are still some error cases but much better now!

Can now go 9 rounds with things getting exceedingly difficult!! 

Pretty cool!!!