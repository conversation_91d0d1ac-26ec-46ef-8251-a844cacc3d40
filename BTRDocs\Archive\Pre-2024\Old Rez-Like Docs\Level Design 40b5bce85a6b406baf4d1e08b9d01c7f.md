# Level Design

Even though the player controls the camera, we can create cinematic experiences through controlling the environment

[https://www.youtube.com/watch?v=L27Qb20AYmc&t=1s](https://www.youtube.com/watch?v=L27Qb20AYmc&t=1s)

Divide screen into 1/3rds, and have the main compositional elements of your image sitting on those 1/3 markers

![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Rez-Like%20Docs/Level%20Design/Untitled.png)

Weight of composition on edges of screen in first - center in second

![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Rez-Like%20Docs/Level%20Design/Untitled%201.png)

Avoid Tangents!

![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Rez-Like%20Docs/Level%20Design/Untitled%202.png)

Desk lining up with wall at this angle in first image - not good!

Can read the space / spatial information better in second

Can be difficult to find these Tangents at times but often easy to fix

**Setups**

![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Rez-Like%20Docs/Level%20Design/Untitled%203.png)

Exploring camera setup vs Combat camera setup

Compose environment for ideal look with any camera setup - try to, never perfect!

**Feininger Principles of Composition in Photography has more info**

**Avoiding Tangents in Art - google and lots of info**

**Creating Depth**

Create Layers - Foregound / Midground / Background

Parrallax animation created this way

![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Rez-Like%20Docs/Level%20Design/Untitled%204.png)

Repeating elements also help create depth

![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Rez-Like%20Docs/Level%20Design/Untitled%205.png)

Use Atmospherics - particles in atmos scatter light and make distant objects less distinct

![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Rez-Like%20Docs/Level%20Design/Untitled%206.png)

Shapes on Screen

![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Rez-Like%20Docs/Level%20Design/Untitled%207.png)

![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Rez-Like%20Docs/Level%20Design/Untitled%208.png)

Emotional weight of shapes

Comforting vs dangerous shapes

Circular shapes are often seen as friendly

Jagged shapes as dangerous

![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Rez-Like%20Docs/Level%20Design/Untitled%209.png)

Horizontal shapes as Power, Control, Visibility

Vertical Shapes (trees, cliffs) - powerful but not as unchanging?

**Picture This - How Pictures Work - Book Recommendation**

**Mythbusting**

Aesthetic Concept - Strong line in painting will lead the eye through the painting

This is not what's happening!

We are not following the line, we're moving past it and getting caught like a fish in a net

Lighting does not really lead the player either

Saliency & Affordances

Saliency  = Attention

Bottom up or top down Saliency

Bottom up is all about contrasts - different types demonstrated here

![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Rez-Like%20Docs/Level%20Design/Untitled%2010.png)

Top down is about meaningful things - descriptions in video

![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Rez-Like%20Docs/Level%20Design/Untitled%2011.png)

Final Saliency is a mix of both

![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Rez-Like%20Docs/Level%20Design/Untitled%2012.png)

Example where bottom up would dictate eaves - highest contrast point - should lead the eye, but the meaning of the sign, door, windows overrides this

REFERENCE - Lecture 11: VIsual Attention and Conciousness, Allen Institue - Youtube

Introduction tutorial on visual attention and visual salience

Visual Attention in 3D video games academic study - Su Yan

**Affordances**

the action that an object allows - door affords opening, etc

![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Rez-Like%20Docs/Level%20Design/Untitled%2013.png)

Misperceived affordances are a thing too

A solution to this - Signifiers

- Yellow lines and pointers in video games often dictate affordances

Does break 4th wall, context dependent

DESIGN OF EVERYDAY THINGS RECOMMENDED

Surface Affordances - Floor affords walking for example

Study on movement through the TATE gallery

![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Rez-Like%20Docs/Level%20Design/Untitled%2014.png)

Attempt to prove ideas around Surface Affordances

Archetecture - Prospect (view) and Refuge (safety) concepts

These can be broken down to primary and secondary as well

Primary Prospect and Refuge - View where we are now, safety where we are now

Secondary Prospect and Refuge - See and spot that could afford either of these things

**Book Recs: Origins of Architectural Pleasure**

**Mystery in terms of Affordances - potential for things to learn!**

Wayfinding elements - 5 types - waymarks, districts, linear elements/paths, edges, nodes

Think of all of them as effecting the mental map, rather then directly leading the player

**Time**

Space = Time

Time to see or time to travel influence this

Repetion = Rhythm

Repeat elements archetecturally as rhythmic elements

![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Rez-Like%20Docs/Level%20Design/Untitled%2015.png)

Spacing of left colums during movement vs right columns create differetn visual rhythm (strobing?)

Density over space effects intensity over time

**BOOK RECComentation: Experiencing Achectecture**

**Transitions**

Classic example - Disneyland

Stylistic transitions between spaces

No abrupt transition - slowly stop one elemnt and introduce another

Lamp posts and hand rails change styles at different points (staggered) to transition you to new area

Stylistic dissolves between areas

**BOOK REC: Francis DK Ching Archetecture Form, Space & Order**

We are composing the 3d for how it looks in 2d

![Untitled](Archive/Admin%20&%20Archive/Archive/Old%20Rez-Like%20Docs/Level%20Design/Untitled%2016.png)

[Titanfall 2 Action Blocks](Titanfall%202%20Action%20Blocks%20e82248a5919f4740a973fba454372345.md)

[Naughty Dog Whitebox](Naughty%20Dog%20Whitebox%20ab3cf8c16b3c4469b7a9dca894a5fa06.md)