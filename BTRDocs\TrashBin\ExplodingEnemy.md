---
title: Exploding Enemy
date: 2024-01-15
tags: [enemy, exploding, configuration]
aliases: [Exploding<PERSON><PERSON><PERSON>, KamikazeEnemy]
---

# Exploding Enemy

## Overview
The Exploding Enemy is a kamikaze-style enemy that rushes toward the player and explodes when in close proximity. It uses the [[ExplosionCombatBehavior]] for its primary attack mechanism.

## Configuration
```yaml
# Core Settings
maxHealth: 50
moveSpeed: 25
isVulnerable: true
clockKey: "Exploder"

# Combat Settings
minPlayerDistance: 0.5
maxPlayerDistance: 15
hitsToKillPart: 2
damageAmount: 50

# Explosion Settings
explosionRadius: 3
explosionDamage: 50
explosionTriggerDistance: 0.5

# Phase Settings
phase1Distance: 10  # Initial approach
phase1Duration: 10
phase2Distance: 5   # Final rush
phase2Duration: 5
explosionDistance: 0.5
```

## Required Components
1. [[EnemyCore]]
2. [[PathfindingMovementBehavior]]
3. [[ExplosionCombatBehavior]]
4. [[DamageVisualizationBehavior]]
5. [[TargetIndicatorBehavior]]

## Behavior Implementation
The Exploding Enemy implements:
- Aggressive pathfinding movement
- Proximity-based explosion triggering
- Visual pulse feedback when near player
- Material emission effects
- Two-phase movement pattern:
  1. Initial approach phase
  2. Final rush phase

## Special Features
- Self-destructs after explosion
- Damage scales with distance from explosion center
- Visual pulse indicates imminent explosion
- FMOD audio integration for explosion effects

## Related Documents
- [[EnemySystemGuide#Special Enemy Types|Enemy System Documentation]]
- [[ExplosionCombatBehavior|Explosion Combat Documentation]]
- [[SystemsDesignAnalysis#Implementation Strategy|Systems Design Analysis]] 