# May 15

Value and reference types

[Value & Reference types in C#. Write Better Code!](https://www.youtube.com/watch?v=ldq7ZByB4p8)

Difference between structs and class illustrated to show the difference between values and reference

struct is value type, b = a copies code 

class if reference type, b = a references code, just a new pointer to the same data

C# - data is stored in the stack and the heap

stack is for super fast allocation and deallocation, only works with fixed data size though

Heap

Grow and shrink at run time, this data we have no idea at compile time how big these things will actually be

- but ex if size allocated to a list is exceeded, an entirely new list allocation is created

Value types hold the data themselves on the stack

Referenece types hole a reference to the data while is held on the heap

THis isnt totally right, need verification

When to use a struct and when to use a class? 

Heap is fast, so why wouldn’t we do this all the time?

Strings are immutable, so when we add to a string it actually throws it away 

[Object Pooling in Unity 2021 is Dope AF](https://www.youtube.com/watch?v=7EZ2F-TzHYw&)

Important thing to revisit! Forgot about this. New object pooling in Unity 2021 - Can probably do a lot with this

Need more aggreesive LOD groups 

What should a second level look like?

Trying out deconstrucitng and destroying a model in blender to create interesting results

This is called Decimate

Add Modifier → Decimate → Planar gives really cool decimated look 

Control J to join all selected mesh together

Can I export items batch at different decimate levels?

[Best Modifiers in Blender](https://www.youtube.com/watch?v=X7ueTvXzoFE)

Lattice modifier to stretch and adjust things

Build Modifier → Can I turn this into an animation? Build up model by component parts

Displace Modifier → Adda texture that will make the object displaced. Distort a mesh

Boolean → Can use to cut a chunk out of a building or object, watch video for how

Solidify → Create bevels and markings easily

Wireframe - Easily wireframe objects! Pretty cool

IMPORTANT - how to reduce number of materials needed for a mesh? How do i pair it down so one material can work for entire mesh? Do I want to do this in all cases?

Joost shader can add movement / wiggle to objects - can i implement this type of thing on other shaders for movement? Can i make it transparent?

Scripts like Joost glitch effect these properties - expand on this with scripts for size -maybe make size relative to scale of obejct? that way it might scale properties appropriately?

Digital debris look is interesting!

I can create digital debris worlds pretty easy i think - good scenery

Find out how the planet transparency thing works and implement that 

Reference twitter for more visual inspo