using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using Pathfinding;
using BTR.EnemySystem;
using BTR.Utilities;
using ZLinq;

namespace BTR.EnemySystem
{
    /// <summary>
    /// Centralized pathfinding service that optimizes A* pathfinding operations
    /// by caching results, batching requests, and using spatial optimization.
    /// Integrates with EnemyManager's spatial grid for maximum efficiency.
    /// </summary>
    public class CentralizedPathfindingService : MonoBehaviour
    {
        [Header("Caching Settings")]
        [SerializeField] private float nodeCacheTime = 0.5f;
        [SerializeField] private float nodeCacheDistance = 1.5f;
        [SerializeField] private int maxCachedNodes = 1000;
        [SerializeField] private float pathCacheTime = 1f;
        [SerializeField] private float pathCacheDistance = 2f;

        [Header("Batch Processing")]
        [SerializeField] private int maxPathRequestsPerFrame = 10;
        [SerializeField] private float batchProcessingInterval = 0.05f;

        [Header("Distance-Based LOD")]
        [SerializeField] private bool enableDistanceLOD = true;
        [SerializeField] private float closeDistance = 20f;
        [SerializeField] private float mediumDistance = 50f;
        [SerializeField] private float farDistance = 100f;
        [SerializeField] private float closeUpdateInterval = 0.1f;
        [SerializeField] private float mediumUpdateInterval = 0.3f;
        [SerializeField] private float farUpdateInterval = 0.8f;
        [SerializeField] private float veryFarUpdateInterval = 2f;

        [Header("Debug")]
        [SerializeField] private bool enableDebugLogs = false;
        [SerializeField] private bool showDebugVisualization = false;

        // Spatial node caching
        private class CachedNodeData
        {
            public Vector3 position;
            public Vector3 nearestNode;
            public float timestamp;
            public Vector2Int gridCell;
        }

        // Path caching
        private class CachedPathData
        {
            public Vector3 startPos;
            public Vector3 endPos;
            public List<Vector3> path;
            public float timestamp;
            public Vector2Int gridCell;
        }

        // Path request data
        public class PathRequest
        {
            public IEntity entity;
            public Vector3 startPosition;
            public Vector3 targetPosition;
            public System.Action<List<Vector3>> callback;
            public float priority;
            public float timestamp;
            public PathfindingLOD lodLevel;
        }

        public enum PathfindingLOD
        {
            High,    // < 20 units: frequent updates
            Medium,  // 20-50 units: moderate updates  
            Low,     // 50-100 units: infrequent updates
            VeryLow  // > 100 units: rare updates
        }

        // Cache storage
        private Dictionary<Vector2Int, List<CachedNodeData>> spatialNodeCache = new Dictionary<Vector2Int, List<CachedNodeData>>();
        private Dictionary<Vector2Int, List<CachedPathData>> spatialPathCache = new Dictionary<Vector2Int, List<CachedPathData>>();
        private Queue<PathRequest> pathRequestQueue = new Queue<PathRequest>();
        private Dictionary<IEntity, float> lastPathRequestTime = new Dictionary<IEntity, float>();

        // Performance tracking
        private int cacheHits = 0;
        private int cacheMisses = 0;
        private int totalRequests = 0;
        private float lastBatchProcessTime;

        // Grid settings (should match EnemyManager)
        private float gridCellSize = 5f;
        private Transform playerTransform;

        public static CentralizedPathfindingService Instance { get; private set; }

        // Domain reload handling
        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.SubsystemRegistration)]
        private static void ResetStaticData()
        {
            Instance = null;
        }

        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                InitializeService();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void InitializeService()
        {
            // Get grid cell size from EnemyManager if available
            if (EnemyManager.Instance != null)
            {
                gridCellSize = 5f; // Match EnemyManager's grid cell size
            }

            // Find player transform
            var player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                playerTransform = player.transform;
            }

            if (enableDebugLogs)
            {
                Debug.Log($"[CentralizedPathfindingService] Initialized with grid cell size: {gridCellSize}");
            }
        }

        private void Update()
        {
            if (Time.time >= lastBatchProcessTime + batchProcessingInterval)
            {
                ProcessBatchRequests();
                lastBatchProcessTime = Time.time;
            }

            // Periodic cache cleanup
            if (Time.frameCount % 300 == 0) // Every ~5 seconds at 60fps
            {
                CleanupExpiredCache();
            }
        }

        /// <summary>
        /// Request a path using the centralized pathfinding system
        /// </summary>
        public void RequestPath(IEntity entity, Vector3 targetPosition, System.Action<List<Vector3>> callback)
        {
            if (entity?.GameObject == null || callback == null)
            {
                if (enableDebugLogs)
                    Debug.LogWarning("[CentralizedPathfindingService] Invalid path request - null entity or callback");
                return;
            }

            Vector3 startPosition = entity.GameObject.transform.position;

            // Check if we should throttle this request based on LOD
            PathfindingLOD lodLevel = CalculateLODLevel(startPosition);
            if (ShouldThrottleRequest(entity, lodLevel))
            {
                return;
            }

            // Try to get cached path first
            if (TryGetCachedPath(startPosition, targetPosition, out List<Vector3> cachedPath))
            {
                cacheHits++;
                callback(cachedPath);

                if (enableDebugLogs)
                    Debug.Log($"[CentralizedPathfindingService] Cache hit for {entity.EntityID}");
                return;
            }

            // Queue new path request
            var request = new PathRequest
            {
                entity = entity,
                startPosition = startPosition,
                targetPosition = targetPosition,
                callback = callback,
                priority = CalculatePriority(startPosition, targetPosition),
                timestamp = Time.time,
                lodLevel = lodLevel
            };

            pathRequestQueue.Enqueue(request);
            lastPathRequestTime[entity] = Time.time;
            totalRequests++;
            cacheMisses++;

            if (enableDebugLogs)
                Debug.Log($"[CentralizedPathfindingService] Queued path request for {entity.EntityID}, LOD: {lodLevel}");
        }

        private PathfindingLOD CalculateLODLevel(Vector3 position)
        {
            if (!enableDistanceLOD || playerTransform == null)
                return PathfindingLOD.High;

            float distance = Vector3.Distance(position, playerTransform.position);

            if (distance < closeDistance) return PathfindingLOD.High;
            if (distance < mediumDistance) return PathfindingLOD.Medium;
            if (distance < farDistance) return PathfindingLOD.Low;
            return PathfindingLOD.VeryLow;
        }

        private bool ShouldThrottleRequest(IEntity entity, PathfindingLOD lodLevel)
        {
            if (!lastPathRequestTime.TryGetValue(entity, out float lastRequest))
                return false;

            float interval = lodLevel switch
            {
                PathfindingLOD.High => closeUpdateInterval,
                PathfindingLOD.Medium => mediumUpdateInterval,
                PathfindingLOD.Low => farUpdateInterval,
                PathfindingLOD.VeryLow => veryFarUpdateInterval,
                _ => closeUpdateInterval
            };

            return Time.time - lastRequest < interval;
        }

        private float CalculatePriority(Vector3 start, Vector3 target)
        {
            float distance = Vector3.Distance(start, target);
            // Closer enemies get higher priority (inverse distance)
            return 1f / (1f + distance * 0.1f);
        }

        private bool TryGetCachedPath(Vector3 start, Vector3 target, out List<Vector3> path)
        {
            path = null;
            Vector2Int gridCell = GetGridCell(start);

            if (!spatialPathCache.TryGetValue(gridCell, out var cellPaths))
                return false;

            float currentTime = Time.time;

            foreach (var cachedPath in cellPaths)
            {
                if (currentTime - cachedPath.timestamp > pathCacheTime)
                    continue;

                if (Vector3.Distance(start, cachedPath.startPos) <= pathCacheDistance &&
                    Vector3.Distance(target, cachedPath.endPos) <= pathCacheDistance)
                {
                    path = new List<Vector3>(cachedPath.path);
                    return true;
                }
            }

            return false;
        }

        private void ProcessBatchRequests()
        {
            if (pathRequestQueue.Count == 0) return;

            // Sort requests by priority
            var requests = new List<PathRequest>();
            int processCount = Mathf.Min(maxPathRequestsPerFrame, pathRequestQueue.Count);

            for (int i = 0; i < processCount && pathRequestQueue.Count > 0; i++)
            {
                requests.Add(pathRequestQueue.Dequeue());
            }

            // Sort by priority (highest first)
            requests.Sort((a, b) => b.priority.CompareTo(a.priority));

            foreach (var request in requests)
            {
                ProcessPathRequest(request);
            }

            if (enableDebugLogs && requests.Count > 0)
            {
                Debug.Log($"[CentralizedPathfindingService] Processed {requests.Count} path requests");
            }
        }

        private void ProcessPathRequest(PathRequest request)
        {
            if (request.entity?.GameObject == null)
                return;

            // Use actual A* pathfinding through AstarPath
            if (AstarPath.active != null)
            {
                // Create a path request using A* Pathfinding Project
                var pathRequest = ABPath.Construct(request.startPosition, request.targetPosition);
                pathRequest.callback += (Path p) =>
                {
                    if (p.error)
                    {
                        // Fallback to direct path if A* fails
                        var fallbackPath = new List<Vector3> { request.startPosition, request.targetPosition };
                        CachePath(request.startPosition, request.targetPosition, fallbackPath);
                        request.callback(fallbackPath);

                        if (enableDebugLogs)
                            Debug.LogWarning($"[CentralizedPathfindingService] A* pathfinding failed for {request.entity.EntityID}: {p.errorLog}");
                    }
                    else
                    {
                        // Convert A* path to Vector3 list
                        var pathPoints = new List<Vector3>();
                        if (p.path != null && p.path.Count > 0)
                        {
                            for (int i = 0; i < p.path.Count; i++)
                            {
                                pathPoints.Add((Vector3)p.path[i].position);
                            }
                        }
                        else
                        {
                            // Fallback if no path nodes
                            pathPoints.Add(request.startPosition);
                            pathPoints.Add(request.targetPosition);
                        }

                        // Cache the result
                        CachePath(request.startPosition, request.targetPosition, pathPoints);

                        // Execute callback
                        request.callback(pathPoints);

                        if (enableDebugLogs)
                            Debug.Log($"[CentralizedPathfindingService] A* path found for {request.entity.EntityID} with {pathPoints.Count} points");
                    }
                };

                // Start the A* pathfinding calculation
                AstarPath.StartPath(pathRequest);
            }
            else
            {
                // Fallback to direct path if A* is not available
                var fallbackPath = new List<Vector3> { request.startPosition, request.targetPosition };
                CachePath(request.startPosition, request.targetPosition, fallbackPath);
                request.callback(fallbackPath);

                if (enableDebugLogs)
                    Debug.LogWarning("[CentralizedPathfindingService] AstarPath not active, using direct path fallback");
            }
        }

        private void CachePath(Vector3 start, Vector3 target, List<Vector3> path)
        {
            Vector2Int gridCell = GetGridCell(start);

            if (!spatialPathCache.TryGetValue(gridCell, out var cellPaths))
            {
                cellPaths = new List<CachedPathData>();
                spatialPathCache[gridCell] = cellPaths;
            }

            var cachedPath = new CachedPathData
            {
                startPos = start,
                endPos = target,
                path = new List<Vector3>(path),
                timestamp = Time.time,
                gridCell = gridCell
            };

            cellPaths.Add(cachedPath);

            // Limit cache size per cell
            if (cellPaths.Count > 10)
            {
                cellPaths.RemoveAt(0);
            }
        }

        private Vector2Int GetGridCell(Vector3 position)
        {
            return new Vector2Int(
                Mathf.FloorToInt(position.x / gridCellSize),
                Mathf.FloorToInt(position.z / gridCellSize)
            );
        }

        private void CleanupExpiredCache()
        {
            float currentTime = Time.time;
            int cleanedNodes = 0;
            int cleanedPaths = 0;

            // Clean node cache
            foreach (var cell in spatialNodeCache.Keys.AsValueEnumerable().ToList())
            {
                var nodes = spatialNodeCache[cell];
                int originalCount = nodes.Count;
                nodes.RemoveAll(n => currentTime - n.timestamp > nodeCacheTime);
                cleanedNodes += originalCount - nodes.Count;

                if (nodes.Count == 0)
                    spatialNodeCache.Remove(cell);
            }

            // Clean path cache
            foreach (var cell in spatialPathCache.Keys.AsValueEnumerable().ToList())
            {
                var paths = spatialPathCache[cell];
                int originalCount = paths.Count;
                paths.RemoveAll(p => currentTime - p.timestamp > pathCacheTime);
                cleanedPaths += originalCount - paths.Count;

                if (paths.Count == 0)
                    spatialPathCache.Remove(cell);
            }

            if (enableDebugLogs && (cleanedNodes > 0 || cleanedPaths > 0))
            {
                Debug.Log($"[CentralizedPathfindingService] Cleaned {cleanedNodes} nodes, {cleanedPaths} paths");
            }
        }

        /// <summary>
        /// Get performance statistics for monitoring
        /// </summary>
        public (int hits, int misses, float hitRate, int queueSize) GetPerformanceStats()
        {
            float hitRate = totalRequests > 0 ? (float)cacheHits / totalRequests : 0f;
            return (cacheHits, cacheMisses, hitRate, pathRequestQueue.Count);
        }

        private void OnDrawGizmos()
        {
            if (!showDebugVisualization || !Application.isPlaying) return;

            // Draw cached paths
            Gizmos.color = Color.green;
            foreach (var cellPaths in spatialPathCache.Values)
            {
                foreach (var cachedPath in cellPaths)
                {
                    if (cachedPath.path != null && cachedPath.path.Count > 1)
                    {
                        for (int i = 0; i < cachedPath.path.Count - 1; i++)
                        {
                            Gizmos.DrawLine(cachedPath.path[i], cachedPath.path[i + 1]);
                        }
                    }
                }
            }

            // Draw grid cells with cache data
            Gizmos.color = new Color(0.5f, 0.5f, 1f, 0.3f);
            foreach (var cell in spatialPathCache.Keys)
            {
                Vector3 cellCenter = new Vector3(
                    (cell.x + 0.5f) * gridCellSize,
                    0,
                    (cell.y + 0.5f) * gridCellSize
                );
                Gizmos.DrawWireCube(cellCenter, new Vector3(gridCellSize, 0.1f, gridCellSize));
            }
        }

        private void OnDestroy()
        {
            if (Instance == this)
            {
                Instance = null;
            }
        }
    }
}
