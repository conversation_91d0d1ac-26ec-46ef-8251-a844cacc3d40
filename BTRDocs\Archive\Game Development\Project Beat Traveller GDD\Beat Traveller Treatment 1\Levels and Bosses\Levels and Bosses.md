# Levels and Bosses

Levels and Bosses:

[Structure Attempt 1](Levels%20and%20Bosses%20886141f0e43e466596d4deddb3298175/Structure%20Attempt%201%2068f95d87c5d84301b909e6155a4aeaf0.md)

## **Level: <PERSON><PERSON><PERSON>'s Rings - Earth Element**

Player encounters <PERSON><PERSON><PERSON>, a celestial being composed of numerous interlocking rings. As the player traverses through the rings, they must dodge and shoot to the rhythm of the music. Each ring contains a fragment of the Tower? Upon defeating <PERSON><PERSON><PERSON>, the Composer collects the first fragment and moves on to the next level.

Variations among each ring? 

Get to the center of the ophanim - what does this look like? 

Inside a cube - various eyes looking at you? 

Earth Element - symbolizes the solid, tangible foundation of the physical world. Thinking industrial, cities, anything more physical. societal constructions

**Areas**

**Ophanim Rings -** Metatron enemies as you traverse each ring

**Center Cube** - Eyes looking inward, shooting? Opening and closing, shooting in different directions to take them out

Repeat these several times? 

Several ophanim or several layers?

## **Level: Solar Flare Seraphim's Nebula - Fire Element**

Colorful, digital-looking nebula guarded by <PERSON><PERSON><PERSON>, a multiple tendril ling being. harnesses the power of the sun itself, using solar flares and coronal mass ejections as its weapons. It could have multiple stages, each one representing a different phase of the sun's activity, and players would have to dodge and weave through intense bursts while targeting weak points on the Seraphim's body

Volcanoes? 

[Seraphim Concept Images](Levels%20and%20Bosses%20886141f0e43e466596d4deddb3298175/Seraphim%20Concept%20Images%204d346e99a16a4a09a2e42e660f13e63f.md)

Fire element - Fire - Light, lasers. In the cosmic context, fire might represent the nuclear fusion processes that power stars, as well as the birth and death of celestial bodies, which lead to the creation of new elements and the constant transformation of the universe.

**Areas**

Tendrils of light you’re running along, slowly moving, covered in Metatron enemies

Lock on to tendrils to shoot them back at something, possible environment

Lock on and hold onto it long enough to enact some action

## **Level: Ouroboros' Space-Time Loop - Ether Element**

[Ouroboros Concept Images](Levels%20and%20Bosses%20886141f0e43e466596d4deddb3298175/Ouroboros%20Concept%20Images%201c110c8f9b994658a3e85b2727fd76ff.md)

The Seeker navigates a level designed like a Mobius strip, symbolizing the cyclic nature of life and death. In this level, the Composer encounters Ouroboros, a serpent-like creature consuming its own tail. The spacecraft is caught in the creature's eternal loop. The Composer must navigate the loop and dodge the serpent's attacks, using the time manipulation abilities to their advantage. After defeating Ouroboros, the Composer retrieves another fragment.

Ether (Quntessessence) Element ? 

the digital realm itself—the virtual space where all digital interactions and experiences take place. This could encompass the internet, virtual reality, and the abstract concepts and structures that underpin digital systems, such as algorithms, protocols, and software. In music, this could refer to the mathematical relationships and patterns that govern musical intervals, scales, and harmonics, which give rise to the structure and beauty of compositions.

These principles bring order and predictability to the seemingly chaotic vastness of space, allowing celestial bodies to interact and form complex systems.

Feature an hourglass?

Ouroboros is in the shape of infinity symbol and you move along a seeminly endless mobius strip

[How to make a Animated Mobius Strip in Blender](https://www.youtube.com/watch?v=pF91ExiRkko)

intelocking mobius strips, like a pit of snakes?

[Mobius Strips - Download Free 3D model by William Zarek (@bugbilly)](https://sketchfab.com/3d-models/mobius-strips-a3906ec3e14741e39547c523d3160dc7)

Enemies rewind time in this section

Boss

Bullets only work in reverse time - only damage boss then

Double headed snake? One takes damage in forward time, one in reverse time

Music communicates some idea but only when in reverse

**Areas**

You enter a bubble - reflective - and then within it becomes the white space universe

Rings of the Ouroboros using space / transparent shaders

![Untitled](Levels%20and%20Bosses%20886141f0e43e466596d4deddb3298175/Untitled.png)

![Untitled](Levels%20and%20Bosses%20886141f0e43e466596d4deddb3298175/Untitled.jpeg)

Becomes Amphisbaena? 

![Untitled](Levels%20and%20Bosses%20886141f0e43e466596d4deddb3298175/Untitled.webp)

## Level: **Void Behemoth - Air Element**

In the cosmos, air could symbolize the invisible forces that permeate the universe, such as gravity and dark matter, which, despite being imperceptible, have significant impacts on the behavior and formation of celestial bodies. This boss could be a monstrous, shadowy creature that lurks in the emptiness of space. It could attack by using its massive claws to swipe at the player and summoning waves of darkness to obscure the player's vision.

plunges players into a dark and enigmatic void, where cosmic objects and celestial phenomena are barely distinguishable from the surrounding darkness. Players must rely on their senses and intuition to navigate this disorienting realm, using sound and vibration to reveal hidden pathways and platforms. As they progress, players collect the scattered pieces of Metatron and face off against powerful enemies from the previous levels.

**Areas**

Variety of black holes - navigating around them 

possible inverse relations between light and sound

want some of one, less of other

Ambiguity of movement, shape, vessel may work here

Black hole as portals (like Portal)

egyptian gob medjed light outline of crazy creature, 

Boss - dark creature, light outline, just see its’ eyes

Creature at the end of Princess Mononoke for ref 

ENd of Evangelion adam - unit 1 - tendrils - etc

Chasing things, running on their trails

Move between several layouts of this?

## Level: World Tree Yggdrasil - Water Element

Data flows through networks, can change form (analog to digital, structured to unstructured), and has the ability to adapt to different environments, much like water adapts to the shape of its container. Data is also essential for the functioning of the digital world, as water is to life. In the cosmic context, water could represent the fluid dynamics observed in celestial phenomena, such as the flow of gases and liquids in the formation of stars and planetary systems.

**Areas**

Moving along the vines of the world tree

Boss

The Norns

[Norn | Megami Tensei Wiki | Fandom](https://megamitensei.fandom.com/wiki/Norn)

shin megami tensei nocturne moirae sisters

the three that is one

attached with thread

the norns water the roots

![Untitled](Levels%20and%20Bosses%20886141f0e43e466596d4deddb3298175/Untitled%201.png)

![Untitled](Levels%20and%20Bosses%20886141f0e43e466596d4deddb3298175/Untitled%202.png)

## Level ???: Metatron’s Cube - All Elements?  b

Player moves along the structure, not entirely obvious that it is the layout of Metatron’s Cube.

**Areas**

Moving along the structure of Metatron’s Cube, in multiple directions, turning and twisting. 

Titan's Colossus

When you land on Saturn's largest moon, you'd encounter a colossal machine that resembles the Ouroboros symbol. It would have multiple heads and limbs that could be destroyed one by one, and you'd need to avoid its crushing attacks while finding a way to disable its weapons.

Level: Rebirth of the Tower
The Composer rebuilds the Tower of Babel, unwittingly causing it to resonate with the cosmic rhythm. The heavens respond, shattering the Tower once more. The levels are remixed, and the Composer must revisit the previous levels, facing more powerful versions of the bosses while the celestial environment has become more hostile.

Possible remix of levels when the tower is built up and crashes again?

Maybe this is an addendum to the current game

---

---

---

GPT4 

Story Synopsis:
In a world where the Tower of Babel was constructed as a means to achieve perfect sound and music, an attempt to reach the divine, the tower's collapse has fragmented the essence of harmony across the land. The protagonist, a skilled musician and adventurer, embarks on a journey to reassemble the Tower of Babel and restore harmony to the world. The angel Metatron, shattered and scattered across the remnants of the tower, serves as a mysterious guardian who obstructs the protagonist's progress. Throughout the adventure, players uncover the cyclical nature of rebuilding and destroying the tower, and how this process continually reshapes music, culture, and human experience.

Level 1: The Ophanim's Resonant Rings
Upon entering the first level, players find themselves in a mesmerizing realm of floating crystalline rings, each emanating ethereal music. The Ophanim, celestial beings with multiple eyes and wheels, maintain the harmony of these resonant rings. As players progress, they must solve puzzles involving sound frequencies and the alignment of the rings to create pathways. Reaching the central chamber, they face the Ophanim's leader, who manipulates the rings to create powerful sonic attacks and defenses.

Level 2: The Seraphim's Luminous Domain
The second level transports players to a radiant landscape of crystalline spires, bathed in an otherworldly light. Guarded by the Seraphim, these angelic beings possess tentacle-like appendages made of light instead of fire. Players navigate a maze of shifting light beams, refracting through crystals, and creating illusions. In the heart of this level, the Seraphim's commander awaits, wielding an array of laser-like tentacles capable of slicing through the environment and creating intricate light-based attacks.

Level 3: The Oroboros' Temporal Sphere
In the third level, players enter a bubble of white space, where time and reality only exist within the Oroboros' Mobius strips and rings. The space within the bubble shifts and warps as players navigate the level, with pathways appearing and disappearing based on their perspective. Puzzles involve manipulating the rings and strips to create connections and synchronize the flow of time. At the center of the level, the Oroboros, a cosmic serpent, manifests as an ever-shifting entity that challenges players with a range of time-based attacks and illusions.

Grants 

Introduction tonthebgrant you have the right to have 

IRAP

There is a st John's location 

Innovation through r&d

Client engagement advisors - can discuss whether you're a fit with this

[Tech-access.ca](http://Tech-access.ca) 

Look into this

Ayming

Toronto office

Help with grants 

Cmf and unity , can export, Ontario creates

Assist with this

What is the 500 000 unity envelope? 

Unity humanity, get up to 50 k

Look up export ready stream from can export 

Any kind of programming or outside of sred work is research and applicable to this? Look into this, Serge Landry was talking about this

A program to get out of ei for entrepreneurs

 

Chloe giusti 

Maximizing success with I dat Xbox 

@

Froginus

Accessibility is a big aspect, platforms and accessing games in different ways

Qualified game creators - self publishing !

Get started with the GDK

Register with them to get hardware provided?

Online connectivity - Playable Party

Cloud requirements? ID@azure

How much for x cloud?

No work needed! But you can integrate touch control APIs and some other aspects 

Id@azure 

Game data and analytics - ook this up!

Use new releases on a Friday on Xbox cause you will stay there throughout the weekend 

Pitching to id

Speak to them early!

Share assets 

The less announced, the more flexibility for them

How does your game innovate? Why is it best in class?

Nail what makes it unique

Cleaned.messgaong on core gameplay mechanics, loop, engagement, 

Be confident about what makes your game cool!

Find your champion!

This means finding someone in the Xbox environment who can be your champion

Game studio, marketing , or wherever! 

Research the platform - see this as why this fits so well on the platforn

Ask questions and tell us what you need!

Look on xbox’s accessibility highlight !

Can register on the site and submit all game info there

Approval process is by their internal portfolio team

<EMAIL> for developer acceleration 

Unity tools workshop

Unity Build Presets

- Abandoned currently but works in 2021 with tweaks
- Reduces environment switching to a single click
- 

Game back end and cloud save options 

Cloud code - server hosted logic or data tables 

Remote config and game overrides 

Test changes 

May apply for updating logic

Look into game ovberises for performance choices 

Also double XP weekends, all sorts of options to rebalance the game easily

 Can define things with JSON

Ineditor remote config options panel is ppssoppssoble

What kind of build automation access do I have?

**Mechanics Ideas**

Ophanim Level:

1. **Filters and Effects (Synthesizer/Ableton Live):** The reverb effect could cause the Ophanim rings to ripple or distort, creating echoes of the rings that the player needs to traverse and dodge. Delay could slow down the movement of Metatron enemies, giving players more time to aim and shoot. Phaser could warp the rings, making them twist and turn unexpectedly.
2. **DJ Scratching (DJ Mixer):** As players traverse the rings, they could use this mechanic to rewind their position to avoid an incoming attack or fast-forward to reach a specific ring faster.
3. **Sound Waves (Synthesizer):** Attacks could generate sound waves that resonate with the rings of the Ophanim, causing them to shift and move. Different waveforms could correspond to different types of ring movements and effects. - need to do multi lock on attacks to damage each ring with a shock wave
4. **Pitch Shift (Ableton Live/Synthesizer):** This mechanic could affect the vertical position of the player within the rings. Higher pitch could make the player ascend within the rings, while lower pitch could cause descent. - Modulate track on each ring
5. **Cue Points (DJ Mixer/Ableton Live):** Players could set cue points at specific rings or positions, allowing them to quickly return to these points to dodge incoming attacks or traverse rings faster.
6. **Sound Isolation (DJ Mixer):** This mechanic could allow players to expose hidden paths or weaknesses in enemies by isolating specific musical frequencies. The closer the player is to the center of the Ophanim, the more frequencies are available to isolate.

Solar Flare Seraphim's Nebula 

1. **Filters and Effects (Synthesizer/Ableton Live):** Phaser could be particularly interesting in this level - by activating the phaser power-up, the game space could become warped, altering the paths of solar flares or causing coronal mass ejections to twist and turn.
2. **Time Stretching (Ableton Live/DJ Mixer):** In moments of intense solar activity, players could use time stretching to slow down the onslaught and weave through solar flares and tendrils with precision.
3. **DJ Scratching (DJ Mixer):** This mechanic could be used to rewind time and dodge an unexpected solar flare or to fast-forward when traversing a calm segment.
4. **Sound Waves (Synthesizer):** Each attack could generate sound waves that resonate with the tendrils of light. Different waveforms could cause different reactions - for example, a sine wave might cause the tendril to move gently, while a square wave could make it move in sharp, abrupt angles.
5. **Pitch Shift (Ableton Live/Synthesizer):** In the nebula, pitch shifting could alter the player's altitude. Higher pitches could propel the player upwards into safer zones or to dodge attacks, while lower pitches could make them dive through the tendrils.
6. **Sync (DJ Mixer):** The rhythmic pulsing of solar flares and the celestial music could serve as a rhythm to sync with, allowing for powerful synced attacks or quick evasion maneuvers.
7. **Warp Modes (Ableton Live):** The player could use warping to distort space, helping to dodge solar flares or to travel through the tendrils more swiftly.
8. **Granular Synthesis (Synthesizer/Ableton Live):** Players could break down their abilities into grains to form a defensive shield or to construct a concentrated beam of energy to cut through the tendrils.
9. **Sends and Returns (Ableton Live):** The player could fire an attack that then returns after a time, possibly sweeping across the screen to clear out enemies or divert incoming solar flares.
10. **Sound Isolation (DJ Mixer):** This mechanic could allow players to "tune in" to the frequency of the solar flares or the tendrils, revealing patterns or creating safe zones that help them navigate the level.

These mechanics add a strategic element to the game and make the most out of your solar-themed level, but their success will depend on the actual implementation and balancing. Playtesting is crucial to fine-tune these mechanics for the best player experience.

The Void Behemoth level with its elements of obscurity, invisibility, and disorientation provides a lot of space for unique mechanics that could enhance the sensory experience of the player. Here are how some of your proposed mechanics could be adapted:

1. **Filters and Effects (Synthesizer/Ableton Live):** The phaser effect could cause the void to ripple and warp, revealing hidden paths or distort enemy attacks. Reverb could amplify the sounds in the void, causing echoes that help reveal the position of hidden objects or enemies.
2. **Sampling (Ableton Live):** In this level, sampling could allow players to absorb the powers of defeated shadow enemies, using their abilities to manipulate the void and gain an advantage against the boss.
3. **Sound Design (Synthesizer):** Given the ambiguity of the void, players could create unique sounds that illuminate the surroundings or reveal hidden enemies. Different sound parameters could have varied effects on the environment, for instance, a high-frequency sound could light up a large area but attract more enemies.
4. **Time Stretching (Ableton Live/DJ Mixer):** In the dark environment, time stretching could slow down the speed of attacks from the shadowy creature, giving the player more time to dodge or counter.
5. **DJ Scratching (DJ Mixer):** This mechanic could be used to rewind the level slightly, giving the player a chance to correct a mistake, or fast-forward through relatively calm segments.
6. **Sound Waves (Synthesizer):** Each attack could generate a unique sound wave that reveals part of the void environment or disturbs the enemies. For instance, a sawtooth wave might cause a sharp disruption in the enemy's behavior.
7. **Pitch Shift (Ableton Live/Synthesizer):** Shifting the pitch could manipulate the player's perception of the void, causing illusions that disorient enemies or reveal hidden paths.
8. **Sync (DJ Mixer):** Players could need to "sync" their actions with the ambient sounds of the void to maximize their attack power or avoid the void creature's attacks.
9. **Warp Modes (Ableton Live):** The player could use warping to distort the void, altering the enemy's attacks or bending the paths around black holes.
10. **Granular Synthesis (Synthesizer/Ableton Live):** By breaking their abilities into grains and rearranging them, players could customize their tools to suit the ever-changing environment of the void.
11. **Sound Isolation (DJ Mixer):** In the nebulous void environment, isolating specific sound frequencies could reveal the hidden void creature or unmask obscure pathways.

As always, balancing and testing these mechanics in the context of the level and overall game will be critical to creating a cohesive and engaging player experience.

**General Ideas**

Powerful attacks could cause a temporary decrease in the player's speed, symbolizing the exertion of the attack. This would force players to time their powerful attacks wisely.

 Players could customize the sound of their weapons to match the high energy and intensity of the level. A powerful bassy weapon sound could be used to create shockwaves that disrupt solar flares or destabilize tendrils.