# Graphics Ideas

This dev - lakov

[http://www.joosteggermont.nl/](http://www.joosteggermont.nl/)

slit screen effects made through Amplify Shader Editor

- movement is a result from scripted tweens and the shader
- the vertex positions are being rounded in shader.

[https://twitter.com/aJoostEggermont](https://twitter.com/aJoostEggermont)

[https://twitter.com/aJoostEggermont/status/1352359248269869057](https://twitter.com/aJoostEggermont/status/1352359248269869057)

Breakbeat / DnB fast rhythms applied to enemies so they move rhythmically

think spike FFT sphere's and such

Think Floating Points style music. EX. Last Bloom

[https://www.youtube.com/watch?v=PjZLIiupIsQ](https://www.youtube.com/watch?v=PjZLIiupIsQ)

This video as influence? Percussion elements intertwined with generative enemy system possibly?

Bass hits, enemies generated across the screen in a horizontal line?

LOVE the idea of changing colour palettes influenced by back ground here. 

BASIC, but two / three colours important to visual aesthtetic

Use Video as background or generate visual in unity? Unsure at this point 

Mesh simplifier - reduce complex models - make adaptable for game?

[https://assetstore.unity.com/packages/tools/utilities/poly-few-mesh-simplifier-and-auto-lod-generator-160139](https://assetstore.unity.com/packages/tools/utilities/poly-few-mesh-simplifier-and-auto-lod-generator-160139)

---

[https://www.behance.net/gallery/115521269/Typographic-Exploration?tracking_source=search_projects_recommended](https://www.behance.net/gallery/115521269/Typographic-Exploration?tracking_source=search_projects_recommended)

Anything [Are.na](http://are.na) ???

Houdini useful?

[https://www.reddit.com/r/Unity3D/comments/mamdfe/modular_wall_feature_in_houdini_plugin_for_unity/](https://www.reddit.com/r/Unity3D/comments/mamdfe/modular_wall_feature_in_houdini_plugin_for_unity/)

ART DIRECTION / CREATIVE DIRECTOR

How to decide? How to settle on something?

3D Animation Tool

[https://deepmotion.com/animate-3d](https://deepmotion.com/animate-3d)

Collage based visuals? Vibrant Matter ex. Look up more club visuals. flat panes/2d collage? more sample based triggering flashing visuals?

Evangelion UI

[https://www.youtube.com/watch?v=brpKGTnkVdI](https://www.youtube.com/watch?v=brpKGTnkVdI)