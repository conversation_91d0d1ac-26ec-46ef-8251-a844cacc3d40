# Nov 30th

**Organizing Links**

[Digital Divinity](https://restofworld.org/series/digital-divinity/)

[Game UI Database - Mirror's Edge](https://gameuidatabase.com/gameData.php?id=815)

Events

[Indie Superboost: The Do's and Dont's of Self Publishing w <PERSON>](https://www.eventbrite.ca/e/indie-superboost-the-dos-and-donts-of-self-publishing-w-lee-vermeulen-tickets-1079697216439?aff=ebemoffollowpublishemail&ref=eemail&utm_campaign=following_published_event&utm_content=follow_notification&utm_medium=email&utm_source=eventbrite)

[Awkward First Date Tours | Art Gallery of Ontario](https://ago.ca/events/awkward-first-date-tours)

[AV Live: Future Underground | Art Gallery of Ontario](https://ago.ca/events/av-live-future-underground)

Unity Tech Tutorials

[Build Your Own Dependency Injection in less than 15 Minutes |  Unity C#](https://www.youtube.com/watch?v=PJcBJ60C970)

[PracticAPI](https://www.youtube.com/@practicapiglobal/videos)

[Code Like a Pro: Refactoring to Patterns!](https://www.youtube.com/watch?v=OnlR4TczIPY)

**VFX**

[VFX Graph Learning Templates | Tutorial](https://www.youtube.com/watch?v=DKVdg8DsIVY)

[EricWang(Unity VFX Artist) - BOOTH](https://ericwang.booth.pm/)

[Eric Wang_VFX Artist](https://www.youtube.com/@EricWang0110)

[Mirza Beig on Twitter / X](https://x.com/TheMirzaBeig/status/1836885186560118940)

[I made this waterfall effect without any textures. It inherits colours from animated lights.](https://www.reddit.com/r/Unity3D/comments/1g3dk31/i_made_this_waterfall_effect_without_any_textures/)

[Create advanced visual effects in VFX Graph: Decals | Unity](https://www.youtube.com/watch?v=nqhkB8CG8pc)

Design Knowledge

[Featured Blog | The art of game balance: evolution](https://www.gamedeveloper.com/design/the-art-of-game-balance-evolution)

[Naming Things](https://www.namingthings.co/)

VFX Inspo

[Ishmael - The Merciful One - Beta Movesets Showcase - Punishing Gray Raven CN](https://www.youtube.com/watch?v=BBRKQM-JCiA&t=116s)

Assets

[https://github.com/EduardMalkhasyan/Serializable-Dictionary-Unity/releases](https://github.com/EduardMalkhasyan/Serializable-Dictionary-Unity/releases)

[https://www.flexalon.com/templates?utm_source=fxmenu](https://www.flexalon.com/templates?utm_source=fxmenu)

[https://github.com/johanhelsing/UniSdf](https://github.com/johanhelsing/UniSdf)?

[https://github.com/DumoeDss/AquaSmoothNormals](https://github.com/DumoeDss/AquaSmoothNormals)

Black Friday Deals

[Galaxy Materials (Skybox Update)](https://assetstore.unity.com/packages/vfx/shaders/galaxy-materials-skybox-update-191773)

[Stylized Shoot & Hit Vol.1](https://assetstore.unity.com/packages/vfx/particles/stylized-shoot-hit-vol-1-216558)

[Creative Lights](https://assetstore.unity.com/packages/vfx/particles/fire-explosions/creative-lights-282858#reviews)

[GIBLION 2024: Anime-NPR-Toon Framework](https://assetstore.unity.com/packages/tools/level-design/giblion-2024-anime-npr-toon-framework-299579)

[Sci Fi Hologram Shader](https://assetstore.unity.com/packages/vfx/shaders/sci-fi-hologram-shader-170106)

[Stylized Shoot & Hit Vol.2](https://assetstore.unity.com/packages/vfx/particles/stylized-shoot-hit-vol-2-222939)

https://assetstore.unity.com/publishers/60810

Tech Knowledge

[Unity Custom SRP](https://catlikecoding.com/unity/custom-srp/)

Render Graph

[Video Conferencing, Web Conferencing, Webinars, Screen Sharing](https://unity3d.zoom.us/rec/play/7RuwCLk-1B8xnUjpUzpPRqfzkM_745tUQaikPJHDui3CBizLG-yBPJ_r1_uRfVaEScNRBHipqx-eFxgk.NsemXUQjLJVwDZvr?canPlayFromShare=true&from=share_recording_detail&continueMode=true&componentName=rec-play&originRequestUrl=https%3A%2F%2Funity3d.zoom.us%2Frec%2Fshare%2FiOwgx3jdHqpgG62-GN4Mhp0ZRTyoqMyP_O4MvuyePD2YH_sxAor7dAoGlqTm9ZTm.iGnJ6Xwi1ftRkcqb)

Procedural 

[Procedurally Generating plant stems](https://www.fraculation.com/blog/trunk-generation/)

Work - Osler

https://www.reddit.com/r/PowerApps/comments/1gtw7ul/admin_access_best_practice/

https://www.youtube.com/playlist?list=PLcwrIWK7WBcRHasuKkgh0BPySO9vuD_wX

https://www.reddit.com/r/PowerPlatform/comments/1guineg/road_to_power_platform_developer_questions/

https://pragmaticworks.com/resources/cheat-sheets

https://pcf.gallery/sendgrid-emailer/

https://www.reddit.com/r/PowerApps/comments/1gmcqq8/how_to_learn_advance_concepts_in_powerapps/

https://www.youtube.com/@powerdynamite365/videos

https://www.youtube.com/watch?v=vTraPvXMvRo

https://www.reddit.com/r/PowerApps/comments/1gu2z7w/courses/

[Codebase Restructure](Nov%2030th%2014effc7f811f80c0b45fd3b1a40c574d/Codebase%20Restructure%2014effc7f811f801688fbfa0faeb9dc2e.md)