# May 5

After some tehcnical issues- back in action!

Some bugfixing needs to be done. 

**Navmesh Test Scene - Ouroboros 3 - More complex Behaviors**

<aside>
💡 Bug!

</aside>

Character not rotating properly? or animation not changing properly? 

- Appears to fix itself, but slowly. Issue occurs when in total backwards view, character veers in a different direction, to the side a bit. Not an animation Issue
- Suspect this is related to script which tilts player chracter according to ground position, need to verify

Need to free up the Y value of the camera - cant look up enough. Important in this level especially 

Trying this with Cinemachine Camera Blending

Never found a good way to do it, trying to just script it all now 

Adjust elements to be the scaled down size of this scene as well. Many things were made for a larger scale - radar, projectile calculations, etc

<aside>
💡 Bug!

</aside>

**Ground check** doesn’t appear to be altering the height of the player game object - look into this 

Also only operation on forward facing at the moment. Caused problems when in reverse, need to program this to work in all four directions. 

**CameraFollow -vcam1 script**

Made some improvements to the camera system, can look around more freely, removed jittery aspect of that. 

Added a zoom in and out as the reticle looks up and down, but needs to be fine tuned, a bit wonky at the moment but usable. 

Made an ouroboros version of bullet, but needs fine tuning for this scale. Still wraps around enemies aimlessly at times. 

Need to fix enemies to navmesh better, waiting on response from Aron for this, but may hack together my own solution to do appropriate testing.