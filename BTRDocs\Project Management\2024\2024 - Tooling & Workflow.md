

**January 10th:**

- Trying to do a build of a scene, upgrading things and moving to laptop seem to bring up old issues

**January 15th:**

- Many Links!
    - Primarily focused on Game Dev links related to tooling, shader graph, github repos for optimization, and other asset store tools.

**January 17th:**

- Need to find a way to reduce build times - Assembly Definitions? Look into it

**Feb 1st:**

- Also good blender editing tool possibly 0 Quad Remesher - already installed
- Look into Quadriflow Remesh as well?

**Feb 13:**

- Spatial Communication in Level Design
- Practical generators for blender
- 3 Game Programming Patterns WE ACTUALLY NEED.
- In game settings
    - [https://github.com/xZenvin/UnitySettingsFramework](https://github.com/xZenvin/UnitySettingsFramework)?

**Feb 23:**

- Updated Amplify Shader Editor
- Need to investigate Leak issues
- Created Kanban board to be able to log and address issues easier

**Feb 28:**

- Editing Animations 
- Umotion Notes (UMotion)
- Copy to other side tool - good for adjustment of mirror things!
- Editing Mesh 
- Trying to edit mesh in Blender, Skinned Mesh proving troublesome

**March 6:**

- Unity Profiler Walkthrough & Tutorial

**March 12:**

- Prefab painter should be able to do this?

**March 15:**

- Figured out A Star Debug messages being A LOT - change logging level in A* object

**March 21:**

- Use Object Particle Spawner or adapt Uni Bullet Hell into 3D?

**March 22:**

- Fixed this! It was the order Buto was renderer. Was kind of cool, worth considering for a level
    - SIDENOTE
    - I need to consolidate these input chekcs

**March 30:**

- May need to rebuild volume to get Post Process going

**April 22:**

- Very Cool GDC Talks
    - How to Make Things "POP" with Audio and Color

**May 2, 2024:**

- Need to pick a day to sort through particle effects!

**May 7, 2024:**

- May need to assess UI - How should it exist within each scene? Prefab? Singleton?
    - Load a scene additively as pause menu? Look into this - interesting
        - [Unity - Scripting API: SceneManager](https://docs.unity3d.com/ScriptReference/SceneManagement.SceneManager.html)

**May 8, 2024:**

- Also trying to rewrite my reload scene script - want thing working better - fails currently, may not be easily solvable. May not be worth it!

**May 13, 2024:**

- VS 2019 warning - need upgrade

**May 17, 2024:**

- Look at some of the Jason Booth optimizations to apply to my project.

**June 18, 2024:**

- Strangely, performance is showing a locked 60 and i hav no ida why. there are dips but gnereally it’s at 60, seems strange to me. Requires investigating.
**June 18, 2024:**

- Replacing dotween with primetween, may be better. taking a look!
- Adding this to update method of projectiles in attempt to get them to respond to time changes more effectively, but doesnt appear to be working - look more into the clock part

**July 15, 2024:**

- normal / transparent rendering
    - Only have one? Find a simpler way to do this?

**July 29 30:**

- Setup GIt for this project again

**August 12, 2024:**

- Converting some DOtWeen to Prime Tween 
- Code base scan showing these potential issues 

**August 14, 2024:**

- Thoughts on sound design
- Bookends and Highway method
- Use FilterFreak and Speakerphone for processing your SFX
- Plan creatures as Blue / Red - Soft / Angry
- Pre-attack sounds
- design by making the first bookend (front)
- Copy it and it can be your pre attack

**August 30, 2024:**

- Some recommendations on Game Design
    - GDC talks
    - Designer Notes Podcast
    - Google Scholar
        - games and culture
        - gamestudies.org
    - Abstract - The Art of Design

**Sept 1, 2024:**

- Integrating proper death → restart to loop
    - Split Game Manager, Crosshair, Projectile Manager into many classes.

**Oct 2, 2024:**

- Rough overview of things (top of head)
    - Tim Cain videos
    - Design Docs

**Oct 14, 2024**

- Implemented Jobs system for projectile collisions
- Using Particle system Manager to better track and optimize any large amounts of particle systems 
- Need to assess if using VFX graph will solve the problem.

**Oct 21, 2024**

- updating Cinemachine 3 camera 
- Using  Feell.SO is better with the cimachine 3

**Nov 4, 2024**

- Was trying to build ECS effects system

**Nov 27, 2024**

- VFX: Need to develop how to implement visual effect
- Digital Layer Effect: Try implement the method to achieve this.

**Nov 30, 2024**

Organizeing useful links
-VFX graph leanring templates
- Unity Custom SRP

