# Feb 7

Overview of Coding tips for Games

[https://www.youtube.com/watch?v=8Hy4JvtfUb8&list=WL&index=4](https://www.youtube.com/watch?v=8Hy4JvtfUb8&list=WL&index=4)

- Read up on State Machines?
- Large Classes - split things out as you’re moving forward
    - Consider how to separate
- Everything is Public! That’s not good
    - Spagetti code issues
        - everything referencing everything else in a way that is confusing to follow
        - really think about if it needs to be public - private by default
        - is there a better way to modify without being public
- Having setters with side effects - ex: Damage method
    - Damage calls out several method that does network calls (slow)
    - We dont realize as we use Damage that we are also doing lots of network calls
        - could be doing those calls collectively and not individually
    - Would accidentally cause performance problems, and hides complexity
    - **ADVICE** Have setters do nothing special, only set. Make a set method for special things
- Giant Prefabs
    - hard to save, use functionality, work with other people, etc
    - Split them up!
    - Use nested prefabs
    - Use prefab variants
        - Can swap out components or visuals easily (enemies I suppose?)
- Not using Interfaces
    - Get into it!
    - Ex. of Damagable Interface as useful example
- Ignoring Garbage Collection
    - Get into the habit of investigating garbage allocations every so often
- Not sharing work!
    - Let other people see your work
    - Look at other people’s work!
    

Prominent current issues overview to reach playable state

- A* Pathfinding not working when moving
    - posted in forum
- Ultimate Spawner waves not regening
- Bullets spinning in circles / general behavior issues
    - Have some leads for this in previous notes
    

Playing with some lighting and camera settings

Tunnel Light as Sun in Fog Camera Settings

Investigating A* Issue for a bit

- Adjusting paramters to see if any effect movement
    - Max Slope - NO

**FIX FOUND I THINK**

Rigidbody is being used for physics, and if set to interpolate it fixes things!

Next big issue - wave spawning

- Not sure if i’ve locked on to target, need to fix this to ease testing
- Fixed but trouble even locking targets - making new rayspawn to see specifically for this to see if it helps

FIxing Lock On system 

![Untitled](Feb%207%20594206d95a7f43e4b370fd920e865e55/Untitled.png)

![Untitled](Feb%207%20594206d95a7f43e4b370fd920e865e55/Untitled%201.png)

This issue keeps turning up

I believe issue is, if previous enemy is killed, then there is still  target.  So it tries to set this on enemy that is inActive, messes up. 

Cleaned this up. Maybe dont need a seperate Ray for this? Leaving it for now 

Seems to work! Visually need a more present LOCKED ON representation though

Bug Found - Player Character lost on turning - due to new camera movement that’s been added

Fixing character lost issues

Need to follow in Scene View! [https://www.youtube.com/watch?v=4sUXxaXlYY8](https://www.youtube.com/watch?v=4sUXxaXlYY8)

its all in localMove of playerMovement - dig into this deeper

---

Slack Updates thoughts

[https://www.gameuidatabase.com/index.php?scrn=47&scroll=141](https://www.gameuidatabase.com/index.php?scrn=47&scroll=141)

Aiming and targetting