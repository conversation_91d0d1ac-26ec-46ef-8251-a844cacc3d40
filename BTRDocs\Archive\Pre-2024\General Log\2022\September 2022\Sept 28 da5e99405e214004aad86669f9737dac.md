# Sept 28

Had a meeting with <PERSON> yesterday

Some takeaways on developing Beat Traveller

> 
> 
> 
> Get back into the project
> Make updates on what's happening,
> walk through ideas of what needs to be done
> do i have a good work environment for this project right now?
> what might i need to work on to advance things?
> 

Made mechanics demonstration video

Outlining current mechanics of the game

Takeaways

- Adjusted Lock / Shooting sound to LOUDER
    - Bringing more musical elements to the forefront
- Need different sound for absorbing projectiles
- Lots of room to play with scale / melody in the shooting / lock on sounds. Make some decisions here, decide how it elevates / changes between sections. This could be a good way of colouring things differently as the game evolves / the player progresses.
- When bullets are fired from player, locked on to a target, they will still move backwards in time.
    - Do I want this? Could be fun for setting up multiple locked targets
    - Currently this seems to introduce a bug where the bullets could die before they reach the target

Have basic ideas of Rez environment / digital world. Need to really pick at that more and make some strict choices about the the world the game inhabits. 

What games do I see existing in a similar camp?

Thumper, Rez, Rollerdrome, Superhot, 

Mechanical mostly - Neon White, Boomerang X

Hyper Demon

Shadow of the Colossus - simplicity of story, small bottle tale

I like things implied by environment more than outright said / dialogue

Mood board 

[https://www.figma.com/file/INP1bi0nvSmmcCCu4RVie2/Game-Design-Mood-Board?node-id=0%3A1](https://www.figma.com/file/INP1bi0nvSmmcCCu4RVie2/Game-Design-Mood-Board?node-id=0%3A1)