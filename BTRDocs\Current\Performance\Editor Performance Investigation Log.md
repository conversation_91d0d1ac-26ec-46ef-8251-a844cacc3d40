# Editor Performance Investigation Log

## Issue Description

- **Problem**: Massive editor performance spikes (65ms+ frame times, 15FPS)
- **Symptoms**: CPU usage hitting 54.3% on EditorLoop, frequent lag spikes
- **When Started**: User reports this wasn't always the case
- **Impact**: Makes Unity editor nearly unusable

## Investigation Timeline

### Session 1 - Initial Analysis (Current)

#### Potential Culprits Identified

1. **VTabs Plugin** ⚠️ HIGH PRIORITY

   - **Status**: FIXED - Disabled assembly definition + Fixed compilation errors
   - **Evidence**: Multiple EditorApplication.update callbacks registered
   - **Action Taken**:
     - Renamed VTabs.asmdef to VTabs_DISABLED.asmdef
     - Fixed namespace conflicts in MeshPrefabPlacerEditor.cs and EnemyConfigurationEditor.cs
   - **Result**: READY FOR TEST

2. **GPU Instancer Pro Demo Components** ⚠️ MEDIUM PRIORITY

   - **Status**: IDENTIFIED - Need to check if user has these in scenes
   - **Evidence**: ExecuteInEditMode with Update loops
   - **Components**: GP<PERSON>NoGOUpdates, GPUINoGOUpdatesCompute, GPUINoGOPrefabDrawer
   - **Action Needed**: Check current scenes for these components

3. **PrefabReplacer Script** ✅ FIXED

   - **Status**: FIXED - Disabled ExecuteInEditMode
   - **Evidence**: ExecuteInEditMode with Update loop
   - **Action Taken**: Commented out [ExecuteInEditMode] attribute
   - **Result**: Should reduce some overhead

4. **FMOD OnGUI Helper** ✅ RULED OUT
   - **Status**: NOT THE ISSUE
   - **Evidence**: FMOD overlay disabled in settings (Value: 0)
   - **Action**: No action needed

#### Ruled Out (Not Active/Not the Issue)

- **FMOD Debug Overlay**: Confirmed disabled in FMODStudioSettings.asset
- **Altos Sky Director OnGUI**: Need to verify if user has this component active
- **Third-party asset InitializeOnLoad**: Previous optimization guide shows this was already addressed

#### Tools Created

- **EditorPerformanceMonitor.cs**: Created monitoring tool to track performance issues
- **Features**: Real-time frame time monitoring, performance issue logging, culprit detection

## Next Steps (Priority Order)

### Immediate Actions Needed

1. **Test VTabs Fix**: Restart Unity and check if performance improves
2. **Scene Audit**: Check current scene for GPU Instancer demo components
3. **Verify Altos**: Check if AltosSkyDirector has enableDebugView = true
4. **Run Performance Monitor**: Use new tool to identify remaining issues

### If Issues Persist

1. **Deep Scene Analysis**: Look for other ExecuteInEditMode scripts in current scene
2. **Custom Inspector Audit**: Check for heavy custom inspectors causing repaints
3. **Asset Import Analysis**: Check if asset importing is causing spikes
4. **Third-party Plugin Review**: Audit other plugins for editor callbacks

## Testing Protocol

### Before/After Measurements

- **Before Fix**: 65ms frame times (15FPS), 54.3% CPU on EditorLoop
- **After VTabs Fix**: ✅ MUCH BETTER - User confirmed improvement
- **After AudioMemoryOptimizer ZLogger Fix**: PENDING TEST

### Performance Targets

- **Target Frame Time**: <16ms (60FPS)
- **Target CPU Usage**: <20% on EditorLoop
- **Acceptable Spikes**: Occasional <30ms spikes during compilation

## Known Working Solutions (From Previous Optimization)

- ✅ Fast Enter Play Mode enabled
- ✅ Domain reload disabled
- ✅ Excessive logging wrapped in conditional compilation
- ✅ ZLogger minimum log levels optimized
- ✅ Heavy singleton initialization optimized

## Investigation Notes

### VTabs Plugin Analysis

- **File**: Assets/vTabs/VTabs.cs
- **Issue**: Lines 1710-1711 register EditorApplication.update callbacks
- **Impact**: Multiple editor callbacks running continuously
- **Fix Applied**: Disabled entire assembly via asmdef rename

### GPU Instancer Analysis

- **Demo Components**: Found in Packages/com.gurbu.gpui-pro/Runtime/Scripts/DemoComponents/
- **Issue**: [ExecuteInEditMode] with Update() methods
- **User Status**: UNKNOWN - Need to check if user has these in scenes
- **Potential Impact**: High if present in scene

### Performance Monitoring

- **Tool Created**: EditorPerformanceMonitor.cs
- **Location**: Assets/\_Scripts/Editor/
- **Features**: Frame time tracking, issue detection, culprit identification

## Action Items for Next Session

- [x] Restart Unity and test VTabs fix - ✅ SUCCESSFUL
- [x] Apply ZLogger optimizations to AudioMemoryOptimizer - ✅ COMPLETED
- [x] Apply ZLogger optimizations to MovementStrategy - ✅ COMPLETED
- [x] Apply ZLogger optimizations to EnemyShootingDiagnostic - ✅ COMPLETED
- [ ] Test all ZLogger performance improvements
- [ ] Run EditorPerformanceMonitor to get baseline
- [ ] Check current scene for GPU Instancer demo components
- [ ] Verify no AltosSkyDirector with debug enabled
- [ ] Document final results and performance improvement

**Note**: CombatStrategy already had ZLogger implemented and was not causing performance issues.

## Additional Fixes Applied

### AudioMemoryOptimizer ZLogger Optimization ✅ COMPLETED

- **Status**: FIXED - Applied same ZLogger pattern as ProjectileManager
- **Issue**: 16 Debug.Log statements causing string allocation performance issues
- **Action Taken**:
  - Added Microsoft.Extensions.Logging using statement
  - Added ILogger field with LoggerProvider.CreateLogger<AudioMemoryOptimizer>()
  - Added enableDebugLogs configuration field
  - Wrapped all Debug.Log calls in conditional compilation (#if UNITY_EDITOR || DEVELOPMENT_BUILD)
  - Replaced Debug.Log with \_logger?.LogDebug for info messages
  - Replaced Debug.LogError with \_logger?.LogError for errors
  - Replaced Debug.LogWarning with \_logger?.LogWarning for warnings
- **Result**: Zero-allocation logging, massive performance improvement expected

### MovementStrategy ZLogger Optimization ✅ COMPLETED

- **Status**: FIXED - Converted remaining Debug.Log statements to ZLogger
- **Issue**: 5 Debug.Log statements causing string allocation performance issues
- **Action Taken**:
  - Wrapped Debug.Log calls in conditional compilation (#if UNITY_EDITOR || DEVELOPMENT_BUILD)
  - Replaced Debug.Log with \_logger?.LogDebug for movement events
  - Used structured logging parameters for better performance
- **Result**: Zero-allocation logging for movement diagnostics

### EnemyShootingDiagnostic ZLogger Optimization ✅ COMPLETED

- **Status**: FIXED - Applied comprehensive ZLogger optimization
- **Issue**: 23 Debug.Log statements causing massive string allocation performance issues
- **Action Taken**:
  - Added Microsoft.Extensions.Logging using statement
  - Added ILogger field with LoggerProvider.CreateLogger<EnemyShootingDiagnostic>()
  - Added enableDebugLogs configuration field
  - Wrapped all Debug.Log calls in conditional compilation
  - Converted all diagnostic logging to structured ZLogger calls
  - Maintained context menu functionality with optimized logging
- **Result**: Zero-allocation logging for shooting diagnostics, major performance improvement expected

## Success Criteria

- Editor frame time consistently <20ms
- No more 65ms+ spikes during normal editing
- Smooth editor experience restored
- Performance monitoring tool available for future issues
