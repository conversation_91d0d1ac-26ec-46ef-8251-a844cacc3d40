using UnityEngine;
using BTR.EnemySystem;

namespace BTR.EnemySystem
{
    /// <summary>
    /// Base class for all combat strategies in the new entity system.
    /// Provides common combat functionality and target management.
    /// </summary>
    public abstract class CombatStrategy : BaseStrategy
    {
        // Removed ZLogger - using Unity Debug.Log instead
        [Header("Combat Strategy Settings")]
        [ReadOnly][SerializeField] protected float attackRange = 10f; // Overridden by configuration
        [ReadOnly][SerializeField] protected float attackCooldown = 1f; // Overridden by configuration
        [ReadOnly][SerializeField] protected bool requiresLineOfSight = true; // Overridden by configuration
        [ReadOnly][SerializeField] protected LayerMask obstacleLayerMask = -1; // Overridden by configuration

        // Target management
        protected Transform currentTarget;
        protected Transform playerTransform;

        // Combat state
        protected float lastAttackTime;
        protected bool inCombat;
        protected bool canAttack;

        // Combat properties
        public virtual bool CanAttack => canAttack && IsActive && Time.time >= lastAttackTime + attackCooldown;
        public virtual bool InCombat => inCombat;
        public virtual float AttackRange => attackRange;
        public virtual float AttackCooldown => attackCooldown;
        public virtual Transform CurrentTarget => currentTarget;
        public virtual float LastAttackTime => lastAttackTime;

        // Combat events
        public event System.Action<Transform> OnTargetAcquired;
        public event System.Action<Transform> OnTargetLost;
        public event System.Action OnAttackStarted;
        public event System.Action OnAttackCompleted;
        public event System.Action OnCombatEntered;
        public event System.Action OnCombatExited;

        protected override void PerformInitialization()
        {
            // Find player reference
            FindPlayer();
            // Using Unity Debug.Log for logging

            // Initialize combat state
            lastAttackTime = 0f;
            inCombat = false;
            canAttack = true;

            // Call subclass initialization
            PerformCombatInitialization();

#if UNITY_EDITOR || DEVELOPMENT_BUILD
            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Combat strategy initialized - Range: {attackRange}, Cooldown: {attackCooldown}");
            }
#endif
        }

        protected override void PerformActivation()
        {
            // Reset combat state
            lastAttackTime = 0f;
            inCombat = false;
            canAttack = true;

            // Set initial target
            if (playerTransform != null)
            {
                SetTarget(playerTransform);
            }

            // Call subclass activation
            PerformCombatActivation();
        }

        protected override void PerformDeactivation()
        {
            // Clear combat state
            inCombat = false;
            canAttack = false;

            // Call subclass deactivation
            PerformCombatDeactivation();
        }

        protected override void PerformCleanup()
        {
            // Clear target
            currentTarget = null;

            // Clear events
            OnTargetAcquired = null;
            OnTargetLost = null;
            OnAttackStarted = null;
            OnAttackCompleted = null;
            OnCombatEntered = null;
            OnCombatExited = null;

            // Call subclass cleanup
            PerformCombatCleanup();
        }

        protected virtual void Update()
        {
            if (!IsActive)
            {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs && Time.time % 10f < 0.1f) // Log every 10 seconds
                {
                    Debug.LogWarning($"[{GetType().Name}] Update called but strategy is not active!");
                }
#endif
                return;
            }

            // Debug that Update is being called
#if UNITY_EDITOR || DEVELOPMENT_BUILD
            if (enableDebugLogs && Time.time % 8f < 0.1f) // Log every 8 seconds
            {
                Debug.Log($"[{GetType().Name}] Update running - IsActive: {IsActive}");
            }
#endif

            UpdateCombatLogic();
        }

        protected virtual void UpdateCombatLogic()
        {
            // Update target tracking
            UpdateTargetTracking();

            // Update combat state
            UpdateCombatState();

            // Perform combat update
            PerformCombatUpdate();
        }

        protected virtual void UpdateTargetTracking()
        {
            if (currentTarget == null)
            {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs && Time.time % 5f < 0.1f) // Log every 5 seconds
                {
                    Debug.LogWarning($"[{GetType().Name}] UpdateTargetTracking - No target set!");
                }
#endif
                return;
            }

            float distanceToTarget = Vector3.Distance(entityTransform.position, currentTarget.position);
            bool targetInRange = distanceToTarget <= attackRange;
            bool hasLineOfSight = !requiresLineOfSight || CheckLineOfSight(currentTarget);

            bool shouldBeInCombat = targetInRange && hasLineOfSight;

            // Debug target tracking every few seconds
#if UNITY_EDITOR || DEVELOPMENT_BUILD
            if (enableDebugLogs && Time.time % 4f < 0.1f) // Log every 4 seconds
            {
                Debug.Log($"[{GetType().Name}] Target Tracking - Distance: {distanceToTarget}/{attackRange}, InRange: {targetInRange}, LineOfSight: {hasLineOfSight}, ShouldBeInCombat: {shouldBeInCombat}, CurrentlyInCombat: {inCombat}");
            }
#endif

            if (shouldBeInCombat != inCombat)
            {
                SetCombatState(shouldBeInCombat);
            }
        }

        protected virtual void UpdateCombatState()
        {
            if (!inCombat)
            {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs && Time.time % 6f < 0.1f) // Log every 6 seconds
                {
                    Debug.Log($"[{GetType().Name}] UpdateCombatState - Not in combat, skipping attack checks");
                }
#endif
                return;
            }

            // Check if we can attack
            if (CanAttack && ShouldAttack())
            {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs)
                {
                    Debug.Log($"[{GetType().Name}] *** ATTEMPTING ATTACK *** - CanAttack: {CanAttack}, ShouldAttack: true");
                }
#endif
                PerformAttack();
            }
#if UNITY_EDITOR || DEVELOPMENT_BUILD
            else if (enableDebugLogs && Time.time % 2f < 0.1f) // Log every 2 seconds when in combat but not attacking
            {
                Debug.Log($"[{GetType().Name}] In combat but not attacking - CanAttack: {CanAttack}, ShouldAttack: {ShouldAttack()}, Time since last: {(Time.time - lastAttackTime)}, Cooldown: {attackCooldown}");
            }
#endif
        }

        public virtual void SetTarget(Transform target)
        {
            if (currentTarget == target)
                return;

            Transform oldTarget = currentTarget;
            currentTarget = target;

            if (oldTarget != null)
            {
                OnTargetLost?.Invoke(oldTarget);
            }

            if (currentTarget != null)
            {
                OnTargetAcquired?.Invoke(currentTarget);

#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs)
                {
                    Debug.Log($"[{GetType().Name}] Target acquired: {currentTarget.name}");
                }
#endif
            }
        }

        public virtual void PerformAttack()
        {
            if (!CanAttack)
            {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs)
                {
                    Debug.LogWarning($"[{GetType().Name}] Cannot attack - conditions not met");
                }
#endif
                return;
            }

            lastAttackTime = Time.time;
            OnAttackStarted?.Invoke();

#if UNITY_EDITOR || DEVELOPMENT_BUILD
            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Performing attack on {currentTarget?.name ?? "NULL"}");
            }
#endif

            // Call subclass attack implementation
            ExecuteAttack();

            OnAttackCompleted?.Invoke();
        }

        protected virtual void SetCombatState(bool inCombatState)
        {
            if (inCombat == inCombatState)
                return;

            inCombat = inCombatState;

            if (inCombat)
            {
                OnCombatEntered?.Invoke();
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs)
                {
                    float range = currentTarget != null ? Vector3.Distance(entityTransform.position, currentTarget.position) : -1f;
                    Debug.Log($"[{GetType().Name}] *** ENTERED COMBAT *** - Target: {currentTarget?.name ?? "NULL"}, Range: {range}");
                }
#endif
            }
            else
            {
                OnCombatExited?.Invoke();
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs)
                {
                    Debug.Log($"[{GetType().Name}] *** EXITED COMBAT ***");
                }
#endif
            }
        }

        protected virtual bool CheckLineOfSight(Transform target)
        {
            if (target == null)
                return false;

            Vector3 direction = target.position - entityTransform.position;
            float distance = direction.magnitude;

            if (Physics.Raycast(entityTransform.position, direction.normalized, distance, obstacleLayerMask))
            {
                return false; // Obstacle in the way
            }

            return true;
        }

        protected virtual void FindPlayer()
        {
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                playerTransform = player.transform;
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (enableDebugLogs)
                {
                    Debug.Log($"[{GetType().Name}] Player found: {player.name}");
                }
#endif
            }
#if UNITY_EDITOR || DEVELOPMENT_BUILD
            else if (enableDebugLogs)
            {
                Debug.LogWarning($"[{GetType().Name}] No player found with 'Player' tag");
            }
#endif
        }

        // Abstract methods for subclasses to implement
        protected abstract void PerformCombatInitialization();
        protected abstract void PerformCombatActivation();
        protected abstract void PerformCombatDeactivation();
        protected abstract void PerformCombatCleanup();
        protected abstract void PerformCombatUpdate();
        protected abstract bool ShouldAttack();
        protected abstract void ExecuteAttack();

        // Public API for external control
        public virtual void SetAttackRange(float range)
        {
            attackRange = Mathf.Max(0f, range);
        }

        public virtual void SetAttackCooldown(float cooldown)
        {
            attackCooldown = Mathf.Max(0f, cooldown);
        }

        public virtual void SetCanAttack(bool canAttackState)
        {
            canAttack = canAttackState;
        }
    }
}
