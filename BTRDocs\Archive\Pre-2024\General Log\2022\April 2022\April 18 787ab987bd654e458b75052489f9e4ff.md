# April 18

Finally getting around to hi hat controller vibrate

Is this cool? Interesting? Does it work? <PERSON><PERSON>

Implemented in Shooting / Crosshair class as OnMusicalFakeHiHat

Fast speeds are not that effective - latency is part of the problem

In part due to the speed at which the motor can be kicked in. Can this be accounted for? 

Is it even that interesting?

for FX, things happening on screen, lock on, I think the latency may be ok.

But representing a quick musical sound - Not working

Options within Unity’s new input system but not sure it’s worth pursuing

[https://www.youtube.com/watch?v=WSw82nKXibc](https://www.youtube.com/watch?v=WSw82nKXibc)

Thinking about a few highlighted good short, minimalist, and other games

 [https://www.gamingscan.com/best-short-games/](https://www.gamingscan.com/best-short-games/)

Sayonara Wild Hearts

Going Under

Incredibox - music game

Monomals - another music sort-of game

Trying to document aspects of the game in Figma instead of Miro

 

Not feeling like this is super beneficial

What do I currently have?

Lock on system - can shoot enemies

Basic enemy AI for shooting me and running around the playing field

My lock on system - Can work overhead / first person / third person 

Narrative Idea - You are an AI auto protection system guarding a being transmitted through earth

Think AI protection in Alien, Prometheus, etc. 

Make camera lean in opposite direction as we aim?

Need wide open spaces to see bullets flying at me currently

But we want fast movement / intense action, quick decisions

How to rectify this?

Think Panzer Dragoon 

Think After Burner - First one is so FAST!!!

<aside>
💡 Tilting Camera from After Burner!

</aside>

Slight tilt, nothing too major

<aside>
💡 Make the grab and launch a quicker action - HOW

</aside>

Current slight slower pace work for some types of music

Do faster for other style of song

FAST enemies - die quickly

Approach the player more directly, Should not move between quadrants too much?

IF there’s lots of movement between quadrants - don’t shoot as much!

- How to do this with Behavior Tree?

Slower thoughtful enemies - move around the player, take a few hits

WAVES of projectiles around the player to grab and shoot at enemies in these cases

How does a level like this function??

[https://www.youtube.com/watch?v=PjZLIiupIsQ&t=1559s](https://www.youtube.com/watch?v=PjZLIiupIsQ&t=1559s)

Collecting lots of bullets, pushed to the limits

Enemy Type - CANNOT shoot but do not want to touch, 

Will approach you and self destruct

Shoot down before it does!!

Collect bullets up and take these guys out

Maybe work with opposite colours??

Ways in which a track will progress / change

- Death of an enemy Waves
- Pass a barrier / object

Also have unlockable bullets - require close proximity blast / time glitch