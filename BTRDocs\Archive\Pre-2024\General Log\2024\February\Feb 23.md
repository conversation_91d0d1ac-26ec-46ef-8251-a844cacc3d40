# Feb 23

Updated Amplify Shader Editor

Got this message

- A new URP version 14.0.10 was detected and new templates are being imported.
Please hit the Update button on your ASE canvas to recompile your shader under the newest version.

Need to investigate Leak issues

Leak Detected : Persistent allocates 4 individual allocations. To find out more please enable 'Preferences > Jobs > Leak Detection Level > Enabled With Stack Trace' and reproduce the leak again.

Working on Materials swap, not properly working currently. Leaving for now

Created Kanban board to be able to log and address issues easier

Working on Ouroboros level, trying to make it playable throughout

Mobius Tube 2 is pretty good

Mobius Tube 2 Variation has a solid start

Mobius Tube 7 has a solid start

Mobius Tube 6 needs a lot of work - no enemies? Maybe a bad structure