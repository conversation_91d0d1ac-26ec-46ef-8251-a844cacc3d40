# 2022 - Tooling & Workflow - Summary

This document summarizes the tooling and workflow notes from 2022, focusing on software updates, workflow adjustments, and advice sought for improving development practices.

## Key Summary Points:

*   **Unity Version Upgrades (April):** Successfully upgraded to Unity 2021 LTS from 2020.3.30f, noting no immediate issues. This reflects a proactive approach to keeping the project aligned with current Unity versions.
*   **Asset Management and Hard Drive Cleanup (April):**  Focused on optimizing local development environment by cleaning up the hard drive and efficiently managing project assets, including moving landforms to external storage.
*   **Documentation and Planning (April, April):** Experimented with Figma for game documentation, but found it less beneficial than Miro. Emphasized the need for solid planning and evaluation to drive project progress.
*   **Software Architecture and Coding Practices (May):** Sought advice on OOP techniques and SOLID principles from <PERSON>, and studied <PERSON>'s videos on clean code structuring, indicating a focus on improving code quality and architecture.
*   **Performance Optimization Focus (April, May):**  Actively researched performance enhancement tips and techniques, watching videos and seeking strategies to optimize the game's performance.
*   **Workflow and Creativity (April, April):** Considered the balance between creative freedom and limitations, noting that limitations can sometimes stoke creativity.
*   **External Tools and Integrations (April):** Explored Random Flow for Blender for creating metallic textures and materials, and investigated using ShaderForge and Pro Builder for texturing and UV mapping.
*   **YouTube Tutorials for Specific Features (Jan, May, July):**  Utilized YouTube tutorials for guidance on specific features like "You Died" screens, pause screens, better coding practices in Unity, and object pooling, showing a hands-on approach to learning and implementing new features.
*   **Dynamic Crosshair (Sept):** नोटेड the development of a dynamic crosshair, suggesting iterative improvements to core gameplay elements.

This summary indicates a year focused on both the technical foundations of the project (Unity upgrades, asset management) and the refinement of development workflows and coding practices. The emphasis on performance optimization and seeking expert advice underscores a commitment to creating a polished and well-structured game.